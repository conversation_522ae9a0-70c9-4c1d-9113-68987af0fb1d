namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class bybit : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "bybit" },
            { "name", "Bybit" },
            { "countries", new List<object>() {"VG"} },
            { "version", "v5" },
            { "userAgent", null },
            { "rateLimit", 20 },
            { "hostname", "bybit.com" },
            { "pro", true },
            { "certified", true },
            { "has", new Dictionary<string, object>() {
                { "CORS", true },
                { "spot", true },
                { "margin", true },
                { "swap", true },
                { "future", true },
                { "option", true },
                { "borrowCrossMargin", true },
                { "cancelAllOrders", true },
                { "cancelAllOrdersAfter", true },
                { "cancelOrder", true },
                { "cancelOrders", true },
                { "cancelOrdersForSymbols", true },
                { "closeAllPositions", false },
                { "closePosition", false },
                { "createConvertTrade", true },
                { "createMarketBuyOrderWithCost", true },
                { "createMarketSellOrderWithCost", true },
                { "createOrder", true },
                { "createOrders", true },
                { "createOrderWithTakeProfitAndStopLoss", true },
                { "createPostOnlyOrder", true },
                { "createReduceOnlyOrder", true },
                { "createStopLimitOrder", true },
                { "createStopLossOrder", true },
                { "createStopMarketOrder", true },
                { "createStopOrder", true },
                { "createTakeProfitOrder", true },
                { "createTrailingAmountOrder", true },
                { "createTriggerOrder", true },
                { "editOrder", true },
                { "editOrders", true },
                { "fetchAllGreeks", true },
                { "fetchBalance", true },
                { "fetchBidsAsks", "emulated" },
                { "fetchBorrowInterest", false },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchCanceledAndClosedOrders", true },
                { "fetchCanceledOrders", true },
                { "fetchClosedOrder", true },
                { "fetchClosedOrders", true },
                { "fetchConvertCurrencies", true },
                { "fetchConvertQuote", true },
                { "fetchConvertTrade", true },
                { "fetchConvertTradeHistory", true },
                { "fetchCrossBorrowRate", true },
                { "fetchCrossBorrowRates", false },
                { "fetchCurrencies", true },
                { "fetchDeposit", false },
                { "fetchDepositAddress", true },
                { "fetchDepositAddresses", false },
                { "fetchDepositAddressesByNetwork", true },
                { "fetchDeposits", true },
                { "fetchDepositWithdrawFee", "emulated" },
                { "fetchDepositWithdrawFees", true },
                { "fetchFundingHistory", true },
                { "fetchFundingRate", "emulated" },
                { "fetchFundingRateHistory", true },
                { "fetchFundingRates", true },
                { "fetchGreeks", true },
                { "fetchIndexOHLCV", true },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchLedger", true },
                { "fetchLeverage", true },
                { "fetchLeverageTiers", true },
                { "fetchLongShortRatio", false },
                { "fetchLongShortRatioHistory", true },
                { "fetchMarginAdjustmentHistory", false },
                { "fetchMarketLeverageTiers", true },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", true },
                { "fetchMyLiquidations", true },
                { "fetchMySettlementHistory", true },
                { "fetchMyTrades", true },
                { "fetchOHLCV", true },
                { "fetchOpenInterest", true },
                { "fetchOpenInterestHistory", true },
                { "fetchOpenOrder", true },
                { "fetchOpenOrders", true },
                { "fetchOption", true },
                { "fetchOptionChain", true },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchOrders", false },
                { "fetchOrderTrades", true },
                { "fetchPosition", true },
                { "fetchPositionHistory", "emulated" },
                { "fetchPositions", true },
                { "fetchPositionsHistory", true },
                { "fetchPremiumIndexOHLCV", true },
                { "fetchSettlementHistory", true },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTime", true },
                { "fetchTrades", true },
                { "fetchTradingFee", true },
                { "fetchTradingFees", true },
                { "fetchTransactions", false },
                { "fetchTransfers", true },
                { "fetchUnderlyingAssets", false },
                { "fetchVolatilityHistory", true },
                { "fetchWithdrawals", true },
                { "repayCrossMargin", true },
                { "sandbox", true },
                { "setLeverage", true },
                { "setMarginMode", true },
                { "setPositionMode", true },
                { "transfer", true },
                { "withdraw", true },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", "1" },
                { "3m", "3" },
                { "5m", "5" },
                { "15m", "15" },
                { "30m", "30" },
                { "1h", "60" },
                { "2h", "120" },
                { "4h", "240" },
                { "6h", "360" },
                { "12h", "720" },
                { "1d", "D" },
                { "1w", "W" },
                { "1M", "M" },
            } },
            { "urls", new Dictionary<string, object>() {
                { "test", new Dictionary<string, object>() {
                    { "spot", "https://api-testnet.{hostname}" },
                    { "futures", "https://api-testnet.{hostname}" },
                    { "v2", "https://api-testnet.{hostname}" },
                    { "public", "https://api-testnet.{hostname}" },
                    { "private", "https://api-testnet.{hostname}" },
                } },
                { "logo", "https://github.com/user-attachments/assets/97a5d0b3-de10-423d-90e1-6620960025ed" },
                { "api", new Dictionary<string, object>() {
                    { "spot", "https://api.{hostname}" },
                    { "futures", "https://api.{hostname}" },
                    { "v2", "https://api.{hostname}" },
                    { "public", "https://api.{hostname}" },
                    { "private", "https://api.{hostname}" },
                } },
                { "demotrading", new Dictionary<string, object>() {
                    { "spot", "https://api-demo.{hostname}" },
                    { "futures", "https://api-demo.{hostname}" },
                    { "v2", "https://api-demo.{hostname}" },
                    { "public", "https://api-demo.{hostname}" },
                    { "private", "https://api-demo.{hostname}" },
                } },
                { "www", "https://www.bybit.com" },
                { "doc", new List<object>() {"https://bybit-exchange.github.io/docs/inverse/", "https://bybit-exchange.github.io/docs/linear/", "https://github.com/bybit-exchange"} },
                { "fees", "https://help.bybit.com/hc/en-us/articles/360039261154" },
                { "referral", "https://www.bybit.com/register?affiliate_id=35953" },
            } },
            { "api", new Dictionary<string, object>() {
                { "public", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "spot/v3/public/symbols", 1 },
                        { "spot/v3/public/quote/depth", 1 },
                        { "spot/v3/public/quote/depth/merged", 1 },
                        { "spot/v3/public/quote/trades", 1 },
                        { "spot/v3/public/quote/kline", 1 },
                        { "spot/v3/public/quote/ticker/24hr", 1 },
                        { "spot/v3/public/quote/ticker/price", 1 },
                        { "spot/v3/public/quote/ticker/bookTicker", 1 },
                        { "spot/v3/public/server-time", 1 },
                        { "spot/v3/public/infos", 1 },
                        { "spot/v3/public/margin-product-infos", 1 },
                        { "spot/v3/public/margin-ensure-tokens", 1 },
                        { "v3/public/time", 1 },
                        { "contract/v3/public/copytrading/symbol/list", 1 },
                        { "derivatives/v3/public/order-book/L2", 1 },
                        { "derivatives/v3/public/kline", 1 },
                        { "derivatives/v3/public/tickers", 1 },
                        { "derivatives/v3/public/instruments-info", 1 },
                        { "derivatives/v3/public/mark-price-kline", 1 },
                        { "derivatives/v3/public/index-price-kline", 1 },
                        { "derivatives/v3/public/funding/history-funding-rate", 1 },
                        { "derivatives/v3/public/risk-limit/list", 1 },
                        { "derivatives/v3/public/delivery-price", 1 },
                        { "derivatives/v3/public/recent-trade", 1 },
                        { "derivatives/v3/public/open-interest", 1 },
                        { "derivatives/v3/public/insurance", 1 },
                        { "v5/announcements/index", 5 },
                        { "v5/market/time", 5 },
                        { "v5/market/kline", 5 },
                        { "v5/market/mark-price-kline", 5 },
                        { "v5/market/index-price-kline", 5 },
                        { "v5/market/premium-index-price-kline", 5 },
                        { "v5/market/instruments-info", 5 },
                        { "v5/market/orderbook", 5 },
                        { "v5/market/tickers", 5 },
                        { "v5/market/funding/history", 5 },
                        { "v5/market/recent-trade", 5 },
                        { "v5/market/open-interest", 5 },
                        { "v5/market/historical-volatility", 5 },
                        { "v5/market/insurance", 5 },
                        { "v5/market/risk-limit", 5 },
                        { "v5/market/delivery-price", 5 },
                        { "v5/market/account-ratio", 5 },
                        { "v5/spot-lever-token/info", 5 },
                        { "v5/spot-lever-token/reference", 5 },
                        { "v5/spot-margin-trade/data", 5 },
                        { "v5/spot-margin-trade/collateral", 5 },
                        { "v5/spot-cross-margin-trade/data", 5 },
                        { "v5/spot-cross-margin-trade/pledge-token", 5 },
                        { "v5/spot-cross-margin-trade/borrow-token", 5 },
                        { "v5/crypto-loan/collateral-data", 5 },
                        { "v5/crypto-loan/loanable-data", 5 },
                        { "v5/ins-loan/product-infos", 5 },
                        { "v5/ins-loan/ensure-tokens-convert", 5 },
                        { "v5/earn/product", 5 },
                    } },
                } },
                { "private", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "v5/market/instruments-info", 5 },
                        { "v2/private/wallet/fund/records", 25 },
                        { "spot/v3/private/order", 2.5 },
                        { "spot/v3/private/open-orders", 2.5 },
                        { "spot/v3/private/history-orders", 2.5 },
                        { "spot/v3/private/my-trades", 2.5 },
                        { "spot/v3/private/account", 2.5 },
                        { "spot/v3/private/reference", 2.5 },
                        { "spot/v3/private/record", 2.5 },
                        { "spot/v3/private/cross-margin-orders", 10 },
                        { "spot/v3/private/cross-margin-account", 10 },
                        { "spot/v3/private/cross-margin-loan-info", 10 },
                        { "spot/v3/private/cross-margin-repay-history", 10 },
                        { "spot/v3/private/margin-loan-infos", 10 },
                        { "spot/v3/private/margin-repaid-infos", 10 },
                        { "spot/v3/private/margin-ltv", 10 },
                        { "asset/v3/private/transfer/inter-transfer/list/query", 50 },
                        { "asset/v3/private/transfer/sub-member/list/query", 50 },
                        { "asset/v3/private/transfer/sub-member-transfer/list/query", 50 },
                        { "asset/v3/private/transfer/universal-transfer/list/query", 25 },
                        { "asset/v3/private/coin-info/query", 25 },
                        { "asset/v3/private/deposit/address/query", 10 },
                        { "contract/v3/private/copytrading/order/list", 30 },
                        { "contract/v3/private/copytrading/position/list", 40 },
                        { "contract/v3/private/copytrading/wallet/balance", 25 },
                        { "contract/v3/private/position/limit-info", 25 },
                        { "contract/v3/private/order/unfilled-orders", 1 },
                        { "contract/v3/private/order/list", 1 },
                        { "contract/v3/private/position/list", 1 },
                        { "contract/v3/private/execution/list", 1 },
                        { "contract/v3/private/position/closed-pnl", 1 },
                        { "contract/v3/private/account/wallet/balance", 1 },
                        { "contract/v3/private/account/fee-rate", 1 },
                        { "contract/v3/private/account/wallet/fund-records", 1 },
                        { "unified/v3/private/order/unfilled-orders", 1 },
                        { "unified/v3/private/order/list", 1 },
                        { "unified/v3/private/position/list", 1 },
                        { "unified/v3/private/execution/list", 1 },
                        { "unified/v3/private/delivery-record", 1 },
                        { "unified/v3/private/settlement-record", 1 },
                        { "unified/v3/private/account/wallet/balance", 1 },
                        { "unified/v3/private/account/transaction-log", 1 },
                        { "unified/v3/private/account/borrow-history", 1 },
                        { "unified/v3/private/account/borrow-rate", 1 },
                        { "unified/v3/private/account/info", 1 },
                        { "user/v3/private/frozen-sub-member", 10 },
                        { "user/v3/private/query-sub-members", 5 },
                        { "user/v3/private/query-api", 5 },
                        { "user/v3/private/get-member-type", 1 },
                        { "asset/v3/private/transfer/transfer-coin/list/query", 50 },
                        { "asset/v3/private/transfer/account-coin/balance/query", 50 },
                        { "asset/v3/private/transfer/account-coins/balance/query", 25 },
                        { "asset/v3/private/transfer/asset-info/query", 50 },
                        { "asset/v3/public/deposit/allowed-deposit-list/query", 0.17 },
                        { "asset/v3/private/deposit/record/query", 10 },
                        { "asset/v3/private/withdraw/record/query", 10 },
                        { "v5/order/realtime", 5 },
                        { "v5/order/history", 5 },
                        { "v5/order/spot-borrow-check", 1 },
                        { "v5/position/list", 5 },
                        { "v5/execution/list", 5 },
                        { "v5/position/closed-pnl", 5 },
                        { "v5/position/move-history", 5 },
                        { "v5/pre-upgrade/order/history", 5 },
                        { "v5/pre-upgrade/execution/list", 5 },
                        { "v5/pre-upgrade/position/closed-pnl", 5 },
                        { "v5/pre-upgrade/account/transaction-log", 5 },
                        { "v5/pre-upgrade/asset/delivery-record", 5 },
                        { "v5/pre-upgrade/asset/settlement-record", 5 },
                        { "v5/account/wallet-balance", 1 },
                        { "v5/account/borrow-history", 1 },
                        { "v5/account/collateral-info", 1 },
                        { "v5/asset/coin-greeks", 1 },
                        { "v5/account/fee-rate", 10 },
                        { "v5/account/info", 5 },
                        { "v5/account/transaction-log", 1 },
                        { "v5/account/contract-transaction-log", 1 },
                        { "v5/account/smp-group", 1 },
                        { "v5/account/mmp-state", 5 },
                        { "v5/account/withdrawal", 5 },
                        { "v5/asset/exchange/query-coin-list", 0.5 },
                        { "v5/asset/exchange/convert-result-query", 0.5 },
                        { "v5/asset/exchange/query-convert-history", 0.5 },
                        { "v5/asset/exchange/order-record", 5 },
                        { "v5/asset/delivery-record", 5 },
                        { "v5/asset/settlement-record", 5 },
                        { "v5/asset/transfer/query-asset-info", 50 },
                        { "v5/asset/transfer/query-account-coins-balance", 25 },
                        { "v5/asset/transfer/query-account-coin-balance", 50 },
                        { "v5/asset/transfer/query-transfer-coin-list", 50 },
                        { "v5/asset/transfer/query-inter-transfer-list", 50 },
                        { "v5/asset/transfer/query-sub-member-list", 50 },
                        { "v5/asset/transfer/query-universal-transfer-list", 25 },
                        { "v5/asset/deposit/query-allowed-list", 5 },
                        { "v5/asset/deposit/query-record", 10 },
                        { "v5/asset/deposit/query-sub-member-record", 10 },
                        { "v5/asset/deposit/query-internal-record", 5 },
                        { "v5/asset/deposit/query-address", 10 },
                        { "v5/asset/deposit/query-sub-member-address", 10 },
                        { "v5/asset/coin/query-info", 28 },
                        { "v5/asset/withdraw/query-record", 10 },
                        { "v5/asset/withdraw/withdrawable-amount", 5 },
                        { "v5/asset/withdraw/vasp/list", 5 },
                        { "v5/user/query-sub-members", 5 },
                        { "v5/user/query-api", 5 },
                        { "v5/user/sub-apikeys", 5 },
                        { "v5/user/get-member-type", 5 },
                        { "v5/user/aff-customer-info", 5 },
                        { "v5/user/del-submember", 5 },
                        { "v5/user/submembers", 5 },
                        { "v5/affiliate/aff-user-list", 5 },
                        { "v5/spot-lever-token/order-record", 1 },
                        { "v5/spot-margin-trade/interest-rate-history", 5 },
                        { "v5/spot-margin-trade/state", 5 },
                        { "v5/spot-cross-margin-trade/loan-info", 1 },
                        { "v5/spot-cross-margin-trade/account", 1 },
                        { "v5/spot-cross-margin-trade/orders", 1 },
                        { "v5/spot-cross-margin-trade/repay-history", 1 },
                        { "v5/crypto-loan/borrowable-collateralisable-number", 5 },
                        { "v5/crypto-loan/ongoing-orders", 5 },
                        { "v5/crypto-loan/repayment-history", 5 },
                        { "v5/crypto-loan/borrow-history", 5 },
                        { "v5/crypto-loan/max-collateral-amount", 5 },
                        { "v5/crypto-loan/adjustment-history", 5 },
                        { "v5/ins-loan/product-infos", 5 },
                        { "v5/ins-loan/ensure-tokens-convert", 5 },
                        { "v5/ins-loan/loan-order", 5 },
                        { "v5/ins-loan/repaid-history", 5 },
                        { "v5/ins-loan/ltv-convert", 5 },
                        { "v5/lending/info", 5 },
                        { "v5/lending/history-order", 5 },
                        { "v5/lending/account", 5 },
                        { "v5/broker/earning-record", 5 },
                        { "v5/broker/earnings-info", 5 },
                        { "v5/broker/account-info", 5 },
                        { "v5/broker/asset/query-sub-member-deposit-record", 10 },
                        { "v5/earn/order", 5 },
                        { "v5/earn/position", 5 },
                    } },
                    { "post", new Dictionary<string, object>() {
                        { "spot/v3/private/order", 2.5 },
                        { "spot/v3/private/cancel-order", 2.5 },
                        { "spot/v3/private/cancel-orders", 2.5 },
                        { "spot/v3/private/cancel-orders-by-ids", 2.5 },
                        { "spot/v3/private/purchase", 2.5 },
                        { "spot/v3/private/redeem", 2.5 },
                        { "spot/v3/private/cross-margin-loan", 10 },
                        { "spot/v3/private/cross-margin-repay", 10 },
                        { "asset/v3/private/transfer/inter-transfer", 150 },
                        { "asset/v3/private/withdraw/create", 300 },
                        { "asset/v3/private/withdraw/cancel", 50 },
                        { "asset/v3/private/transfer/sub-member-transfer", 150 },
                        { "asset/v3/private/transfer/transfer-sub-member-save", 150 },
                        { "asset/v3/private/transfer/universal-transfer", 10 },
                        { "user/v3/private/create-sub-member", 10 },
                        { "user/v3/private/create-sub-api", 10 },
                        { "user/v3/private/update-api", 10 },
                        { "user/v3/private/delete-api", 10 },
                        { "user/v3/private/update-sub-api", 10 },
                        { "user/v3/private/delete-sub-api", 10 },
                        { "contract/v3/private/copytrading/order/create", 30 },
                        { "contract/v3/private/copytrading/order/cancel", 30 },
                        { "contract/v3/private/copytrading/order/close", 30 },
                        { "contract/v3/private/copytrading/position/close", 40 },
                        { "contract/v3/private/copytrading/position/set-leverage", 40 },
                        { "contract/v3/private/copytrading/wallet/transfer", 25 },
                        { "contract/v3/private/copytrading/order/trading-stop", 2.5 },
                        { "contract/v3/private/order/create", 1 },
                        { "contract/v3/private/order/cancel", 1 },
                        { "contract/v3/private/order/cancel-all", 1 },
                        { "contract/v3/private/order/replace", 1 },
                        { "contract/v3/private/position/set-auto-add-margin", 1 },
                        { "contract/v3/private/position/switch-isolated", 1 },
                        { "contract/v3/private/position/switch-mode", 1 },
                        { "contract/v3/private/position/switch-tpsl-mode", 1 },
                        { "contract/v3/private/position/set-leverage", 1 },
                        { "contract/v3/private/position/trading-stop", 1 },
                        { "contract/v3/private/position/set-risk-limit", 1 },
                        { "contract/v3/private/account/setMarginMode", 1 },
                        { "unified/v3/private/order/create", 30 },
                        { "unified/v3/private/order/replace", 30 },
                        { "unified/v3/private/order/cancel", 30 },
                        { "unified/v3/private/order/create-batch", 30 },
                        { "unified/v3/private/order/replace-batch", 30 },
                        { "unified/v3/private/order/cancel-batch", 30 },
                        { "unified/v3/private/order/cancel-all", 30 },
                        { "unified/v3/private/position/set-leverage", 2.5 },
                        { "unified/v3/private/position/tpsl/switch-mode", 2.5 },
                        { "unified/v3/private/position/set-risk-limit", 2.5 },
                        { "unified/v3/private/position/trading-stop", 2.5 },
                        { "unified/v3/private/account/upgrade-unified-account", 2.5 },
                        { "unified/v3/private/account/setMarginMode", 2.5 },
                        { "fht/compliance/tax/v3/private/registertime", 50 },
                        { "fht/compliance/tax/v3/private/create", 50 },
                        { "fht/compliance/tax/v3/private/status", 50 },
                        { "fht/compliance/tax/v3/private/url", 50 },
                        { "v5/order/create", 2.5 },
                        { "v5/order/amend", 5 },
                        { "v5/order/cancel", 2.5 },
                        { "v5/order/cancel-all", 50 },
                        { "v5/order/create-batch", 5 },
                        { "v5/order/amend-batch", 5 },
                        { "v5/order/cancel-batch", 5 },
                        { "v5/order/disconnected-cancel-all", 5 },
                        { "v5/position/set-leverage", 5 },
                        { "v5/position/switch-isolated", 5 },
                        { "v5/position/set-tpsl-mode", 5 },
                        { "v5/position/switch-mode", 5 },
                        { "v5/position/set-risk-limit", 5 },
                        { "v5/position/trading-stop", 5 },
                        { "v5/position/set-auto-add-margin", 5 },
                        { "v5/position/add-margin", 5 },
                        { "v5/position/move-positions", 5 },
                        { "v5/position/confirm-pending-mmr", 5 },
                        { "v5/account/upgrade-to-uta", 5 },
                        { "v5/account/quick-repayment", 5 },
                        { "v5/account/set-margin-mode", 5 },
                        { "v5/account/set-hedging-mode", 5 },
                        { "v5/account/mmp-modify", 5 },
                        { "v5/account/mmp-reset", 5 },
                        { "v5/asset/exchange/quote-apply", 1 },
                        { "v5/asset/exchange/convert-execute", 1 },
                        { "v5/asset/transfer/inter-transfer", 50 },
                        { "v5/asset/transfer/save-transfer-sub-member", 150 },
                        { "v5/asset/transfer/universal-transfer", 10 },
                        { "v5/asset/deposit/deposit-to-account", 5 },
                        { "v5/asset/withdraw/create", 50 },
                        { "v5/asset/withdraw/cancel", 50 },
                        { "v5/user/create-sub-member", 10 },
                        { "v5/user/create-sub-api", 10 },
                        { "v5/user/frozen-sub-member", 10 },
                        { "v5/user/update-api", 10 },
                        { "v5/user/update-sub-api", 10 },
                        { "v5/user/delete-api", 10 },
                        { "v5/user/delete-sub-api", 10 },
                        { "v5/spot-lever-token/purchase", 2.5 },
                        { "v5/spot-lever-token/redeem", 2.5 },
                        { "v5/spot-margin-trade/switch-mode", 5 },
                        { "v5/spot-margin-trade/set-leverage", 5 },
                        { "v5/spot-cross-margin-trade/loan", 2.5 },
                        { "v5/spot-cross-margin-trade/repay", 2.5 },
                        { "v5/spot-cross-margin-trade/switch", 2.5 },
                        { "v5/crypto-loan/borrow", 5 },
                        { "v5/crypto-loan/repay", 5 },
                        { "v5/crypto-loan/adjust-ltv", 5 },
                        { "v5/ins-loan/association-uid", 5 },
                        { "v5/lending/purchase", 5 },
                        { "v5/lending/redeem", 5 },
                        { "v5/lending/redeem-cancel", 5 },
                        { "v5/account/set-collateral-switch", 5 },
                        { "v5/account/set-collateral-switch-batch", 5 },
                        { "v5/account/demo-apply-money", 5 },
                        { "v5/broker/award/info", 5 },
                        { "v5/broker/award/distribute-award", 5 },
                        { "v5/broker/award/distribution-record", 5 },
                        { "v5/earn/place-order", 5 },
                    } },
                } },
            } },
            { "httpExceptions", new Dictionary<string, object>() {
                { "403", typeof(RateLimitExceeded) },
            } },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "-10009", typeof(BadRequest) },
                    { "-1004", typeof(BadRequest) },
                    { "-1021", typeof(BadRequest) },
                    { "-1103", typeof(BadRequest) },
                    { "-1140", typeof(InvalidOrder) },
                    { "-1197", typeof(InvalidOrder) },
                    { "-2013", typeof(InvalidOrder) },
                    { "-2015", typeof(AuthenticationError) },
                    { "-6017", typeof(BadRequest) },
                    { "-6025", typeof(BadRequest) },
                    { "-6029", typeof(BadRequest) },
                    { "5004", typeof(ExchangeError) },
                    { "7001", typeof(BadRequest) },
                    { "10001", typeof(BadRequest) },
                    { "10002", typeof(InvalidNonce) },
                    { "10003", typeof(AuthenticationError) },
                    { "10004", typeof(AuthenticationError) },
                    { "10005", typeof(PermissionDenied) },
                    { "10006", typeof(RateLimitExceeded) },
                    { "10007", typeof(AuthenticationError) },
                    { "10008", typeof(AccountSuspended) },
                    { "10009", typeof(AuthenticationError) },
                    { "10010", typeof(PermissionDenied) },
                    { "10014", typeof(BadRequest) },
                    { "10016", typeof(ExchangeError) },
                    { "10017", typeof(BadRequest) },
                    { "10018", typeof(RateLimitExceeded) },
                    { "10020", typeof(PermissionDenied) },
                    { "10024", typeof(PermissionDenied) },
                    { "10027", typeof(PermissionDenied) },
                    { "10028", typeof(PermissionDenied) },
                    { "10029", typeof(PermissionDenied) },
                    { "12137", typeof(InvalidOrder) },
                    { "12201", typeof(BadRequest) },
                    { "12141", typeof(BadRequest) },
                    { "100028", typeof(PermissionDenied) },
                    { "110001", typeof(OrderNotFound) },
                    { "110003", typeof(InvalidOrder) },
                    { "110004", typeof(InsufficientFunds) },
                    { "110005", typeof(InvalidOrder) },
                    { "110006", typeof(InsufficientFunds) },
                    { "110007", typeof(InsufficientFunds) },
                    { "110008", typeof(InvalidOrder) },
                    { "110009", typeof(InvalidOrder) },
                    { "110010", typeof(InvalidOrder) },
                    { "110011", typeof(InvalidOrder) },
                    { "110012", typeof(InsufficientFunds) },
                    { "110013", typeof(BadRequest) },
                    { "110014", typeof(InsufficientFunds) },
                    { "110015", typeof(BadRequest) },
                    { "110016", typeof(InvalidOrder) },
                    { "110017", typeof(InvalidOrder) },
                    { "110018", typeof(BadRequest) },
                    { "110019", typeof(InvalidOrder) },
                    { "110020", typeof(InvalidOrder) },
                    { "110021", typeof(InvalidOrder) },
                    { "110022", typeof(InvalidOrder) },
                    { "110023", typeof(InvalidOrder) },
                    { "110024", typeof(BadRequest) },
                    { "110025", typeof(NoChange) },
                    { "110026", typeof(MarginModeAlreadySet) },
                    { "110027", typeof(NoChange) },
                    { "110028", typeof(BadRequest) },
                    { "110029", typeof(BadRequest) },
                    { "110030", typeof(InvalidOrder) },
                    { "110031", typeof(InvalidOrder) },
                    { "110032", typeof(InvalidOrder) },
                    { "110033", typeof(InvalidOrder) },
                    { "110034", typeof(InvalidOrder) },
                    { "110035", typeof(InvalidOrder) },
                    { "110036", typeof(InvalidOrder) },
                    { "110037", typeof(InvalidOrder) },
                    { "110038", typeof(InvalidOrder) },
                    { "110039", typeof(InvalidOrder) },
                    { "110040", typeof(InvalidOrder) },
                    { "110041", typeof(InvalidOrder) },
                    { "110042", typeof(InvalidOrder) },
                    { "110043", typeof(BadRequest) },
                    { "110044", typeof(InsufficientFunds) },
                    { "110045", typeof(InsufficientFunds) },
                    { "110046", typeof(BadRequest) },
                    { "110047", typeof(BadRequest) },
                    { "110048", typeof(BadRequest) },
                    { "110049", typeof(BadRequest) },
                    { "110050", typeof(BadRequest) },
                    { "110051", typeof(InsufficientFunds) },
                    { "110052", typeof(InsufficientFunds) },
                    { "110053", typeof(InsufficientFunds) },
                    { "110054", typeof(InvalidOrder) },
                    { "110055", typeof(InvalidOrder) },
                    { "110056", typeof(InvalidOrder) },
                    { "110057", typeof(InvalidOrder) },
                    { "110058", typeof(InvalidOrder) },
                    { "110059", typeof(InvalidOrder) },
                    { "110060", typeof(BadRequest) },
                    { "110061", typeof(BadRequest) },
                    { "110062", typeof(BadRequest) },
                    { "110063", typeof(ExchangeError) },
                    { "110064", typeof(InvalidOrder) },
                    { "110065", typeof(PermissionDenied) },
                    { "110066", typeof(ExchangeError) },
                    { "110067", typeof(PermissionDenied) },
                    { "110068", typeof(PermissionDenied) },
                    { "110069", typeof(PermissionDenied) },
                    { "110070", typeof(InvalidOrder) },
                    { "110071", typeof(ExchangeError) },
                    { "110072", typeof(InvalidOrder) },
                    { "110073", typeof(ExchangeError) },
                    { "110092", typeof(InvalidOrder) },
                    { "110093", typeof(InvalidOrder) },
                    { "110094", typeof(InvalidOrder) },
                    { "130006", typeof(InvalidOrder) },
                    { "130021", typeof(InsufficientFunds) },
                    { "130074", typeof(InvalidOrder) },
                    { "131001", typeof(InsufficientFunds) },
                    { "131084", typeof(ExchangeError) },
                    { "131200", typeof(ExchangeError) },
                    { "131201", typeof(ExchangeError) },
                    { "131202", typeof(BadRequest) },
                    { "131203", typeof(BadRequest) },
                    { "131204", typeof(BadRequest) },
                    { "131205", typeof(BadRequest) },
                    { "131206", typeof(ExchangeError) },
                    { "131207", typeof(BadRequest) },
                    { "131208", typeof(ExchangeError) },
                    { "131209", typeof(BadRequest) },
                    { "131210", typeof(BadRequest) },
                    { "131211", typeof(BadRequest) },
                    { "131212", typeof(InsufficientFunds) },
                    { "131213", typeof(BadRequest) },
                    { "131214", typeof(BadRequest) },
                    { "131215", typeof(BadRequest) },
                    { "131216", typeof(ExchangeError) },
                    { "131217", typeof(ExchangeError) },
                    { "131231", typeof(NotSupported) },
                    { "131232", typeof(NotSupported) },
                    { "131002", typeof(BadRequest) },
                    { "131003", typeof(ExchangeError) },
                    { "131004", typeof(AuthenticationError) },
                    { "131085", typeof(InsufficientFunds) },
                    { "131086", typeof(BadRequest) },
                    { "131088", typeof(BadRequest) },
                    { "131089", typeof(BadRequest) },
                    { "131090", typeof(ExchangeError) },
                    { "131091", typeof(ExchangeError) },
                    { "131092", typeof(ExchangeError) },
                    { "131093", typeof(ExchangeError) },
                    { "131094", typeof(BadRequest) },
                    { "131095", typeof(BadRequest) },
                    { "131096", typeof(BadRequest) },
                    { "131097", typeof(ExchangeError) },
                    { "131098", typeof(ExchangeError) },
                    { "131099", typeof(ExchangeError) },
                    { "140001", typeof(OrderNotFound) },
                    { "140003", typeof(InvalidOrder) },
                    { "140004", typeof(InsufficientFunds) },
                    { "140005", typeof(InvalidOrder) },
                    { "140006", typeof(InsufficientFunds) },
                    { "140007", typeof(InsufficientFunds) },
                    { "140008", typeof(InvalidOrder) },
                    { "140009", typeof(InvalidOrder) },
                    { "140010", typeof(InvalidOrder) },
                    { "140011", typeof(InvalidOrder) },
                    { "140012", typeof(InsufficientFunds) },
                    { "140013", typeof(BadRequest) },
                    { "140014", typeof(InsufficientFunds) },
                    { "140015", typeof(InvalidOrder) },
                    { "140016", typeof(InvalidOrder) },
                    { "140017", typeof(InvalidOrder) },
                    { "140018", typeof(BadRequest) },
                    { "140019", typeof(InvalidOrder) },
                    { "140020", typeof(InvalidOrder) },
                    { "140021", typeof(InvalidOrder) },
                    { "140022", typeof(InvalidOrder) },
                    { "140023", typeof(InvalidOrder) },
                    { "140024", typeof(BadRequest) },
                    { "140025", typeof(BadRequest) },
                    { "140026", typeof(BadRequest) },
                    { "140027", typeof(BadRequest) },
                    { "140028", typeof(InvalidOrder) },
                    { "140029", typeof(BadRequest) },
                    { "140030", typeof(InvalidOrder) },
                    { "140031", typeof(BadRequest) },
                    { "140032", typeof(InvalidOrder) },
                    { "140033", typeof(InvalidOrder) },
                    { "140034", typeof(InvalidOrder) },
                    { "140035", typeof(InvalidOrder) },
                    { "140036", typeof(BadRequest) },
                    { "140037", typeof(InvalidOrder) },
                    { "140038", typeof(BadRequest) },
                    { "140039", typeof(BadRequest) },
                    { "140040", typeof(InvalidOrder) },
                    { "140041", typeof(InvalidOrder) },
                    { "140042", typeof(InvalidOrder) },
                    { "140043", typeof(BadRequest) },
                    { "140044", typeof(InsufficientFunds) },
                    { "140045", typeof(InsufficientFunds) },
                    { "140046", typeof(BadRequest) },
                    { "140047", typeof(BadRequest) },
                    { "140048", typeof(BadRequest) },
                    { "140049", typeof(BadRequest) },
                    { "140050", typeof(InvalidOrder) },
                    { "140051", typeof(InsufficientFunds) },
                    { "140052", typeof(InsufficientFunds) },
                    { "140053", typeof(InsufficientFunds) },
                    { "140054", typeof(InvalidOrder) },
                    { "140055", typeof(InvalidOrder) },
                    { "140056", typeof(InvalidOrder) },
                    { "140057", typeof(InvalidOrder) },
                    { "140058", typeof(InvalidOrder) },
                    { "140059", typeof(InvalidOrder) },
                    { "140060", typeof(BadRequest) },
                    { "140061", typeof(BadRequest) },
                    { "140062", typeof(BadRequest) },
                    { "140063", typeof(ExchangeError) },
                    { "140064", typeof(InvalidOrder) },
                    { "140065", typeof(PermissionDenied) },
                    { "140066", typeof(ExchangeError) },
                    { "140067", typeof(PermissionDenied) },
                    { "140068", typeof(PermissionDenied) },
                    { "140069", typeof(PermissionDenied) },
                    { "140070", typeof(InvalidOrder) },
                    { "170001", typeof(ExchangeError) },
                    { "170005", typeof(InvalidOrder) },
                    { "170007", typeof(RequestTimeout) },
                    { "170010", typeof(InvalidOrder) },
                    { "170011", typeof(InvalidOrder) },
                    { "170019", typeof(InvalidOrder) },
                    { "170031", typeof(ExchangeError) },
                    { "170032", typeof(ExchangeError) },
                    { "170033", typeof(InsufficientFunds) },
                    { "170034", typeof(InsufficientFunds) },
                    { "170035", typeof(BadRequest) },
                    { "170036", typeof(BadRequest) },
                    { "170037", typeof(BadRequest) },
                    { "170105", typeof(BadRequest) },
                    { "170115", typeof(InvalidOrder) },
                    { "170116", typeof(InvalidOrder) },
                    { "170117", typeof(InvalidOrder) },
                    { "170121", typeof(InvalidOrder) },
                    { "170124", typeof(InvalidOrder) },
                    { "170130", typeof(BadRequest) },
                    { "170131", typeof(InsufficientFunds) },
                    { "170132", typeof(InvalidOrder) },
                    { "170133", typeof(InvalidOrder) },
                    { "170134", typeof(InvalidOrder) },
                    { "170135", typeof(InvalidOrder) },
                    { "170136", typeof(InvalidOrder) },
                    { "170137", typeof(InvalidOrder) },
                    { "170139", typeof(InvalidOrder) },
                    { "170140", typeof(InvalidOrder) },
                    { "170141", typeof(InvalidOrder) },
                    { "170142", typeof(InvalidOrder) },
                    { "170143", typeof(InvalidOrder) },
                    { "170144", typeof(InvalidOrder) },
                    { "170145", typeof(InvalidOrder) },
                    { "170146", typeof(InvalidOrder) },
                    { "170147", typeof(InvalidOrder) },
                    { "170148", typeof(InvalidOrder) },
                    { "170149", typeof(ExchangeError) },
                    { "170150", typeof(ExchangeError) },
                    { "170151", typeof(InvalidOrder) },
                    { "170157", typeof(InvalidOrder) },
                    { "170159", typeof(InvalidOrder) },
                    { "170190", typeof(InvalidOrder) },
                    { "170191", typeof(InvalidOrder) },
                    { "170192", typeof(InvalidOrder) },
                    { "170193", typeof(InvalidOrder) },
                    { "170194", typeof(InvalidOrder) },
                    { "170195", typeof(InvalidOrder) },
                    { "170196", typeof(InvalidOrder) },
                    { "170197", typeof(InvalidOrder) },
                    { "170198", typeof(InvalidOrder) },
                    { "170199", typeof(InvalidOrder) },
                    { "170200", typeof(InvalidOrder) },
                    { "170201", typeof(PermissionDenied) },
                    { "170202", typeof(InvalidOrder) },
                    { "170203", typeof(InvalidOrder) },
                    { "170204", typeof(InvalidOrder) },
                    { "170206", typeof(InvalidOrder) },
                    { "170210", typeof(InvalidOrder) },
                    { "170213", typeof(OrderNotFound) },
                    { "170217", typeof(InvalidOrder) },
                    { "170218", typeof(InvalidOrder) },
                    { "170221", typeof(BadRequest) },
                    { "170222", typeof(RateLimitExceeded) },
                    { "170223", typeof(InsufficientFunds) },
                    { "170224", typeof(PermissionDenied) },
                    { "170226", typeof(InsufficientFunds) },
                    { "170227", typeof(ExchangeError) },
                    { "170228", typeof(InvalidOrder) },
                    { "170229", typeof(InvalidOrder) },
                    { "170234", typeof(ExchangeError) },
                    { "170241", typeof(ManualInteractionNeeded) },
                    { "175000", typeof(InvalidOrder) },
                    { "175001", typeof(InvalidOrder) },
                    { "175002", typeof(InvalidOrder) },
                    { "175003", typeof(InsufficientFunds) },
                    { "175004", typeof(InvalidOrder) },
                    { "175005", typeof(InvalidOrder) },
                    { "175006", typeof(InsufficientFunds) },
                    { "175007", typeof(InvalidOrder) },
                    { "175008", typeof(InvalidOrder) },
                    { "175009", typeof(InvalidOrder) },
                    { "175010", typeof(PermissionDenied) },
                    { "175012", typeof(InvalidOrder) },
                    { "175013", typeof(InvalidOrder) },
                    { "175014", typeof(InvalidOrder) },
                    { "175015", typeof(InvalidOrder) },
                    { "175016", typeof(InvalidOrder) },
                    { "175017", typeof(InvalidOrder) },
                    { "175027", typeof(ExchangeError) },
                    { "176002", typeof(BadRequest) },
                    { "176004", typeof(BadRequest) },
                    { "176003", typeof(BadRequest) },
                    { "176006", typeof(BadRequest) },
                    { "176005", typeof(BadRequest) },
                    { "176008", typeof(BadRequest) },
                    { "176007", typeof(BadRequest) },
                    { "176010", typeof(BadRequest) },
                    { "176009", typeof(BadRequest) },
                    { "176012", typeof(BadRequest) },
                    { "176011", typeof(BadRequest) },
                    { "176014", typeof(BadRequest) },
                    { "176013", typeof(BadRequest) },
                    { "176015", typeof(InsufficientFunds) },
                    { "176016", typeof(BadRequest) },
                    { "176017", typeof(BadRequest) },
                    { "176018", typeof(BadRequest) },
                    { "176019", typeof(BadRequest) },
                    { "176020", typeof(BadRequest) },
                    { "176021", typeof(BadRequest) },
                    { "176022", typeof(BadRequest) },
                    { "176023", typeof(BadRequest) },
                    { "176024", typeof(BadRequest) },
                    { "176025", typeof(BadRequest) },
                    { "176026", typeof(BadRequest) },
                    { "176027", typeof(BadRequest) },
                    { "176028", typeof(BadRequest) },
                    { "176029", typeof(BadRequest) },
                    { "176030", typeof(BadRequest) },
                    { "176031", typeof(BadRequest) },
                    { "176034", typeof(BadRequest) },
                    { "176035", typeof(PermissionDenied) },
                    { "176036", typeof(PermissionDenied) },
                    { "176037", typeof(PermissionDenied) },
                    { "176038", typeof(BadRequest) },
                    { "176039", typeof(BadRequest) },
                    { "176040", typeof(BadRequest) },
                    { "181000", typeof(BadRequest) },
                    { "181001", typeof(BadRequest) },
                    { "181002", typeof(InvalidOrder) },
                    { "181003", typeof(InvalidOrder) },
                    { "181004", typeof(InvalidOrder) },
                    { "182000", typeof(InvalidOrder) },
                    { "181017", typeof(BadRequest) },
                    { "20001", typeof(OrderNotFound) },
                    { "20003", typeof(InvalidOrder) },
                    { "20004", typeof(InvalidOrder) },
                    { "20005", typeof(InvalidOrder) },
                    { "20006", typeof(InvalidOrder) },
                    { "20007", typeof(InvalidOrder) },
                    { "20008", typeof(InvalidOrder) },
                    { "20009", typeof(InvalidOrder) },
                    { "20010", typeof(InvalidOrder) },
                    { "20011", typeof(InvalidOrder) },
                    { "20012", typeof(InvalidOrder) },
                    { "20013", typeof(InvalidOrder) },
                    { "20014", typeof(InvalidOrder) },
                    { "20015", typeof(InvalidOrder) },
                    { "20016", typeof(InvalidOrder) },
                    { "20017", typeof(InvalidOrder) },
                    { "20018", typeof(InvalidOrder) },
                    { "20019", typeof(InvalidOrder) },
                    { "20020", typeof(InvalidOrder) },
                    { "20021", typeof(InvalidOrder) },
                    { "20022", typeof(BadRequest) },
                    { "20023", typeof(BadRequest) },
                    { "20031", typeof(BadRequest) },
                    { "20070", typeof(BadRequest) },
                    { "20071", typeof(BadRequest) },
                    { "20084", typeof(BadRequest) },
                    { "30001", typeof(BadRequest) },
                    { "30003", typeof(InvalidOrder) },
                    { "30004", typeof(InvalidOrder) },
                    { "30005", typeof(InvalidOrder) },
                    { "30007", typeof(InvalidOrder) },
                    { "30008", typeof(InvalidOrder) },
                    { "30009", typeof(ExchangeError) },
                    { "30010", typeof(InsufficientFunds) },
                    { "30011", typeof(PermissionDenied) },
                    { "30012", typeof(PermissionDenied) },
                    { "30013", typeof(PermissionDenied) },
                    { "30014", typeof(InvalidOrder) },
                    { "30015", typeof(InvalidOrder) },
                    { "30016", typeof(ExchangeError) },
                    { "30017", typeof(InvalidOrder) },
                    { "30018", typeof(InvalidOrder) },
                    { "30019", typeof(InvalidOrder) },
                    { "30020", typeof(InvalidOrder) },
                    { "30021", typeof(InvalidOrder) },
                    { "30022", typeof(InvalidOrder) },
                    { "30023", typeof(InvalidOrder) },
                    { "30024", typeof(InvalidOrder) },
                    { "30025", typeof(InvalidOrder) },
                    { "30026", typeof(InvalidOrder) },
                    { "30027", typeof(InvalidOrder) },
                    { "30028", typeof(InvalidOrder) },
                    { "30029", typeof(InvalidOrder) },
                    { "30030", typeof(InvalidOrder) },
                    { "30031", typeof(InsufficientFunds) },
                    { "30032", typeof(InvalidOrder) },
                    { "30033", typeof(RateLimitExceeded) },
                    { "30034", typeof(OrderNotFound) },
                    { "30035", typeof(RateLimitExceeded) },
                    { "30036", typeof(ExchangeError) },
                    { "30037", typeof(InvalidOrder) },
                    { "30041", typeof(ExchangeError) },
                    { "30042", typeof(InsufficientFunds) },
                    { "30043", typeof(InvalidOrder) },
                    { "30044", typeof(InvalidOrder) },
                    { "30045", typeof(InvalidOrder) },
                    { "30049", typeof(InsufficientFunds) },
                    { "30050", typeof(ExchangeError) },
                    { "30051", typeof(ExchangeError) },
                    { "30052", typeof(ExchangeError) },
                    { "30054", typeof(ExchangeError) },
                    { "30057", typeof(ExchangeError) },
                    { "30063", typeof(ExchangeError) },
                    { "30067", typeof(InsufficientFunds) },
                    { "30068", typeof(ExchangeError) },
                    { "30074", typeof(InvalidOrder) },
                    { "30075", typeof(InvalidOrder) },
                    { "30078", typeof(ExchangeError) },
                    { "33004", typeof(AuthenticationError) },
                    { "34026", typeof(ExchangeError) },
                    { "34036", typeof(BadRequest) },
                    { "35015", typeof(BadRequest) },
                    { "340099", typeof(ExchangeError) },
                    { "3400045", typeof(ExchangeError) },
                    { "3100116", typeof(BadRequest) },
                    { "3100198", typeof(BadRequest) },
                    { "3200300", typeof(InsufficientFunds) },
                } },
                { "broad", new Dictionary<string, object>() {
                    { "Not supported symbols", typeof(BadSymbol) },
                    { "Request timeout", typeof(RequestTimeout) },
                    { "unknown orderInfo", typeof(OrderNotFound) },
                    { "invalid api_key", typeof(AuthenticationError) },
                    { "oc_diff", typeof(InsufficientFunds) },
                    { "new_oc", typeof(InsufficientFunds) },
                    { "openapi sign params error!", typeof(AuthenticationError) },
                } },
            } },
            { "precisionMode", TICK_SIZE },
            { "options", new Dictionary<string, object>() {
                { "usePrivateInstrumentsInfo", false },
                { "enableDemoTrading", false },
                { "fetchMarkets", new Dictionary<string, object>() {
                    { "types", new List<object>() {"spot", "linear", "inverse", "option"} },
                } },
                { "enableUnifiedMargin", null },
                { "enableUnifiedAccount", null },
                { "unifiedMarginStatus", null },
                { "createMarketBuyOrderRequiresPrice", false },
                { "createUnifiedMarginAccount", false },
                { "defaultType", "swap" },
                { "defaultSubType", "linear" },
                { "defaultSettle", "USDT" },
                { "code", "BTC" },
                { "recvWindow", multiply(5, 1000) },
                { "timeDifference", 0 },
                { "adjustForTimeDifference", false },
                { "loadAllOptions", false },
                { "loadExpiredOptions", false },
                { "brokerId", "CCXT" },
                { "accountsByType", new Dictionary<string, object>() {
                    { "spot", "SPOT" },
                    { "margin", "SPOT" },
                    { "future", "CONTRACT" },
                    { "swap", "CONTRACT" },
                    { "option", "OPTION" },
                    { "investment", "INVESTMENT" },
                    { "unified", "UNIFIED" },
                    { "funding", "FUND" },
                    { "fund", "FUND" },
                    { "contract", "CONTRACT" },
                } },
                { "accountsById", new Dictionary<string, object>() {
                    { "SPOT", "spot" },
                    { "MARGIN", "spot" },
                    { "CONTRACT", "contract" },
                    { "OPTION", "option" },
                    { "INVESTMENT", "investment" },
                    { "UNIFIED", "unified" },
                    { "FUND", "fund" },
                } },
                { "networks", new Dictionary<string, object>() {
                    { "ERC20", "ETH" },
                    { "TRC20", "TRX" },
                    { "BEP20", "BSC" },
                    { "SOL", "SOL" },
                    { "ACA", "ACA" },
                    { "ADA", "ADA" },
                    { "ALGO", "ALGO" },
                    { "APT", "APTOS" },
                    { "AR", "AR" },
                    { "ARBONE", "ARBI" },
                    { "AVAXC", "CAVAX" },
                    { "AVAXX", "XAVAX" },
                    { "ATOM", "ATOM" },
                    { "BCH", "BCH" },
                    { "BEP2", "BNB" },
                    { "CHZ", "CHZ" },
                    { "DCR", "DCR" },
                    { "DGB", "DGB" },
                    { "DOGE", "DOGE" },
                    { "DOT", "DOT" },
                    { "EGLD", "EGLD" },
                    { "EOS", "EOS" },
                    { "ETC", "ETC" },
                    { "ETHF", "ETHF" },
                    { "ETHW", "ETHW" },
                    { "FIL", "FIL" },
                    { "STEP", "FITFI" },
                    { "FLOW", "FLOW" },
                    { "FTM", "FTM" },
                    { "GLMR", "GLMR" },
                    { "HBAR", "HBAR" },
                    { "HNT", "HNT" },
                    { "ICP", "ICP" },
                    { "ICX", "ICX" },
                    { "KDA", "KDA" },
                    { "KLAY", "KLAY" },
                    { "KMA", "KMA" },
                    { "KSM", "KSM" },
                    { "LTC", "LTC" },
                    { "MATIC", "MATIC" },
                    { "MINA", "MINA" },
                    { "MOVR", "MOVR" },
                    { "NEAR", "NEAR" },
                    { "NEM", "NEM" },
                    { "OASYS", "OAS" },
                    { "OASIS", "ROSE" },
                    { "OMNI", "OMNI" },
                    { "ONE", "ONE" },
                    { "OPTIMISM", "OP" },
                    { "POKT", "POKT" },
                    { "QTUM", "QTUM" },
                    { "RVN", "RVN" },
                    { "SC", "SC" },
                    { "SCRT", "SCRT" },
                    { "STX", "STX" },
                    { "THETA", "THETA" },
                    { "TON", "TON" },
                    { "WAVES", "WAVES" },
                    { "WAX", "WAXP" },
                    { "XDC", "XDC" },
                    { "XEC", "XEC" },
                    { "XLM", "XLM" },
                    { "XRP", "XRP" },
                    { "XTZ", "XTZ" },
                    { "XYM", "XYM" },
                    { "ZEN", "ZEN" },
                    { "ZIL", "ZIL" },
                    { "ZKSYNC", "ZKSYNC" },
                } },
                { "networksById", new Dictionary<string, object>() {
                    { "ETH", "ERC20" },
                    { "TRX", "TRC20" },
                    { "BSC", "BEP20" },
                    { "OMNI", "OMNI" },
                    { "SPL", "SOL" },
                } },
                { "defaultNetwork", "ERC20" },
                { "defaultNetworks", new Dictionary<string, object>() {
                    { "USDT", "TRC20" },
                } },
                { "intervals", new Dictionary<string, object>() {
                    { "5m", "5min" },
                    { "15m", "15min" },
                    { "30m", "30min" },
                    { "1h", "1h" },
                    { "4h", "4h" },
                    { "1d", "1d" },
                } },
                { "useMarkPriceForPositionCollateral", false },
            } },
            { "features", new Dictionary<string, object>() {
                { "default", new Dictionary<string, object>() {
                    { "sandbox", true },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "triggerPrice", true },
                        { "triggerPriceType", new Dictionary<string, object>() {
                            { "last", true },
                            { "mark", true },
                            { "index", true },
                        } },
                        { "triggerDirection", true },
                        { "stopLossPrice", true },
                        { "takeProfitPrice", true },
                        { "attachedStopLossTakeProfit", new Dictionary<string, object>() {
                            { "triggerPriceType", new Dictionary<string, object>() {
                                { "last", true },
                                { "mark", true },
                                { "index", true },
                            } },
                            { "price", true },
                        } },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", true },
                            { "PO", true },
                            { "GTD", false },
                        } },
                        { "hedged", true },
                        { "selfTradePrevention", true },
                        { "trailing", true },
                        { "iceberg", false },
                        { "leverage", false },
                        { "marketBuyRequiresPrice", false },
                        { "marketBuyByCost", true },
                    } },
                    { "createOrders", new Dictionary<string, object>() {
                        { "max", 10 },
                    } },
                    { "fetchMyTrades", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 100 },
                        { "daysBack", multiply(365, 2) },
                        { "untilDays", 7 },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", true },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 50 },
                        { "trigger", true },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrders", null },
                    { "fetchClosedOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 50 },
                        { "daysBack", multiply(365, 2) },
                        { "daysBackCanceled", 1 },
                        { "untilDays", 7 },
                        { "trigger", true },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 1000 },
                    } },
                    { "editOrders", new Dictionary<string, object>() {
                        { "max", 10 },
                    } },
                } },
                { "spot", new Dictionary<string, object>() {
                    { "extends", "default" },
                    { "createOrder", new Dictionary<string, object>() {
                        { "triggerPriceType", null },
                        { "triggerDirection", false },
                        { "attachedStopLossTakeProfit", new Dictionary<string, object>() {
                            { "triggerPriceType", null },
                            { "price", true },
                        } },
                        { "marketBuyRequiresPrice", true },
                    } },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                    { "inverse", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                    { "inverse", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "feeSide", "get" },
                    { "tierBased", true },
                    { "percentage", true },
                    { "taker", 0.00075 },
                    { "maker", 0.0001 },
                } },
                { "funding", new Dictionary<string, object>() {
                    { "tierBased", false },
                    { "percentage", false },
                    { "withdraw", new Dictionary<string, object>() {} },
                    { "deposit", new Dictionary<string, object>() {} },
                } },
            } },
        });
    }

    public virtual void enableDemoTrading(object enable)
    {
        /**
         * @method
         * @name bybit#enableDemoTrading
         * @description enables or disables demo trading mode
         * @see https://bybit-exchange.github.io/docs/v5/demo
         * @param {boolean} [enable] true if demo trading should be enabled, false otherwise
         */
        if (isTrue(this.isSandboxModeEnabled))
        {
            throw new NotSupported ((string)add(this.id, " demo trading does not support in sandbox environment")) ;
        }
        // enable demo trading in bybit, see: https://bybit-exchange.github.io/docs/v5/demo
        if (isTrue(enable))
        {
            ((IDictionary<string,object>)this.urls)["apiBackupDemoTrading"] = getValue(this.urls, "api");
            ((IDictionary<string,object>)this.urls)["api"] = getValue(this.urls, "demotrading");
        } else if (isTrue(inOp(this.urls, "apiBackupDemoTrading")))
        {
            ((IDictionary<string,object>)this.urls)["api"] = ((object)getValue(this.urls, "apiBackupDemoTrading"));
            object newUrls = this.omit(this.urls, "apiBackupDemoTrading");
            this.urls = newUrls;
        }
        ((IDictionary<string,object>)this.options)["enableDemoTrading"] = enable;
    }

    public override object nonce()
    {
        return subtract(this.milliseconds(), getValue(this.options, "timeDifference"));
    }

    public virtual object addPaginationCursorToResult(object response)
    {
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeListN(result, new List<object>() {"list", "rows", "data", "dataList"}, new List<object>() {});
        object paginationCursor = this.safeString2(result, "nextPageCursor", "cursor");
        object dataLength = getArrayLength(data);
        if (isTrue(isTrue((!isEqual(paginationCursor, null))) && isTrue((isGreaterThan(dataLength, 0)))))
        {
            object first = getValue(data, 0);
            ((IDictionary<string,object>)first)["nextPageCursor"] = paginationCursor;
            ((List<object>)data)[Convert.ToInt32(0)] = first;
        }
        return data;
    }

    /**
     * @method
     * @name bybit#isUnifiedEnabled
     * @see https://bybit-exchange.github.io/docs/v5/user/apikey-info#http-request
     * @see https://bybit-exchange.github.io/docs/v5/account/account-info
     * @description returns [enableUnifiedMargin, enableUnifiedAccount] so the user can check if unified account is enabled
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {any} [enableUnifiedMargin, enableUnifiedAccount]
     */
    public async virtual Task<object> isUnifiedEnabled(object parameters = null)
    {
        // The API key of user id must own one of permissions will be allowed to call following API endpoints:
        // SUB UID: "Account Transfer"
        // MASTER UID: "Account Transfer", "Subaccount Transfer", "Withdrawal"
        parameters ??= new Dictionary<string, object>();
        object enableUnifiedMargin = this.safeBool(this.options, "enableUnifiedMargin");
        object enableUnifiedAccount = this.safeBool(this.options, "enableUnifiedAccount");
        if (isTrue(isTrue(isEqual(enableUnifiedMargin, null)) || isTrue(isEqual(enableUnifiedAccount, null))))
        {
            if (isTrue(getValue(this.options, "enableDemoTrading")))
            {
                // info endpoint is not available in demo trading
                // so we're assuming UTA is enabled
                ((IDictionary<string,object>)this.options)["enableUnifiedMargin"] = false;
                ((IDictionary<string,object>)this.options)["enableUnifiedAccount"] = true;
                ((IDictionary<string,object>)this.options)["unifiedMarginStatus"] = 6;
                return new List<object>() {getValue(this.options, "enableUnifiedMargin"), getValue(this.options, "enableUnifiedAccount")};
            }
            object rawPromises = new List<object> {this.privateGetV5UserQueryApi(parameters), this.privateGetV5AccountInfo(parameters)};
            object promises = await promiseAll(rawPromises);
            object response = getValue(promises, 0);
            object accountInfo = getValue(promises, 1);
            //
            //     {
            //         "retCode": 0,
            //         "retMsg": "",
            //         "result": {
            //             "id": "********",
            //             "note": "XXXXXX",
            //             "apiKey": "XXXXXX",
            //             "readOnly": 0,
            //             "secret": "",
            //             "permissions": {
            //                 "ContractTrade": [...],
            //                 "Spot": [...],
            //                 "Wallet": [...],
            //                 "Options": [...],
            //                 "Derivatives": [...],
            //                 "CopyTrading": [...],
            //                 "BlockTrade": [...],
            //                 "Exchange": [...],
            //                 "NFT": [...],
            //             },
            //             "ips": [...],
            //             "type": 1,
            //             "deadlineDay": 83,
            //             "expiredAt": "2023-05-15T03:21:05Z",
            //             "createdAt": "2022-10-16T02:24:40Z",
            //             "unified": 0,
            //             "uta": 0,
            //             "userID": ********,
            //             "inviterID": 0,
            //             "vipLevel": "No VIP",
            //             "mktMakerLevel": "0",
            //             "affiliateID": 0,
            //             "rsaPublicKey": "",
            //             "isMaster": false
            //         },
            //         "retExtInfo": {},
            //         "time": *************
            //     }
            // account info
            //     {
            //         "retCode": 0,
            //         "retMsg": "OK",
            //         "result": {
            //             "marginMode": "REGULAR_MARGIN",
            //             "updatedTime": "*************",
            //             "unifiedMarginStatus": 4,
            //             "dcpStatus": "OFF",
            //             "timeWindow": 10,
            //             "smpGroup": 0,
            //             "isMasterTrader": false,
            //             "spotHedgingStatus": "OFF"
            //         }
            //     }
            //
            object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
            object accountResult = this.safeDict(accountInfo, "result", new Dictionary<string, object>() {});
            ((IDictionary<string,object>)this.options)["enableUnifiedMargin"] = isEqual(this.safeInteger(result, "unified"), 1);
            ((IDictionary<string,object>)this.options)["enableUnifiedAccount"] = isEqual(this.safeInteger(result, "uta"), 1);
            ((IDictionary<string,object>)this.options)["unifiedMarginStatus"] = this.safeInteger(accountResult, "unifiedMarginStatus", 6); // default to uta 2.0 pro if not found
        }
        return new List<object>() {getValue(this.options, "enableUnifiedMargin"), getValue(this.options, "enableUnifiedAccount")};
    }

    /**
     * @method
     * @name bybit#upgradeUnifiedTradeAccount
     * @description upgrades the account to unified trade account *warning* this is irreversible
     * @see https://bybit-exchange.github.io/docs/v5/account/upgrade-unified-account
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {any} nothing
     */
    public async virtual Task<object> upgradeUnifiedTradeAccount(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.privatePostV5AccountUpgradeToUta(parameters);
    }

    public override object createExpiredOptionMarket(object symbol)
    {
        // support expired option contracts
        object quote = null;
        object settle = null;
        object optionParts = ((string)symbol).Split(new [] {((string)"-")}, StringSplitOptions.None).ToList<object>();
        object symbolBase = ((string)symbol).Split(new [] {((string)"/")}, StringSplitOptions.None).ToList<object>();
        object bs = null;
        object expiry = null;
        if (isTrue(isGreaterThan(getIndexOf(symbol, "/"), -1)))
        {
            bs = this.safeString(symbolBase, 0);
            expiry = this.safeString(optionParts, 1);
            object symbolQuoteAndSettle = this.safeString(symbolBase, 1);
            object splitQuote = ((string)symbolQuoteAndSettle).Split(new [] {((string)":")}, StringSplitOptions.None).ToList<object>();
            object quoteAndSettle = this.safeString(splitQuote, 0);
            quote = quoteAndSettle;
            settle = quoteAndSettle;
        } else
        {
            bs = this.safeString(optionParts, 0);
            expiry = this.convertMarketIdExpireDate(this.safeString(optionParts, 1));
            if (isTrue(((string)symbol).EndsWith(((string)"-USDT"))))
            {
                quote = "USDT";
                settle = "USDT";
            } else
            {
                quote = "USDC";
                settle = "USDC";
            }
        }
        object strike = this.safeString(optionParts, 2);
        object optionType = this.safeString(optionParts, 3);
        object datetime = this.convertExpireDate(expiry);
        object timestamp = this.parse8601(datetime);
        object amountPrecision = null;
        object pricePrecision = null;
        // hard coded amount and price precisions from fetchOptionMarkets
        if (isTrue(isEqual(bs, "BTC")))
        {
            amountPrecision = this.parseNumber("0.01");
            pricePrecision = this.parseNumber("5");
        } else if (isTrue(isEqual(bs, "ETH")))
        {
            amountPrecision = this.parseNumber("0.1");
            pricePrecision = this.parseNumber("0.1");
        } else if (isTrue(isEqual(bs, "SOL")))
        {
            amountPrecision = this.parseNumber("1");
            pricePrecision = this.parseNumber("0.01");
        }
        return new Dictionary<string, object>() {
            { "id", add(add(add(add(add(add(bs, "-"), this.convertExpireDateToMarketIdDate(expiry)), "-"), strike), "-"), optionType) },
            { "symbol", add(add(add(add(add(add(add(add(add(add(bs, "/"), quote), ":"), settle), "-"), expiry), "-"), strike), "-"), optionType) },
            { "base", bs },
            { "quote", quote },
            { "settle", settle },
            { "baseId", bs },
            { "quoteId", quote },
            { "settleId", settle },
            { "active", false },
            { "type", "option" },
            { "linear", null },
            { "inverse", null },
            { "spot", false },
            { "swap", false },
            { "future", false },
            { "option", true },
            { "margin", false },
            { "contract", true },
            { "contractSize", this.parseNumber("1") },
            { "expiry", timestamp },
            { "expiryDatetime", datetime },
            { "optionType", ((bool) isTrue((isEqual(optionType, "C")))) ? "call" : "put" },
            { "strike", this.parseNumber(strike) },
            { "precision", new Dictionary<string, object>() {
                { "amount", amountPrecision },
                { "price", pricePrecision },
            } },
            { "limits", new Dictionary<string, object>() {
                { "amount", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "price", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "cost", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
            } },
            { "info", null },
        };
    }

    public override object safeMarket(object marketId = null, object market = null, object delimiter = null, object marketType = null)
    {
        object isOption = isTrue((!isEqual(marketId, null))) && isTrue((isTrue((isGreaterThan(getIndexOf(marketId, "-C"), -1))) || isTrue((isGreaterThan(getIndexOf(marketId, "-P"), -1)))));
        if (isTrue(isTrue(isOption) && !isTrue((inOp(this.markets_by_id, marketId)))))
        {
            // handle expired option contracts
            return this.createExpiredOptionMarket(marketId);
        }
        return base.safeMarket(marketId, market, delimiter, marketType);
    }

    public virtual object getBybitType(object method, object market, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object type = null;
        var typeparametersVariable = this.handleMarketTypeAndParams(method, market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        object subType = null;
        var subTypeparametersVariable = this.handleSubTypeAndParams(method, market, parameters);
        subType = ((IList<object>)subTypeparametersVariable)[0];
        parameters = ((IList<object>)subTypeparametersVariable)[1];
        if (isTrue(isTrue(isEqual(type, "option")) || isTrue(isEqual(type, "spot"))))
        {
            return new List<object>() {type, parameters};
        }
        return new List<object>() {subType, parameters};
    }

    public virtual object getAmount(object symbol, object amount)
    {
        // some markets like options might not have the precision available
        // and we shouldn't crash in those cases
        object market = this.market(symbol);
        object emptyPrecisionAmount = (isEqual(getValue(getValue(market, "precision"), "amount"), null));
        object amountString = this.numberToString(amount);
        if (isTrue(!isTrue(emptyPrecisionAmount) && isTrue((!isEqual(amountString, "0")))))
        {
            return this.amountToPrecision(symbol, amount);
        }
        return amountString;
    }

    public virtual object getPrice(object symbol, object price)
    {
        if (isTrue(isEqual(price, null)))
        {
            return price;
        }
        object market = this.market(symbol);
        object emptyPrecisionPrice = (isEqual(getValue(getValue(market, "precision"), "price"), null));
        if (!isTrue(emptyPrecisionPrice))
        {
            return this.priceToPrecision(symbol, price);
        }
        return price;
    }

    public virtual object getCost(object symbol, object cost)
    {
        object market = this.market(symbol);
        object emptyPrecisionPrice = (isEqual(getValue(getValue(market, "precision"), "price"), null));
        if (!isTrue(emptyPrecisionPrice))
        {
            return this.costToPrecision(symbol, cost);
        }
        return cost;
    }

    /**
     * @method
     * @name bybit#fetchTime
     * @description fetches the current integer timestamp in milliseconds from the exchange server
     * @see https://bybit-exchange.github.io/docs/v5/market/time
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int} the current integer timestamp in milliseconds from the exchange server
     */
    public async override Task<object> fetchTime(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetV5MarketTime(parameters);
        //
        //    {
        //         "retCode": "0",
        //         "retMsg": "OK",
        //         "result": {
        //             "timeSecond": "1666879482",
        //             "timeNano": "1666879482792685914"
        //         },
        //         "retExtInfo": {},
        //         "time": "1666879482792"
        //     }
        //
        return this.safeInteger(response, "time");
    }

    /**
     * @method
     * @name bybit#fetchCurrencies
     * @description fetches all available currencies on an exchange
     * @see https://bybit-exchange.github.io/docs/v5/asset/coin-info
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an associative dictionary of currencies
     */
    public async override Task<object> fetchCurrencies(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(this.checkRequiredCredentials(false)))
        {
            return null;
        }
        if (isTrue(getValue(this.options, "enableDemoTrading")))
        {
            return null;
        }
        object response = await this.privateGetV5AssetCoinQueryInfo(parameters);
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "",
        //         "result": {
        //             "rows": [
        //                 {
        //                     "name": "BTC",
        //                     "coin": "BTC",
        //                     "remainAmount": "150",
        //                     "chains": [
        //                         {
        //                             "chainType": "BTC",
        //                             "confirmation": "10000",
        //                             "withdrawFee": "0.0005",
        //                             "depositMin": "0.0005",
        //                             "withdrawMin": "0.001",
        //                             "chain": "BTC",
        //                             "chainDeposit": "1",
        //                             "chainWithdraw": "1",
        //                             "minAccuracy": "8"
        //                         }
        //                     ]
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672194582264
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object rows = this.safeList(data, "rows", new List<object>() {});
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(rows)); postFixIncrement(ref i))
        {
            object currency = getValue(rows, i);
            object currencyId = this.safeString(currency, "coin");
            object code = this.safeCurrencyCode(currencyId);
            object name = this.safeString(currency, "name");
            object chains = this.safeList(currency, "chains", new List<object>() {});
            object networks = new Dictionary<string, object>() {};
            for (object j = 0; isLessThan(j, getArrayLength(chains)); postFixIncrement(ref j))
            {
                object chain = getValue(chains, j);
                object networkId = this.safeString(chain, "chain");
                object networkCode = this.networkIdToCode(networkId);
                ((IDictionary<string,object>)networks)[(string)networkCode] = new Dictionary<string, object>() {
                    { "info", chain },
                    { "id", networkId },
                    { "network", networkCode },
                    { "active", null },
                    { "deposit", isEqual(this.safeInteger(chain, "chainDeposit"), 1) },
                    { "withdraw", isEqual(this.safeInteger(chain, "chainWithdraw"), 1) },
                    { "fee", this.safeNumber(chain, "withdrawFee") },
                    { "precision", this.parseNumber(this.parsePrecision(this.safeString(chain, "minAccuracy"))) },
                    { "limits", new Dictionary<string, object>() {
                        { "withdraw", new Dictionary<string, object>() {
                            { "min", this.safeNumber(chain, "withdrawMin") },
                            { "max", null },
                        } },
                        { "deposit", new Dictionary<string, object>() {
                            { "min", this.safeNumber(chain, "depositMin") },
                            { "max", null },
                        } },
                    } },
                };
            }
            ((IDictionary<string,object>)result)[(string)code] = this.safeCurrencyStructure(new Dictionary<string, object>() {
                { "info", currency },
                { "code", code },
                { "id", currencyId },
                { "name", name },
                { "active", null },
                { "deposit", null },
                { "withdraw", null },
                { "fee", null },
                { "precision", null },
                { "limits", new Dictionary<string, object>() {
                    { "amount", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "withdraw", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "deposit", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                } },
                { "networks", networks },
                { "type", "crypto" },
            });
        }
        return result;
    }

    /**
     * @method
     * @name bybit#fetchMarkets
     * @description retrieves data on all markets for bybit
     * @see https://bybit-exchange.github.io/docs/v5/market/instrument
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.options, "adjustForTimeDifference")))
        {
            await this.loadTimeDifference();
        }
        object promisesUnresolved = new List<object>() {};
        object types = null;
        object defaultTypes = new List<object>() {"spot", "linear", "inverse", "option"};
        object fetchMarketsOptions = this.safeDict(this.options, "fetchMarkets");
        if (isTrue(!isEqual(fetchMarketsOptions, null)))
        {
            types = this.safeList(fetchMarketsOptions, "types", defaultTypes);
        } else
        {
            // for backward-compatibility
            types = this.safeList(this.options, "fetchMarkets", defaultTypes);
        }
        for (object i = 0; isLessThan(i, getArrayLength(types)); postFixIncrement(ref i))
        {
            object marketType = getValue(types, i);
            if (isTrue(isEqual(marketType, "spot")))
            {
                ((IList<object>)promisesUnresolved).Add(this.fetchSpotMarkets(parameters));
            } else if (isTrue(isEqual(marketType, "linear")))
            {
                ((IList<object>)promisesUnresolved).Add(this.fetchFutureMarkets(new Dictionary<string, object>() {
                    { "category", "linear" },
                }));
            } else if (isTrue(isEqual(marketType, "inverse")))
            {
                ((IList<object>)promisesUnresolved).Add(this.fetchFutureMarkets(new Dictionary<string, object>() {
                    { "category", "inverse" },
                }));
            } else if (isTrue(isEqual(marketType, "option")))
            {
                ((IList<object>)promisesUnresolved).Add(this.fetchOptionMarkets(new Dictionary<string, object>() {
                    { "baseCoin", "BTC" },
                }));
                ((IList<object>)promisesUnresolved).Add(this.fetchOptionMarkets(new Dictionary<string, object>() {
                    { "baseCoin", "ETH" },
                }));
                ((IList<object>)promisesUnresolved).Add(this.fetchOptionMarkets(new Dictionary<string, object>() {
                    { "baseCoin", "SOL" },
                }));
            } else
            {
                throw new ExchangeError ((string)add(add(add(this.id, " fetchMarkets() this.options fetchMarkets \""), marketType), "\" is not a supported market type")) ;
            }
        }
        object promises = await promiseAll(promisesUnresolved);
        object spotMarkets = this.safeList(promises, 0, new List<object>() {});
        object linearMarkets = this.safeList(promises, 1, new List<object>() {});
        object inverseMarkets = this.safeList(promises, 2, new List<object>() {});
        object btcOptionMarkets = this.safeList(promises, 3, new List<object>() {});
        object ethOptionMarkets = this.safeList(promises, 4, new List<object>() {});
        object solOptionMarkets = this.safeList(promises, 5, new List<object>() {});
        object futureMarkets = this.arrayConcat(linearMarkets, inverseMarkets);
        object optionMarkets = this.arrayConcat(btcOptionMarkets, ethOptionMarkets);
        optionMarkets = this.arrayConcat(optionMarkets, solOptionMarkets);
        object derivativeMarkets = this.arrayConcat(futureMarkets, optionMarkets);
        return this.arrayConcat(spotMarkets, derivativeMarkets);
    }

    public async virtual Task<object> fetchSpotMarkets(object parameters)
    {
        object request = new Dictionary<string, object>() {
            { "category", "spot" },
        };
        object usePrivateInstrumentsInfo = this.safeBool(this.options, "usePrivateInstrumentsInfo", false);
        object response = null;
        if (isTrue(usePrivateInstrumentsInfo))
        {
            response = await this.privateGetV5MarketInstrumentsInfo(this.extend(request, parameters));
        } else
        {
            response = await this.publicGetV5MarketInstrumentsInfo(this.extend(request, parameters));
        }
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "category": "spot",
        //             "list": [
        //                 {
        //                     "symbol": "BTCUSDT",
        //                     "baseCoin": "BTC",
        //                     "quoteCoin": "USDT",
        //                     "innovation": "0",
        //                     "status": "Trading",
        //                     "marginTrading": "both",
        //                     "lotSizeFilter": {
        //                         "basePrecision": "0.000001",
        //                         "quotePrecision": "0.00000001",
        //                         "minOrderQty": "0.00004",
        //                         "maxOrderQty": "63.01197227",
        //                         "minOrderAmt": "1",
        //                         "maxOrderAmt": "100000"
        //                     },
        //                     "priceFilter": {
        //                         "tickSize": "0.01"
        //                     }
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672712468011
        //     }
        //
        object responseResult = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object markets = this.safeList(responseResult, "list", new List<object>() {});
        object result = new List<object>() {};
        object takerFee = this.parseNumber("0.001");
        object makerFee = this.parseNumber("0.001");
        for (object i = 0; isLessThan(i, getArrayLength(markets)); postFixIncrement(ref i))
        {
            object market = getValue(markets, i);
            object id = this.safeString(market, "symbol");
            object baseId = this.safeString(market, "baseCoin");
            object quoteId = this.safeString(market, "quoteCoin");
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object symbol = add(add(bs, "/"), quote);
            object status = this.safeString(market, "status");
            object active = (isEqual(status, "Trading"));
            object lotSizeFilter = this.safeDict(market, "lotSizeFilter");
            object priceFilter = this.safeDict(market, "priceFilter");
            object quotePrecision = this.safeNumber(lotSizeFilter, "quotePrecision");
            object marginTrading = this.safeString(market, "marginTrading", "none");
            object allowsMargin = !isEqual(marginTrading, "none");
            ((IList<object>)result).Add(this.safeMarketStructure(new Dictionary<string, object>() {
                { "id", id },
                { "symbol", symbol },
                { "base", bs },
                { "quote", quote },
                { "settle", null },
                { "baseId", baseId },
                { "quoteId", quoteId },
                { "settleId", null },
                { "type", "spot" },
                { "spot", true },
                { "margin", allowsMargin },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "active", active },
                { "contract", false },
                { "linear", null },
                { "inverse", null },
                { "taker", takerFee },
                { "maker", makerFee },
                { "contractSize", null },
                { "expiry", null },
                { "expiryDatetime", null },
                { "strike", null },
                { "optionType", null },
                { "precision", new Dictionary<string, object>() {
                    { "amount", this.safeNumber(lotSizeFilter, "basePrecision") },
                    { "price", this.safeNumber(priceFilter, "tickSize", quotePrecision) },
                } },
                { "limits", new Dictionary<string, object>() {
                    { "leverage", new Dictionary<string, object>() {
                        { "min", this.parseNumber("1") },
                        { "max", null },
                    } },
                    { "amount", new Dictionary<string, object>() {
                        { "min", this.safeNumber(lotSizeFilter, "minOrderQty") },
                        { "max", this.safeNumber(lotSizeFilter, "maxOrderQty") },
                    } },
                    { "price", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "cost", new Dictionary<string, object>() {
                        { "min", this.safeNumber(lotSizeFilter, "minOrderAmt") },
                        { "max", this.safeNumber(lotSizeFilter, "maxOrderAmt") },
                    } },
                } },
                { "created", null },
                { "info", market },
            }));
        }
        return result;
    }

    public async virtual Task<object> fetchFutureMarkets(object parameters)
    {
        parameters = this.extend(parameters);
        ((IDictionary<string,object>)parameters)["limit"] = 1000; // minimize number of requests
        object preLaunchMarkets = ((object)new List<object>() {});
        object usePrivateInstrumentsInfo = this.safeBool(this.options, "usePrivateInstrumentsInfo", false);
        object response = null;
        if (isTrue(usePrivateInstrumentsInfo))
        {
            response = await this.privateGetV5MarketInstrumentsInfo(parameters);
        } else
        {
            object linearPromises = new List<object> {this.publicGetV5MarketInstrumentsInfo(parameters), this.publicGetV5MarketInstrumentsInfo(this.extend(parameters, new Dictionary<string, object>() {
    { "status", "PreLaunch" },
}))};
            object promises = await promiseAll(linearPromises);
            response = this.safeDict(promises, 0, new Dictionary<string, object>() {});
            preLaunchMarkets = this.safeDict(promises, 1, new Dictionary<string, object>() {});
        }
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object markets = this.safeList(data, "list", new List<object>() {});
        object paginationCursor = this.safeString(data, "nextPageCursor");
        if (isTrue(!isEqual(paginationCursor, null)))
        {
            while (!isEqual(paginationCursor, null))
            {
                ((IDictionary<string,object>)parameters)["cursor"] = paginationCursor;
                object responseInner = null;
                if (isTrue(usePrivateInstrumentsInfo))
                {
                    responseInner = await this.privateGetV5MarketInstrumentsInfo(parameters);
                } else
                {
                    responseInner = await this.publicGetV5MarketInstrumentsInfo(parameters);
                }
                object dataNew = this.safeDict(responseInner, "result", new Dictionary<string, object>() {});
                object rawMarkets = this.safeList(dataNew, "list", new List<object>() {});
                object rawMarketsLength = getArrayLength(rawMarkets);
                if (isTrue(isEqual(rawMarketsLength, 0)))
                {
                    break;
                }
                markets = this.arrayConcat(rawMarkets, markets);
                paginationCursor = this.safeString(dataNew, "nextPageCursor");
            }
        }
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "BTCUSDT",
        //                     "contractType": "LinearPerpetual",
        //                     "status": "Trading",
        //                     "baseCoin": "BTC",
        //                     "quoteCoin": "USDT",
        //                     "launchTime": "1585526400000",
        //                     "deliveryTime": "0",
        //                     "deliveryFeeRate": "",
        //                     "priceScale": "2",
        //                     "leverageFilter": {
        //                         "minLeverage": "1",
        //                         "maxLeverage": "100.00",
        //                         "leverageStep": "0.01"
        //                     },
        //                     "priceFilter": {
        //                         "minPrice": "0.50",
        //                         "maxPrice": "999999.00",
        //                         "tickSize": "0.50"
        //                     },
        //                     "lotSizeFilter": {
        //                         "maxOrderQty": "100.000",
        //                         "minOrderQty": "0.001",
        //                         "qtyStep": "0.001",
        //                         "postOnlyMaxOrderQty": "1000.000"
        //                     },
        //                     "unifiedMarginTrade": true,
        //                     "fundingInterval": 480,
        //                     "settleCoin": "USDT"
        //                 }
        //             ],
        //             "nextPageCursor": ""
        //         },
        //         "retExtInfo": {},
        //         "time": 1672712495660
        //     }
        //
        object preLaunchData = this.safeDict(preLaunchMarkets, "result", new Dictionary<string, object>() {});
        object preLaunchMarketsList = this.safeList(preLaunchData, "list", new List<object>() {});
        markets = this.arrayConcat(markets, preLaunchMarketsList);
        object result = new List<object>() {};
        object category = this.safeString(data, "category");
        for (object i = 0; isLessThan(i, getArrayLength(markets)); postFixIncrement(ref i))
        {
            object market = getValue(markets, i);
            if (isTrue(isEqual(category, null)))
            {
                category = this.safeString(market, "category");
            }
            object linear = (isEqual(category, "linear"));
            object inverse = (isEqual(category, "inverse"));
            object contractType = this.safeString(market, "contractType");
            object inverseFutures = (isEqual(contractType, "InverseFutures"));
            object linearFutures = (isEqual(contractType, "LinearFutures"));
            object linearPerpetual = (isEqual(contractType, "LinearPerpetual"));
            object inversePerpetual = (isEqual(contractType, "InversePerpetual"));
            object id = this.safeString(market, "symbol");
            object baseId = this.safeString(market, "baseCoin");
            object quoteId = this.safeString(market, "quoteCoin");
            object defaultSettledId = ((bool) isTrue(linear)) ? quoteId : baseId;
            object settleId = this.safeString(market, "settleCoin", defaultSettledId);
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object settle = null;
            if (isTrue(isTrue(linearPerpetual) && isTrue((isEqual(settleId, "USD")))))
            {
                settle = "USDC";
            } else
            {
                settle = this.safeCurrencyCode(settleId);
            }
            object symbol = add(add(bs, "/"), quote);
            object lotSizeFilter = this.safeDict(market, "lotSizeFilter", new Dictionary<string, object>() {});
            object priceFilter = this.safeDict(market, "priceFilter", new Dictionary<string, object>() {});
            object leverage = this.safeDict(market, "leverageFilter", new Dictionary<string, object>() {});
            object status = this.safeString(market, "status");
            object swap = isTrue(linearPerpetual) || isTrue(inversePerpetual);
            object future = isTrue(inverseFutures) || isTrue(linearFutures);
            object type = null;
            if (isTrue(swap))
            {
                type = "swap";
            } else if (isTrue(future))
            {
                type = "future";
            }
            object expiry = null;
            // some swaps have deliveryTime meaning delisting time
            if (!isTrue(swap))
            {
                expiry = this.omitZero(this.safeString(market, "deliveryTime"));
                if (isTrue(!isEqual(expiry, null)))
                {
                    expiry = parseInt(expiry);
                }
            }
            object expiryDatetime = this.iso8601(expiry);
            symbol = add(add(symbol, ":"), settle);
            if (isTrue(!isEqual(expiry, null)))
            {
                symbol = add(add(symbol, "-"), this.yymmdd(expiry));
            }
            object contractSize = ((bool) isTrue(inverse)) ? this.safeNumber2(lotSizeFilter, "minTradingQty", "minOrderQty") : this.parseNumber("1");
            ((IList<object>)result).Add(this.safeMarketStructure(new Dictionary<string, object>() {
                { "id", id },
                { "symbol", symbol },
                { "base", bs },
                { "quote", quote },
                { "settle", settle },
                { "baseId", baseId },
                { "quoteId", quoteId },
                { "settleId", settleId },
                { "type", type },
                { "spot", false },
                { "margin", null },
                { "swap", swap },
                { "future", future },
                { "option", false },
                { "active", (isEqual(status, "Trading")) },
                { "contract", true },
                { "linear", linear },
                { "inverse", inverse },
                { "taker", this.safeNumber(market, "takerFee", this.parseNumber("0.0006")) },
                { "maker", this.safeNumber(market, "makerFee", this.parseNumber("0.0001")) },
                { "contractSize", contractSize },
                { "expiry", expiry },
                { "expiryDatetime", expiryDatetime },
                { "strike", null },
                { "optionType", null },
                { "precision", new Dictionary<string, object>() {
                    { "amount", this.safeNumber(lotSizeFilter, "qtyStep") },
                    { "price", this.safeNumber(priceFilter, "tickSize") },
                } },
                { "limits", new Dictionary<string, object>() {
                    { "leverage", new Dictionary<string, object>() {
                        { "min", this.safeNumber(leverage, "minLeverage") },
                        { "max", this.safeNumber(leverage, "maxLeverage") },
                    } },
                    { "amount", new Dictionary<string, object>() {
                        { "min", this.safeNumber2(lotSizeFilter, "minTradingQty", "minOrderQty") },
                        { "max", this.safeNumber2(lotSizeFilter, "maxTradingQty", "maxOrderQty") },
                    } },
                    { "price", new Dictionary<string, object>() {
                        { "min", this.safeNumber(priceFilter, "minPrice") },
                        { "max", this.safeNumber(priceFilter, "maxPrice") },
                    } },
                    { "cost", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                } },
                { "created", this.safeInteger(market, "launchTime") },
                { "info", market },
            }));
        }
        return result;
    }

    public async virtual Task<object> fetchOptionMarkets(object parameters)
    {
        object request = new Dictionary<string, object>() {
            { "category", "option" },
        };
        object usePrivateInstrumentsInfo = this.safeBool(this.options, "usePrivateInstrumentsInfo", false);
        object response = null;
        if (isTrue(usePrivateInstrumentsInfo))
        {
            response = await this.privateGetV5MarketInstrumentsInfo(this.extend(request, parameters));
        } else
        {
            response = await this.publicGetV5MarketInstrumentsInfo(this.extend(request, parameters));
        }
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object markets = this.safeList(data, "list", new List<object>() {});
        if (isTrue(getValue(this.options, "loadAllOptions")))
        {
            ((IDictionary<string,object>)request)["limit"] = 1000;
            object paginationCursor = this.safeString(data, "nextPageCursor");
            if (isTrue(!isEqual(paginationCursor, null)))
            {
                while (!isEqual(paginationCursor, null))
                {
                    ((IDictionary<string,object>)request)["cursor"] = paginationCursor;
                    object responseInner = null;
                    if (isTrue(usePrivateInstrumentsInfo))
                    {
                        responseInner = await this.privateGetV5MarketInstrumentsInfo(this.extend(request, parameters));
                    } else
                    {
                        responseInner = await this.publicGetV5MarketInstrumentsInfo(this.extend(request, parameters));
                    }
                    object dataNew = this.safeDict(responseInner, "result", new Dictionary<string, object>() {});
                    object rawMarkets = this.safeList(dataNew, "list", new List<object>() {});
                    object rawMarketsLength = getArrayLength(rawMarkets);
                    if (isTrue(isEqual(rawMarketsLength, 0)))
                    {
                        break;
                    }
                    markets = this.arrayConcat(rawMarkets, markets);
                    paginationCursor = this.safeString(dataNew, "nextPageCursor");
                }
            }
        }
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "category": "option",
        //             "nextPageCursor": "0%2C2",
        //             "list": [
        //                 {
        //                     "symbol": "BTC-29DEC23-80000-C",
        //                     "status": "Trading",
        //                     "baseCoin": "BTC",
        //                     "quoteCoin": "USD",
        //                     "settleCoin": "USDC",
        //                     "optionsType": "Call",
        //                     "launchTime": "1688630400000",
        //                     "deliveryTime": "1703836800000",
        //                     "deliveryFeeRate": "0.00015",
        //                     "priceFilter": {
        //                         "minPrice": "5",
        //                         "maxPrice": "10000000",
        //                         "tickSize": "5"
        //                     },
        //                     "lotSizeFilter": {
        //                         "maxOrderQty": "500",
        //                         "minOrderQty": "0.01",
        //                         "qtyStep": "0.01"
        //                     }
        //                 },
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1688873094448
        //     }
        //
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(markets)); postFixIncrement(ref i))
        {
            object market = getValue(markets, i);
            object id = this.safeString(market, "symbol");
            object baseId = this.safeString(market, "baseCoin");
            object quoteId = this.safeString(market, "quoteCoin");
            object settleId = this.safeString(market, "settleCoin");
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object settle = this.safeCurrencyCode(settleId);
            object lotSizeFilter = this.safeDict(market, "lotSizeFilter", new Dictionary<string, object>() {});
            object priceFilter = this.safeDict(market, "priceFilter", new Dictionary<string, object>() {});
            object status = this.safeString(market, "status");
            object expiry = this.safeInteger(market, "deliveryTime");
            object splitId = ((string)id).Split(new [] {((string)"-")}, StringSplitOptions.None).ToList<object>();
            object strike = this.safeString(splitId, 2);
            object optionLetter = this.safeString(splitId, 3);
            object isActive = (isEqual(status, "Trading"));
            object isInverse = isEqual(bs, settle);
            if (isTrue(isTrue(isTrue(isActive) || isTrue((getValue(this.options, "loadAllOptions")))) || isTrue((getValue(this.options, "loadExpiredOptions")))))
            {
                ((IList<object>)result).Add(this.safeMarketStructure(new Dictionary<string, object>() {
                    { "id", id },
                    { "symbol", add(add(add(add(add(add(add(add(add(add(bs, "/"), quote), ":"), settle), "-"), this.yymmdd(expiry)), "-"), strike), "-"), optionLetter) },
                    { "base", bs },
                    { "quote", quote },
                    { "settle", settle },
                    { "baseId", baseId },
                    { "quoteId", quoteId },
                    { "settleId", settleId },
                    { "type", "option" },
                    { "subType", null },
                    { "spot", false },
                    { "margin", false },
                    { "swap", false },
                    { "future", false },
                    { "option", true },
                    { "active", isActive },
                    { "contract", true },
                    { "linear", !isTrue(isInverse) },
                    { "inverse", isInverse },
                    { "taker", this.safeNumber(market, "takerFee", this.parseNumber("0.0006")) },
                    { "maker", this.safeNumber(market, "makerFee", this.parseNumber("0.0001")) },
                    { "contractSize", this.parseNumber("1") },
                    { "expiry", expiry },
                    { "expiryDatetime", this.iso8601(expiry) },
                    { "strike", this.parseNumber(strike) },
                    { "optionType", this.safeStringLower(market, "optionsType") },
                    { "precision", new Dictionary<string, object>() {
                        { "amount", this.safeNumber(lotSizeFilter, "qtyStep") },
                        { "price", this.safeNumber(priceFilter, "tickSize") },
                    } },
                    { "limits", new Dictionary<string, object>() {
                        { "leverage", new Dictionary<string, object>() {
                            { "min", null },
                            { "max", null },
                        } },
                        { "amount", new Dictionary<string, object>() {
                            { "min", this.safeNumber(lotSizeFilter, "minOrderQty") },
                            { "max", this.safeNumber(lotSizeFilter, "maxOrderQty") },
                        } },
                        { "price", new Dictionary<string, object>() {
                            { "min", this.safeNumber(priceFilter, "minPrice") },
                            { "max", this.safeNumber(priceFilter, "maxPrice") },
                        } },
                        { "cost", new Dictionary<string, object>() {
                            { "min", null },
                            { "max", null },
                        } },
                    } },
                    { "created", this.safeInteger(market, "launchTime") },
                    { "info", market },
                }));
            }
        }
        return result;
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        // spot
        //
        //     {
        //         "symbol": "BTCUSDT",
        //         "bid1Price": "20517.96",
        //         "bid1Size": "2",
        //         "ask1Price": "20527.77",
        //         "ask1Size": "1.862172",
        //         "lastPrice": "20533.13",
        //         "prevPrice24h": "20393.48",
        //         "price24hPcnt": "0.0068",
        //         "highPrice24h": "21128.12",
        //         "lowPrice24h": "20318.89",
        //         "turnover24h": "243765620.65899866",
        //         "volume24h": "11801.27771",
        //         "usdIndexPrice": "20784.12009279"
        //     }
        //
        // linear/inverse
        //
        //     {
        //         "symbol": "BTCUSD",
        //         "lastPrice": "16597.00",
        //         "indexPrice": "16598.54",
        //         "markPrice": "16596.00",
        //         "prevPrice24h": "16464.50",
        //         "price24hPcnt": "0.008047",
        //         "highPrice24h": "30912.50",
        //         "lowPrice24h": "15700.00",
        //         "prevPrice1h": "16595.50",
        //         "openInterest": "373504107",
        //         "openInterestValue": "22505.67",
        //         "turnover24h": "2352.94950046",
        //         "volume24h": "49337318",
        //         "fundingRate": "-0.001034",
        //         "nextFundingTime": "1672387200000",
        //         "predictedDeliveryPrice": "",
        //         "basisRate": "",
        //         "deliveryFeeRate": "",
        //         "deliveryTime": "0",
        //         "ask1Size": "1",
        //         "bid1Price": "16596.00",
        //         "ask1Price": "16597.50",
        //         "bid1Size": "1"
        //     }
        //
        // option
        //
        //     {
        //         "symbol": "BTC-30DEC22-18000-C",
        //         "bid1Price": "0",
        //         "bid1Size": "0",
        //         "bid1Iv": "0",
        //         "ask1Price": "435",
        //         "ask1Size": "0.66",
        //         "ask1Iv": "5",
        //         "lastPrice": "435",
        //         "highPrice24h": "435",
        //         "lowPrice24h": "165",
        //         "markPrice": "0.00000009",
        //         "indexPrice": "16600.55",
        //         "markIv": "0.7567",
        //         "underlyingPrice": "16590.42",
        //         "openInterest": "6.3",
        //         "turnover24h": "2482.73",
        //         "volume24h": "0.15",
        //         "totalVolume": "99",
        //         "totalTurnover": "1967653",
        //         "delta": "0.00000001",
        //         "gamma": "0.00000001",
        //         "vega": "0.00000004",
        //         "theta": "-0.00000152",
        //         "predictedDeliveryPrice": "0",
        //         "change24h": "86"
        //     }
        //
        object isSpot = isEqual(this.safeString(ticker, "openInterestValue"), null);
        object timestamp = this.safeInteger(ticker, "time");
        object marketId = this.safeString(ticker, "symbol");
        object type = ((bool) isTrue(isSpot)) ? "spot" : "contract";
        market = this.safeMarket(marketId, market, null, type);
        object symbol = this.safeSymbol(marketId, market, null, type);
        object last = this.safeString(ticker, "lastPrice");
        object open = this.safeString(ticker, "prevPrice24h");
        object percentage = this.safeString(ticker, "price24hPcnt");
        percentage = Precise.stringMul(percentage, "100");
        object quoteVolume = this.safeString(ticker, "turnover24h");
        object baseVolume = this.safeString(ticker, "volume24h");
        object bid = this.safeString(ticker, "bid1Price");
        object ask = this.safeString(ticker, "ask1Price");
        object high = this.safeString(ticker, "highPrice24h");
        object low = this.safeString(ticker, "lowPrice24h");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", symbol },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "high", high },
            { "low", low },
            { "bid", bid },
            { "bidVolume", this.safeString2(ticker, "bidSize", "bid1Size") },
            { "ask", ask },
            { "askVolume", this.safeString2(ticker, "askSize", "ask1Size") },
            { "vwap", null },
            { "open", open },
            { "close", last },
            { "last", last },
            { "previousClose", null },
            { "change", null },
            { "percentage", percentage },
            { "average", null },
            { "baseVolume", baseVolume },
            { "quoteVolume", quoteVolume },
            { "markPrice", this.safeString(ticker, "markPrice") },
            { "indexPrice", this.safeString(ticker, "indexPrice") },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name bybit#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://bybit-exchange.github.io/docs/v5/market/tickers
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchTicker() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object category = null;
        var categoryparametersVariable = this.getBybitType("fetchTicker", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = category;
        object response = await this.publicGetV5MarketTickers(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "category": "inverse",
        //             "list": [
        //                 {
        //                     "symbol": "BTCUSD",
        //                     "lastPrice": "16597.00",
        //                     "indexPrice": "16598.54",
        //                     "markPrice": "16596.00",
        //                     "prevPrice24h": "16464.50",
        //                     "price24hPcnt": "0.008047",
        //                     "highPrice24h": "30912.50",
        //                     "lowPrice24h": "15700.00",
        //                     "prevPrice1h": "16595.50",
        //                     "openInterest": "373504107",
        //                     "openInterestValue": "22505.67",
        //                     "turnover24h": "2352.94950046",
        //                     "volume24h": "49337318",
        //                     "fundingRate": "-0.001034",
        //                     "nextFundingTime": "1672387200000",
        //                     "predictedDeliveryPrice": "",
        //                     "basisRate": "",
        //                     "deliveryFeeRate": "",
        //                     "deliveryTime": "0",
        //                     "ask1Size": "1",
        //                     "bid1Price": "16596.00",
        //                     "ask1Price": "16597.50",
        //                     "bid1Size": "1"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672376496682
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object tickers = this.safeList(result, "list", new List<object>() {});
        object rawTicker = this.safeDict(tickers, 0);
        return this.parseTicker(rawTicker, market);
    }

    /**
     * @method
     * @name bybit#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://bybit-exchange.github.io/docs/v5/market/tickers
     * @param {string[]} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.subType] *contract only* 'linear', 'inverse'
     * @param {string} [params.baseCoin] *option only* base coin, default is 'BTC'
     * @returns {object} an array of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object code = this.safeStringN(parameters, new List<object>() {"code", "currency", "baseCoin"});
        object market = null;
        object parsedSymbols = null;
        if (isTrue(!isEqual(symbols, null)))
        {
            parsedSymbols = new List<object>() {};
            object marketTypeInfo = this.handleMarketTypeAndParams("fetchTickers", null, parameters);
            object defaultType = getValue(marketTypeInfo, 0); // don't omit here
            // we can't use marketSymbols here due to the conflicing ids between markets
            object currentType = null;
            for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
            {
                object symbol = getValue(symbols, i);
                // using safeMarket here because if the user provides for instance BTCUSDT and "type": "spot" in params we should
                // infer the market type from the type provided and not from the conflicting id (BTCUSDT might be swap or spot)
                object isExchangeSpecificSymbol = (isEqual(getIndexOf(symbol, "/"), -1));
                if (isTrue(isExchangeSpecificSymbol))
                {
                    market = this.safeMarket(symbol, null, null, defaultType);
                } else
                {
                    market = this.market(symbol);
                }
                if (isTrue(isEqual(currentType, null)))
                {
                    currentType = getValue(market, "type");
                } else if (isTrue(!isEqual(getValue(market, "type"), currentType)))
                {
                    throw new BadRequest ((string)add(this.id, " fetchTickers can only accept a list of symbols of the same type")) ;
                }
                if (isTrue(getValue(market, "option")))
                {
                    if (isTrue(isTrue(!isEqual(code, null)) && isTrue(!isEqual(code, getValue(market, "base")))))
                    {
                        throw new BadRequest ((string)add(this.id, " fetchTickers the base currency must be the same for all symbols, this endpoint only supports one base currency at a time. Read more about it here: https://bybit-exchange.github.io/docs/v5/market/tickers")) ;
                    }
                    if (isTrue(isEqual(code, null)))
                    {
                        code = getValue(market, "base");
                    }
                    parameters = this.omit(parameters, new List<object>() {"code", "currency"});
                }
                ((IList<object>)parsedSymbols).Add(getValue(market, "symbol"));
            }
        }
        object request = new Dictionary<string, object>() {};
        object category = null;
        var categoryparametersVariable = this.getBybitType("fetchTickers", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = category;
        if (isTrue(isEqual(category, "option")))
        {
            ((IDictionary<string,object>)request)["category"] = "option";
            if (isTrue(isEqual(code, null)))
            {
                code = "BTC";
            }
            ((IDictionary<string,object>)request)["baseCoin"] = code;
        }
        object response = await this.publicGetV5MarketTickers(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "category": "inverse",
        //             "list": [
        //                 {
        //                     "symbol": "BTCUSD",
        //                     "lastPrice": "16597.00",
        //                     "indexPrice": "16598.54",
        //                     "markPrice": "16596.00",
        //                     "prevPrice24h": "16464.50",
        //                     "price24hPcnt": "0.008047",
        //                     "highPrice24h": "30912.50",
        //                     "lowPrice24h": "15700.00",
        //                     "prevPrice1h": "16595.50",
        //                     "openInterest": "373504107",
        //                     "openInterestValue": "22505.67",
        //                     "turnover24h": "2352.94950046",
        //                     "volume24h": "49337318",
        //                     "fundingRate": "-0.001034",
        //                     "nextFundingTime": "1672387200000",
        //                     "predictedDeliveryPrice": "",
        //                     "basisRate": "",
        //                     "deliveryFeeRate": "",
        //                     "deliveryTime": "0",
        //                     "ask1Size": "1",
        //                     "bid1Price": "16596.00",
        //                     "ask1Price": "16597.50",
        //                     "bid1Size": "1"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672376496682
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object tickerList = this.safeList(result, "list", new List<object>() {});
        return this.parseTickers(tickerList, parsedSymbols);
    }

    /**
     * @method
     * @name bybit#fetchBidsAsks
     * @description fetches the bid and ask price and volume for multiple markets
     * @see https://bybit-exchange.github.io/docs/v5/market/tickers
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the bids and asks for, all markets are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.subType] *contract only* 'linear', 'inverse'
     * @param {string} [params.baseCoin] *option only* base coin, default is 'BTC'
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchBidsAsks(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.fetchTickers(symbols, parameters);
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        //
        //     [
        //         "1621162800",
        //         "49592.43",
        //         "49644.91",
        //         "49342.37",
        //         "49349.42",
        //         "1451.59",
        //         "2.4343353100000003"
        //     ]
        //
        object volumeIndex = ((bool) isTrue((getValue(market, "inverse")))) ? 6 : 5;
        return new List<object> {this.safeInteger(ohlcv, 0), this.safeNumber(ohlcv, 1), this.safeNumber(ohlcv, 2), this.safeNumber(ohlcv, 3), this.safeNumber(ohlcv, 4), this.safeNumber(ohlcv, volumeIndex)};
    }

    /**
     * @method
     * @name bybit#fetchOHLCV
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @see https://bybit-exchange.github.io/docs/v5/market/kline
     * @see https://bybit-exchange.github.io/docs/v5/market/mark-kline
     * @see https://bybit-exchange.github.io/docs/v5/market/index-kline
     * @see https://bybit-exchange.github.io/docs/v5/market/preimum-index-kline
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch orders for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOHLCV() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOHLCV", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDeterministic("fetchOHLCV", symbol, since, limit, timeframe, parameters, 1000);
        }
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(isEqual(limit, null)))
        {
            limit = 200; // default is 200 when requested with `since`
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // max 1000, default 1000
        }
        var requestparametersVariable = this.handleUntilOption("end", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        ((IDictionary<string,object>)request)["interval"] = this.safeString(this.timeframes, timeframe, timeframe);
        object response = null;
        if (isTrue(getValue(market, "spot")))
        {
            ((IDictionary<string,object>)request)["category"] = "spot";
            response = await this.publicGetV5MarketKline(this.extend(request, parameters));
        } else
        {
            object price = this.safeString(parameters, "price");
            parameters = this.omit(parameters, "price");
            if (isTrue(getValue(market, "linear")))
            {
                ((IDictionary<string,object>)request)["category"] = "linear";
            } else if (isTrue(getValue(market, "inverse")))
            {
                ((IDictionary<string,object>)request)["category"] = "inverse";
            } else
            {
                throw new NotSupported ((string)add(this.id, " fetchOHLCV() is not supported for option markets")) ;
            }
            if (isTrue(isEqual(price, "mark")))
            {
                response = await this.publicGetV5MarketMarkPriceKline(this.extend(request, parameters));
            } else if (isTrue(isEqual(price, "index")))
            {
                response = await this.publicGetV5MarketIndexPriceKline(this.extend(request, parameters));
            } else if (isTrue(isEqual(price, "premiumIndex")))
            {
                response = await this.publicGetV5MarketPremiumIndexPriceKline(this.extend(request, parameters));
            } else
            {
                response = await this.publicGetV5MarketKline(this.extend(request, parameters));
            }
        }
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "symbol": "BTCUSD",
        //             "category": "inverse",
        //             "list": [
        //                 [
        //                     "1670608800000",
        //                     "17071",
        //                     "17073",
        //                     "17027",
        //                     "17055.5",
        //                     "268611",
        //                     "15.74462667"
        //                 ],
        //                 [
        //                     "1670605200000",
        //                     "17071.5",
        //                     "17071.5",
        //                     "17061",
        //                     "17071",
        //                     "4177",
        //                     "0.24469757"
        //                 ],
        //                 [
        //                     "1670601600000",
        //                     "17086.5",
        //                     "17088",
        //                     "16978",
        //                     "17071.5",
        //                     "6356",
        //                     "0.37288112"
        //                 ]
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672025956592
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object ohlcvs = this.safeList(result, "list", new List<object>() {});
        return this.parseOHLCVs(ohlcvs, market, timeframe, since, limit);
    }

    public override object parseFundingRate(object ticker, object market = null)
    {
        //
        //     {
        //         "symbol": "BTCUSDT",
        //         "bidPrice": "19255",
        //         "askPrice": "19255.5",
        //         "lastPrice": "19255.50",
        //         "lastTickDirection": "ZeroPlusTick",
        //         "prevPrice24h": "18634.50",
        //         "price24hPcnt": "0.033325",
        //         "highPrice24h": "19675.00",
        //         "lowPrice24h": "18610.00",
        //         "prevPrice1h": "19278.00",
        //         "markPrice": "19255.00",
        //         "indexPrice": "19260.68",
        //         "openInterest": "48069.549",
        //         "turnover24h": "4686694853.047006",
        //         "volume24h": "243730.252",
        //         "fundingRate": "0.0001",
        //         "nextFundingTime": "1663689600000",
        //         "predictedDeliveryPrice": "",
        //         "basisRate": "",
        //         "deliveryFeeRate": "",
        //         "deliveryTime": "0"
        //     }
        //
        object timestamp = this.safeInteger(ticker, "timestamp"); // added artificially to avoid changing the signature
        ticker = this.omit(ticker, "timestamp");
        object marketId = this.safeString(ticker, "symbol");
        object symbol = this.safeSymbol(marketId, market, null, "swap");
        object fundingRate = this.safeNumber(ticker, "fundingRate");
        object fundingTimestamp = this.safeInteger(ticker, "nextFundingTime");
        object markPrice = this.safeNumber(ticker, "markPrice");
        object indexPrice = this.safeNumber(ticker, "indexPrice");
        object info = this.safeDict(this.safeMarket(marketId, market, null, "swap"), "info");
        object fundingInterval = this.safeInteger(info, "fundingInterval");
        object intervalString = null;
        if (isTrue(!isEqual(fundingInterval, null)))
        {
            object interval = this.parseToInt(divide(fundingInterval, 60));
            intervalString = add(((object)interval).ToString(), "h");
        }
        return new Dictionary<string, object>() {
            { "info", ticker },
            { "symbol", symbol },
            { "markPrice", markPrice },
            { "indexPrice", indexPrice },
            { "interestRate", null },
            { "estimatedSettlePrice", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "fundingRate", fundingRate },
            { "fundingTimestamp", fundingTimestamp },
            { "fundingDatetime", this.iso8601(fundingTimestamp) },
            { "nextFundingRate", null },
            { "nextFundingTimestamp", null },
            { "nextFundingDatetime", null },
            { "previousFundingRate", null },
            { "previousFundingTimestamp", null },
            { "previousFundingDatetime", null },
            { "interval", intervalString },
        };
    }

    /**
     * @method
     * @name bybit#fetchFundingRates
     * @description fetches funding rates for multiple markets
     * @see https://bybit-exchange.github.io/docs/v5/market/tickers
     * @param {string[]} symbols unified symbols of the markets to fetch the funding rates for, all market funding rates are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-structure}
     */
    public async override Task<object> fetchFundingRates(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbols, null)))
        {
            symbols = this.marketSymbols(symbols);
            market = this.market(getValue(symbols, 0));
            object symbolsLength = getArrayLength(symbols);
            if (isTrue(isEqual(symbolsLength, 1)))
            {
                ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
            }
        }
        object type = null;
        var typeparametersVariable = this.handleMarketTypeAndParams("fetchFundingRates", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(!isEqual(type, "swap")))
        {
            throw new NotSupported ((string)add(add(add(this.id, " fetchFundingRates() does not support "), type), " markets")) ;
        } else
        {
            object subType = null;
            var subTypeparametersVariable = this.handleSubTypeAndParams("fetchFundingRates", market, parameters, "linear");
            subType = ((IList<object>)subTypeparametersVariable)[0];
            parameters = ((IList<object>)subTypeparametersVariable)[1];
            ((IDictionary<string,object>)request)["category"] = subType;
        }
        object response = await this.publicGetV5MarketTickers(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "BTCUSDT",
        //                     "bidPrice": "19255",
        //                     "askPrice": "19255.5",
        //                     "lastPrice": "19255.50",
        //                     "lastTickDirection": "ZeroPlusTick",
        //                     "prevPrice24h": "18634.50",
        //                     "price24hPcnt": "0.033325",
        //                     "highPrice24h": "19675.00",
        //                     "lowPrice24h": "18610.00",
        //                     "prevPrice1h": "19278.00",
        //                     "markPrice": "19255.00",
        //                     "indexPrice": "19260.68",
        //                     "openInterest": "48069.549",
        //                     "turnover24h": "4686694853.047006",
        //                     "volume24h": "243730.252",
        //                     "fundingRate": "0.0001",
        //                     "nextFundingTime": "1663689600000",
        //                     "predictedDeliveryPrice": "",
        //                     "basisRate": "",
        //                     "deliveryFeeRate": "",
        //                     "deliveryTime": "0"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": null,
        //         "time": 1663670053454
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object tickerList = this.safeList(data, "list", new List<object>() {});
        object timestamp = this.safeInteger(response, "time");
        for (object i = 0; isLessThan(i, getArrayLength(tickerList)); postFixIncrement(ref i))
        {
            ((IDictionary<string,object>)getValue(tickerList, i))["timestamp"] = timestamp; // will be removed inside the parser
        }
        return this.parseFundingRates(tickerList, symbols);
    }

    /**
     * @method
     * @name bybit#fetchFundingRateHistory
     * @description fetches historical funding rate prices
     * @see https://bybit-exchange.github.io/docs/v5/market/history-fund-rate
     * @param {string} symbol unified symbol of the market to fetch the funding rate history for
     * @param {int} [since] timestamp in ms of the earliest funding rate to fetch
     * @param {int} [limit] the maximum amount of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-history-structure} to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms of the latest funding rate
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {object[]} a list of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-history-structure}
     */
    public async override Task<object> fetchFundingRateHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchFundingRateHistory() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchFundingRateHistory", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDeterministic("fetchFundingRateHistory", symbol, since, limit, "8h", parameters, 200);
        }
        if (isTrue(isEqual(limit, null)))
        {
            limit = 200;
        }
        object request = new Dictionary<string, object>() {
            { "limit", limit },
        };
        object market = this.market(symbol);
        symbol = getValue(market, "symbol");
        ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchFundingRateHistory", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(isTrue(isEqual(type, "spot")) || isTrue(isEqual(type, "option"))))
        {
            throw new NotSupported ((string)add(this.id, " fetchFundingRateHistory() only support linear and inverse market")) ;
        }
        ((IDictionary<string,object>)request)["category"] = type;
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        object until = this.safeInteger(parameters, "until"); // unified in milliseconds
        object endTime = this.safeInteger(parameters, "endTime", until); // exchange-specific in milliseconds
        parameters = this.omit(parameters, new List<object>() {"endTime", "until"});
        if (isTrue(!isEqual(endTime, null)))
        {
            ((IDictionary<string,object>)request)["endTime"] = endTime;
        } else
        {
            if (isTrue(!isEqual(since, null)))
            {
                // end time is required when since is not empty
                object fundingInterval = multiply(multiply(multiply(60, 60), 8), 1000);
                ((IDictionary<string,object>)request)["endTime"] = add(since, multiply(limit, fundingInterval));
            }
        }
        object response = await this.publicGetV5MarketFundingHistory(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "ETHPERP",
        //                     "fundingRate": "0.0001",
        //                     "fundingRateTimestamp": "1672041600000"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672051897447
        //     }
        //
        object rates = new List<object>() {};
        object result = this.safeDict(response, "result");
        object resultList = this.safeList(result, "list");
        for (object i = 0; isLessThan(i, getArrayLength(resultList)); postFixIncrement(ref i))
        {
            object entry = getValue(resultList, i);
            object timestamp = this.safeInteger(entry, "fundingRateTimestamp");
            ((IList<object>)rates).Add(new Dictionary<string, object>() {
                { "info", entry },
                { "symbol", this.safeSymbol(this.safeString(entry, "symbol"), null, null, "swap") },
                { "fundingRate", this.safeNumber(entry, "fundingRate") },
                { "timestamp", timestamp },
                { "datetime", this.iso8601(timestamp) },
            });
        }
        object sorted = this.sortBy(rates, "timestamp");
        return this.filterBySymbolSinceLimit(sorted, symbol, since, limit);
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        // public https://bybit-exchange.github.io/docs/v5/market/recent-trade
        //
        //     {
        //         "execId": "666042b4-50c6-58f3-bd9c-89b2088663ff",
        //         "symbol": "ETHUSD",
        //         "price": "1162.95",
        //         "size": "1",
        //         "side": "Sell",
        //         "time": "1669191277315",
        //         "isBlockTrade": false
        //     }
        //
        // private trades classic spot https://bybit-exchange.github.io/docs/v5/position/execution
        //
        //     {
        //         "symbol": "QNTUSDT",
        //         "orderId": "1538686353240339712",
        //         "orderLinkId": "",
        //         "side": "Sell",
        //         "orderPrice": "",
        //         "orderQty": "",
        //         "leavesQty": "",
        //         "orderType": "Limit",
        //         "stopOrderType": "",
        //         "execFee": "0.040919",
        //         "execId": "2210000000097330907",
        //         "execPrice": "98.6",
        //         "execQty": "0.415",
        //         "execType": "",
        //         "execValue": "",
        //         "execTime": "1698161716634",
        //         "isMaker": true,
        //         "feeRate": "",
        //         "tradeIv": "",
        //         "markIv": "",
        //         "markPrice": "",
        //         "indexPrice": "",
        //         "underlyingPrice": "",
        //         "blockTradeId": ""
        //     }
        //
        // private trades unified https://bybit-exchange.github.io/docs/v5/position/execution
        //
        //     {
        //         "symbol": "QNTUSDT",
        //         "orderType": "Limit",
        //         "underlyingPrice": "",
        //         "orderLinkId": "1549452573428424449",
        //         "orderId": "1549452573428424448",
        //         "stopOrderType": "",
        //         "execTime": "1699445151998",
        //         "feeRate": "0.00025",
        //         "tradeIv": "",
        //         "blockTradeId": "",
        //         "markPrice": "",
        //         "execPrice": "102.8",
        //         "markIv": "",
        //         "orderQty": "3.652",
        //         "orderPrice": "102.8",
        //         "execValue": "1.028",
        //         "closedSize": "",
        //         "execType": "Trade",
        //         "seq": "19157444346",
        //         "side": "Buy",
        //         "indexPrice": "",
        //         "leavesQty": "3.642",
        //         "isMaker": true,
        //         "execFee": "0.0000025",
        //         "execId": "2210000000101610464",
        //         "execQty": "0.01",
        //         "nextPageCursor": "267951%3A0%2C38567%3A0"
        //     },
        //
        // private USDC settled trades
        //
        //     {
        //         "symbol": "ETHPERP",
        //         "orderLinkId": "",
        //         "side": "Buy",
        //         "orderId": "aad0ee44-ce12-4112-aeee-b7829f6c3a26",
        //         "execFee": "0.0210",
        //         "feeRate": "0.000600",
        //         "blockTradeId": "",
        //         "tradeTime": "1669196417930",
        //         "execPrice": "1162.15",
        //         "lastLiquidityInd": "TAKER",
        //         "execValue": "34.8645",
        //         "execType": "Trade",
        //         "execQty": "0.030",
        //         "tradeId": "0e94eaf5-b08e-5505-b43f-7f1f30b1ca80"
        //     }
        //
        object id = this.safeStringN(trade, new List<object>() {"execId", "id", "tradeId"});
        object marketId = this.safeString(trade, "symbol");
        object marketType = ((bool) isTrue((inOp(trade, "createType")))) ? "contract" : "spot";
        if (isTrue(!isEqual(market, null)))
        {
            marketType = getValue(market, "type");
        }
        object category = this.safeString(trade, "category");
        if (isTrue(!isEqual(category, null)))
        {
            if (isTrue(isEqual(category, "spot")))
            {
                marketType = "spot";
            }
        }
        market = this.safeMarket(marketId, market, null, marketType);
        object symbol = getValue(market, "symbol");
        object amountString = this.safeStringN(trade, new List<object>() {"execQty", "orderQty", "size"});
        object priceString = this.safeStringN(trade, new List<object>() {"execPrice", "orderPrice", "price"});
        object costString = this.safeString(trade, "execValue");
        object timestamp = this.safeIntegerN(trade, new List<object>() {"time", "execTime", "tradeTime"});
        object side = this.safeStringLower(trade, "side");
        if (isTrue(isEqual(side, null)))
        {
            object isBuyer = this.safeInteger(trade, "isBuyer");
            if (isTrue(!isEqual(isBuyer, null)))
            {
                side = ((bool) isTrue(isBuyer)) ? "buy" : "sell";
            }
        }
        object isMaker = this.safeBool(trade, "isMaker");
        object takerOrMaker = null;
        if (isTrue(!isEqual(isMaker, null)))
        {
            takerOrMaker = ((bool) isTrue(isMaker)) ? "maker" : "taker";
        } else
        {
            object lastLiquidityInd = this.safeString(trade, "lastLiquidityInd");
            if (isTrue(isEqual(lastLiquidityInd, "UNKNOWN")))
            {
                lastLiquidityInd = null;
            }
            if (isTrue(!isEqual(lastLiquidityInd, null)))
            {
                if (isTrue(isTrue((isEqual(lastLiquidityInd, "TAKER"))) || isTrue((isEqual(lastLiquidityInd, "MAKER")))))
                {
                    takerOrMaker = ((string)lastLiquidityInd).ToLower();
                } else
                {
                    takerOrMaker = ((bool) isTrue((isEqual(lastLiquidityInd, "AddedLiquidity")))) ? "maker" : "taker";
                }
            }
        }
        object orderType = this.safeStringLower(trade, "orderType");
        if (isTrue(isEqual(orderType, "unknown")))
        {
            orderType = null;
        }
        object feeCostString = this.safeString(trade, "execFee");
        object fee = null;
        if (isTrue(!isEqual(feeCostString, null)))
        {
            object feeRateString = this.safeString(trade, "feeRate");
            object feeCurrencyCode = null;
            if (isTrue(getValue(market, "spot")))
            {
                if (isTrue(Precise.stringGt(feeCostString, "0")))
                {
                    if (isTrue(isEqual(side, "buy")))
                    {
                        feeCurrencyCode = getValue(market, "base");
                    } else
                    {
                        feeCurrencyCode = getValue(market, "quote");
                    }
                } else
                {
                    if (isTrue(isEqual(side, "buy")))
                    {
                        feeCurrencyCode = getValue(market, "quote");
                    } else
                    {
                        feeCurrencyCode = getValue(market, "base");
                    }
                }
            } else
            {
                feeCurrencyCode = ((bool) isTrue(getValue(market, "inverse"))) ? getValue(market, "base") : getValue(market, "settle");
            }
            fee = new Dictionary<string, object>() {
                { "cost", feeCostString },
                { "currency", feeCurrencyCode },
                { "rate", feeRateString },
            };
        }
        return this.safeTrade(new Dictionary<string, object>() {
            { "id", id },
            { "info", trade },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", symbol },
            { "order", this.safeString(trade, "orderId") },
            { "type", orderType },
            { "side", side },
            { "takerOrMaker", takerOrMaker },
            { "price", priceString },
            { "amount", amountString },
            { "cost", costString },
            { "fee", fee },
        }, market);
    }

    /**
     * @method
     * @name bybit#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://bybit-exchange.github.io/docs/v5/market/recent-trade
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchTrades() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            // spot: [1,60], default: 60.
            // others: [1,1000], default: 500
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchTrades", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = type;
        object response = await this.publicGetV5MarketRecentTrade(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "category": "spot",
        //             "list": [
        //                 {
        //                     "execId": "2100000000007764263",
        //                     "symbol": "BTCUSDT",
        //                     "price": "16618.49",
        //                     "size": "0.00012",
        //                     "side": "Buy",
        //                     "time": "1672052955758",
        //                     "isBlockTrade": false
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672053054358
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object trades = this.safeList(result, "list", new List<object>() {});
        return this.parseTrades(trades, market, since, limit);
    }

    /**
     * @method
     * @name bybit#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://bybit-exchange.github.io/docs/v5/market/orderbook
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrderBook() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object defaultLimit = 25;
        if (isTrue(getValue(market, "spot")))
        {
            // limit: [1, 50]. Default: 1
            defaultLimit = 50;
            ((IDictionary<string,object>)request)["category"] = "spot";
        } else
        {
            if (isTrue(getValue(market, "option")))
            {
                // limit: [1, 25]. Default: 1
                ((IDictionary<string,object>)request)["category"] = "option";
            } else if (isTrue(getValue(market, "linear")))
            {
                // limit: [1, 500]. Default: 25
                ((IDictionary<string,object>)request)["category"] = "linear";
            } else if (isTrue(getValue(market, "inverse")))
            {
                // limit: [1, 500]. Default: 25
                ((IDictionary<string,object>)request)["category"] = "inverse";
            }
        }
        ((IDictionary<string,object>)request)["limit"] = ((bool) isTrue((!isEqual(limit, null)))) ? limit : defaultLimit;
        object response = await this.publicGetV5MarketOrderbook(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "s": "BTCUSDT",
        //             "a": [
        //                 [
        //                     "16638.64",
        //                     "0.008479"
        //                 ]
        //             ],
        //             "b": [
        //                 [
        //                     "16638.27",
        //                     "0.305749"
        //                 ]
        //             ],
        //             "ts": 1672765737733,
        //             "u": 5277055
        //         },
        //         "retExtInfo": {},
        //         "time": 1672765737734
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object timestamp = this.safeInteger(result, "ts");
        return this.parseOrderBook(result, symbol, timestamp, "b", "a");
    }

    public override object parseBalance(object response)
    {
        //
        // cross
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "acctBalanceSum": "0.122995614474732872",
        //             "debtBalanceSum": "0.011734191124529754",
        //             "loanAccountList": [
        //                 {
        //                     "free": "0.*********",
        //                     "interest": "0",
        //                     "loan": "0",
        //                     "locked": "0",
        //                     "tokenId": "BTC",
        //                     "total": "0.*********"
        //                 },
        //                 {
        //                     "free": "200.********",
        //                     "interest": "0.0008391",
        //                     "loan": "200",
        //                     "locked": "0",
        //                     "tokenId": "USDT",
        //                     "total": "200.********"
        //                 },
        //             ],
        //             "riskRate": "0.0954",
        //             "status": 1
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        // funding
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "memberId": "533285",
        //             "accountType": "FUND",
        //             "balance": [
        //                 {
        //                     "coin": "USDT",
        //                     "transferBalance": "1010",
        //                     "walletBalance": "1010",
        //                     "bonus": ""
        //                 },
        //                 {
        //                     "coin": "USDC",
        //                     "transferBalance": "0",
        //                     "walletBalance": "0",
        //                     "bonus": ""
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        //  spot & swap
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "list": [
        //                 {
        //                     "totalEquity": "18070.********",
        //                     "accountIMRate": "0.0101",
        //                     "totalMarginBalance": "18070.********",
        //                     "totalInitialMargin": "182.********",
        //                     "accountType": "UNIFIED",
        //                     "totalAvailableBalance": "17887.********",
        //                     "accountMMRate": "0",
        //                     "totalPerpUPL": "-0.********",
        //                     "totalWalletBalance": "18070.********",
        //                     "accountLTV": "0.017",
        //                     "totalMaintenanceMargin": "0.********",
        //                     "coin": [
        //                         {
        //                             "availableToBorrow": "2.5",
        //                             "bonus": "0",
        //                             "accruedInterest": "0",
        //                             "availableToWithdraw": "0.805994",
        //                             "totalOrderIM": "0",
        //                             "equity": "0.805994",
        //                             "totalPositionMM": "0",
        //                             "usdValue": "12920.********",
        //                             "unrealisedPnl": "0",
        //                             "borrowAmount": "0",
        //                             "totalPositionIM": "0",
        //                             "walletBalance": "0.805994",
        //                             "cumRealisedPnl": "0",
        //                             "coin": "BTC"
        //                         }
        //                     ]
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object timestamp = this.safeInteger(response, "time");
        object result = new Dictionary<string, object>() {
            { "info", response },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        };
        object responseResult = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object currencyList = this.safeListN(responseResult, new List<object>() {"loanAccountList", "list", "balance"});
        if (isTrue(isEqual(currencyList, null)))
        {
            // usdc wallet
            object code = "USDC";
            object account = this.account();
            ((IDictionary<string,object>)account)["free"] = this.safeString(responseResult, "availableBalance");
            ((IDictionary<string,object>)account)["total"] = this.safeString(responseResult, "walletBalance");
            ((IDictionary<string,object>)result)[(string)code] = account;
        } else
        {
            for (object i = 0; isLessThan(i, getArrayLength(currencyList)); postFixIncrement(ref i))
            {
                object entry = getValue(currencyList, i);
                object accountType = this.safeString(entry, "accountType");
                if (isTrue(isTrue(isTrue(isEqual(accountType, "UNIFIED")) || isTrue(isEqual(accountType, "CONTRACT"))) || isTrue(isEqual(accountType, "SPOT"))))
                {
                    object coins = this.safeList(entry, "coin");
                    for (object j = 0; isLessThan(j, getArrayLength(coins)); postFixIncrement(ref j))
                    {
                        object account = this.account();
                        object coinEntry = getValue(coins, j);
                        object loan = this.safeString(coinEntry, "borrowAmount");
                        object interest = this.safeString(coinEntry, "accruedInterest");
                        if (isTrue(isTrue((!isEqual(loan, null))) && isTrue((!isEqual(interest, null)))))
                        {
                            ((IDictionary<string,object>)account)["debt"] = Precise.stringAdd(loan, interest);
                        }
                        ((IDictionary<string,object>)account)["total"] = this.safeString(coinEntry, "walletBalance");
                        object free = this.safeString2(coinEntry, "availableToWithdraw", "free");
                        if (isTrue(!isEqual(free, null)))
                        {
                            ((IDictionary<string,object>)account)["free"] = free;
                        } else
                        {
                            object locked = this.safeString(coinEntry, "locked", "0");
                            object totalPositionIm = this.safeString(coinEntry, "totalPositionIM", "0");
                            object totalOrderIm = this.safeString(coinEntry, "totalOrderIM", "0");
                            object totalUsed = Precise.stringAdd(locked, totalPositionIm);
                            totalUsed = Precise.stringAdd(totalUsed, totalOrderIm);
                            ((IDictionary<string,object>)account)["used"] = totalUsed;
                        }
                        // account['used'] = this.safeString (coinEntry, 'locked');
                        object currencyId = this.safeString(coinEntry, "coin");
                        object code = this.safeCurrencyCode(currencyId);
                        ((IDictionary<string,object>)result)[(string)code] = account;
                    }
                } else
                {
                    object account = this.account();
                    object loan = this.safeString(entry, "loan");
                    object interest = this.safeString(entry, "interest");
                    if (isTrue(isTrue((!isEqual(loan, null))) && isTrue((!isEqual(interest, null)))))
                    {
                        ((IDictionary<string,object>)account)["debt"] = Precise.stringAdd(loan, interest);
                    }
                    ((IDictionary<string,object>)account)["total"] = this.safeString2(entry, "total", "walletBalance");
                    ((IDictionary<string,object>)account)["free"] = this.safeStringN(entry, new List<object>() {"free", "availableBalanceWithoutConvert", "availableBalance", "transferBalance"});
                    ((IDictionary<string,object>)account)["used"] = this.safeString(entry, "locked");
                    object currencyId = this.safeStringN(entry, new List<object>() {"tokenId", "coin", "currencyCoin"});
                    object code = this.safeCurrencyCode(currencyId);
                    ((IDictionary<string,object>)result)[(string)code] = account;
                }
            }
        }
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name bybit#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://bybit-exchange.github.io/docs/v5/spot-margin-normal/account-info
     * @see https://bybit-exchange.github.io/docs/v5/asset/all-balance
     * @see https://bybit-exchange.github.io/docs/v5/account/wallet-balance
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.type] wallet type, ['spot', 'swap', 'funding']
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        var enableUnifiedMarginenableUnifiedAccountVariable = await this.isUnifiedEnabled();
        var enableUnifiedMargin = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[0];
        var enableUnifiedAccount = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[1];
        object isUnifiedAccount = (isTrue(enableUnifiedMargin) || isTrue(enableUnifiedAccount));
        object type = null;
        // don't use getBybitType here
        var typeparametersVariable = this.handleMarketTypeAndParams("fetchBalance", null, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        object subType = null;
        var subTypeparametersVariable = this.handleSubTypeAndParams("fetchBalance", null, parameters);
        subType = ((IList<object>)subTypeparametersVariable)[0];
        parameters = ((IList<object>)subTypeparametersVariable)[1];
        if (isTrue(isTrue((isEqual(type, "swap"))) || isTrue((isEqual(type, "future")))))
        {
            type = subType;
        }
        object lowercaseRawType = ((bool) isTrue((!isEqual(type, null)))) ? ((string)type).ToLower() : null;
        object isSpot = (isEqual(type, "spot"));
        object isLinear = (isEqual(type, "linear"));
        object isInverse = (isEqual(type, "inverse"));
        object isFunding = isTrue((isEqual(lowercaseRawType, "fund"))) || isTrue((isEqual(lowercaseRawType, "funding")));
        if (isTrue(isUnifiedAccount))
        {
            object unifiedMarginStatus = this.safeInteger(this.options, "unifiedMarginStatus", 6);
            if (isTrue(isLessThan(unifiedMarginStatus, 5)))
            {
                // it's not uta.20 where inverse are unified
                if (isTrue(isInverse))
                {
                    type = "contract";
                } else
                {
                    type = "unified";
                }
            } else
            {
                type = "unified"; // uta.20 where inverse are unified
            }
        } else
        {
            if (isTrue(isTrue(isLinear) || isTrue(isInverse)))
            {
                type = "contract";
            }
        }
        object accountTypes = this.safeDict(this.options, "accountsByType", new Dictionary<string, object>() {});
        object unifiedType = this.safeStringUpper(accountTypes, type, type);
        object marginMode = null;
        var marginModeparametersVariable = this.handleMarginModeAndParams("fetchBalance", parameters);
        marginMode = ((IList<object>)marginModeparametersVariable)[0];
        parameters = ((IList<object>)marginModeparametersVariable)[1];
        object response = null;
        if (isTrue(isTrue(isSpot) && isTrue((!isEqual(marginMode, null)))))
        {
            response = await this.privateGetV5SpotCrossMarginTradeAccount(this.extend(request, parameters));
        } else if (isTrue(isFunding))
        {
            // use this endpoint only we have no other choice
            // because it requires transfer permission
            ((IDictionary<string,object>)request)["accountType"] = "FUND";
            response = await this.privateGetV5AssetTransferQueryAccountCoinsBalance(this.extend(request, parameters));
        } else
        {
            ((IDictionary<string,object>)request)["accountType"] = unifiedType;
            response = await this.privateGetV5AccountWalletBalance(this.extend(request, parameters));
        }
        //
        // cross
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "acctBalanceSum": "0.122995614474732872",
        //             "debtBalanceSum": "0.011734191124529754",
        //             "loanAccountList": [
        //                 {
        //                     "free": "0.*********",
        //                     "interest": "0",
        //                     "loan": "0",
        //                     "locked": "0",
        //                     "tokenId": "BTC",
        //                     "total": "0.*********"
        //                 },
        //                 {
        //                     "free": "200.********",
        //                     "interest": "0.0008391",
        //                     "loan": "200",
        //                     "locked": "0",
        //                     "tokenId": "USDT",
        //                     "total": "200.********"
        //                 },
        //             ],
        //             "riskRate": "0.0954",
        //             "status": 1
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        // funding
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "memberId": "533285",
        //             "accountType": "FUND",
        //             "balance": [
        //                 {
        //                     "coin": "USDT",
        //                     "transferBalance": "1010",
        //                     "walletBalance": "1010",
        //                     "bonus": ""
        //                 },
        //                 {
        //                     "coin": "USDC",
        //                     "transferBalance": "0",
        //                     "walletBalance": "0",
        //                     "bonus": ""
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        //  spot & swap
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "list": [
        //                 {
        //                     "totalEquity": "18070.********",
        //                     "accountIMRate": "0.0101",
        //                     "totalMarginBalance": "18070.********",
        //                     "totalInitialMargin": "182.********",
        //                     "accountType": "UNIFIED",
        //                     "totalAvailableBalance": "17887.********",
        //                     "accountMMRate": "0",
        //                     "totalPerpUPL": "-0.********",
        //                     "totalWalletBalance": "18070.********",
        //                     "accountLTV": "0.017",
        //                     "totalMaintenanceMargin": "0.********",
        //                     "coin": [
        //                         {
        //                             "availableToBorrow": "2.5",
        //                             "bonus": "0",
        //                             "accruedInterest": "0",
        //                             "availableToWithdraw": "0.805994",
        //                             "totalOrderIM": "0",
        //                             "equity": "0.805994",
        //                             "totalPositionMM": "0",
        //                             "usdValue": "12920.********",
        //                             "unrealisedPnl": "0",
        //                             "borrowAmount": "0",
        //                             "totalPositionIM": "0",
        //                             "walletBalance": "0.805994",
        //                             "cumRealisedPnl": "0",
        //                             "coin": "BTC"
        //                         }
        //                     ]
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        return this.parseBalance(response);
    }

    public virtual object parseOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "NEW", "open" },
            { "PARTIALLY_FILLED", "open" },
            { "FILLED", "closed" },
            { "CANCELED", "canceled" },
            { "PENDING_CANCEL", "open" },
            { "PENDING_NEW", "open" },
            { "REJECTED", "rejected" },
            { "PARTIALLY_FILLED_CANCELLED", "closed" },
            { "Created", "open" },
            { "New", "open" },
            { "Rejected", "rejected" },
            { "PartiallyFilled", "open" },
            { "PartiallyFilledCanceled", "closed" },
            { "Filled", "closed" },
            { "PendingCancel", "open" },
            { "Cancelled", "canceled" },
            { "Untriggered", "open" },
            { "Deactivated", "canceled" },
            { "Triggered", "open" },
            { "Active", "open" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object parseTimeInForce(object timeInForce)
    {
        object timeInForces = new Dictionary<string, object>() {
            { "GoodTillCancel", "GTC" },
            { "ImmediateOrCancel", "IOC" },
            { "FillOrKill", "FOK" },
            { "PostOnly", "PO" },
        };
        return this.safeString(timeInForces, timeInForce, timeInForce);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        // v1 for usdc normal account
        //     {
        //         "symbol": "BTCPERP",
        //         "orderType": "Market",
        //         "orderLinkId": "",
        //         "orderId": "36190ad3-de08-4b83-9ad3-56942f684b79",
        //         "cancelType": "UNKNOWN",
        //         "stopOrderType": "UNKNOWN",
        //         "orderStatus": "Filled",
        //         "updateTimeStamp": "*************",
        //         "takeProfit": "0.0000",
        //         "cumExecValue": "259.6830",
        //         "createdAt": "*************",
        //         "blockTradeId": "",
        //         "orderPnl": "",
        //         "price": "24674.7",
        //         "tpTriggerBy": "UNKNOWN",
        //         "timeInForce": "ImmediateOrCancel",
        //         "updatedAt": "*************",
        //         "basePrice": "0.0",
        //         "realisedPnl": "0.0000",
        //         "side": "Sell",
        //         "triggerPrice": "0.0",
        //         "cumExecFee": "0.1429",
        //         "leavesQty": "0.000",
        //         "cashFlow": "",
        //         "slTriggerBy": "UNKNOWN",
        //         "iv": "",
        //         "closeOnTrigger": "UNKNOWN",
        //         "cumExecQty": "0.010",
        //         "reduceOnly": 0,
        //         "qty": "0.010",
        //         "stopLoss": "0.0000",
        //         "triggerBy": "UNKNOWN",
        //         "orderIM": ""
        //     }
        //
        // v5
        //     {
        //         "orderId": "14bad3a1-6454-43d8-bcf2-5345896cf74d",
        //         "orderLinkId": "YLxaWKMiHU",
        //         "blockTradeId": "",
        //         "symbol": "BTCUSDT",
        //         "price": "26864.40",
        //         "qty": "0.003",
        //         "side": "Buy",
        //         "isLeverage": "",
        //         "positionIdx": 1,
        //         "orderStatus": "Cancelled",
        //         "cancelType": "UNKNOWN",
        //         "rejectReason": "EC_PostOnlyWillTakeLiquidity",
        //         "avgPrice": "0",
        //         "leavesQty": "0.000",
        //         "leavesValue": "0",
        //         "cumExecQty": "0.000",
        //         "cumExecValue": "0",
        //         "cumExecFee": "0",
        //         "timeInForce": "PostOnly",
        //         "orderType": "Limit",
        //         "stopOrderType": "UNKNOWN",
        //         "orderIv": "",
        //         "triggerPrice": "0.00",
        //         "takeProfit": "0.00",
        //         "stopLoss": "0.00",
        //         "tpTriggerBy": "UNKNOWN",
        //         "slTriggerBy": "UNKNOWN",
        //         "triggerDirection": 0,
        //         "triggerBy": "UNKNOWN",
        //         "lastPriceOnCreated": "0.00",
        //         "reduceOnly": false,
        //         "closeOnTrigger": false,
        //         "smpType": "None",
        //         "smpGroup": 0,
        //         "smpOrderId": "",
        //         "tpslMode": "",
        //         "tpLimitPrice": "",
        //         "slLimitPrice": "",
        //         "placeType": "",
        //         "createdTime": "1684476068369",
        //         "updatedTime": "1684476068372"
        //     }
        // createOrders failed order
        //    {
        //        "category": "linear",
        //        "symbol": "LTCUSDT",
        //        "orderId": '',
        //        "orderLinkId": '',
        //        "createAt": '',
        //        "code": "10001",
        //        "msg": "The number of contracts exceeds maximum limit allowed: too large"
        //    }
        //
        object code = this.safeString(order, "code");
        if (isTrue(!isEqual(code, null)))
        {
            if (isTrue(!isEqual(code, "0")))
            {
                object category = this.safeString(order, "category");
                object inferredMarketType = ((bool) isTrue((isEqual(category, "spot")))) ? "spot" : "contract";
                return this.safeOrder(new Dictionary<string, object>() {
                    { "info", order },
                    { "status", "rejected" },
                    { "id", this.safeString(order, "orderId") },
                    { "clientOrderId", this.safeString(order, "orderLinkId") },
                    { "symbol", this.safeSymbol(this.safeString(order, "symbol"), null, null, inferredMarketType) },
                });
            }
        }
        object marketId = this.safeString(order, "symbol");
        object isContract = (inOp(order, "tpslMode"));
        object marketType = null;
        if (isTrue(!isEqual(market, null)))
        {
            marketType = getValue(market, "type");
        } else
        {
            marketType = ((bool) isTrue(isContract)) ? "contract" : "spot";
        }
        market = this.safeMarket(marketId, market, null, marketType);
        object symbol = getValue(market, "symbol");
        object timestamp = this.safeInteger2(order, "createdTime", "createdAt");
        object marketUnit = this.safeString(order, "marketUnit", "baseCoin");
        object id = this.safeString(order, "orderId");
        object type = this.safeStringLower(order, "orderType");
        object price = this.safeString(order, "price");
        object amount = null;
        object cost = null;
        if (isTrue(isEqual(marketUnit, "baseCoin")))
        {
            amount = this.safeString(order, "qty");
            cost = this.safeString(order, "cumExecValue");
        } else
        {
            cost = this.safeString(order, "cumExecValue");
        }
        object filled = this.safeString(order, "cumExecQty");
        object remaining = this.safeString(order, "leavesQty");
        object lastTradeTimestamp = this.safeInteger2(order, "updatedTime", "updatedAt");
        object rawStatus = this.safeString(order, "orderStatus");
        object status = this.parseOrderStatus(rawStatus);
        object side = this.safeStringLower(order, "side");
        object fee = null;
        object feeCostString = this.safeString(order, "cumExecFee");
        if (isTrue(!isEqual(feeCostString, null)))
        {
            object feeCurrencyCode = null;
            if (isTrue(getValue(market, "spot")))
            {
                if (isTrue(Precise.stringGt(feeCostString, "0")))
                {
                    if (isTrue(isEqual(side, "buy")))
                    {
                        feeCurrencyCode = getValue(market, "base");
                    } else
                    {
                        feeCurrencyCode = getValue(market, "quote");
                    }
                } else
                {
                    if (isTrue(isEqual(side, "buy")))
                    {
                        feeCurrencyCode = getValue(market, "quote");
                    } else
                    {
                        feeCurrencyCode = getValue(market, "base");
                    }
                }
            } else
            {
                feeCurrencyCode = ((bool) isTrue(getValue(market, "inverse"))) ? getValue(market, "base") : getValue(market, "settle");
            }
            fee = new Dictionary<string, object>() {
                { "cost", this.parseNumber(feeCostString) },
                { "currency", feeCurrencyCode },
            };
        }
        object clientOrderId = this.safeString(order, "orderLinkId");
        if (isTrue(isTrue((!isEqual(clientOrderId, null))) && isTrue((isLessThan(((string)clientOrderId).Length, 1)))))
        {
            clientOrderId = null;
        }
        object avgPrice = this.omitZero(this.safeString(order, "avgPrice"));
        object rawTimeInForce = this.safeString(order, "timeInForce");
        object timeInForce = this.parseTimeInForce(rawTimeInForce);
        object triggerPrice = this.omitZero(this.safeString(order, "triggerPrice"));
        object reduceOnly = this.safeBool(order, "reduceOnly");
        object takeProfitPrice = this.omitZero(this.safeString(order, "takeProfit"));
        object stopLossPrice = this.omitZero(this.safeString(order, "stopLoss"));
        object triggerDirection = this.safeString(order, "triggerDirection");
        object isAscending = (isEqual(triggerDirection, "1"));
        object isStopOrderType2 = isTrue((!isEqual(triggerPrice, null))) && isTrue(reduceOnly);
        if (isTrue(isTrue((isEqual(stopLossPrice, null))) && isTrue(isStopOrderType2)))
        {
            // check if order is stop order type 2 - stopLossPrice
            if (isTrue(isTrue(isAscending) && isTrue((isEqual(side, "buy")))))
            {
                // stopLoss order against short position
                stopLossPrice = triggerPrice;
            }
            if (isTrue(!isTrue(isAscending) && isTrue((isEqual(side, "sell")))))
            {
                // stopLoss order against a long position
                stopLossPrice = triggerPrice;
            }
        }
        if (isTrue(isTrue((isEqual(takeProfitPrice, null))) && isTrue(isStopOrderType2)))
        {
            // check if order is stop order type 2 - takeProfitPrice
            if (isTrue(isTrue(isAscending) && isTrue((isEqual(side, "sell")))))
            {
                // takeprofit order against a long position
                takeProfitPrice = triggerPrice;
            }
            if (isTrue(!isTrue(isAscending) && isTrue((isEqual(side, "buy")))))
            {
                // takeprofit order against a short position
                takeProfitPrice = triggerPrice;
            }
        }
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", order },
            { "id", id },
            { "clientOrderId", clientOrderId },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastTradeTimestamp", lastTradeTimestamp },
            { "lastUpdateTimestamp", lastTradeTimestamp },
            { "symbol", symbol },
            { "type", type },
            { "timeInForce", timeInForce },
            { "postOnly", null },
            { "reduceOnly", this.safeBool(order, "reduceOnly") },
            { "side", side },
            { "price", price },
            { "triggerPrice", triggerPrice },
            { "takeProfitPrice", takeProfitPrice },
            { "stopLossPrice", stopLossPrice },
            { "amount", amount },
            { "cost", cost },
            { "average", avgPrice },
            { "filled", filled },
            { "remaining", remaining },
            { "status", status },
            { "fee", fee },
            { "trades", null },
        }, market);
    }

    /**
     * @method
     * @name bybit#createMarketBuyOrderWithCost
     * @description create a market buy order by providing the symbol and cost
     * @see https://bybit-exchange.github.io/docs/v5/order/create-order
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {float} cost how much you want to trade in units of the quote currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createMarketBuyOrderWithCost(object symbol, object cost, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "spot")))
        {
            throw new NotSupported ((string)add(this.id, " createMarketBuyOrderWithCost() supports spot orders only")) ;
        }
        object req = new Dictionary<string, object>() {
            { "cost", cost },
        };
        return await this.createOrder(symbol, "market", "buy", -1, null, this.extend(req, parameters));
    }

    /**
     * @method
     * @name bybit#createMarkeSellOrderWithCost
     * @description create a market sell order by providing the symbol and cost
     * @see https://bybit-exchange.github.io/docs/v5/order/create-order
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {float} cost how much you want to trade in units of the quote currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createMarketSellOrderWithCost(object symbol, object cost, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object types = await this.isUnifiedEnabled();
        object enableUnifiedAccount = getValue(types, 1);
        if (!isTrue(enableUnifiedAccount))
        {
            throw new NotSupported ((string)add(this.id, " createMarketSellOrderWithCost() supports UTA accounts only")) ;
        }
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "spot")))
        {
            throw new NotSupported ((string)add(this.id, " createMarketSellOrderWithCost() supports spot orders only")) ;
        }
        object req = new Dictionary<string, object>() {
            { "cost", cost },
        };
        return await this.createOrder(symbol, "market", "sell", -1, null, this.extend(req, parameters));
    }

    /**
     * @method
     * @name bybit#createOrder
     * @description create a trade order
     * @see https://bybit-exchange.github.io/docs/v5/order/create-order
     * @see https://bybit-exchange.github.io/docs/v5/position/trading-stop
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.timeInForce] "GTC", "IOC", "FOK"
     * @param {bool} [params.postOnly] true or false whether the order is post-only
     * @param {bool} [params.reduceOnly] true or false whether the order is reduce-only
     * @param {string} [params.positionIdx] *contracts only* 0 for one-way mode, 1 buy side of hedged mode, 2 sell side of hedged mode
     * @param {bool} [params.hedged] *contracts only* true for hedged mode, false for one way mode, default is false
     * @param {int} [params.isLeverage] *unified spot only* false then spot trading true then margin trading
     * @param {string} [params.tpslMode] *contract only* 'full' or 'partial'
     * @param {string} [params.mmp] *option only* market maker protection
     * @param {string} [params.triggerDirection] *contract only* the direction for trigger orders, 'ascending' or 'descending'
     * @param {float} [params.triggerPrice] The price at which a trigger order is triggered at
     * @param {float} [params.stopLossPrice] The price at which a stop loss order is triggered at
     * @param {float} [params.takeProfitPrice] The price at which a take profit order is triggered at
     * @param {object} [params.takeProfit] *takeProfit object in params* containing the triggerPrice at which the attached take profit order will be triggered
     * @param {float} [params.takeProfit.triggerPrice] take profit trigger price
     * @param {object} [params.stopLoss] *stopLoss object in params* containing the triggerPrice at which the attached stop loss order will be triggered
     * @param {float} [params.stopLoss.triggerPrice] stop loss trigger price
     * @param {string} [params.trailingAmount] the quote amount to trail away from the current market price
     * @param {string} [params.trailingTriggerPrice] the price to trigger a trailing order, default uses the price argument
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object parts = await this.isUnifiedEnabled();
        object enableUnifiedAccount = getValue(parts, 1);
        object trailingAmount = this.safeString2(parameters, "trailingAmount", "trailingStop");
        object stopLossPrice = this.safeString(parameters, "stopLossPrice");
        object takeProfitPrice = this.safeString(parameters, "takeProfitPrice");
        object isTrailingAmountOrder = !isEqual(trailingAmount, null);
        object isStopLoss = !isEqual(stopLossPrice, null);
        object isTakeProfit = !isEqual(takeProfitPrice, null);
        object orderRequest = this.createOrderRequest(symbol, type, side, amount, price, parameters, enableUnifiedAccount);
        object defaultMethod = null;
        if (isTrue(isTrue((isTrue(isTrue(isTrailingAmountOrder) || isTrue(isStopLoss)) || isTrue(isTakeProfit))) && !isTrue(getValue(market, "spot"))))
        {
            defaultMethod = "privatePostV5PositionTradingStop";
        } else
        {
            defaultMethod = "privatePostV5OrderCreate";
        }
        object method = null;
        var methodparametersVariable = this.handleOptionAndParams(parameters, "createOrder", "method", defaultMethod);
        method = ((IList<object>)methodparametersVariable)[0];
        parameters = ((IList<object>)methodparametersVariable)[1];
        object response = null;
        if (isTrue(isEqual(method, "privatePostV5PositionTradingStop")))
        {
            response = await this.privatePostV5PositionTradingStop(orderRequest);
        } else
        {
            response = await this.privatePostV5OrderCreate(orderRequest); // already extended inside createOrderRequest
        }
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "orderId": "1321003749386327552",
        //             "orderLinkId": "spot-test-postonly"
        //         },
        //         "retExtInfo": {},
        //         "time": 1672211918471
        //     }
        //
        object order = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseOrder(order, market);
    }

    public virtual object createOrderRequest(object symbol, object type, object side, object amount, object price = null, object parameters = null, object isUTA = null)
    {
        parameters ??= new Dictionary<string, object>();
        isUTA ??= true;
        object market = this.market(symbol);
        symbol = getValue(market, "symbol");
        object lowerCaseType = ((string)type).ToLower();
        if (isTrue(isTrue((isEqual(price, null))) && isTrue((isEqual(lowerCaseType, "limit")))))
        {
            throw new ArgumentsRequired ((string)add(this.id, " createOrder requires a price argument for limit orders")) ;
        }
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object hedged = this.safeBool(parameters, "hedged", false);
        object reduceOnly = this.safeBool(parameters, "reduceOnly");
        object triggerPrice = this.safeValue2(parameters, "triggerPrice", "stopPrice");
        object stopLossTriggerPrice = this.safeValue(parameters, "stopLossPrice");
        object takeProfitTriggerPrice = this.safeValue(parameters, "takeProfitPrice");
        object stopLoss = this.safeValue(parameters, "stopLoss");
        object takeProfit = this.safeValue(parameters, "takeProfit");
        object trailingTriggerPrice = this.safeString2(parameters, "trailingTriggerPrice", "activePrice", this.numberToString(price));
        object trailingAmount = this.safeString2(parameters, "trailingAmount", "trailingStop");
        object isTrailingAmountOrder = !isEqual(trailingAmount, null);
        object isTriggerOrder = !isEqual(triggerPrice, null);
        object isStopLossTriggerOrder = !isEqual(stopLossTriggerPrice, null);
        object isTakeProfitTriggerOrder = !isEqual(takeProfitTriggerPrice, null);
        object isStopLoss = !isEqual(stopLoss, null);
        object isTakeProfit = !isEqual(takeProfit, null);
        object isMarket = isEqual(lowerCaseType, "market");
        object isLimit = isEqual(lowerCaseType, "limit");
        object isBuy = isEqual(side, "buy");
        object defaultMethod = null;
        if (isTrue(isTrue((isTrue(isTrue(isTrailingAmountOrder) || isTrue(isStopLossTriggerOrder)) || isTrue(isTakeProfitTriggerOrder))) && !isTrue(getValue(market, "spot"))))
        {
            defaultMethod = "privatePostV5PositionTradingStop";
        } else
        {
            defaultMethod = "privatePostV5OrderCreate";
        }
        object method = null;
        var methodparametersVariable = this.handleOptionAndParams(parameters, "createOrder", "method", defaultMethod);
        method = ((IList<object>)methodparametersVariable)[0];
        parameters = ((IList<object>)methodparametersVariable)[1];
        object isAlternativeEndpoint = isEqual(method, "privatePostV5PositionTradingStop");
        object amountString = this.getAmount(symbol, amount);
        object priceString = ((bool) isTrue((!isEqual(price, null)))) ? this.getPrice(symbol, this.numberToString(price)) : null;
        if (isTrue(isTrue(isTrailingAmountOrder) || isTrue(isAlternativeEndpoint)))
        {
            if (isTrue(isTrue(isTrue(isTrue(isStopLoss) || isTrue(isTakeProfit)) || isTrue(isTriggerOrder)) || isTrue(getValue(market, "spot"))))
            {
                throw new InvalidOrder ((string)add(this.id, " the API endpoint used only supports contract trailingAmount, stopLossPrice and takeProfitPrice orders")) ;
            }
            if (isTrue(isTrue(isStopLossTriggerOrder) || isTrue(isTakeProfitTriggerOrder)))
            {
                if (isTrue(isStopLossTriggerOrder))
                {
                    ((IDictionary<string,object>)request)["stopLoss"] = this.getPrice(symbol, stopLossTriggerPrice);
                    if (isTrue(isLimit))
                    {
                        ((IDictionary<string,object>)request)["tpslMode"] = "Partial";
                        ((IDictionary<string,object>)request)["slOrderType"] = "Limit";
                        ((IDictionary<string,object>)request)["slLimitPrice"] = priceString;
                        ((IDictionary<string,object>)request)["slSize"] = amountString;
                    }
                } else if (isTrue(isTakeProfitTriggerOrder))
                {
                    ((IDictionary<string,object>)request)["takeProfit"] = this.getPrice(symbol, takeProfitTriggerPrice);
                    if (isTrue(isLimit))
                    {
                        ((IDictionary<string,object>)request)["tpslMode"] = "Partial";
                        ((IDictionary<string,object>)request)["tpOrderType"] = "Limit";
                        ((IDictionary<string,object>)request)["tpLimitPrice"] = priceString;
                        ((IDictionary<string,object>)request)["tpSize"] = amountString;
                    }
                }
            }
        } else
        {
            ((IDictionary<string,object>)request)["side"] = this.capitalize(side);
            ((IDictionary<string,object>)request)["orderType"] = this.capitalize(lowerCaseType);
            object timeInForce = this.safeStringLower(parameters, "timeInForce"); // this is same as exchange specific param
            object postOnly = null;
            var postOnlyparametersVariable = this.handlePostOnly(isMarket, isEqual(timeInForce, "postonly"), parameters);
            postOnly = ((IList<object>)postOnlyparametersVariable)[0];
            parameters = ((IList<object>)postOnlyparametersVariable)[1];
            if (isTrue(postOnly))
            {
                ((IDictionary<string,object>)request)["timeInForce"] = "PostOnly";
            } else if (isTrue(isEqual(timeInForce, "gtc")))
            {
                ((IDictionary<string,object>)request)["timeInForce"] = "GTC";
            } else if (isTrue(isEqual(timeInForce, "fok")))
            {
                ((IDictionary<string,object>)request)["timeInForce"] = "FOK";
            } else if (isTrue(isEqual(timeInForce, "ioc")))
            {
                ((IDictionary<string,object>)request)["timeInForce"] = "IOC";
            }
            if (isTrue(getValue(market, "spot")))
            {
                // only works for spot market
                if (isTrue(!isEqual(triggerPrice, null)))
                {
                    ((IDictionary<string,object>)request)["orderFilter"] = "StopOrder";
                } else if (isTrue(isTrue(isTrue(isTrue(!isEqual(stopLossTriggerPrice, null)) || isTrue(!isEqual(takeProfitTriggerPrice, null))) || isTrue(isStopLoss)) || isTrue(isTakeProfit)))
                {
                    ((IDictionary<string,object>)request)["orderFilter"] = "tpslOrder";
                }
            }
            object clientOrderId = this.safeString(parameters, "clientOrderId");
            if (isTrue(!isEqual(clientOrderId, null)))
            {
                ((IDictionary<string,object>)request)["orderLinkId"] = clientOrderId;
            } else if (isTrue(getValue(market, "option")))
            {
                // mandatory field for options
                ((IDictionary<string,object>)request)["orderLinkId"] = this.uuid16();
            }
            if (isTrue(isLimit))
            {
                ((IDictionary<string,object>)request)["price"] = priceString;
            }
        }
        object category = null;
        var categoryparametersVariable = this.getBybitType("createOrderRequest", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = category;
        object cost = this.safeString(parameters, "cost");
        parameters = this.omit(parameters, "cost");
        // if the cost is inferable, let's keep the old logic and ignore marketUnit, to minimize the impact of the changes
        object isMarketBuyAndCostInferable = isTrue(isTrue((isEqual(lowerCaseType, "market"))) && isTrue((isEqual(side, "buy")))) && isTrue((isTrue((!isEqual(price, null))) || isTrue((!isEqual(cost, null)))));
        if (isTrue(isTrue(isTrue(isTrue(getValue(market, "spot")) && isTrue((isEqual(type, "market")))) && isTrue(isUTA)) && !isTrue(isMarketBuyAndCostInferable)))
        {
            // UTA account can specify the cost of the order on both sides
            if (isTrue(isTrue((!isEqual(cost, null))) || isTrue((!isEqual(price, null)))))
            {
                ((IDictionary<string,object>)request)["marketUnit"] = "quoteCoin";
                object orderCost = null;
                if (isTrue(!isEqual(cost, null)))
                {
                    orderCost = cost;
                } else
                {
                    object quoteAmount = Precise.stringMul(amountString, priceString);
                    orderCost = quoteAmount;
                }
                ((IDictionary<string,object>)request)["qty"] = this.getCost(symbol, orderCost);
            } else
            {
                ((IDictionary<string,object>)request)["marketUnit"] = "baseCoin";
                ((IDictionary<string,object>)request)["qty"] = amountString;
            }
        } else if (isTrue(isTrue(isTrue(getValue(market, "spot")) && isTrue((isEqual(type, "market")))) && isTrue((isEqual(side, "buy")))))
        {
            // classic accounts
            // for market buy it requires the amount of quote currency to spend
            object createMarketBuyOrderRequiresPrice = true;
            var createMarketBuyOrderRequiresPriceparametersVariable = this.handleOptionAndParams(parameters, "createOrder", "createMarketBuyOrderRequiresPrice");
            createMarketBuyOrderRequiresPrice = ((IList<object>)createMarketBuyOrderRequiresPriceparametersVariable)[0];
            parameters = ((IList<object>)createMarketBuyOrderRequiresPriceparametersVariable)[1];
            if (isTrue(createMarketBuyOrderRequiresPrice))
            {
                if (isTrue(isTrue((isEqual(price, null))) && isTrue((isEqual(cost, null)))))
                {
                    throw new InvalidOrder ((string)add(this.id, " createOrder() requires the price argument for market buy orders to calculate the total cost to spend (amount * price), alternatively set the createMarketBuyOrderRequiresPrice option or param to false and pass the cost to spend in the amount argument")) ;
                } else
                {
                    object quoteAmount = Precise.stringMul(this.numberToString(amount), priceString);
                    object costRequest = ((bool) isTrue((!isEqual(cost, null)))) ? cost : quoteAmount;
                    ((IDictionary<string,object>)request)["qty"] = this.getCost(symbol, costRequest);
                }
            } else
            {
                if (isTrue(!isEqual(cost, null)))
                {
                    ((IDictionary<string,object>)request)["qty"] = this.getCost(symbol, this.numberToString(cost));
                } else if (isTrue(!isEqual(price, null)))
                {
                    ((IDictionary<string,object>)request)["qty"] = this.getCost(symbol, Precise.stringMul(amountString, priceString));
                } else
                {
                    ((IDictionary<string,object>)request)["qty"] = amountString;
                }
            }
        } else
        {
            if (isTrue(!isTrue(isTrailingAmountOrder) && !isTrue(isAlternativeEndpoint)))
            {
                ((IDictionary<string,object>)request)["qty"] = amountString;
            }
        }
        if (isTrue(isTrailingAmountOrder))
        {
            if (isTrue(!isEqual(trailingTriggerPrice, null)))
            {
                ((IDictionary<string,object>)request)["activePrice"] = this.getPrice(symbol, trailingTriggerPrice);
            }
            ((IDictionary<string,object>)request)["trailingStop"] = trailingAmount;
        } else if (isTrue(isTrue(isTriggerOrder) && !isTrue(isAlternativeEndpoint)))
        {
            object triggerDirection = this.safeString(parameters, "triggerDirection");
            parameters = this.omit(parameters, new List<object>() {"triggerPrice", "stopPrice", "triggerDirection"});
            if (isTrue(getValue(market, "spot")))
            {
                if (isTrue(!isEqual(triggerDirection, null)))
                {
                    throw new NotSupported ((string)add(this.id, " createOrder() : trigger order does not support triggerDirection for spot markets yet")) ;
                }
            } else
            {
                if (isTrue(isEqual(triggerDirection, null)))
                {
                    throw new ArgumentsRequired ((string)add(this.id, " stop/trigger orders require a triggerDirection parameter, either \"ascending\" or \"descending\" to determine the direction of the trigger.")) ;
                }
                object isAsending = (isTrue(isTrue((isEqual(triggerDirection, "ascending"))) || isTrue((isEqual(triggerDirection, "above")))) || isTrue((isEqual(triggerDirection, "1"))));
                ((IDictionary<string,object>)request)["triggerDirection"] = ((bool) isTrue(isAsending)) ? 1 : 2;
            }
            ((IDictionary<string,object>)request)["triggerPrice"] = this.getPrice(symbol, triggerPrice);
        } else if (isTrue(isTrue((isTrue(isStopLossTriggerOrder) || isTrue(isTakeProfitTriggerOrder))) && !isTrue(isAlternativeEndpoint)))
        {
            if (isTrue(isBuy))
            {
                ((IDictionary<string,object>)request)["triggerDirection"] = ((bool) isTrue(isStopLossTriggerOrder)) ? 1 : 2;
            } else
            {
                ((IDictionary<string,object>)request)["triggerDirection"] = ((bool) isTrue(isStopLossTriggerOrder)) ? 2 : 1;
            }
            triggerPrice = ((bool) isTrue(isStopLossTriggerOrder)) ? stopLossTriggerPrice : takeProfitTriggerPrice;
            ((IDictionary<string,object>)request)["triggerPrice"] = this.getPrice(symbol, triggerPrice);
            ((IDictionary<string,object>)request)["reduceOnly"] = true;
        }
        if (isTrue(isTrue((isTrue(isStopLoss) || isTrue(isTakeProfit))) && !isTrue(isAlternativeEndpoint)))
        {
            if (isTrue(isStopLoss))
            {
                object slTriggerPrice = this.safeValue2(stopLoss, "triggerPrice", "stopPrice", stopLoss);
                ((IDictionary<string,object>)request)["stopLoss"] = this.getPrice(symbol, slTriggerPrice);
                object slLimitPrice = this.safeValue(stopLoss, "price");
                if (isTrue(!isEqual(slLimitPrice, null)))
                {
                    ((IDictionary<string,object>)request)["tpslMode"] = "Partial";
                    ((IDictionary<string,object>)request)["slOrderType"] = "Limit";
                    ((IDictionary<string,object>)request)["slLimitPrice"] = this.getPrice(symbol, slLimitPrice);
                }
            }
            if (isTrue(isTakeProfit))
            {
                object tpTriggerPrice = this.safeValue2(takeProfit, "triggerPrice", "stopPrice", takeProfit);
                ((IDictionary<string,object>)request)["takeProfit"] = this.getPrice(symbol, tpTriggerPrice);
                object tpLimitPrice = this.safeValue(takeProfit, "price");
                if (isTrue(!isEqual(tpLimitPrice, null)))
                {
                    ((IDictionary<string,object>)request)["tpslMode"] = "Partial";
                    ((IDictionary<string,object>)request)["tpOrderType"] = "Limit";
                    ((IDictionary<string,object>)request)["tpLimitPrice"] = this.getPrice(symbol, tpLimitPrice);
                }
            }
        }
        if (isTrue(!isTrue(getValue(market, "spot")) && isTrue(hedged)))
        {
            if (isTrue(reduceOnly))
            {
                parameters = this.omit(parameters, "reduceOnly");
                side = ((bool) isTrue((isEqual(side, "buy")))) ? "sell" : "buy";
            }
            ((IDictionary<string,object>)request)["positionIdx"] = ((bool) isTrue((isEqual(side, "buy")))) ? 1 : 2;
        }
        parameters = this.omit(parameters, new List<object>() {"stopPrice", "timeInForce", "stopLossPrice", "takeProfitPrice", "postOnly", "clientOrderId", "triggerPrice", "stopLoss", "takeProfit", "trailingAmount", "trailingTriggerPrice", "hedged"});
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bybit#createOrders
     * @description create a list of trade orders
     * @see https://bybit-exchange.github.io/docs/v5/order/batch-place
     * @param {Array} orders list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrders(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object accounts = await this.isUnifiedEnabled();
        object isUta = getValue(accounts, 1);
        object ordersRequests = new List<object>() {};
        object orderSymbols = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(orders)); postFixIncrement(ref i))
        {
            object rawOrder = getValue(orders, i);
            object marketId = this.safeString(rawOrder, "symbol");
            ((IList<object>)orderSymbols).Add(marketId);
            object type = this.safeString(rawOrder, "type");
            object side = this.safeString(rawOrder, "side");
            object amount = this.safeValue(rawOrder, "amount");
            object price = this.safeValue(rawOrder, "price");
            object orderParams = this.safeDict(rawOrder, "params", new Dictionary<string, object>() {});
            object orderRequest = this.createOrderRequest(marketId, type, side, amount, price, orderParams, isUta);
            ((IDictionary<string,object>)orderRequest).Remove((string)"category");
            ((IList<object>)ordersRequests).Add(orderRequest);
        }
        object symbols = this.marketSymbols(orderSymbols, null, false, true, true);
        object market = this.market(getValue(symbols, 0));
        object unifiedMarginStatus = this.safeInteger(this.options, "unifiedMarginStatus", 6);
        object category = null;
        var categoryparametersVariable = this.getBybitType("createOrders", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        if (isTrue(isTrue((isEqual(category, "inverse"))) && isTrue((isLessThan(unifiedMarginStatus, 5)))))
        {
            throw new NotSupported ((string)add(this.id, " createOrders does not allow inverse orders for non UTA2.0 account")) ;
        }
        object request = new Dictionary<string, object>() {
            { "category", category },
            { "request", ordersRequests },
        };
        object response = await this.privatePostV5OrderCreateBatch(this.extend(request, parameters));
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "list", new List<object>() {});
        object retInfo = this.safeDict(response, "retExtInfo", new Dictionary<string, object>() {});
        object codes = this.safeList(retInfo, "list", new List<object>() {});
        // extend the error with the unsuccessful orders
        for (object i = 0; isLessThan(i, getArrayLength(codes)); postFixIncrement(ref i))
        {
            object code = getValue(codes, i);
            object retCode = this.safeInteger(code, "code");
            if (isTrue(!isEqual(retCode, 0)))
            {
                ((List<object>)data)[Convert.ToInt32(i)] = this.extend(getValue(data, i), code);
            }
        }
        //
        // {
        //     "retCode":0,
        //     "retMsg":"OK",
        //     "result":{
        //        "list":[
        //           {
        //              "category":"linear",
        //              "symbol":"LTCUSDT",
        //              "orderId":"",
        //              "orderLinkId":"",
        //              "createAt":""
        //           },
        //           {
        //              "category":"linear",
        //              "symbol":"LTCUSDT",
        //              "orderId":"3c9f65b6-01ad-4ac0-9741-df17e02a4223",
        //              "orderLinkId":"",
        //              "createAt":"1698075516029"
        //           }
        //        ]
        //     },
        //     "retExtInfo":{
        //        "list":[
        //           {
        //              "code":10001,
        //              "msg":"The number of contracts exceeds maximum limit allowed: too large"
        //           },
        //           {
        //              "code":0,
        //              "msg":"OK"
        //           }
        //        ]
        //     },
        //     "time":1698075516029
        // }
        //
        return this.parseOrders(data);
    }

    public virtual object editOrderRequest(object id, object symbol, object type, object side, object amount = null, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "orderId", id },
        };
        object category = null;
        var categoryparametersVariable = this.getBybitType("editOrderRequest", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = category;
        if (isTrue(!isEqual(amount, null)))
        {
            ((IDictionary<string,object>)request)["qty"] = this.getAmount(symbol, amount);
        }
        if (isTrue(!isEqual(price, null)))
        {
            ((IDictionary<string,object>)request)["price"] = this.getPrice(symbol, this.numberToString(price));
        }
        object triggerPrice = this.safeString2(parameters, "triggerPrice", "stopPrice");
        object stopLossTriggerPrice = this.safeString(parameters, "stopLossPrice");
        object takeProfitTriggerPrice = this.safeString(parameters, "takeProfitPrice");
        object stopLoss = this.safeValue(parameters, "stopLoss");
        object takeProfit = this.safeValue(parameters, "takeProfit");
        object isStopLossTriggerOrder = !isEqual(stopLossTriggerPrice, null);
        object isTakeProfitTriggerOrder = !isEqual(takeProfitTriggerPrice, null);
        object isStopLoss = !isEqual(stopLoss, null);
        object isTakeProfit = !isEqual(takeProfit, null);
        if (isTrue(isTrue(isStopLossTriggerOrder) || isTrue(isTakeProfitTriggerOrder)))
        {
            triggerPrice = ((bool) isTrue(isStopLossTriggerOrder)) ? stopLossTriggerPrice : takeProfitTriggerPrice;
        }
        if (isTrue(!isEqual(triggerPrice, null)))
        {
            object triggerPriceRequest = ((bool) isTrue((isEqual(triggerPrice, "0")))) ? triggerPrice : this.getPrice(symbol, triggerPrice);
            ((IDictionary<string,object>)request)["triggerPrice"] = triggerPriceRequest;
            object triggerBy = this.safeString(parameters, "triggerBy", "LastPrice");
            ((IDictionary<string,object>)request)["triggerBy"] = triggerBy;
        }
        if (isTrue(isTrue(isStopLoss) || isTrue(isTakeProfit)))
        {
            if (isTrue(isStopLoss))
            {
                object slTriggerPrice = this.safeString2(stopLoss, "triggerPrice", "stopPrice", stopLoss);
                object stopLossRequest = ((bool) isTrue((isEqual(slTriggerPrice, "0")))) ? slTriggerPrice : this.getPrice(symbol, slTriggerPrice);
                ((IDictionary<string,object>)request)["stopLoss"] = stopLossRequest;
                object slTriggerBy = this.safeString(parameters, "slTriggerBy", "LastPrice");
                ((IDictionary<string,object>)request)["slTriggerBy"] = slTriggerBy;
            }
            if (isTrue(isTakeProfit))
            {
                object tpTriggerPrice = this.safeString2(takeProfit, "triggerPrice", "stopPrice", takeProfit);
                object takeProfitRequest = ((bool) isTrue((isEqual(tpTriggerPrice, "0")))) ? tpTriggerPrice : this.getPrice(symbol, tpTriggerPrice);
                ((IDictionary<string,object>)request)["takeProfit"] = takeProfitRequest;
                object tpTriggerBy = this.safeString(parameters, "tpTriggerBy", "LastPrice");
                ((IDictionary<string,object>)request)["tpTriggerBy"] = tpTriggerBy;
            }
        }
        object clientOrderId = this.safeString(parameters, "clientOrderId");
        if (isTrue(!isEqual(clientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["orderLinkId"] = clientOrderId;
        }
        parameters = this.omit(parameters, new List<object>() {"stopPrice", "stopLossPrice", "takeProfitPrice", "triggerPrice", "clientOrderId", "stopLoss", "takeProfit"});
        return request;
    }

    /**
     * @method
     * @name bybit#editOrder
     * @description edit a trade order
     * @see https://bybit-exchange.github.io/docs/v5/order/amend-order
     * @see https://bybit-exchange.github.io/docs/derivatives/unified/replace-order
     * @see https://bybit-exchange.github.io/docs/api-explorer/derivatives/trade/contract/replace-order
     * @param {string} id cancel order id
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} price the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {float} [params.triggerPrice] The price that a trigger order is triggered at
     * @param {float} [params.stopLossPrice] The price that a stop loss order is triggered at
     * @param {float} [params.takeProfitPrice] The price that a take profit order is triggered at
     * @param {object} [params.takeProfit] *takeProfit object in params* containing the triggerPrice that the attached take profit order will be triggered
     * @param {float} [params.takeProfit.triggerPrice] take profit trigger price
     * @param {object} [params.stopLoss] *stopLoss object in params* containing the triggerPrice that the attached stop loss order will be triggered
     * @param {float} [params.stopLoss.triggerPrice] stop loss trigger price
     * @param {string} [params.triggerBy] 'IndexPrice', 'MarkPrice' or 'LastPrice', default is 'LastPrice', required if no initial value for triggerPrice
     * @param {string} [params.slTriggerBy] 'IndexPrice', 'MarkPrice' or 'LastPrice', default is 'LastPrice', required if no initial value for stopLoss
     * @param {string} [params.tpTriggerby] 'IndexPrice', 'MarkPrice' or 'LastPrice', default is 'LastPrice', required if no initial value for takeProfit
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> editOrder(object id, object symbol, object type, object side, object amount = null, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " editOrder() requires a symbol argument")) ;
        }
        object request = this.editOrderRequest(id, symbol, type, side, amount, price, parameters);
        object response = await this.privatePostV5OrderAmend(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "orderId": "c6f055d9-7f21-4079-913d-e6523a9cfffa",
        //             "orderLinkId": "linear-004"
        //         },
        //         "retExtInfo": {},
        //         "time": 1672217093461
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", response },
            { "id", this.safeString(result, "orderId") },
        });
    }

    /**
     * @method
     * @name bybit#editOrders
     * @description edit a list of trade orders
     * @see https://bybit-exchange.github.io/docs/v5/order/batch-amend
     * @param {Array} orders list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> editOrders(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object ordersRequests = new List<object>() {};
        object orderSymbols = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(orders)); postFixIncrement(ref i))
        {
            object rawOrder = getValue(orders, i);
            object symbol = this.safeString(rawOrder, "symbol");
            ((IList<object>)orderSymbols).Add(symbol);
            object id = this.safeString(rawOrder, "id");
            object type = this.safeString(rawOrder, "type");
            object side = this.safeString(rawOrder, "side");
            object amount = this.safeValue(rawOrder, "amount");
            object price = this.safeValue(rawOrder, "price");
            object orderParams = this.safeDict(rawOrder, "params", new Dictionary<string, object>() {});
            object orderRequest = this.editOrderRequest(id, symbol, type, side, amount, price, orderParams);
            ((IDictionary<string,object>)orderRequest).Remove((string)"category");
            ((IList<object>)ordersRequests).Add(orderRequest);
        }
        orderSymbols = this.marketSymbols(orderSymbols, null, false, true, true);
        object market = this.market(getValue(orderSymbols, 0));
        object unifiedMarginStatus = this.safeInteger(this.options, "unifiedMarginStatus", 6);
        object category = null;
        var categoryparametersVariable = this.getBybitType("editOrders", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        if (isTrue(isTrue((isEqual(category, "inverse"))) && isTrue((isLessThan(unifiedMarginStatus, 5)))))
        {
            throw new NotSupported ((string)add(this.id, " editOrders does not allow inverse orders for non UTA2.0 account")) ;
        }
        object request = new Dictionary<string, object>() {
            { "category", category },
            { "request", ordersRequests },
        };
        object response = await this.privatePostV5OrderAmendBatch(this.extend(request, parameters));
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "list", new List<object>() {});
        object retInfo = this.safeDict(response, "retExtInfo", new Dictionary<string, object>() {});
        object codes = this.safeList(retInfo, "list", new List<object>() {});
        // extend the error with the unsuccessful orders
        for (object i = 0; isLessThan(i, getArrayLength(codes)); postFixIncrement(ref i))
        {
            object code = getValue(codes, i);
            object retCode = this.safeInteger(code, "code");
            if (isTrue(!isEqual(retCode, 0)))
            {
                ((List<object>)data)[Convert.ToInt32(i)] = this.extend(getValue(data, i), code);
            }
        }
        //
        // {
        //     "retCode": 0,
        //     "retMsg": "OK",
        //     "result": {
        //         "list": [
        //             {
        //                 "category": "option",
        //                 "symbol": "ETH-30DEC22-500-C",
        //                 "orderId": "b551f227-7059-4fb5-a6a6-699c04dbd2f2",
        //                 "orderLinkId": ""
        //             },
        //             {
        //                 "category": "option",
        //                 "symbol": "ETH-30DEC22-700-C",
        //                 "orderId": "fa6a595f-1a57-483f-b9d3-30e9c8235a52",
        //                 "orderLinkId": ""
        //             }
        //         ]
        //     },
        //     "retExtInfo": {
        //         "list": [
        //             {
        //                 "code": 0,
        //                 "msg": "OK"
        //             },
        //             {
        //                 "code": 0,
        //                 "msg": "OK"
        //             }
        //         ]
        //     },
        //     "time": 1672222808060
        // }
        //
        return this.parseOrders(data);
    }

    public virtual object cancelOrderRequest(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(getValue(market, "spot")))
        {
            // only works for spot market
            object isTrigger = this.safeBool2(parameters, "stop", "trigger", false);
            parameters = this.omit(parameters, new List<object>() {"stop", "trigger"});
            ((IDictionary<string,object>)request)["orderFilter"] = ((bool) isTrue(isTrigger)) ? "StopOrder" : "Order";
        }
        if (isTrue(!isEqual(id, null)))
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        }
        object category = null;
        var categoryparametersVariable = this.getBybitType("cancelOrderRequest", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = category;
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name bybit#cancelOrder
     * @description cancels an open order
     * @see https://bybit-exchange.github.io/docs/v5/order/cancel-order
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] *spot only* whether the order is a trigger order
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.orderFilter] *spot only* 'Order' or 'StopOrder' or 'tpslOrder'
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object requestExtended = this.cancelOrderRequest(id, symbol, parameters);
        object response = await this.privatePostV5OrderCancel(requestExtended);
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "orderId": "c6f055d9-7f21-4079-913d-e6523a9cfffa",
        //             "orderLinkId": "linear-004"
        //         },
        //         "retExtInfo": {},
        //         "time": 1672217377164
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseOrder(result, market);
    }

    /**
     * @method
     * @name bybit#cancelOrders
     * @description cancel multiple orders
     * @see https://bybit-exchange.github.io/docs/v5/order/batch-cancel
     * @param {string[]} ids order ids
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string[]} [params.clientOrderIds] client order ids
     * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> cancelOrders(object ids, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object types = await this.isUnifiedEnabled();
        object enableUnifiedAccount = getValue(types, 1);
        if (!isTrue(enableUnifiedAccount))
        {
            throw new NotSupported ((string)add(this.id, " cancelOrders() supports UTA accounts only")) ;
        }
        object category = null;
        var categoryparametersVariable = this.getBybitType("cancelOrders", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        if (isTrue(isEqual(category, "inverse")))
        {
            throw new NotSupported ((string)add(this.id, " cancelOrders does not allow inverse orders")) ;
        }
        object ordersRequests = new List<object>() {};
        object clientOrderIds = this.safeList2(parameters, "clientOrderIds", "clientOids", new List<object>() {});
        parameters = this.omit(parameters, new List<object>() {"clientOrderIds", "clientOids"});
        for (object i = 0; isLessThan(i, getArrayLength(clientOrderIds)); postFixIncrement(ref i))
        {
            ((IList<object>)ordersRequests).Add(new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
                { "orderLinkId", this.safeString(clientOrderIds, i) },
            });
        }
        for (object i = 0; isLessThan(i, getArrayLength(ids)); postFixIncrement(ref i))
        {
            ((IList<object>)ordersRequests).Add(new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
                { "orderId", this.safeString(ids, i) },
            });
        }
        object request = new Dictionary<string, object>() {
            { "category", category },
            { "request", ordersRequests },
        };
        object response = await this.privatePostV5OrderCancelBatch(this.extend(request, parameters));
        //
        //     {
        //         "retCode": "0",
        //         "retMsg": "OK",
        //         "result": {
        //             "list": [
        //                 {
        //                     "category": "spot",
        //                     "symbol": "BTCUSDT",
        //                     "orderId": "1636282505818800896",
        //                     "orderLinkId": "1636282505818800897"
        //                 },
        //                 {
        //                     "category": "spot",
        //                     "symbol": "BTCUSDT",
        //                     "orderId": "1636282505818800898",
        //                     "orderLinkId": "1636282505818800899"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {
        //             "list": [
        //                 {
        //                     "code": "0",
        //                     "msg": "OK"
        //                 },
        //                 {
        //                     "code": "0",
        //                     "msg": "OK"
        //                 }
        //             ]
        //         },
        //         "time": "1709796158501"
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object row = this.safeList(result, "list", new List<object>() {});
        return this.parseOrders(row, market);
    }

    /**
     * @method
     * @name bybit#cancelAllOrdersAfter
     * @description dead man's switch, cancel all orders after the given timeout
     * @see https://bybit-exchange.github.io/docs/v5/order/dcp
     * @param {number} timeout time in milliseconds
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.product] OPTIONS, DERIVATIVES, SPOT, default is 'DERIVATIVES'
     * @returns {object} the api result
     */
    public async override Task<object> cancelAllOrdersAfter(object timeout, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "timeWindow", this.parseToInt(divide(timeout, 1000)) },
        };
        object type = null;
        var typeparametersVariable = this.handleMarketTypeAndParams("cancelAllOrdersAfter", null, parameters, "swap");
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        object productMap = new Dictionary<string, object>() {
            { "spot", "SPOT" },
            { "swap", "DERIVATIVES" },
            { "option", "OPTIONS" },
        };
        object product = this.safeString(productMap, type, type);
        ((IDictionary<string,object>)request)["product"] = product;
        object response = await this.privatePostV5OrderDisconnectedCancelAll(this.extend(request, parameters));
        //
        // {
        //     "retCode": 0,
        //     "retMsg": "success"
        // }
        //
        return response;
    }

    /**
     * @method
     * @name bybit#cancelOrdersForSymbols
     * @description cancel multiple orders for multiple symbols
     * @see https://bybit-exchange.github.io/docs/v5/order/batch-cancel
     * @param {CancellationRequest[]} orders list of order ids with symbol, example [{"id": "a", "symbol": "BTC/USDT"}, {"id": "b", "symbol": "ETH/USDT"}]
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrdersForSymbols(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object types = await this.isUnifiedEnabled();
        object enableUnifiedAccount = getValue(types, 1);
        if (!isTrue(enableUnifiedAccount))
        {
            throw new NotSupported ((string)add(this.id, " cancelOrdersForSymbols() supports UTA accounts only")) ;
        }
        object ordersRequests = new List<object>() {};
        object category = null;
        for (object i = 0; isLessThan(i, getArrayLength(orders)); postFixIncrement(ref i))
        {
            object order = getValue(orders, i);
            object symbol = this.safeString(order, "symbol");
            object market = this.market(symbol);
            object currentCategory = null;
            var currentCategoryparametersVariable = this.getBybitType("cancelOrders", market, parameters);
            currentCategory = ((IList<object>)currentCategoryparametersVariable)[0];
            parameters = ((IList<object>)currentCategoryparametersVariable)[1];
            if (isTrue(isEqual(currentCategory, "inverse")))
            {
                throw new NotSupported ((string)add(this.id, " cancelOrdersForSymbols does not allow inverse orders")) ;
            }
            if (isTrue(isTrue((!isEqual(category, null))) && isTrue((!isEqual(category, currentCategory)))))
            {
                throw new ExchangeError ((string)add(this.id, " cancelOrdersForSymbols requires all orders to be of the same category (linear, spot or option))")) ;
            }
            category = currentCategory;
            object id = this.safeString(order, "id");
            object clientOrderId = this.safeString(order, "clientOrderId");
            object idKey = "orderId";
            if (isTrue(!isEqual(clientOrderId, null)))
            {
                idKey = "orderLinkId";
            }
            object orderItem = new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
            };
            ((IDictionary<string,object>)orderItem)[(string)idKey] = ((bool) isTrue((isEqual(idKey, "orderId")))) ? id : clientOrderId;
            ((IList<object>)ordersRequests).Add(orderItem);
        }
        object request = new Dictionary<string, object>() {
            { "category", category },
            { "request", ordersRequests },
        };
        object response = await this.privatePostV5OrderCancelBatch(this.extend(request, parameters));
        //
        //     {
        //         "retCode": "0",
        //         "retMsg": "OK",
        //         "result": {
        //             "list": [
        //                 {
        //                     "category": "spot",
        //                     "symbol": "BTCUSDT",
        //                     "orderId": "1636282505818800896",
        //                     "orderLinkId": "1636282505818800897"
        //                 },
        //                 {
        //                     "category": "spot",
        //                     "symbol": "BTCUSDT",
        //                     "orderId": "1636282505818800898",
        //                     "orderLinkId": "1636282505818800899"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {
        //             "list": [
        //                 {
        //                     "code": "0",
        //                     "msg": "OK"
        //                 },
        //                 {
        //                     "code": "0",
        //                     "msg": "OK"
        //                 }
        //             ]
        //         },
        //         "time": "1709796158501"
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object row = this.safeList(result, "list", new List<object>() {});
        return this.parseOrders(row, null);
    }

    /**
     * @method
     * @name bybit#cancelAllOrders
     * @description cancel all open orders
     * @see https://bybit-exchange.github.io/docs/v5/order/cancel-all
     * @param {string} symbol unified market symbol, only orders in the market of this symbol are cancelled when symbol is not undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] true if trigger order
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.baseCoin] Base coin. Supports linear, inverse & option
     * @param {string} [params.settleCoin] Settle coin. Supports linear, inverse & option
     * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        var enableUnifiedMarginenableUnifiedAccountVariable = await this.isUnifiedEnabled();
        var enableUnifiedMargin = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[0];
        var enableUnifiedAccount = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[1];
        object isUnifiedAccount = (isTrue(enableUnifiedMargin) || isTrue(enableUnifiedAccount));
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("cancelAllOrders", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = type;
        if (isTrue(isTrue((isEqual(type, "option"))) && !isTrue(isUnifiedAccount)))
        {
            throw new NotSupported ((string)add(add(add(this.id, " cancelAllOrders() Normal Account not support "), type), " market")) ;
        }
        if (isTrue(isTrue((isEqual(type, "linear"))) || isTrue((isEqual(type, "inverse")))))
        {
            object baseCoin = this.safeString(parameters, "baseCoin");
            if (isTrue(isTrue(isEqual(symbol, null)) && isTrue(isEqual(baseCoin, null))))
            {
                object defaultSettle = this.safeString(this.options, "defaultSettle", "USDT");
                ((IDictionary<string,object>)request)["settleCoin"] = this.safeString(parameters, "settleCoin", defaultSettle);
            }
        }
        object isTrigger = this.safeBool2(parameters, "stop", "trigger", false);
        parameters = this.omit(parameters, new List<object>() {"stop", "trigger"});
        if (isTrue(isTrigger))
        {
            ((IDictionary<string,object>)request)["orderFilter"] = "StopOrder";
        }
        object response = await this.privatePostV5OrderCancelAll(this.extend(request, parameters));
        //
        // linear / inverse / option
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "list": [
        //                 {
        //                     "orderId": "f6a73e1f-39b5-4dee-af21-1460b2e3b27c",
        //                     "orderLinkId": "a001"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672219780463
        //     }
        //
        // spot
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "success": "1"
        //         },
        //         "retExtInfo": {},
        //         "time": 1676962409398
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object orders = this.safeList(result, "list");
        if (!isTrue(((orders is IList<object>) || (orders.GetType().IsGenericType && orders.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            return new List<object> {this.safeOrder(new Dictionary<string, object>() {
    { "info", response },
})};
        }
        return this.parseOrders(orders, market);
    }

    /**
     * @method
     * @name bybit#fetchOrderClassic
     * @description fetches information on an order made by the user *classic accounts only*
     * @see https://bybit-exchange.github.io/docs/v5/order/order-list
     * @param {string} id the order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> fetchOrderClassic(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        if (isTrue(getValue(market, "spot")))
        {
            throw new NotSupported ((string)add(this.id, " fetchOrder() is not supported for spot markets")) ;
        }
        object request = new Dictionary<string, object>() {
            { "orderId", id },
        };
        object result = await this.fetchOrders(symbol, null, null, this.extend(request, parameters));
        object length = getArrayLength(result);
        if (isTrue(isEqual(length, 0)))
        {
            object isTrigger = this.safeBoolN(parameters, new List<object>() {"trigger", "stop"}, false);
            object extra = ((bool) isTrue(isTrigger)) ? "" : " If you are trying to fetch SL/TP conditional order, you might try setting params[\"trigger\"] = true";
            throw new OrderNotFound ((string)add(add(add("Order ", ((object)id).ToString()), " was not found."), extra)) ;
        }
        if (isTrue(isGreaterThan(length, 1)))
        {
            throw new InvalidOrder ((string)add(this.id, " returned more than one order")) ;
        }
        return this.safeValue(result, 0);
    }

    /**
     * @method
     * @name bybit#fetchOrder
     * @description  *classic accounts only/ spot not supported*  fetches information on an order made by the user *classic accounts only*
     * @see https://bybit-exchange.github.io/docs/v5/order/order-list
     * @param {string} id the order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {object} [params.acknowledged] to suppress the warning, set to true
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        var enableUnifiedMarginenableUnifiedAccountVariable = await this.isUnifiedEnabled();
        var enableUnifiedMargin = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[0];
        var enableUnifiedAccount = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[1];
        object isUnifiedAccount = (isTrue(enableUnifiedMargin) || isTrue(enableUnifiedAccount));
        if (!isTrue(isUnifiedAccount))
        {
            return await this.fetchOrderClassic(id, symbol, parameters);
        }
        object acknowledge = false;
        var acknowledgeparametersVariable = this.handleOptionAndParams(parameters, "fetchOrder", "acknowledged");
        acknowledge = ((IList<object>)acknowledgeparametersVariable)[0];
        parameters = ((IList<object>)acknowledgeparametersVariable)[1];
        if (!isTrue(acknowledge))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrder() can only access an order if it is in last 500 orders (of any status) for your account. Set params[\"acknowledged\"] = true to hide this warning. Alternatively, we suggest to use fetchOpenOrder or fetchClosedOrder")) ;
        }
        object market = this.market(symbol);
        object marketType = null;
        var marketTypeparametersVariable = this.getBybitType("fetchOrder", market, parameters);
        marketType = ((IList<object>)marketTypeparametersVariable)[0];
        parameters = ((IList<object>)marketTypeparametersVariable)[1];
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "orderId", id },
            { "category", marketType },
        };
        object isTrigger = null;
        var isTriggerparametersVariable = this.handleParamBool2(parameters, "trigger", "stop", false);
        isTrigger = ((IList<object>)isTriggerparametersVariable)[0];
        parameters = ((IList<object>)isTriggerparametersVariable)[1];
        if (isTrue(isTrigger))
        {
            ((IDictionary<string,object>)request)["orderFilter"] = "StopOrder";
        }
        object response = await this.privateGetV5OrderRealtime(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "nextPageCursor": "1321052653536515584%3A1672217748287%2C1321052653536515584%3A1672217748287",
        //             "category": "spot",
        //             "list": [
        //                 {
        //                     "symbol": "ETHUSDT",
        //                     "orderType": "Limit",
        //                     "orderLinkId": "1672217748277652",
        //                     "orderId": "1321052653536515584",
        //                     "cancelType": "UNKNOWN",
        //                     "avgPrice": "",
        //                     "stopOrderType": "tpslOrder",
        //                     "lastPriceOnCreated": "",
        //                     "orderStatus": "Cancelled",
        //                     "takeProfit": "",
        //                     "cumExecValue": "0",
        //                     "triggerDirection": 0,
        //                     "isLeverage": "0",
        //                     "rejectReason": "",
        //                     "price": "1000",
        //                     "orderIv": "",
        //                     "createdTime": "1672217748287",
        //                     "tpTriggerBy": "",
        //                     "positionIdx": 0,
        //                     "timeInForce": "GTC",
        //                     "leavesValue": "500",
        //                     "updatedTime": "1672217748287",
        //                     "side": "Buy",
        //                     "triggerPrice": "1500",
        //                     "cumExecFee": "0",
        //                     "leavesQty": "0",
        //                     "slTriggerBy": "",
        //                     "closeOnTrigger": false,
        //                     "cumExecQty": "0",
        //                     "reduceOnly": false,
        //                     "qty": "0.5",
        //                     "stopLoss": "",
        //                     "triggerBy": "1192.5"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672219526294
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object innerList = this.safeList(result, "list", new List<object>() {});
        if (isTrue(isEqual(getArrayLength(innerList), 0)))
        {
            object extra = ((bool) isTrue(isTrigger)) ? "" : " If you are trying to fetch SL/TP conditional order, you might try setting params[\"trigger\"] = true";
            throw new OrderNotFound ((string)add(add(add("Order ", ((object)id).ToString()), " was not found."), extra)) ;
        }
        object order = this.safeDict(innerList, 0, new Dictionary<string, object>() {});
        return this.parseOrder(order, market);
    }

    public async override Task<object> fetchOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object res = await this.isUnifiedEnabled();
        /**
         * @method
         * @name bybit#fetchOrders
         * @description *classic accounts only/ spot not supported* fetches information on multiple orders made by the user *classic accounts only/ spot not supported*
         * @see https://bybit-exchange.github.io/docs/v5/order/order-list
         * @param {string} symbol unified market symbol of the market orders were made in
         * @param {int} [since] the earliest time in ms to fetch orders for
         * @param {int} [limit] the maximum number of order structures to retrieve
         * @param {object} [params] extra parameters specific to the exchange API endpoint
         * @param {boolean} [params.trigger] true if trigger order
         * @param {boolean} [params.stop] alias for trigger
         * @param {string} [params.type] market type, ['swap', 'option']
         * @param {string} [params.subType] market subType, ['linear', 'inverse']
         * @param {string} [params.orderFilter] 'Order' or 'StopOrder' or 'tpslOrder'
         * @param {int} [params.until] the latest time in ms to fetch entries for
         * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
         * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
         */
        object enableUnifiedAccount = this.safeBool(res, 1);
        if (isTrue(enableUnifiedAccount))
        {
            throw new NotSupported ((string)add(this.id, " fetchOrders() is not supported after the 5/02 update for UTA accounts, please use fetchOpenOrders, fetchClosedOrders or fetchCanceledOrders")) ;
        }
        return await this.fetchOrdersClassic(symbol, since, limit, parameters);
    }

    /**
     * @method
     * @name bybit#fetchOrdersClassic
     * @description fetches information on multiple orders made by the user *classic accounts only*
     * @see https://bybit-exchange.github.io/docs/v5/order/order-list
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] true if trigger order
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.orderFilter] 'Order' or 'StopOrder' or 'tpslOrder'
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> fetchOrdersClassic(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOrders", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchOrders", symbol, since, limit, parameters, "nextPageCursor", "cursor", null, 50);
        }
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchOrders", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(isEqual(type, "spot")))
        {
            throw new NotSupported ((string)add(this.id, " fetchOrders() is not supported for spot markets")) ;
        }
        ((IDictionary<string,object>)request)["category"] = type;
        object isTrigger = this.safeBoolN(parameters, new List<object>() {"trigger", "stop"}, false);
        parameters = this.omit(parameters, new List<object>() {"trigger", "stop"});
        if (isTrue(isTrigger))
        {
            ((IDictionary<string,object>)request)["orderFilter"] = "StopOrder";
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        object until = this.safeInteger(parameters, "until"); // unified in milliseconds
        object endTime = this.safeInteger(parameters, "endTime", until); // exchange-specific in milliseconds
        parameters = this.omit(parameters, new List<object>() {"endTime", "until"});
        if (isTrue(!isEqual(endTime, null)))
        {
            ((IDictionary<string,object>)request)["endTime"] = endTime;
        }
        object response = await this.privateGetV5OrderHistory(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "nextPageCursor": "03234de9-1332-41eb-b805-4a9f42c136a3%3A1672220109387%2C03234de9-1332-41eb-b805-4a9f42c136a3%3A1672220109387",
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "BTCUSDT",
        //                     "orderType": "Limit",
        //                     "orderLinkId": "test-001",
        //                     "orderId": "03234de9-1332-41eb-b805-4a9f42c136a3",
        //                     "cancelType": "CancelByUser",
        //                     "avgPrice": "0",
        //                     "stopOrderType": "UNKNOWN",
        //                     "lastPriceOnCreated": "16656.5",
        //                     "orderStatus": "Cancelled",
        //                     "takeProfit": "",
        //                     "cumExecValue": "0",
        //                     "triggerDirection": 0,
        //                     "blockTradeId": "",
        //                     "rejectReason": "EC_PerCancelRequest",
        //                     "isLeverage": "",
        //                     "price": "18000",
        //                     "orderIv": "",
        //                     "createdTime": "1672220109387",
        //                     "tpTriggerBy": "UNKNOWN",
        //                     "positionIdx": 0,
        //                     "timeInForce": "GoodTillCancel",
        //                     "leavesValue": "0",
        //                     "updatedTime": "1672220114123",
        //                     "side": "Sell",
        //                     "triggerPrice": "",
        //                     "cumExecFee": "0",
        //                     "slTriggerBy": "UNKNOWN",
        //                     "leavesQty": "0",
        //                     "closeOnTrigger": false,
        //                     "cumExecQty": "0",
        //                     "reduceOnly": false,
        //                     "qty": "0.1",
        //                     "stopLoss": "",
        //                     "triggerBy": "UNKNOWN"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672221263862
        //     }
        //
        object data = this.addPaginationCursorToResult(response);
        return this.parseOrders(data, market, since, limit);
    }

    /**
     * @method
     * @name bybit#fetchClosedOrder
     * @description fetches information on a closed order made by the user
     * @see https://bybit-exchange.github.io/docs/v5/order/order-list
     * @param {string} id order id
     * @param {string} [symbol] unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] set to true for fetching a closed trigger order
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.orderFilter] 'Order' or 'StopOrder' or 'tpslOrder'
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> fetchClosedOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "orderId", id },
        };
        object result = await this.fetchClosedOrders(symbol, null, null, this.extend(request, parameters));
        object length = getArrayLength(result);
        if (isTrue(isEqual(length, 0)))
        {
            object isTrigger = this.safeBoolN(parameters, new List<object>() {"trigger", "stop"}, false);
            object extra = ((bool) isTrue(isTrigger)) ? "" : " If you are trying to fetch SL/TP conditional order, you might try setting params[\"trigger\"] = true";
            throw new OrderNotFound ((string)add(add(add("Order ", ((object)id).ToString()), " was not found."), extra)) ;
        }
        if (isTrue(isGreaterThan(length, 1)))
        {
            throw new InvalidOrder ((string)add(this.id, " returned more than one order")) ;
        }
        return this.safeValue(result, 0);
    }

    /**
     * @method
     * @name bybit#fetchOpenOrder
     * @description fetches information on an open order made by the user
     * @see https://bybit-exchange.github.io/docs/v5/order/open-order
     * @param {string} id order id
     * @param {string} [symbol] unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] set to true for fetching an open trigger order
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.baseCoin] Base coin. Supports linear, inverse & option
     * @param {string} [params.settleCoin] Settle coin. Supports linear, inverse & option
     * @param {string} [params.orderFilter] 'Order' or 'StopOrder' or 'tpslOrder'
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> fetchOpenOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "orderId", id },
        };
        object result = await this.fetchOpenOrders(symbol, null, null, this.extend(request, parameters));
        object length = getArrayLength(result);
        if (isTrue(isEqual(length, 0)))
        {
            object isTrigger = this.safeBoolN(parameters, new List<object>() {"trigger", "stop"}, false);
            object extra = ((bool) isTrue(isTrigger)) ? "" : " If you are trying to fetch SL/TP conditional order, you might try setting params[\"trigger\"] = true";
            throw new OrderNotFound ((string)add(add(add("Order ", ((object)id).ToString()), " was not found."), extra)) ;
        }
        if (isTrue(isGreaterThan(length, 1)))
        {
            throw new InvalidOrder ((string)add(this.id, " returned more than one order")) ;
        }
        return this.safeValue(result, 0);
    }

    /**
     * @method
     * @name bybit#fetchCanceledAndClosedOrders
     * @description fetches information on multiple canceled and closed orders made by the user
     * @see https://bybit-exchange.github.io/docs/v5/order/order-list
     * @param {string} [symbol] unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] set to true for fetching trigger orders
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.orderFilter] 'Order' or 'StopOrder' or 'tpslOrder'
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [available parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchCanceledAndClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchCanceledAndClosedOrders", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchCanceledAndClosedOrders", symbol, since, limit, parameters, "nextPageCursor", "cursor", null, 50);
        }
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchCanceledAndClosedOrders", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = type;
        object isTrigger = this.safeBoolN(parameters, new List<object>() {"trigger", "stop"}, false);
        parameters = this.omit(parameters, new List<object>() {"trigger", "stop"});
        if (isTrue(isTrigger))
        {
            ((IDictionary<string,object>)request)["orderFilter"] = "StopOrder";
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        object until = this.safeInteger(parameters, "until"); // unified in milliseconds
        object endTime = this.safeInteger(parameters, "endTime", until); // exchange-specific in milliseconds
        parameters = this.omit(parameters, new List<object>() {"endTime", "until"});
        if (isTrue(!isEqual(endTime, null)))
        {
            ((IDictionary<string,object>)request)["endTime"] = endTime;
        }
        object response = await this.privateGetV5OrderHistory(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "nextPageCursor": "03234de9-1332-41eb-b805-4a9f42c136a3%3A1672220109387%2C03234de9-1332-41eb-b805-4a9f42c136a3%3A1672220109387",
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "BTCUSDT",
        //                     "orderType": "Limit",
        //                     "orderLinkId": "test-001",
        //                     "orderId": "03234de9-1332-41eb-b805-4a9f42c136a3",
        //                     "cancelType": "CancelByUser",
        //                     "avgPrice": "0",
        //                     "stopOrderType": "UNKNOWN",
        //                     "lastPriceOnCreated": "16656.5",
        //                     "orderStatus": "Cancelled",
        //                     "takeProfit": "",
        //                     "cumExecValue": "0",
        //                     "triggerDirection": 0,
        //                     "blockTradeId": "",
        //                     "rejectReason": "EC_PerCancelRequest",
        //                     "isLeverage": "",
        //                     "price": "18000",
        //                     "orderIv": "",
        //                     "createdTime": "1672220109387",
        //                     "tpTriggerBy": "UNKNOWN",
        //                     "positionIdx": 0,
        //                     "timeInForce": "GoodTillCancel",
        //                     "leavesValue": "0",
        //                     "updatedTime": "1672220114123",
        //                     "side": "Sell",
        //                     "triggerPrice": "",
        //                     "cumExecFee": "0",
        //                     "slTriggerBy": "UNKNOWN",
        //                     "leavesQty": "0",
        //                     "closeOnTrigger": false,
        //                     "cumExecQty": "0",
        //                     "reduceOnly": false,
        //                     "qty": "0.1",
        //                     "stopLoss": "",
        //                     "triggerBy": "UNKNOWN"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672221263862
        //     }
        //
        object data = this.addPaginationCursorToResult(response);
        return this.parseOrders(data, market, since, limit);
    }

    /**
     * @method
     * @name bybit#fetchClosedOrders
     * @description fetches information on multiple closed orders made by the user
     * @see https://bybit-exchange.github.io/docs/v5/order/order-list
     * @param {string} [symbol] unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] set to true for fetching closed trigger orders
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.orderFilter] 'Order' or 'StopOrder' or 'tpslOrder'
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [available parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "orderStatus", "Filled" },
        };
        return await this.fetchCanceledAndClosedOrders(symbol, since, limit, this.extend(request, parameters));
    }

    /**
     * @method
     * @name bybit#fetchCanceledOrders
     * @description fetches information on multiple canceled orders made by the user
     * @see https://bybit-exchange.github.io/docs/v5/order/order-list
     * @param {string} [symbol] unified market symbol of the market orders were made in
     * @param {int} [since] timestamp in ms of the earliest order, default is undefined
     * @param {int} [limit] max number of orders to return, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] true if trigger order
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.orderFilter] 'Order' or 'StopOrder' or 'tpslOrder'
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [available parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {object} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> fetchCanceledOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "orderStatus", "Cancelled" },
        };
        return await this.fetchCanceledAndClosedOrders(symbol, since, limit, this.extend(request, parameters));
    }

    /**
     * @method
     * @name bybit#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://bybit-exchange.github.io/docs/v5/order/open-order
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of open orders structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.trigger] set to true for fetching open trigger orders
     * @param {boolean} [params.stop] alias for trigger
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.baseCoin] Base coin. Supports linear, inverse & option
     * @param {string} [params.settleCoin] Settle coin. Supports linear, inverse & option
     * @param {string} [params.orderFilter] 'Order' or 'StopOrder' or 'tpslOrder'
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOpenOrders", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchOpenOrders", symbol, since, limit, parameters, "nextPageCursor", "cursor", null, 50);
        }
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchOpenOrders", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(isTrue(isEqual(type, "linear")) || isTrue(isEqual(type, "inverse"))))
        {
            object baseCoin = this.safeString(parameters, "baseCoin");
            if (isTrue(isTrue(isEqual(symbol, null)) && isTrue(isEqual(baseCoin, null))))
            {
                object defaultSettle = this.safeString(this.options, "defaultSettle", "USDT");
                object settleCoin = this.safeString(parameters, "settleCoin", defaultSettle);
                ((IDictionary<string,object>)request)["settleCoin"] = settleCoin;
            }
        }
        ((IDictionary<string,object>)request)["category"] = type;
        object isTrigger = this.safeBool2(parameters, "stop", "trigger", false);
        parameters = this.omit(parameters, new List<object>() {"stop", "trigger"});
        if (isTrue(isTrigger))
        {
            ((IDictionary<string,object>)request)["orderFilter"] = "StopOrder";
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.privateGetV5OrderRealtime(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "nextPageCursor": "1321052653536515584%3A1672217748287%2C1321052653536515584%3A1672217748287",
        //             "category": "spot",
        //             "list": [
        //                 {
        //                     "symbol": "ETHUSDT",
        //                     "orderType": "Limit",
        //                     "orderLinkId": "1672217748277652",
        //                     "orderId": "1321052653536515584",
        //                     "cancelType": "UNKNOWN",
        //                     "avgPrice": "",
        //                     "stopOrderType": "tpslOrder",
        //                     "lastPriceOnCreated": "",
        //                     "orderStatus": "Cancelled",
        //                     "takeProfit": "",
        //                     "cumExecValue": "0",
        //                     "triggerDirection": 0,
        //                     "isLeverage": "0",
        //                     "rejectReason": "",
        //                     "price": "1000",
        //                     "orderIv": "",
        //                     "createdTime": "1672217748287",
        //                     "tpTriggerBy": "",
        //                     "positionIdx": 0,
        //                     "timeInForce": "GTC",
        //                     "leavesValue": "500",
        //                     "updatedTime": "1672217748287",
        //                     "side": "Buy",
        //                     "triggerPrice": "1500",
        //                     "cumExecFee": "0",
        //                     "leavesQty": "0",
        //                     "slTriggerBy": "",
        //                     "closeOnTrigger": false,
        //                     "cumExecQty": "0",
        //                     "reduceOnly": false,
        //                     "qty": "0.5",
        //                     "stopLoss": "",
        //                     "triggerBy": "1192.5"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672219526294
        //     }
        //
        object data = this.addPaginationCursorToResult(response);
        return this.parseOrders(data, market, since, limit);
    }

    /**
     * @method
     * @name bybit#fetchOrderTrades
     * @description fetch all the trades made from a single order
     * @see https://bybit-exchange.github.io/docs/v5/position/execution
     * @param {string} id order id
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchOrderTrades(object id, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object request = new Dictionary<string, object>() {};
        object clientOrderId = this.safeString2(parameters, "clientOrderId", "orderLinkId");
        if (isTrue(!isEqual(clientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["orderLinkId"] = clientOrderId;
        } else
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        }
        parameters = this.omit(parameters, new List<object>() {"clientOrderId", "orderLinkId"});
        return await this.fetchMyTrades(symbol, since, limit, this.extend(request, parameters));
    }

    /**
     * @method
     * @name bybit#fetchMyTrades
     * @description fetch all trades made by the user
     * @see https://bybit-exchange.github.io/docs/api-explorer/v5/position/execution
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchMyTrades", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchMyTrades", symbol, since, limit, parameters, "nextPageCursor", "cursor", null, 100);
        }
        object request = new Dictionary<string, object>() {
            { "execType", "Trade" },
        };
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchMyTrades", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = type;
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        var requestparametersVariable = this.handleUntilOption("endTime", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.privateGetV5ExecutionList(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "nextPageCursor": "132766%3A2%2C132766%3A2",
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "ETHPERP",
        //                     "orderType": "Market",
        //                     "underlyingPrice": "",
        //                     "orderLinkId": "",
        //                     "side": "Buy",
        //                     "indexPrice": "",
        //                     "orderId": "8c065341-7b52-4ca9-ac2c-37e31ac55c94",
        //                     "stopOrderType": "UNKNOWN",
        //                     "leavesQty": "0",
        //                     "execTime": "1672282722429",
        //                     "isMaker": false,
        //                     "execFee": "0.071409",
        //                     "feeRate": "0.0006",
        //                     "execId": "e0cbe81d-0f18-5866-9415-cf319b5dab3b",
        //                     "tradeIv": "",
        //                     "blockTradeId": "",
        //                     "markPrice": "1183.54",
        //                     "execPrice": "1190.15",
        //                     "markIv": "",
        //                     "orderQty": "0.1",
        //                     "orderPrice": "1236.9",
        //                     "execValue": "119.015",
        //                     "execType": "Trade",
        //                     "execQty": "0.1"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672283754510
        //     }
        //
        object trades = this.addPaginationCursorToResult(response);
        return this.parseTrades(trades, market, since, limit);
    }

    public override object parseDepositAddress(object depositAddress, object currency = null)
    {
        //
        //     {
        //         "chainType": "ERC20",
        //         "addressDeposit": "******************************************",
        //         "tagDeposit": '',
        //         "chain": "ETH"
        //     }
        //
        object address = this.safeString(depositAddress, "addressDeposit");
        object tag = this.safeString(depositAddress, "tagDeposit");
        object code = this.safeString(currency, "code");
        this.checkAddress(address);
        return new Dictionary<string, object>() {
            { "info", depositAddress },
            { "currency", code },
            { "network", this.networkIdToCode(this.safeString(depositAddress, "chain"), code) },
            { "address", address },
            { "tag", tag },
        };
    }

    /**
     * @method
     * @name bybit#fetchDepositAddressesByNetwork
     * @description fetch a dictionary of addresses for a currency, indexed by network
     * @see https://bybit-exchange.github.io/docs/v5/asset/master-deposit-addr
     * @param {string} code unified currency code of the currency for the deposit address
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [address structures]{@link https://docs.ccxt.com/#/?id=address-structure} indexed by the network
     */
    public async override Task<object> fetchDepositAddressesByNetwork(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
        };
        object networkCode = null;
        var networkCodeparametersVariable = this.handleNetworkCodeAndParams(parameters);
        networkCode = ((IList<object>)networkCodeparametersVariable)[0];
        parameters = ((IList<object>)networkCodeparametersVariable)[1];
        if (isTrue(!isEqual(networkCode, null)))
        {
            ((IDictionary<string,object>)request)["chainType"] = this.networkCodeToId(networkCode, code);
        }
        object response = await this.privateGetV5AssetDepositQueryAddress(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "coin": "USDT",
        //             "chains": [
        //                 {
        //                     "chainType": "ERC20",
        //                     "addressDeposit": "******************************************",
        //                     "tagDeposit": "",
        //                     "chain": "ETH"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672192792860
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object chains = this.safeList(result, "chains", new List<object>() {});
        object coin = this.safeString(result, "coin");
        currency = this.currency(coin);
        object parsed = this.parseDepositAddresses(chains, new List<object>() {getValue(currency, "code")}, false, new Dictionary<string, object>() {
            { "currency", getValue(currency, "code") },
        });
        return this.indexBy(parsed, "network");
    }

    /**
     * @method
     * @name bybit#fetchDepositAddress
     * @description fetch the deposit address for a currency associated with this account
     * @see https://bybit-exchange.github.io/docs/v5/asset/master-deposit-addr
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> fetchDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        var networkCodeparamsOmitedVariable = this.handleNetworkCodeAndParams(parameters);
        var networkCode = ((IList<object>) networkCodeparamsOmitedVariable)[0];
        var paramsOmited = ((IList<object>) networkCodeparamsOmitedVariable)[1];
        object indexedAddresses = await this.fetchDepositAddressesByNetwork(code, paramsOmited);
        object selectedNetworkCode = this.selectNetworkCodeFromUnifiedNetworks(getValue(currency, "code"), networkCode, indexedAddresses);
        return getValue(indexedAddresses, selectedNetworkCode);
    }

    /**
     * @method
     * @name bybit#fetchDeposits
     * @description fetch all deposits made to an account
     * @see https://bybit-exchange.github.io/docs/v5/asset/deposit-record
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for, default = 30 days before the current time
     * @param {int} [limit] the maximum number of deposits structures to retrieve, default = 50, max = 50
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch deposits for, default = 30 days after since
     * EXCHANGE SPECIFIC PARAMETERS
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @param {string} [params.cursor] used for pagination
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDeposits(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchDeposits", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchDeposits", code, since, limit, parameters, "nextPageCursor", "cursor", null, 50);
        }
        object request = new Dictionary<string, object>() {};
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["coin"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        var requestparametersVariable = this.handleUntilOption("endTime", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.privateGetV5AssetDepositQueryRecord(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "rows": [
        //                 {
        //                     "coin": "USDT",
        //                     "chain": "ETH",
        //                     "amount": "10000",
        //                     "txID": "skip-notification-scene-test-amount-202212270944-533285-USDT",
        //                     "status": 3,
        //                     "toAddress": "test-amount-address",
        //                     "tag": "",
        //                     "depositFee": "",
        //                     "successAt": "1672134274000",
        //                     "confirmations": "10000",
        //                     "txIndex": "",
        //                     "blockHash": ""
        //                 }
        //             ],
        //             "nextPageCursor": "eyJtaW5JRCI6MTA0NjA0MywibWF4SUQiOjEwNDYwNDN9"
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object data = this.addPaginationCursorToResult(response);
        return this.parseTransactions(data, currency, since, limit);
    }

    /**
     * @method
     * @name bybit#fetchWithdrawals
     * @description fetch all withdrawals made from an account
     * @see https://bybit-exchange.github.io/docs/v5/asset/withdraw-record
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch withdrawals for
     * @param {int} [limit] the maximum number of withdrawals structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchWithdrawals", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchWithdrawals", code, since, limit, parameters, "nextPageCursor", "cursor", null, 50);
        }
        object request = new Dictionary<string, object>() {};
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["coin"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        var requestparametersVariable = this.handleUntilOption("endTime", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.privateGetV5AssetWithdrawQueryRecord(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "rows": [
        //                 {
        //                     "coin": "USDT",
        //                     "chain": "ETH",
        //                     "amount": "77",
        //                     "txID": "",
        //                     "status": "SecurityCheck",
        //                     "toAddress": "******************************************",
        //                     "tag": "",
        //                     "withdrawFee": "10",
        //                     "createTime": "1670922217000",
        //                     "updateTime": "1670922217000",
        //                     "withdrawId": "9976",
        //                     "withdrawType": 0
        //                 },
        //                 {
        //                     "coin": "USDT",
        //                     "chain": "ETH",
        //                     "amount": "26",
        //                     "txID": "",
        //                     "status": "success",
        //                     "toAddress": "<EMAIL>",
        //                     "tag": "",
        //                     "withdrawFee": "0",
        //                     "createTime": "1669711121000",
        //                     "updateTime": "1669711380000",
        //                     "withdrawId": "9801",
        //                     "withdrawType": 1
        //                 }
        //             ],
        //             "nextPageCursor": "eyJtaW5JRCI6OTgwMSwibWF4SUQiOjk5NzZ9"
        //         },
        //         "retExtInfo": {},
        //         "time": 1672194949928
        //     }
        //
        object data = this.addPaginationCursorToResult(response);
        return this.parseTransactions(data, currency, since, limit);
    }

    public virtual object parseTransactionStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "0", "unknown" },
            { "1", "pending" },
            { "2", "processing" },
            { "3", "ok" },
            { "4", "fail" },
            { "SecurityCheck", "pending" },
            { "Pending", "pending" },
            { "success", "ok" },
            { "CancelByUser", "canceled" },
            { "Reject", "rejected" },
            { "Fail", "failed" },
            { "BlockchainConfirmed", "ok" },
        };
        return this.safeString(statuses, status, status);
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        // fetchWithdrawals
        //
        //     {
        //         "coin": "USDT",
        //         "chain": "TRX",
        //         "amount": "12.34",
        //         "txID": "de5ea0a2f2e59dc9a714837dd3ddc6d5e151b56ec5d786d351c4f52336f80d3c",
        //         "status": "success",
        //         "toAddress": "TQdmFKUoe1Lk2iwZuwRJEHJreTUBoN3BAw",
        //         "tag": "",
        //         "withdrawFee": "0.5",
        //         "createTime": "1665144183000",
        //         "updateTime": "1665144256000",
        //         "withdrawId": "8839035"
        //     }
        //
        // fetchDeposits
        //
        //     {
        //         "coin": "USDT",
        //         "chain": "TRX",
        //         "amount": "44",
        //         "txID": "0b038ea12fa1575e2d66693db3c346b700d4b28347afc39f80321cf089acc960",
        //         "status": "3",
        //         "toAddress": "TC6NCAC5WSVCCiaD3kWZXyW91ZKKhLm53b",
        //         "tag": "",
        //         "depositFee": "",
        //         "successAt": "1665142507000",
        //         "confirmations": "100",
        //         "txIndex": "0",
        //         "blockHash": "0000000002ac3b1064aee94bca1bd0b58c4c09c65813b084b87a2063d961129e"
        //     }
        //
        // withdraw
        //
        //     {
        //         "id": "9377266"
        //     }
        //
        object currencyId = this.safeString(transaction, "coin");
        object code = this.safeCurrencyCode(currencyId, currency);
        object timestamp = this.safeInteger2(transaction, "createTime", "successAt");
        object updated = this.safeInteger(transaction, "updateTime");
        object status = this.parseTransactionStatus(this.safeString(transaction, "status"));
        object feeCost = this.safeNumber2(transaction, "depositFee", "withdrawFee");
        object type = ((bool) isTrue((inOp(transaction, "depositFee")))) ? "deposit" : "withdrawal";
        object fee = null;
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "cost", feeCost },
                { "currency", code },
            };
        }
        object toAddress = this.safeString(transaction, "toAddress");
        return new Dictionary<string, object>() {
            { "info", transaction },
            { "id", this.safeString2(transaction, "id", "withdrawId") },
            { "txid", this.safeString(transaction, "txID") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "network", this.networkIdToCode(this.safeString(transaction, "chain")) },
            { "address", null },
            { "addressTo", toAddress },
            { "addressFrom", null },
            { "tag", this.safeString(transaction, "tag") },
            { "tagTo", null },
            { "tagFrom", null },
            { "type", type },
            { "amount", this.safeNumber(transaction, "amount") },
            { "currency", code },
            { "status", status },
            { "updated", updated },
            { "fee", fee },
            { "internal", null },
            { "comment", null },
        };
    }

    /**
     * @method
     * @name bybit#fetchLedger
     * @description fetch the history of changes, actions done by the user or operations that altered the balance of the user
     * @see https://bybit-exchange.github.io/docs/v5/account/transaction-log
     * @see https://bybit-exchange.github.io/docs/v5/account/contract-transaction-log
     * @param {string} [code] unified currency code, default is undefined
     * @param {int} [since] timestamp in ms of the earliest ledger entry, default is undefined
     * @param {int} [limit] max number of ledger entries to return, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [available parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @param {string} [params.subType] if inverse will use v5/account/contract-transaction-log
     * @returns {object} a [ledger structure]{@link https://docs.ccxt.com/#/?id=ledger}
     */
    public async override Task<object> fetchLedger(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchLedger", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchLedger", code, since, limit, parameters, "nextPageCursor", "cursor", null, 50);
        }
        object request = new Dictionary<string, object>() {};
        object enableUnified = await this.isUnifiedEnabled();
        object currency = null;
        object currencyKey = "coin";
        if (isTrue(getValue(enableUnified, 1)))
        {
            currencyKey = "currency";
            if (isTrue(!isEqual(since, null)))
            {
                ((IDictionary<string,object>)request)["startTime"] = since;
            }
        } else
        {
            if (isTrue(!isEqual(since, null)))
            {
                ((IDictionary<string,object>)request)["start_date"] = this.yyyymmdd(since);
            }
        }
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)[(string)currencyKey] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object subType = null;
        var subTypeparametersVariable = this.handleSubTypeAndParams("fetchLedger", null, parameters);
        subType = ((IList<object>)subTypeparametersVariable)[0];
        parameters = ((IList<object>)subTypeparametersVariable)[1];
        object response = null;
        if (isTrue(getValue(enableUnified, 1)))
        {
            object unifiedMarginStatus = this.safeInteger(this.options, "unifiedMarginStatus", 5); // 3/4 uta 1.0, 5/6 uta 2.0
            if (isTrue(isTrue(isEqual(subType, "inverse")) && isTrue((isLessThan(unifiedMarginStatus, 5)))))
            {
                response = await this.privateGetV5AccountContractTransactionLog(this.extend(request, parameters));
            } else
            {
                response = await this.privateGetV5AccountTransactionLog(this.extend(request, parameters));
            }
        } else
        {
            response = await this.privateGetV5AccountContractTransactionLog(this.extend(request, parameters));
        }
        //
        //     {
        //         "ret_code": 0,
        //         "ret_msg": "ok",
        //         "ext_code": "",
        //         "result": {
        //             "data": [
        //                 {
        //                     "id": 234467,
        //                     "user_id": 1,
        //                     "coin": "BTC",
        //                     "wallet_id": 27913,
        //                     "type": "Realized P&L",
        //                     "amount": "-0.********",
        //                     "tx_id": "",
        //                     "address": "BTCUSD",
        //                     "wallet_balance": "0.********",
        //                     "exec_time": "2019-12-09T00:00:25.000Z",
        //                     "cross_seq": 0
        //                 }
        //             ]
        //         },
        //         "ext_info": null,
        //         "time_now": "1577481867.115552",
        //         "rate_limit_status": 119,
        //         "rate_limit_reset_ms": 1577481867122,
        //         "rate_limit": 120
        //     }
        //
        // v5 transaction log
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "nextPageCursor": "21963%3A1%2C14954%3A1",
        //             "list": [
        //                 {
        //                     "symbol": "XRPUSDT",
        //                     "side": "Buy",
        //                     "funding": "-0.003676",
        //                     "orderLinkId": "",
        //                     "orderId": "1672128000-8-592324-1-2",
        //                     "fee": "0.00000000",
        //                     "change": "-0.003676",
        //                     "cashFlow": "0",
        //                     "transactionTime": "1672128000000",
        //                     "type": "SETTLEMENT",
        //                     "feeRate": "0.0001",
        //                     "size": "100",
        //                     "qty": "100",
        //                     "cashBalance": "5086.55825002",
        //                     "currency": "USDT",
        //                     "category": "linear",
        //                     "tradePrice": "0.3676",
        //                     "tradeId": "534c0003-4bf7-486f-aa02-78cee36825e4"
        //                 },
        //                 {
        //                     "symbol": "XRPUSDT",
        //                     "side": "Buy",
        //                     "funding": "",
        //                     "orderLinkId": "linear-order",
        //                     "orderId": "592b7e41-78fd-42e2-9aa3-91e1835ef3e1",
        //                     "fee": "0.01908720",
        //                     "change": "-0.0190872",
        //                     "cashFlow": "0",
        //                     "transactionTime": "1672121182224",
        //                     "type": "TRADE",
        //                     "feeRate": "0.0006",
        //                     "size": "100",
        //                     "qty": "88",
        //                     "cashBalance": "5086.56192602",
        //                     "currency": "USDT",
        //                     "category": "linear",
        //                     "tradePrice": "0.3615",
        //                     "tradeId": "5184f079-88ec-54c7-8774-5173cafd2b4e"
        //                 },
        //                 {
        //                     "symbol": "XRPUSDT",
        //                     "side": "Buy",
        //                     "funding": "",
        //                     "orderLinkId": "linear-order",
        //                     "orderId": "592b7e41-78fd-42e2-9aa3-91e1835ef3e1",
        //                     "fee": "0.00260280",
        //                     "change": "-0.0026028",
        //                     "cashFlow": "0",
        //                     "transactionTime": "1672121182224",
        //                     "type": "TRADE",
        //                     "feeRate": "0.0006",
        //                     "size": "12",
        //                     "qty": "12",
        //                     "cashBalance": "5086.58101322",
        //                     "currency": "USDT",
        //                     "category": "linear",
        //                     "tradePrice": "0.3615",
        //                     "tradeId": "8569c10f-5061-5891-81c4-a54929847eb3"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672132481405
        //     }
        //
        object data = this.addPaginationCursorToResult(response);
        return this.parseLedger(data, currency, since, limit);
    }

    public override object parseLedgerEntry(object item, object currency = null)
    {
        //
        //     {
        //         "id": 234467,
        //         "user_id": 1,
        //         "coin": "BTC",
        //         "wallet_id": 27913,
        //         "type": "Realized P&L",
        //         "amount": "-0.********",
        //         "tx_id": "",
        //         "address": "BTCUSD",
        //         "wallet_balance": "0.********",
        //         "exec_time": "2019-12-09T00:00:25.000Z",
        //         "cross_seq": 0
        //     }
        //
        //     {
        //         "symbol": "XRPUSDT",
        //         "side": "Buy",
        //         "funding": "",
        //         "orderLinkId": "linear-order",
        //         "orderId": "592b7e41-78fd-42e2-9aa3-91e1835ef3e1",
        //         "fee": "0.00260280",
        //         "change": "-0.0026028",
        //         "cashFlow": "0",
        //         "transactionTime": "1672121182224",
        //         "type": "TRADE",
        //         "feeRate": "0.0006",
        //         "size": "12",
        //         "qty": "12",
        //         "cashBalance": "5086.58101322",
        //         "currency": "USDT",
        //         "category": "linear",
        //         "tradePrice": "0.3615",
        //         "tradeId": "8569c10f-5061-5891-81c4-a54929847eb3"
        //     }
        //
        object currencyId = this.safeString2(item, "coin", "currency");
        object code = this.safeCurrencyCode(currencyId, currency);
        currency = this.safeCurrency(currencyId, currency);
        object amountString = this.safeString2(item, "amount", "change");
        object afterString = this.safeString2(item, "wallet_balance", "cashBalance");
        object direction = ((bool) isTrue(Precise.stringLt(amountString, "0"))) ? "out" : "in";
        object before = null;
        object after = null;
        object amount = null;
        if (isTrue(isTrue(!isEqual(afterString, null)) && isTrue(!isEqual(amountString, null))))
        {
            object difference = ((bool) isTrue((isEqual(direction, "out")))) ? amountString : Precise.stringNeg(amountString);
            before = this.parseToNumeric(Precise.stringAdd(afterString, difference));
            after = this.parseToNumeric(afterString);
            amount = this.parseToNumeric(Precise.stringAbs(amountString));
        }
        object timestamp = this.parse8601(this.safeString(item, "exec_time"));
        if (isTrue(isEqual(timestamp, null)))
        {
            timestamp = this.safeInteger(item, "transactionTime");
        }
        return this.safeLedgerEntry(new Dictionary<string, object>() {
            { "info", item },
            { "id", this.safeString(item, "id") },
            { "direction", direction },
            { "account", this.safeString(item, "wallet_id") },
            { "referenceId", this.safeString(item, "tx_id") },
            { "referenceAccount", null },
            { "type", this.parseLedgerEntryType(this.safeString(item, "type")) },
            { "currency", code },
            { "amount", amount },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "before", before },
            { "after", after },
            { "status", "ok" },
            { "fee", new Dictionary<string, object>() {
                { "currency", code },
                { "cost", this.safeNumber(item, "fee") },
            } },
        }, currency);
    }

    public virtual object parseLedgerEntryType(object type)
    {
        object types = new Dictionary<string, object>() {
            { "Deposit", "transaction" },
            { "Withdraw", "transaction" },
            { "RealisedPNL", "trade" },
            { "Commission", "fee" },
            { "Refund", "cashback" },
            { "Prize", "prize" },
            { "ExchangeOrderWithdraw", "transaction" },
            { "ExchangeOrderDeposit", "transaction" },
            { "TRANSFER_IN", "transaction" },
            { "TRANSFER_OUT", "transaction" },
            { "TRADE", "trade" },
            { "SETTLEMENT", "trade" },
            { "DELIVERY", "trade" },
            { "LIQUIDATION", "trade" },
            { "BONUS", "Prize" },
            { "FEE_REFUND", "cashback" },
            { "INTEREST", "transaction" },
            { "CURRENCY_BUY", "trade" },
            { "CURRENCY_SELL", "trade" },
        };
        return this.safeString(types, type, type);
    }

    /**
     * @method
     * @name bybit#withdraw
     * @description make a withdrawal
     * @see https://bybit-exchange.github.io/docs/v5/asset/withdraw
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address the address to withdraw to
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        var tagparametersVariable = this.handleWithdrawTagAndParams(tag, parameters);
        tag = ((IList<object>)tagparametersVariable)[0];
        parameters = ((IList<object>)tagparametersVariable)[1];
        object accountType = null;
        object accounts = await this.isUnifiedEnabled();
        object isUta = getValue(accounts, 1);
        var accountTypeparametersVariable = this.handleOptionAndParams(parameters, "withdraw", "accountType", "SPOT");
        accountType = ((IList<object>)accountTypeparametersVariable)[0];
        parameters = ((IList<object>)accountTypeparametersVariable)[1];
        if (isTrue(isUta))
        {
            accountType = "UTA";
        }
        await this.loadMarkets();
        this.checkAddress(address);
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
            { "amount", this.numberToString(amount) },
            { "address", address },
            { "timestamp", this.milliseconds() },
            { "accountType", accountType },
        };
        if (isTrue(!isEqual(tag, null)))
        {
            ((IDictionary<string,object>)request)["tag"] = tag;
        }
        var networkCodequeryVariable = this.handleNetworkCodeAndParams(parameters);
        var networkCode = ((IList<object>) networkCodequeryVariable)[0];
        var query = ((IList<object>) networkCodequeryVariable)[1];
        object networkId = this.networkCodeToId(networkCode);
        if (isTrue(!isEqual(networkId, null)))
        {
            ((IDictionary<string,object>)request)["chain"] = ((string)networkId).ToUpper();
        }
        object response = await this.privatePostV5AssetWithdrawCreate(this.extend(request, query));
        //
        //    {
        //         "retCode": "0",
        //         "retMsg": "success",
        //         "result": {
        //             "id": "9377266"
        //         },
        //         "retExtInfo": {},
        //         "time": "*************"
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseTransaction(result, currency);
    }

    /**
     * @method
     * @name bybit#fetchPosition
     * @description fetch data on a single open contract trade position
     * @see https://bybit-exchange.github.io/docs/v5/position
     * @param {string} symbol unified market symbol of the market the position is held in, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPosition(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchPosition() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = null;
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchPosition", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = type;
        response = await this.privateGetV5PositionList(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "nextPageCursor": "updateAt%3D*************",
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "XRPUSDT",
        //                     "leverage": "10",
        //                     "avgPrice": "0.3615",
        //                     "liqPrice": "0.0001",
        //                     "riskLimitValue": "200000",
        //                     "takeProfit": "",
        //                     "positionValue": "36.15",
        //                     "tpslMode": "Full",
        //                     "riskId": 41,
        //                     "trailingStop": "0",
        //                     "unrealisedPnl": "-1.83",
        //                     "markPrice": "0.3432",
        //                     "cumRealisedPnl": "0.********",
        //                     "positionMM": "0.381021",
        //                     "createdTime": "*************",
        //                     "positionIdx": 0,
        //                     "positionIM": "3.634521",
        //                     "updatedTime": "*************",
        //                     "side": "Buy",
        //                     "bustPrice": "",
        //                     "size": "100",
        //                     "positionStatus": "Normal",
        //                     "stopLoss": "",
        //                     "tradeMode": 0
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672280219169
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object positions = this.safeList2(result, "list", "dataList", new List<object>() {});
        object timestamp = this.safeInteger(response, "time");
        object first = this.safeDict(positions, 0, new Dictionary<string, object>() {});
        object position = this.parsePosition(first, market);
        ((IDictionary<string,object>)position)["timestamp"] = timestamp;
        ((IDictionary<string,object>)position)["datetime"] = this.iso8601(timestamp);
        return position;
    }

    /**
     * @method
     * @name bybit#fetchPositions
     * @description fetch all open positions
     * @see https://bybit-exchange.github.io/docs/v5/position
     * @param {string[]} symbols list of unified market symbols
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {string} [params.baseCoin] Base coin. Supports linear, inverse & option
     * @param {string} [params.settleCoin] Settle coin. Supports linear, inverse & option
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times
     * @returns {object[]} a list of [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPositions(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchPositions", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchPositions", ((object)symbols), null, null, parameters, "nextPageCursor", "cursor", null, 200);
        }
        object symbol = null;
        if (isTrue(isTrue((!isEqual(symbols, null))) && isTrue(((symbols is IList<object>) || (symbols.GetType().IsGenericType && symbols.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>)))))))
        {
            object symbolsLength = getArrayLength(symbols);
            if (isTrue(isGreaterThan(symbolsLength, 1)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " fetchPositions() does not accept an array with more than one symbol")) ;
            } else if (isTrue(isEqual(symbolsLength, 1)))
            {
                symbol = getValue(symbols, 0);
            }
            symbols = this.marketSymbols(symbols);
        } else if (isTrue(!isEqual(symbols, null)))
        {
            symbol = symbols;
            symbols = new List<object> {this.symbol(symbol)};
        }
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            symbol = getValue(market, "symbol");
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchPositions", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(isTrue(isEqual(type, "linear")) || isTrue(isEqual(type, "inverse"))))
        {
            object baseCoin = this.safeString(parameters, "baseCoin");
            if (isTrue(isEqual(type, "linear")))
            {
                if (isTrue(isTrue(isEqual(symbol, null)) && isTrue(isEqual(baseCoin, null))))
                {
                    object defaultSettle = this.safeString(this.options, "defaultSettle", "USDT");
                    object settleCoin = this.safeString(parameters, "settleCoin", defaultSettle);
                    ((IDictionary<string,object>)request)["settleCoin"] = settleCoin;
                }
            } else
            {
                // inverse
                if (isTrue(isTrue(isEqual(symbol, null)) && isTrue(isEqual(baseCoin, null))))
                {
                    ((IDictionary<string,object>)request)["category"] = "inverse";
                }
            }
        }
        if (isTrue(isEqual(this.safeInteger(parameters, "limit"), null)))
        {
            ((IDictionary<string,object>)request)["limit"] = 200; // max limit
        }
        parameters = this.omit(parameters, new List<object>() {"type"});
        ((IDictionary<string,object>)request)["category"] = type;
        object response = await this.privateGetV5PositionList(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "Success",
        //         "result": {
        //             "nextPageCursor": "0%3A1657711949945%2C0%3A1657711949945",
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "ETHUSDT",
        //                     "leverage": "10",
        //                     "updatedTime": 1657711949945,
        //                     "side": "Buy",
        //                     "positionValue": "536.92500000",
        //                     "takeProfit": "",
        //                     "tpslMode": "Full",
        //                     "riskId": 11,
        //                     "trailingStop": "",
        //                     "entryPrice": "1073.********",
        //                     "unrealisedPnl": "",
        //                     "markPrice": "1080.********",
        //                     "size": "0.5000",
        //                     "positionStatus": "normal",
        //                     "stopLoss": "",
        //                     "cumRealisedPnl": "-0.********",
        //                     "positionMM": "2.********",
        //                     "createdTime": *************,
        //                     "positionIdx": 0,
        //                     "positionIM": "53.********"
        //                 }
        //             ]
        //         },
        //         "time": 1657713693182
        //     }
        //
        object positions = this.addPaginationCursorToResult(response);
        object results = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(positions)); postFixIncrement(ref i))
        {
            object rawPosition = getValue(positions, i);
            if (isTrue(isTrue((inOp(rawPosition, "data"))) && isTrue((inOp(rawPosition, "is_valid")))))
            {
                // futures only
                rawPosition = this.safeDict(rawPosition, "data");
            }
            ((IList<object>)results).Add(this.parsePosition(rawPosition));
        }
        return this.filterByArrayPositions(results, "symbol", symbols, false);
    }

    public override object parsePosition(object position, object market = null)
    {
        //
        // linear swap
        //
        //     {
        //         "positionIdx": 0,
        //         "riskId": "11",
        //         "symbol": "ETHUSDT",
        //         "side": "Buy",
        //         "size": "0.10",
        //         "positionValue": "119.845",
        //         "entryPrice": "1198.45",
        //         "tradeMode": 1,
        //         "autoAddMargin": 0,
        //         "leverage": "4.2",
        //         "positionBalance": "28.58931118",
        //         "liqPrice": "919.10",
        //         "bustPrice": "913.15",
        //         "takeProfit": "0.00",
        //         "stopLoss": "0.00",
        //         "trailingStop": "0.00",
        //         "unrealisedPnl": "0.083",
        //         "createdTime": "1669097244192",
        //         "updatedTime": "1669413126190",
        //         "tpSlMode": "Full",
        //         "riskLimitValue": "900000",
        //         "activePrice": "0.00"
        //     }
        //
        // usdc
        //    {
        //       "symbol":"BTCPERP",
        //       "leverage":"1.00",
        //       "occClosingFee":"0.0000",
        //       "liqPrice":"",
        //       "positionValue":"30.8100",
        //       "takeProfit":"0.0",
        //       "riskId":"10001",
        //       "trailingStop":"0.0000",
        //       "unrealisedPnl":"0.0000",
        //       "createdAt":"1652451795305",
        //       "markPrice":"30809.41",
        //       "cumRealisedPnl":"0.0000",
        //       "positionMM":"0.1541",
        //       "positionIM":"30.8100",
        //       "updatedAt":"1652451795305",
        //       "tpSLMode":"UNKNOWN",
        //       "side":"Buy",
        //       "bustPrice":"",
        //       "deleverageIndicator":"0",
        //       "entryPrice":"30810.0",
        //       "size":"0.001",
        //       "sessionRPL":"0.0000",
        //       "positionStatus":"NORMAL",
        //       "sessionUPL":"-0.0006",
        //       "stopLoss":"0.0",
        //       "orderMargin":"0.0000",
        //       "sessionAvgPrice":"30810.0"
        //    }
        //
        // unified margin
        //
        //     {
        //         "symbol": "ETHUSDT",
        //         "leverage": "10",
        //         "updatedTime": 1657711949945,
        //         "side": "Buy",
        //         "positionValue": "536.92500000",
        //         "takeProfit": "",
        //         "tpslMode": "Full",
        //         "riskId": 11,
        //         "trailingStop": "",
        //         "entryPrice": "1073.********",
        //         "unrealisedPnl": "",
        //         "markPrice": "1080.********",
        //         "size": "0.5000",
        //         "positionStatus": "normal",
        //         "stopLoss": "",
        //         "cumRealisedPnl": "-0.********",
        //         "positionMM": "2.********",
        //         "createdTime": *************,
        //         "positionIdx": 0,
        //         "positionIM": "53.********"
        //     }
        //
        // unified account
        //
        //     {
        //         "symbol": "XRPUSDT",
        //         "leverage": "10",
        //         "avgPrice": "0.3615",
        //         "liqPrice": "0.0001",
        //         "riskLimitValue": "200000",
        //         "takeProfit": "",
        //         "positionValue": "36.15",
        //         "tpslMode": "Full",
        //         "riskId": 41,
        //         "trailingStop": "0",
        //         "unrealisedPnl": "-1.83",
        //         "markPrice": "0.3432",
        //         "cumRealisedPnl": "0.********",
        //         "positionMM": "0.381021",
        //         "createdTime": "*************",
        //         "positionIdx": 0,
        //         "positionIM": "3.634521",
        //         "updatedTime": "*************",
        //         "side": "Buy",
        //         "bustPrice": "",
        //         "size": "100",
        //         "positionStatus": "Normal",
        //         "stopLoss": "",
        //         "tradeMode": 0
        //     }
        //
        // fetchPositionsHistory
        //
        //    {
        //        symbol: 'XRPUSDT',
        //        orderType: 'Market',
        //        leverage: '10',
        //        updatedTime: '1712717265572',
        //        side: 'Sell',
        //        orderId: '071749f3-a9fa-427b-b5ca-27b2f52b81de',
        //        closedPnl: '-0.00049568',
        //        avgEntryPrice: '0.6045',
        //        qty: '3',
        //        cumEntryValue: '1.8135',
        //        createdTime: '1712717265566',
        //        orderPrice: '0.5744',
        //        closedSize: '3',
        //        avgExitPrice: '0.605',
        //        execType: 'Trade',
        //        fillCount: '1',
        //        cumExitValue: '1.815'
        //    }
        //
        object closedSize = this.safeString(position, "closedSize");
        object isHistory = (!isEqual(closedSize, null));
        object contract = this.safeString(position, "symbol");
        market = this.safeMarket(contract, market, null, "contract");
        object size = Precise.stringAbs(this.safeString2(position, "size", "qty"));
        object side = this.safeString(position, "side");
        if (isTrue(!isEqual(side, null)))
        {
            if (isTrue(isEqual(side, "Buy")))
            {
                side = ((bool) isTrue(isHistory)) ? "short" : "long";
            } else if (isTrue(isEqual(side, "Sell")))
            {
                side = ((bool) isTrue(isHistory)) ? "long" : "short";
            } else
            {
                side = null;
            }
        }
        object notional = this.safeString2(position, "positionValue", "cumExitValue");
        object unrealisedPnl = this.omitZero(this.safeString(position, "unrealisedPnl"));
        object initialMarginString = this.safeStringN(position, new List<object>() {"positionIM", "cumEntryValue"});
        object maintenanceMarginString = this.safeString(position, "positionMM");
        object timestamp = this.safeIntegerN(position, new List<object>() {"createdTime", "createdAt"});
        object lastUpdateTimestamp = this.parse8601(this.safeString(position, "updated_at"));
        if (isTrue(isEqual(lastUpdateTimestamp, null)))
        {
            lastUpdateTimestamp = this.safeIntegerN(position, new List<object>() {"updatedTime", "updatedAt", "updatedTime"});
        }
        object tradeMode = this.safeInteger(position, "tradeMode", 0);
        object marginMode = null;
        if (isTrue(isTrue((!isTrue(getValue(this.options, "enableUnifiedAccount")))) || isTrue((isTrue(getValue(this.options, "enableUnifiedAccount")) && isTrue(getValue(market, "inverse"))))))
        {
            // tradeMode would work for classic and UTA(inverse)
            if (!isTrue(isHistory))
            {
                marginMode = ((bool) isTrue((isEqual(tradeMode, 1)))) ? "isolated" : "cross";
            }
        }
        object collateralString = this.safeString(position, "positionBalance");
        object entryPrice = this.omitZero(this.safeStringN(position, new List<object>() {"entryPrice", "avgPrice", "avgEntryPrice"}));
        object markPrice = this.safeString(position, "markPrice");
        object liquidationPrice = this.omitZero(this.safeString(position, "liqPrice"));
        object leverage = this.safeString(position, "leverage");
        if (isTrue(!isEqual(liquidationPrice, null)))
        {
            if (isTrue(isEqual(getValue(market, "settle"), "USDC")))
            {
                //  (Entry price - Liq price) * Contracts + Maintenance Margin + (unrealised pnl) = Collateral
                object price = ((bool) isTrue(this.safeBool(this.options, "useMarkPriceForPositionCollateral", false))) ? markPrice : entryPrice;
                object difference = Precise.stringAbs(Precise.stringSub(price, liquidationPrice));
                collateralString = Precise.stringAdd(Precise.stringAdd(Precise.stringMul(difference, size), maintenanceMarginString), unrealisedPnl);
            } else
            {
                object bustPrice = this.safeString(position, "bustPrice");
                if (isTrue(getValue(market, "linear")))
                {
                    // derived from the following formulas
                    //  (Entry price - Bust price) * Contracts = Collateral
                    //  (Entry price - Liq price) * Contracts = Collateral - Maintenance Margin
                    // Maintenance Margin = (Bust price - Liq price) x Contracts
                    object maintenanceMarginPriceDifference = Precise.stringAbs(Precise.stringSub(liquidationPrice, bustPrice));
                    maintenanceMarginString = Precise.stringMul(maintenanceMarginPriceDifference, size);
                    // Initial Margin = Contracts x Entry Price / Leverage
                    if (isTrue(!isEqual(entryPrice, null)))
                    {
                        initialMarginString = Precise.stringDiv(Precise.stringMul(size, entryPrice), leverage);
                    }
                } else
                {
                    // Contracts * (1 / Entry price - 1 / Bust price) = Collateral
                    // Contracts * (1 / Entry price - 1 / Liq price) = Collateral - Maintenance Margin
                    // Maintenance Margin = Contracts * (1 / Liq price - 1 / Bust price)
                    // Maintenance Margin = Contracts * (Bust price - Liq price) / (Liq price x Bust price)
                    object difference = Precise.stringAbs(Precise.stringSub(bustPrice, liquidationPrice));
                    object multiply = Precise.stringMul(bustPrice, liquidationPrice);
                    maintenanceMarginString = Precise.stringDiv(Precise.stringMul(size, difference), multiply);
                    // Initial Margin = Leverage x Contracts / EntryPrice
                    if (isTrue(!isEqual(entryPrice, null)))
                    {
                        initialMarginString = Precise.stringDiv(size, Precise.stringMul(entryPrice, leverage));
                    }
                }
            }
        }
        object maintenanceMarginPercentage = Precise.stringDiv(maintenanceMarginString, notional);
        object marginRatio = Precise.stringDiv(maintenanceMarginString, collateralString, 4);
        object positionIdx = this.safeString(position, "positionIdx");
        object hedged = isTrue((!isEqual(positionIdx, null))) && isTrue((!isEqual(positionIdx, "0")));
        return this.safePosition(new Dictionary<string, object>() {
            { "info", position },
            { "id", null },
            { "symbol", getValue(market, "symbol") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastUpdateTimestamp", lastUpdateTimestamp },
            { "initialMargin", this.parseNumber(initialMarginString) },
            { "initialMarginPercentage", this.parseNumber(Precise.stringDiv(initialMarginString, notional)) },
            { "maintenanceMargin", this.parseNumber(maintenanceMarginString) },
            { "maintenanceMarginPercentage", this.parseNumber(maintenanceMarginPercentage) },
            { "entryPrice", this.parseNumber(entryPrice) },
            { "notional", this.parseNumber(notional) },
            { "leverage", this.parseNumber(leverage) },
            { "unrealizedPnl", this.parseNumber(unrealisedPnl) },
            { "realizedPnl", this.safeNumber(position, "closedPnl") },
            { "contracts", this.parseNumber(size) },
            { "contractSize", this.safeNumber(market, "contractSize") },
            { "marginRatio", this.parseNumber(marginRatio) },
            { "liquidationPrice", this.parseNumber(liquidationPrice) },
            { "markPrice", this.parseNumber(markPrice) },
            { "lastPrice", this.safeNumber(position, "avgExitPrice") },
            { "collateral", this.parseNumber(collateralString) },
            { "marginMode", marginMode },
            { "side", side },
            { "percentage", null },
            { "stopLossPrice", this.safeNumber2(position, "stop_loss", "stopLoss") },
            { "takeProfitPrice", this.safeNumber2(position, "take_profit", "takeProfit") },
            { "hedged", hedged },
        });
    }

    /**
     * @method
     * @name bybit#fetchLeverage
     * @description fetch the set leverage for a market
     * @see https://bybit-exchange.github.io/docs/v5/position
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [leverage structure]{@link https://docs.ccxt.com/#/?id=leverage-structure}
     */
    public async override Task<object> fetchLeverage(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object position = await this.fetchPosition(symbol, parameters);
        return this.parseLeverage(position, market);
    }

    public override object parseLeverage(object leverage, object market = null)
    {
        object marketId = this.safeString(leverage, "symbol");
        object leverageValue = this.safeInteger(leverage, "leverage");
        return new Dictionary<string, object>() {
            { "info", leverage },
            { "symbol", this.safeSymbol(marketId, market) },
            { "marginMode", this.safeStringLower(leverage, "marginMode") },
            { "longLeverage", leverageValue },
            { "shortLeverage", leverageValue },
        };
    }

    /**
     * @method
     * @name bybit#setMarginMode
     * @description set margin mode (account) or trade mode (symbol)
     * @see https://bybit-exchange.github.io/docs/v5/account/set-margin-mode
     * @see https://bybit-exchange.github.io/docs/v5/position/cross-isolate
     * @param {string} marginMode account mode must be either [isolated, cross, portfolio], trade mode must be either [isolated, cross]
     * @param {string} symbol unified market symbol of the market the position is held in, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.leverage] the rate of leverage, is required if setting trade mode (symbol)
     * @returns {object} response from the exchange
     */
    public async override Task<object> setMarginMode(object marginMode, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        var enableUnifiedMarginenableUnifiedAccountVariable = await this.isUnifiedEnabled();
        var enableUnifiedMargin = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[0];
        var enableUnifiedAccount = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[1];
        object isUnifiedAccount = (isTrue(enableUnifiedMargin) || isTrue(enableUnifiedAccount));
        object market = null;
        object response = null;
        if (isTrue(isUnifiedAccount))
        {
            if (isTrue(isEqual(marginMode, "isolated")))
            {
                marginMode = "ISOLATED_MARGIN";
            } else if (isTrue(isEqual(marginMode, "cross")))
            {
                marginMode = "REGULAR_MARGIN";
            } else if (isTrue(isEqual(marginMode, "portfolio")))
            {
                marginMode = "PORTFOLIO_MARGIN";
            } else
            {
                throw new NotSupported ((string)add(this.id, " setMarginMode() marginMode must be either [isolated, cross, portfolio]")) ;
            }
            object request = new Dictionary<string, object>() {
                { "setMarginMode", marginMode },
            };
            response = await this.privatePostV5AccountSetMarginMode(this.extend(request, parameters));
        } else
        {
            if (isTrue(isEqual(symbol, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " setMarginMode() requires a symbol parameter for non unified account")) ;
            }
            market = this.market(symbol);
            object isUsdcSettled = isEqual(getValue(market, "settle"), "USDC");
            if (isTrue(isUsdcSettled))
            {
                if (isTrue(isEqual(marginMode, "cross")))
                {
                    marginMode = "REGULAR_MARGIN";
                } else if (isTrue(isEqual(marginMode, "portfolio")))
                {
                    marginMode = "PORTFOLIO_MARGIN";
                } else
                {
                    throw new NotSupported ((string)add(this.id, " setMarginMode() for usdc market marginMode must be either [cross, portfolio]")) ;
                }
                object request = new Dictionary<string, object>() {
                    { "setMarginMode", marginMode },
                };
                response = await this.privatePostV5AccountSetMarginMode(this.extend(request, parameters));
            } else
            {
                object type = null;
                var typeparametersVariable = this.getBybitType("setPositionMode", market, parameters);
                type = ((IList<object>)typeparametersVariable)[0];
                parameters = ((IList<object>)typeparametersVariable)[1];
                object tradeMode = null;
                if (isTrue(isEqual(marginMode, "cross")))
                {
                    tradeMode = 0;
                } else if (isTrue(isEqual(marginMode, "isolated")))
                {
                    tradeMode = 1;
                } else
                {
                    throw new NotSupported ((string)add(this.id, " setMarginMode() with symbol marginMode must be either [isolated, cross]")) ;
                }
                object sellLeverage = null;
                object buyLeverage = null;
                object leverage = this.safeString(parameters, "leverage");
                if (isTrue(isEqual(leverage, null)))
                {
                    sellLeverage = this.safeString2(parameters, "sell_leverage", "sellLeverage");
                    buyLeverage = this.safeString2(parameters, "buy_leverage", "buyLeverage");
                    if (isTrue(isTrue(isEqual(sellLeverage, null)) && isTrue(isEqual(buyLeverage, null))))
                    {
                        throw new ArgumentsRequired ((string)add(this.id, " setMarginMode() requires a leverage parameter or sell_leverage and buy_leverage parameters")) ;
                    }
                    if (isTrue(isEqual(buyLeverage, null)))
                    {
                        buyLeverage = sellLeverage;
                    }
                    if (isTrue(isEqual(sellLeverage, null)))
                    {
                        sellLeverage = buyLeverage;
                    }
                    parameters = this.omit(parameters, new List<object>() {"buy_leverage", "sell_leverage", "sellLeverage", "buyLeverage"});
                } else
                {
                    sellLeverage = leverage;
                    buyLeverage = leverage;
                    parameters = this.omit(parameters, "leverage");
                }
                object request = new Dictionary<string, object>() {
                    { "category", type },
                    { "symbol", getValue(market, "id") },
                    { "tradeMode", tradeMode },
                    { "buyLeverage", buyLeverage },
                    { "sellLeverage", sellLeverage },
                };
                response = await this.privatePostV5PositionSwitchIsolated(this.extend(request, parameters));
            }
        }
        return response;
    }

    /**
     * @method
     * @name bybit#setLeverage
     * @description set the level of leverage for a market
     * @see https://bybit-exchange.github.io/docs/v5/position/leverage
     * @param {float} leverage the rate of leverage
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.buyLeverage] leverage for buy side
     * @param {string} [params.sellLeverage] leverage for sell side
     * @returns {object} response from the exchange
     */
    public async override Task<object> setLeverage(object leverage, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " setLeverage() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        // WARNING: THIS WILL INCREASE LIQUIDATION PRICE FOR OPEN ISOLATED LONG POSITIONS
        // AND DECREASE LIQUIDATION PRICE FOR OPEN ISOLATED SHORT POSITIONS
        // engage in leverage setting
        // we reuse the code here instead of having two methods
        object leverageString = this.numberToString(leverage);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "buyLeverage", leverageString },
            { "sellLeverage", leverageString },
        };
        ((IDictionary<string,object>)request)["buyLeverage"] = leverageString;
        ((IDictionary<string,object>)request)["sellLeverage"] = leverageString;
        if (isTrue(getValue(market, "linear")))
        {
            ((IDictionary<string,object>)request)["category"] = "linear";
        } else if (isTrue(getValue(market, "inverse")))
        {
            ((IDictionary<string,object>)request)["category"] = "inverse";
        } else
        {
            throw new NotSupported ((string)add(this.id, " setLeverage() only support linear and inverse market")) ;
        }
        object response = await this.privatePostV5PositionSetLeverage(this.extend(request, parameters));
        return response;
    }

    /**
     * @method
     * @name bybit#setPositionMode
     * @description set hedged to true or false for a market
     * @see https://bybit-exchange.github.io/docs/v5/position/position-mode
     * @param {bool} hedged
     * @param {string} symbol used for unified account with inverse market
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} response from the exchange
     */
    public async override Task<object> setPositionMode(object hedged, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
        }
        object mode = null;
        if (isTrue(hedged))
        {
            mode = 3;
        } else
        {
            mode = 0;
        }
        object request = new Dictionary<string, object>() {
            { "mode", mode },
        };
        if (isTrue(isEqual(symbol, null)))
        {
            ((IDictionary<string,object>)request)["coin"] = "USDT";
        } else
        {
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(symbol, null)))
        {
            ((IDictionary<string,object>)request)["category"] = ((bool) isTrue(getValue(market, "linear"))) ? "linear" : "inverse";
        } else
        {
            object type = null;
            var typeparametersVariable = this.getBybitType("setPositionMode", market, parameters);
            type = ((IList<object>)typeparametersVariable)[0];
            parameters = ((IList<object>)typeparametersVariable)[1];
            ((IDictionary<string,object>)request)["category"] = type;
        }
        parameters = this.omit(parameters, "type");
        object response = await this.privatePostV5PositionSwitchMode(this.extend(request, parameters));
        //
        // v5
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {},
        //         "retExtInfo": {},
        //         "time": 1675249072814
        //     }
        return response;
    }

    public async virtual Task<object> fetchDerivativesOpenInterestHistory(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1h";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object subType = ((bool) isTrue(getValue(market, "linear"))) ? "linear" : "inverse";
        object category = this.safeString(parameters, "category", subType);
        object intervals = this.safeDict(this.options, "intervals");
        object interval = this.safeString(intervals, timeframe); // 5min,15min,30min,1h,4h,1d
        if (isTrue(isEqual(interval, null)))
        {
            throw new BadRequest ((string)add(add(add(this.id, " fetchOpenInterestHistory() cannot use the "), timeframe), " timeframe")) ;
        }
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "intervalTime", interval },
            { "category", category },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        object until = this.safeInteger(parameters, "until"); // unified in milliseconds
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["endTime"] = until;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.publicGetV5MarketOpenInterest(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "symbol": "BTCUSD",
        //             "category": "inverse",
        //             "list": [
        //                 {
        //                     "openInterest": "461134384.00000000",
        //                     "timestamp": "1669571400000"
        //                 },
        //                 {
        //                     "openInterest": "461134292.00000000",
        //                     "timestamp": "1669571100000"
        //                 }
        //             ],
        //             "nextPageCursor": ""
        //         },
        //         "retExtInfo": {},
        //         "time": 1672053548579
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.addPaginationCursorToResult(response);
        object id = this.safeString(result, "symbol");
        market = this.safeMarket(id, market, null, "contract");
        return this.parseOpenInterestsHistory(data, market, since, limit);
    }

    /**
     * @method
     * @name bybit#fetchOpenInterest
     * @description Retrieves the open interest of a derivative trading pair
     * @see https://bybit-exchange.github.io/docs/v5/market/open-interest
     * @param {string} symbol Unified CCXT market symbol
     * @param {object} [params] exchange specific parameters
     * @param {string} [params.interval] 5m, 15m, 30m, 1h, 4h, 1d
     * @param {string} [params.category] "linear" or "inverse"
     * @returns {object} an open interest structure{@link https://docs.ccxt.com/#/?id=open-interest-structure}
     */
    public async override Task<object> fetchOpenInterest(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "contract")))
        {
            throw new BadRequest ((string)add(this.id, " fetchOpenInterest() supports contract markets only")) ;
        }
        object timeframe = this.safeString(parameters, "interval", "1h");
        object intervals = this.safeDict(this.options, "intervals");
        object interval = this.safeString(intervals, timeframe); // 5min,15min,30min,1h,4h,1d
        if (isTrue(isEqual(interval, null)))
        {
            throw new BadRequest ((string)add(add(add(this.id, " fetchOpenInterest() cannot use the "), timeframe), " timeframe")) ;
        }
        object subType = ((bool) isTrue(getValue(market, "linear"))) ? "linear" : "inverse";
        object category = this.safeString(parameters, "category", subType);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "intervalTime", interval },
            { "category", category },
        };
        object response = await this.publicGetV5MarketOpenInterest(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "symbol": "BTCUSD",
        //             "category": "inverse",
        //             "list": [
        //                 {
        //                     "openInterest": "461134384.00000000",
        //                     "timestamp": "1669571400000"
        //                 },
        //                 {
        //                     "openInterest": "461134292.00000000",
        //                     "timestamp": "1669571100000"
        //                 }
        //             ],
        //             "nextPageCursor": ""
        //         },
        //         "retExtInfo": {},
        //         "time": 1672053548579
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object id = this.safeString(result, "symbol");
        market = this.safeMarket(id, market, null, "contract");
        object data = this.addPaginationCursorToResult(response);
        return this.parseOpenInterest(getValue(data, 0), market);
    }

    /**
     * @method
     * @name bybit#fetchOpenInterestHistory
     * @description Gets the total amount of unsettled contracts. In other words, the total number of contracts held in open positions
     * @see https://bybit-exchange.github.io/docs/v5/market/open-interest
     * @param {string} symbol Unified market symbol
     * @param {string} timeframe "5m", 15m, 30m, 1h, 4h, 1d
     * @param {int} [since] Not used by Bybit
     * @param {int} [limit] The number of open interest structures to return. Max 200, default 50
     * @param {object} [params] Exchange specific parameters
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns An array of open interest structures
     */
    public async override Task<object> fetchOpenInterestHistory(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1h";
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(timeframe, "1m")))
        {
            throw new BadRequest ((string)add(this.id, " fetchOpenInterestHistory cannot use the 1m timeframe")) ;
        }
        await this.loadMarkets();
        object paginate = this.safeBool(parameters, "paginate");
        if (isTrue(paginate))
        {
            parameters = this.omit(parameters, "paginate");
            ((IDictionary<string,object>)parameters)["timeframe"] = timeframe;
            return await this.fetchPaginatedCallCursor("fetchOpenInterestHistory", symbol, since, limit, parameters, "nextPageCursor", "cursor", null, 200);
        }
        object market = this.market(symbol);
        if (isTrue(isTrue(getValue(market, "spot")) || isTrue(getValue(market, "option"))))
        {
            throw new BadRequest ((string)add(add(this.id, " fetchOpenInterestHistory() symbol does not support market "), symbol)) ;
        }
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        return await this.fetchDerivativesOpenInterestHistory(symbol, timeframe, since, limit, parameters);
    }

    public override object parseOpenInterest(object interest, object market = null)
    {
        //
        //    {
        //        "openInterest": 64757.62400000,
        //        "timestamp": 1665784800000,
        //    }
        //
        object timestamp = this.safeInteger(interest, "timestamp");
        object openInterest = this.safeNumber2(interest, "open_interest", "openInterest");
        // the openInterest is in the base asset for linear and quote asset for inverse
        object amount = ((bool) isTrue(getValue(market, "linear"))) ? openInterest : null;
        object value = ((bool) isTrue(getValue(market, "inverse"))) ? openInterest : null;
        return this.safeOpenInterest(new Dictionary<string, object>() {
            { "symbol", getValue(market, "symbol") },
            { "openInterestAmount", amount },
            { "openInterestValue", value },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "info", interest },
        }, market);
    }

    /**
     * @method
     * @name bybit#fetchCrossBorrowRate
     * @description fetch the rate of interest to borrow a currency for margin trading
     * @see https://bybit-exchange.github.io/docs/zh-TW/v5/spot-margin-normal/interest-quota
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [borrow rate structure]{@link https://docs.ccxt.com/#/?id=borrow-rate-structure}
     */
    public async override Task<object> fetchCrossBorrowRate(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
        };
        object response = await this.privateGetV5SpotCrossMarginTradeLoanInfo(this.extend(request, parameters));
        //
        //    {
        //         "retCode": "0",
        //         "retMsg": "success",
        //         "result": {
        //             "coin": "USDT",
        //             "interestRate": "0.000107000000",
        //             "loanAbleAmount": "",
        //             "maxLoanAmount": "79999.999"
        //         },
        //         "retExtInfo": null,
        //         "time": "1666734490778"
        //     }
        //
        object timestamp = this.safeInteger(response, "time");
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        ((IDictionary<string,object>)data)["timestamp"] = timestamp;
        return this.parseBorrowRate(data, currency);
    }

    public override object parseBorrowRate(object info, object currency = null)
    {
        //
        //     {
        //         "coin": "USDT",
        //         "interestRate": "0.000107000000",
        //         "loanAbleAmount": "",
        //         "maxLoanAmount": "79999.999",
        //         "timestamp": 1666734490778
        //     }
        //
        // fetchBorrowRateHistory
        //     {
        //         "timestamp": 1721469600000,
        //         "currency": "USDC",
        //         "hourlyBorrowRate": "0.000014621596",
        //         "vipLevel": "No VIP"
        //     }
        //
        object timestamp = this.safeInteger(info, "timestamp");
        object currencyId = this.safeString2(info, "coin", "currency");
        object hourlyBorrowRate = this.safeNumber(info, "hourlyBorrowRate");
        object period = ((bool) isTrue((!isEqual(hourlyBorrowRate, null)))) ? 3600000 : 86400000; // 1h or 1d
        return new Dictionary<string, object>() {
            { "currency", this.safeCurrencyCode(currencyId, currency) },
            { "rate", this.safeNumber(info, "interestRate", hourlyBorrowRate) },
            { "period", period },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "info", info },
        };
    }

    /**
     * @method
     * @name bybit#fetchBorrowInterest
     * @description fetch the interest owed by the user for borrowing currency for margin trading
     * @see https://bybit-exchange.github.io/docs/zh-TW/v5/spot-margin-normal/account-info
     * @param {string} code unified currency code
     * @param {string} symbol unified market symbol when fetch interest in isolated markets
     * @param {number} [since] the earliest time in ms to fetch borrrow interest for
     * @param {number} [limit] the maximum number of structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [borrow interest structures]{@link https://docs.ccxt.com/#/?id=borrow-interest-structure}
     */
    public async override Task<object> fetchBorrowInterest(object code = null, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object response = await this.privateGetV5SpotCrossMarginTradeAccount(this.extend(request, parameters));
        //
        //     {
        //         "ret_code": 0,
        //         "ret_msg": "",
        //         "ext_code": null,
        //         "ext_info": null,
        //         "result": {
        //             "status": "1",
        //             "riskRate": "0",
        //             "acctBalanceSum": "0.********3817680857",
        //             "debtBalanceSum": "0",
        //             "loanAccountList": [
        //                 {
        //                     "tokenId": "BTC",
        //                     "total": "0.********",
        //                     "locked": "0",
        //                     "loan": "0",
        //                     "interest": "0",
        //                     "free": "0.********"
        //                 },
        //                 ...
        //             ]
        //         }
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object rows = this.safeList(data, "loanAccountList", new List<object>() {});
        object interest = this.parseBorrowInterests(rows, null);
        return this.filterByCurrencySinceLimit(interest, code, since, limit);
    }

    /**
     * @method
     * @name bybit#fetchBorrowRateHistory
     * @description retrieves a history of a currencies borrow interest rate at specific time slots
     * @see https://bybit-exchange.github.io/docs/v5/spot-margin-uta/historical-interest
     * @param {string} code unified currency code
     * @param {int} [since] timestamp for the earliest borrow rate
     * @param {int} [limit] the maximum number of [borrow rate structures]{@link https://docs.ccxt.com/#/?id=borrow-rate-structure} to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @returns {object[]} an array of [borrow rate structures]{@link https://docs.ccxt.com/#/?id=borrow-rate-structure}
     */
    public async virtual Task<object> fetchBorrowRateHistory(object code, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "currency", getValue(currency, "id") },
        };
        if (isTrue(isEqual(since, null)))
        {
            since = subtract(this.milliseconds(), multiply(86400000, 30)); // last 30 days
        }
        ((IDictionary<string,object>)request)["startTime"] = since;
        object endTime = this.safeInteger2(parameters, "until", "endTime");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(isEqual(endTime, null)))
        {
            endTime = add(since, multiply(86400000, 30)); // since + 30 days
        }
        ((IDictionary<string,object>)request)["endTime"] = endTime;
        object response = await this.privateGetV5SpotMarginTradeInterestRateHistory(this.extend(request, parameters));
        //
        //   {
        //       "retCode": 0,
        //       "retMsg": "OK",
        //       "result": {
        //           "list": [
        //               {
        //                   "timestamp": 1721469600000,
        //                   "currency": "USDC",
        //                   "hourlyBorrowRate": "0.000014621596",
        //                   "vipLevel": "No VIP"
        //               }
        //           ]
        //       },
        //       "retExtInfo": "{}",
        //       "time": 1721899048991
        //   }
        //
        object data = this.safeDict(response, "result");
        object rows = this.safeList(data, "list", new List<object>() {});
        return this.parseBorrowRateHistory(rows, code, since, limit);
    }

    public override object parseBorrowInterest(object info, object market = null)
    {
        //
        //     {
        //         "tokenId": "BTC",
        //         "total": "0.********",
        //         "locked": "0",
        //         "loan": "0",
        //         "interest": "0",
        //         "free": "0.********"
        //     },
        //
        return new Dictionary<string, object>() {
            { "info", info },
            { "symbol", null },
            { "currency", this.safeCurrencyCode(this.safeString(info, "tokenId")) },
            { "interest", this.safeNumber(info, "interest") },
            { "interestRate", null },
            { "amountBorrowed", this.safeNumber(info, "loan") },
            { "marginMode", "cross" },
            { "timestamp", null },
            { "datetime", null },
        };
    }

    /**
     * @method
     * @name bybit#transfer
     * @description transfer currency internally between wallets on the same account
     * @see https://bybit-exchange.github.io/docs/v5/asset/create-inter-transfer
     * @param {string} code unified currency code
     * @param {float} amount amount to transfer
     * @param {string} fromAccount account to transfer from
     * @param {string} toAccount account to transfer to
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.transferId] UUID, which is unique across the platform
     * @returns {object} a [transfer structure]{@link https://docs.ccxt.com/#/?id=transfer-structure}
     */
    public async override Task<object> transfer(object code, object amount, object fromAccount, object toAccount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object transferId = this.safeString(parameters, "transferId", this.uuid());
        object accountTypes = this.safeDict(this.options, "accountsByType", new Dictionary<string, object>() {});
        object fromId = this.safeString(accountTypes, fromAccount, fromAccount);
        object toId = this.safeString(accountTypes, toAccount, toAccount);
        object currency = this.currency(code);
        object amountToPrecision = this.currencyToPrecision(code, amount);
        object request = new Dictionary<string, object>() {
            { "transferId", transferId },
            { "fromAccountType", fromId },
            { "toAccountType", toId },
            { "coin", getValue(currency, "id") },
            { "amount", amountToPrecision },
        };
        object response = await this.privatePostV5AssetTransferInterTransfer(this.extend(request, parameters));
        //
        // {
        //     "retCode": 0,
        //     "retMsg": "success",
        //     "result": {
        //         "transferId": "4244af44-f3b0-4cf6-a743-b56560e987bc"
        //     },
        //     "retExtInfo": {},
        //     "time": *************
        // }
        //
        object timestamp = this.safeInteger(response, "time");
        object transfer = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object statusRaw = this.safeStringN(response, new List<object>() {"retCode", "retMsg"});
        object status = this.parseTransferStatus(statusRaw);
        return this.extend(this.parseTransfer(transfer, currency), new Dictionary<string, object>() {
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "amount", this.parseNumber(amountToPrecision) },
            { "fromAccount", fromAccount },
            { "toAccount", toAccount },
            { "status", status },
        });
    }

    /**
     * @method
     * @name bybit#fetchTransfers
     * @description fetch a history of internal transfers made on an account
     * @see https://bybit-exchange.github.io/docs/v5/asset/inter-transfer-list
     * @param {string} code unified currency code of the currency transferred
     * @param {int} [since] the earliest time in ms to fetch transfers for
     * @param {int} [limit] the maximum number of transfer structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch entries for
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {object[]} a list of [transfer structures]{@link https://docs.ccxt.com/#/?id=transfer-structure}
     */
    public async override Task<object> fetchTransfers(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchTransfers", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchTransfers", code, since, limit, parameters, "nextPageCursor", "cursor", null, 50);
        }
        object currency = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.safeCurrency(code);
            ((IDictionary<string,object>)request)["coin"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        var requestparametersVariable = this.handleUntilOption("endTime", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.privateGetV5AssetTransferQueryInterTransferList(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "list": [
        //                 {
        //                     "transferId": "selfTransfer_a1091cc7-9364-4b74-8de1-18f02c6f2d5c",
        //                     "coin": "USDT",
        //                     "amount": "5000",
        //                     "fromAccountType": "SPOT",
        //                     "toAccountType": "UNIFIED",
        //                     "timestamp": "*************",
        //                     "status": "SUCCESS"
        //                 }
        //             ],
        //             "nextPageCursor": "eyJtaW5JRCI6MTM1ODQ2OCwibWF4SUQiOjEzNTg0Njh9"
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object data = this.addPaginationCursorToResult(response);
        return this.parseTransfers(data, currency, since, limit);
    }

    /**
     * @method
     * @name bybit#borrowCrossMargin
     * @description create a loan to borrow margin
     * @see https://bybit-exchange.github.io/docs/v5/spot-margin-normal/borrow
     * @param {string} code unified currency code of the currency to borrow
     * @param {float} amount the amount to borrow
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [margin loan structure]{@link https://docs.ccxt.com/#/?id=margin-loan-structure}
     */
    public async override Task<object> borrowCrossMargin(object code, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
            { "qty", this.currencyToPrecision(code, amount) },
        };
        object response = await this.privatePostV5SpotCrossMarginTradeLoan(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "transactId": "14143"
        //         },
        //         "retExtInfo": null,
        //         "time": 1662617848970
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object transaction = this.parseMarginLoan(result, currency);
        return this.extend(transaction, new Dictionary<string, object>() {
            { "symbol", null },
            { "amount", amount },
        });
    }

    /**
     * @method
     * @name bybit#repayCrossMargin
     * @description repay borrowed margin and interest
     * @see https://bybit-exchange.github.io/docs/v5/spot-margin-normal/repay
     * @param {string} code unified currency code of the currency to repay
     * @param {float} amount the amount to repay
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [margin loan structure]{@link https://docs.ccxt.com/#/?id=margin-loan-structure}
     */
    public async override Task<object> repayCrossMargin(object code, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
            { "qty", this.numberToString(amount) },
        };
        object response = await this.privatePostV5SpotCrossMarginTradeRepay(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //            "repayId": "12128"
        //         },
        //         "retExtInfo": null,
        //         "time": 1662618298452
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object transaction = this.parseMarginLoan(result, currency);
        return this.extend(transaction, new Dictionary<string, object>() {
            { "symbol", null },
            { "amount", amount },
        });
    }

    public virtual object parseMarginLoan(object info, object currency = null)
    {
        //
        // borrowCrossMargin
        //
        //     {
        //         "transactId": "14143"
        //     }
        //
        // repayCrossMargin
        //
        //     {
        //         "repayId": "12128"
        //     }
        //
        return new Dictionary<string, object>() {
            { "id", this.safeString2(info, "transactId", "repayId") },
            { "currency", this.safeString(currency, "code") },
            { "amount", null },
            { "symbol", null },
            { "timestamp", null },
            { "datetime", null },
            { "info", info },
        };
    }

    public virtual object parseTransferStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "0", "ok" },
            { "OK", "ok" },
            { "SUCCESS", "ok" },
        };
        return this.safeString(statuses, status, status);
    }

    public override object parseTransfer(object transfer, object currency = null)
    {
        //
        // transfer
        //
        //     {
        //         "transferId": "22c2bc11-ed5b-49a4-8647-c4e0f5f6f2b2"
        //     }
        //
        // fetchTransfers
        //
        //     {
        //         "transferId": "e9c421c4-b010-4b16-abd6-106179f27702",
        //         "coin": "USDT",
        //         "amount": "8",
        //         "fromAccountType": "FUND",
        //         "toAccountType": "SPOT",
        //         "timestamp": "*************",
        //         "status": "SUCCESS"
        //      }
        //
        object currencyId = this.safeString(transfer, "coin");
        object timestamp = this.safeInteger(transfer, "timestamp");
        object fromAccountId = this.safeString(transfer, "fromAccountType");
        object toAccountId = this.safeString(transfer, "toAccountType");
        object accountIds = this.safeDict(this.options, "accountsById", new Dictionary<string, object>() {});
        object fromAccount = this.safeString(accountIds, fromAccountId, fromAccountId);
        object toAccount = this.safeString(accountIds, toAccountId, toAccountId);
        return new Dictionary<string, object>() {
            { "info", transfer },
            { "id", this.safeString(transfer, "transferId") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "currency", this.safeCurrencyCode(currencyId, currency) },
            { "amount", this.safeNumber(transfer, "amount") },
            { "fromAccount", fromAccount },
            { "toAccount", toAccount },
            { "status", this.parseTransferStatus(this.safeString(transfer, "status")) },
        };
    }

    public async virtual Task<object> fetchDerivativesMarketLeverageTiers(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(getValue(market, "linear")))
        {
            ((IDictionary<string,object>)request)["category"] = "linear";
        } else if (isTrue(getValue(market, "inverse")))
        {
            ((IDictionary<string,object>)request)["category"] = "inverse";
        }
        object response = await this.publicGetV5MarketRiskLimit(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "category": "inverse",
        //             "list": [
        //                 {
        //                     "id": 1,
        //                     "symbol": "BTCUSD",
        //                     "riskLimitValue": "150",
        //                     "maintenanceMargin": "0.5",
        //                     "initialMargin": "1",
        //                     "isLowestRisk": 1,
        //                     "maxLeverage": "100.00"
        //                 },
        //             ....
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672054488010
        //     }
        //
        object result = this.safeDict(response, "result");
        object tiers = this.safeList(result, "list");
        return this.parseMarketLeverageTiers(tiers, market);
    }

    /**
     * @method
     * @name bybit#fetchMarketLeverageTiers
     * @description retrieve information on the maximum leverage, and maintenance margin for trades of varying trade sizes for a single market
     * @see https://bybit-exchange.github.io/docs/v5/market/risk-limit
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [leverage tiers structure]{@link https://docs.ccxt.com/#/?id=leverage-tiers-structure}
     */
    public async override Task<object> fetchMarketLeverageTiers(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object market = null;
        market = this.market(symbol);
        if (isTrue(isTrue(getValue(market, "spot")) || isTrue(getValue(market, "option"))))
        {
            throw new BadRequest ((string)add(add(this.id, " fetchMarketLeverageTiers() symbol does not support market "), symbol)) ;
        }
        ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        return await this.fetchDerivativesMarketLeverageTiers(symbol, parameters);
    }

    public virtual object parseTradingFee(object fee, object market = null)
    {
        //
        //     {
        //         "symbol": "ETHUSDT",
        //         "makerFeeRate": 0.001,
        //         "takerFeeRate": 0.001
        //     }
        //
        object marketId = this.safeString(fee, "symbol");
        object defaultType = ((bool) isTrue((!isEqual(market, null)))) ? getValue(market, "type") : "contract";
        object symbol = this.safeSymbol(marketId, market, null, defaultType);
        return new Dictionary<string, object>() {
            { "info", fee },
            { "symbol", symbol },
            { "maker", this.safeNumber(fee, "makerFeeRate") },
            { "taker", this.safeNumber(fee, "takerFeeRate") },
            { "percentage", null },
            { "tierBased", null },
        };
    }

    /**
     * @method
     * @name bybit#fetchTradingFee
     * @description fetch the trading fees for a market
     * @see https://bybit-exchange.github.io/docs/v5/account/fee-rate
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [fee structure]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchTradingFee(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object category = null;
        var categoryparametersVariable = this.getBybitType("fetchTradingFee", market, parameters);
        category = ((IList<object>)categoryparametersVariable)[0];
        parameters = ((IList<object>)categoryparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = category;
        object response = await this.privateGetV5AccountFeeRate(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "list": [
        //                 {
        //                     "symbol": "ETHUSDT",
        //                     "takerFeeRate": "0.0006",
        //                     "makerFeeRate": "0.0001"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object fees = this.safeList(result, "list", new List<object>() {});
        object first = this.safeDict(fees, 0, new Dictionary<string, object>() {});
        return this.parseTradingFee(first, market);
    }

    /**
     * @method
     * @name bybit#fetchTradingFees
     * @description fetch the trading fees for multiple markets
     * @see https://bybit-exchange.github.io/docs/v5/account/fee-rate
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
     */
    public async override Task<object> fetchTradingFees(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object type = null;
        var typeparametersVariable = this.handleOptionAndParams(parameters, "fetchTradingFees", "type", "future");
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(isEqual(type, "spot")))
        {
            throw new NotSupported ((string)add(this.id, " fetchTradingFees() is not supported for spot market")) ;
        }
        object response = await this.privateGetV5AccountFeeRate(parameters);
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "list": [
        //                 {
        //                     "symbol": "ETHUSDT",
        //                     "takerFeeRate": "0.0006",
        //                     "makerFeeRate": "0.0001"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object fees = this.safeDict(response, "result", new Dictionary<string, object>() {});
        fees = this.safeList(fees, "list", new List<object>() {});
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(fees)); postFixIncrement(ref i))
        {
            object fee = this.parseTradingFee(getValue(fees, i));
            object symbol = getValue(fee, "symbol");
            ((IDictionary<string,object>)result)[(string)symbol] = fee;
        }
        return result;
    }

    public override object parseDepositWithdrawFee(object fee, object currency = null)
    {
        //
        //    {
        //        "name": "BTC",
        //        "coin": "BTC",
        //        "remainAmount": "150",
        //        "chains": [
        //            {
        //                "chainType": "BTC",
        //                "confirmation": "10000",
        //                "withdrawFee": "0.0005",
        //                "depositMin": "0.0005",
        //                "withdrawMin": "0.001",
        //                "chain": "BTC",
        //                "chainDeposit": "1",
        //                "chainWithdraw": "1",
        //                "minAccuracy": "8"
        //            }
        //        ]
        //    }
        //
        object chains = this.safeList(fee, "chains", new List<object>() {});
        object chainsLength = getArrayLength(chains);
        object result = new Dictionary<string, object>() {
            { "info", fee },
            { "withdraw", new Dictionary<string, object>() {
                { "fee", null },
                { "percentage", null },
            } },
            { "deposit", new Dictionary<string, object>() {
                { "fee", null },
                { "percentage", null },
            } },
            { "networks", new Dictionary<string, object>() {} },
        };
        if (isTrue(!isEqual(chainsLength, 0)))
        {
            for (object i = 0; isLessThan(i, chainsLength); postFixIncrement(ref i))
            {
                object chain = getValue(chains, i);
                object networkId = this.safeString(chain, "chain");
                object currencyCode = this.safeString(currency, "code");
                object networkCode = this.networkIdToCode(networkId, currencyCode);
                ((IDictionary<string,object>)getValue(result, "networks"))[(string)networkCode] = new Dictionary<string, object>() {
                    { "deposit", new Dictionary<string, object>() {
                        { "fee", null },
                        { "percentage", null },
                    } },
                    { "withdraw", new Dictionary<string, object>() {
                        { "fee", this.safeNumber(chain, "withdrawFee") },
                        { "percentage", false },
                    } },
                };
                if (isTrue(isEqual(chainsLength, 1)))
                {
                    ((IDictionary<string,object>)getValue(result, "withdraw"))["fee"] = this.safeNumber(chain, "withdrawFee");
                    ((IDictionary<string,object>)getValue(result, "withdraw"))["percentage"] = false;
                }
            }
        }
        return result;
    }

    /**
     * @method
     * @name bybit#fetchDepositWithdrawFees
     * @description fetch deposit and withdraw fees
     * @see https://bybit-exchange.github.io/docs/v5/asset/coin-info
     * @param {string[]} codes list of unified currency codes
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchDepositWithdrawFees(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        this.checkRequiredCredentials();
        await this.loadMarkets();
        object response = await this.privateGetV5AssetCoinQueryInfo(parameters);
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "",
        //         "result": {
        //             "rows": [
        //                 {
        //                     "name": "BTC",
        //                     "coin": "BTC",
        //                     "remainAmount": "150",
        //                     "chains": [
        //                         {
        //                             "chainType": "BTC",
        //                             "confirmation": "10000",
        //                             "withdrawFee": "0.0005",
        //                             "depositMin": "0.0005",
        //                             "withdrawMin": "0.001",
        //                             "chain": "BTC",
        //                             "chainDeposit": "1",
        //                             "chainWithdraw": "1",
        //                             "minAccuracy": "8"
        //                         }
        //                     ]
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672194582264
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object rows = this.safeList(data, "rows", new List<object>() {});
        return this.parseDepositWithdrawFees(rows, codes, "coin");
    }

    /**
     * @method
     * @name bybit#fetchSettlementHistory
     * @description fetches historical settlement records
     * @see https://bybit-exchange.github.io/docs/v5/market/delivery-price
     * @param {string} symbol unified market symbol of the settlement history
     * @param {int} [since] timestamp in ms
     * @param {int} [limit] number of records
     * @param {object} [params] exchange specific params
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @returns {object[]} a list of [settlement history objects]
     */
    public async virtual Task<object> fetchSettlementHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchSettlementHistory", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(isEqual(type, "spot")))
        {
            throw new NotSupported ((string)add(this.id, " fetchSettlementHistory() is not supported for spot market")) ;
        }
        ((IDictionary<string,object>)request)["category"] = type;
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.publicGetV5MarketDeliveryPrice(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "category": "option",
        //             "nextPageCursor": "0%2C3",
        //             "list": [
        //                 {
        //                     "symbol": "SOL-27JUN23-20-C",
        //                     "deliveryPrice": "16.62258889",
        //                     "deliveryTime": "1687852800000"
        //                 },
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1689043527231
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "list", new List<object>() {});
        object settlements = this.parseSettlements(data, market);
        object sorted = this.sortBy(settlements, "timestamp");
        return this.filterBySymbolSinceLimit(sorted, getValue(market, "symbol"), since, limit);
    }

    /**
     * @method
     * @name bybit#fetchMySettlementHistory
     * @description fetches historical settlement records of the user
     * @see https://bybit-exchange.github.io/docs/v5/asset/delivery
     * @param {string} symbol unified market symbol of the settlement history
     * @param {int} [since] timestamp in ms
     * @param {int} [limit] number of records
     * @param {object} [params] exchange specific params
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @returns {object[]} a list of [settlement history objects]
     */
    public async virtual Task<object> fetchMySettlementHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchMySettlementHistory", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(isEqual(type, "spot")))
        {
            throw new NotSupported ((string)add(this.id, " fetchMySettlementHistory() is not supported for spot market")) ;
        }
        ((IDictionary<string,object>)request)["category"] = type;
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.privateGetV5AssetDeliveryRecord(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "success",
        //         "result": {
        //             "category": "option",
        //             "nextPageCursor": "0%2C3",
        //             "list": [
        //                 {
        //                     "symbol": "SOL-27JUN23-20-C",
        //                     "deliveryPrice": "16.62258889",
        //                     "deliveryTime": "1687852800000",
        //                     "side": "Buy",
        //                     "strike": "20",
        //                     "fee": "0.00000000",
        //                     "position": "0.01",
        //                     "deliveryRpl": "3.5"
        //                 },
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1689043527231
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "list", new List<object>() {});
        object settlements = this.parseSettlements(data, market);
        object sorted = this.sortBy(settlements, "timestamp");
        return this.filterBySymbolSinceLimit(sorted, getValue(market, "symbol"), since, limit);
    }

    public virtual object parseSettlement(object settlement, object market)
    {
        //
        // fetchSettlementHistory
        //
        //     {
        //         "symbol": "SOL-27JUN23-20-C",
        //         "deliveryPrice": "16.62258889",
        //         "deliveryTime": "1687852800000"
        //     }
        //
        // fetchMySettlementHistory
        //
        //     {
        //         "symbol": "SOL-27JUN23-20-C",
        //         "deliveryPrice": "16.62258889",
        //         "deliveryTime": "1687852800000",
        //         "side": "Buy",
        //         "strike": "20",
        //         "fee": "0.00000000",
        //         "position": "0.01",
        //         "deliveryRpl": "3.5"
        //     }
        //
        object timestamp = this.safeInteger(settlement, "deliveryTime");
        object marketId = this.safeString(settlement, "symbol");
        return new Dictionary<string, object>() {
            { "info", settlement },
            { "symbol", this.safeSymbol(marketId, market) },
            { "price", this.safeNumber(settlement, "deliveryPrice") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        };
    }

    public virtual object parseSettlements(object settlements, object market)
    {
        //
        // fetchSettlementHistory
        //
        //     [
        //         {
        //             "symbol": "SOL-27JUN23-20-C",
        //             "deliveryPrice": "16.62258889",
        //             "deliveryTime": "1687852800000"
        //         }
        //     ]
        //
        // fetchMySettlementHistory
        //
        //     [
        //         {
        //             "symbol": "SOL-27JUN23-20-C",
        //             "deliveryPrice": "16.62258889",
        //             "deliveryTime": "1687852800000",
        //             "side": "Buy",
        //             "strike": "20",
        //             "fee": "0.00000000",
        //             "position": "0.01",
        //             "deliveryRpl": "3.5"
        //         }
        //     ]
        //
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(settlements)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(this.parseSettlement(getValue(settlements, i), market));
        }
        return result;
    }

    /**
     * @method
     * @name bybit#fetchVolatilityHistory
     * @description fetch the historical volatility of an option market based on an underlying asset
     * @see https://bybit-exchange.github.io/docs/v5/market/iv
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.period] the period in days to fetch the volatility for: 7,14,21,30,60,90,180,270
     * @returns {object[]} a list of [volatility history objects]{@link https://docs.ccxt.com/#/?id=volatility-structure}
     */
    public async virtual Task<object> fetchVolatilityHistory(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "category", "option" },
            { "baseCoin", getValue(currency, "id") },
        };
        object response = await this.publicGetV5MarketHistoricalVolatility(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "SUCCESS",
        //         "category": "option",
        //         "result": [
        //             {
        //                 "period": 7,
        //                 "value": "0.23854072",
        //                 "time": "1690574400000"
        //             }
        //         ]
        //     }
        //
        object volatility = this.safeList(response, "result", new List<object>() {});
        return this.parseVolatilityHistory(volatility);
    }

    public virtual object parseVolatilityHistory(object volatility)
    {
        //
        //     {
        //         "period": 7,
        //         "value": "0.23854072",
        //         "time": "1690574400000"
        //     }
        //
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(volatility)); postFixIncrement(ref i))
        {
            object entry = getValue(volatility, i);
            object timestamp = this.safeInteger(entry, "time");
            ((IList<object>)result).Add(new Dictionary<string, object>() {
                { "info", volatility },
                { "timestamp", timestamp },
                { "datetime", this.iso8601(timestamp) },
                { "volatility", this.safeNumber(entry, "value") },
            });
        }
        return result;
    }

    /**
     * @method
     * @name bybit#fetchGreeks
     * @description fetches an option contracts greeks, financial metrics used to measure the factors that affect the price of an options contract
     * @see https://bybit-exchange.github.io/docs/api-explorer/v5/market/tickers
     * @param {string} symbol unified symbol of the market to fetch greeks for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [greeks structure]{@link https://docs.ccxt.com/#/?id=greeks-structure}
     */
    public async override Task<object> fetchGreeks(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "category", "option" },
        };
        object response = await this.publicGetV5MarketTickers(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "SUCCESS",
        //         "result": {
        //             "category": "option",
        //             "list": [
        //                 {
        //                     "symbol": "BTC-26JAN24-39000-C",
        //                     "bid1Price": "3205",
        //                     "bid1Size": "7.1",
        //                     "bid1Iv": "0.5478",
        //                     "ask1Price": "3315",
        //                     "ask1Size": "1.98",
        //                     "ask1Iv": "0.5638",
        //                     "lastPrice": "3230",
        //                     "highPrice24h": "3255",
        //                     "lowPrice24h": "3200",
        //                     "markPrice": "3273.02263032",
        //                     "indexPrice": "36790.96",
        //                     "markIv": "0.5577",
        //                     "underlyingPrice": "37649.67254894",
        //                     "openInterest": "19.67",
        //                     "turnover24h": "170140.33875912",
        //                     "volume24h": "4.56",
        //                     "totalVolume": "22",
        //                     "totalTurnover": "789305",
        //                     "delta": "0.49640971",
        //                     "gamma": "0.00004131",
        //                     "vega": "69.08651675",
        //                     "theta": "-24.9443226",
        //                     "predictedDeliveryPrice": "0",
        //                     "change24h": "0.18532111"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1699584008326
        //     }
        //
        object timestamp = this.safeInteger(response, "time");
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "list", new List<object>() {});
        object greeks = this.parseGreeks(getValue(data, 0), market);
        return this.extend(greeks, new Dictionary<string, object>() {
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        });
    }

    /**
     * @method
     * @name bybit#fetchAllGreeks
     * @description fetches all option contracts greeks, financial metrics used to measure the factors that affect the price of an options contract
     * @see https://bybit-exchange.github.io/docs/api-explorer/v5/market/tickers
     * @param {string[]} [symbols] unified symbols of the markets to fetch greeks for, all markets are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.baseCoin] the baseCoin of the symbol, default is BTC
     * @returns {object} a [greeks structure]{@link https://docs.ccxt.com/#/?id=greeks-structure}
     */
    public async override Task<object> fetchAllGreeks(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols, null, true, true, true);
        object baseCoin = this.safeString(parameters, "baseCoin", "BTC");
        object request = new Dictionary<string, object>() {
            { "category", "option" },
            { "baseCoin", baseCoin },
        };
        object market = null;
        if (isTrue(!isEqual(symbols, null)))
        {
            object symbolsLength = getArrayLength(symbols);
            if (isTrue(isEqual(symbolsLength, 1)))
            {
                market = this.market(getValue(symbols, 0));
                ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
            }
        }
        object response = await this.publicGetV5MarketTickers(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "SUCCESS",
        //         "result": {
        //             "category": "option",
        //             "list": [
        //                 {
        //                     "symbol": "BTC-26JAN24-39000-C",
        //                     "bid1Price": "3205",
        //                     "bid1Size": "7.1",
        //                     "bid1Iv": "0.5478",
        //                     "ask1Price": "3315",
        //                     "ask1Size": "1.98",
        //                     "ask1Iv": "0.5638",
        //                     "lastPrice": "3230",
        //                     "highPrice24h": "3255",
        //                     "lowPrice24h": "3200",
        //                     "markPrice": "3273.02263032",
        //                     "indexPrice": "36790.96",
        //                     "markIv": "0.5577",
        //                     "underlyingPrice": "37649.67254894",
        //                     "openInterest": "19.67",
        //                     "turnover24h": "170140.33875912",
        //                     "volume24h": "4.56",
        //                     "totalVolume": "22",
        //                     "totalTurnover": "789305",
        //                     "delta": "0.49640971",
        //                     "gamma": "0.00004131",
        //                     "vega": "69.08651675",
        //                     "theta": "-24.9443226",
        //                     "predictedDeliveryPrice": "0",
        //                     "change24h": "0.18532111"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1699584008326
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "list", new List<object>() {});
        return this.parseAllGreeks(data, symbols);
    }

    public override object parseGreeks(object greeks, object market = null)
    {
        //
        //     {
        //         "symbol": "BTC-26JAN24-39000-C",
        //         "bid1Price": "3205",
        //         "bid1Size": "7.1",
        //         "bid1Iv": "0.5478",
        //         "ask1Price": "3315",
        //         "ask1Size": "1.98",
        //         "ask1Iv": "0.5638",
        //         "lastPrice": "3230",
        //         "highPrice24h": "3255",
        //         "lowPrice24h": "3200",
        //         "markPrice": "3273.02263032",
        //         "indexPrice": "36790.96",
        //         "markIv": "0.5577",
        //         "underlyingPrice": "37649.67254894",
        //         "openInterest": "19.67",
        //         "turnover24h": "170140.33875912",
        //         "volume24h": "4.56",
        //         "totalVolume": "22",
        //         "totalTurnover": "789305",
        //         "delta": "0.49640971",
        //         "gamma": "0.00004131",
        //         "vega": "69.08651675",
        //         "theta": "-24.9443226",
        //         "predictedDeliveryPrice": "0",
        //         "change24h": "0.18532111"
        //     }
        //
        object marketId = this.safeString(greeks, "symbol");
        object symbol = this.safeSymbol(marketId, market);
        return new Dictionary<string, object>() {
            { "symbol", symbol },
            { "timestamp", null },
            { "datetime", null },
            { "delta", this.safeNumber(greeks, "delta") },
            { "gamma", this.safeNumber(greeks, "gamma") },
            { "theta", this.safeNumber(greeks, "theta") },
            { "vega", this.safeNumber(greeks, "vega") },
            { "rho", null },
            { "bidSize", this.safeNumber(greeks, "bid1Size") },
            { "askSize", this.safeNumber(greeks, "ask1Size") },
            { "bidImpliedVolatility", this.safeNumber(greeks, "bid1Iv") },
            { "askImpliedVolatility", this.safeNumber(greeks, "ask1Iv") },
            { "markImpliedVolatility", this.safeNumber(greeks, "markIv") },
            { "bidPrice", this.safeNumber(greeks, "bid1Price") },
            { "askPrice", this.safeNumber(greeks, "ask1Price") },
            { "markPrice", this.safeNumber(greeks, "markPrice") },
            { "lastPrice", this.safeNumber(greeks, "lastPrice") },
            { "underlyingPrice", this.safeNumber(greeks, "underlyingPrice") },
            { "info", greeks },
        };
    }

    /**
     * @method
     * @name bybit#fetchMyLiquidations
     * @description retrieves the users liquidated positions
     * @see https://bybit-exchange.github.io/docs/api-explorer/v5/position/execution
     * @param {string} [symbol] unified CCXT market symbol
     * @param {int} [since] the earliest time in ms to fetch liquidations for
     * @param {int} [limit] the maximum number of liquidation structures to retrieve
     * @param {object} [params] exchange specific parameters for the exchange API endpoint
     * @param {string} [params.type] market type, ['swap', 'option', 'spot']
     * @param {string} [params.subType] market subType, ['linear', 'inverse']
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [available parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {object} an array of [liquidation structures]{@link https://docs.ccxt.com/#/?id=liquidation-structure}
     */
    public async override Task<object> fetchMyLiquidations(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchMyLiquidations", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchMyLiquidations", symbol, since, limit, parameters, "nextPageCursor", "cursor", null, 100);
        }
        object request = new Dictionary<string, object>() {
            { "execType", "BustTrade" },
        };
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchMyLiquidations", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = type;
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        var requestparametersVariable = this.handleUntilOption("endTime", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.privateGetV5ExecutionList(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "nextPageCursor": "132766%3A2%2C132766%3A2",
        //             "category": "linear",
        //             "list": [
        //                 {
        //                     "symbol": "ETHPERP",
        //                     "orderType": "Market",
        //                     "underlyingPrice": "",
        //                     "orderLinkId": "",
        //                     "side": "Buy",
        //                     "indexPrice": "",
        //                     "orderId": "8c065341-7b52-4ca9-ac2c-37e31ac55c94",
        //                     "stopOrderType": "UNKNOWN",
        //                     "leavesQty": "0",
        //                     "execTime": "1672282722429",
        //                     "isMaker": false,
        //                     "execFee": "0.071409",
        //                     "feeRate": "0.0006",
        //                     "execId": "e0cbe81d-0f18-5866-9415-cf319b5dab3b",
        //                     "tradeIv": "",
        //                     "blockTradeId": "",
        //                     "markPrice": "1183.54",
        //                     "execPrice": "1190.15",
        //                     "markIv": "",
        //                     "orderQty": "0.1",
        //                     "orderPrice": "1236.9",
        //                     "execValue": "119.015",
        //                     "execType": "Trade",
        //                     "execQty": "0.1"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1672283754510
        //     }
        //
        object liquidations = this.addPaginationCursorToResult(response);
        return this.parseLiquidations(liquidations, market, since, limit);
    }

    public override object parseLiquidation(object liquidation, object market = null)
    {
        //
        //     {
        //         "symbol": "ETHPERP",
        //         "orderType": "Market",
        //         "underlyingPrice": "",
        //         "orderLinkId": "",
        //         "side": "Buy",
        //         "indexPrice": "",
        //         "orderId": "8c065341-7b52-4ca9-ac2c-37e31ac55c94",
        //         "stopOrderType": "UNKNOWN",
        //         "leavesQty": "0",
        //         "execTime": "1672282722429",
        //         "isMaker": false,
        //         "execFee": "0.071409",
        //         "feeRate": "0.0006",
        //         "execId": "e0cbe81d-0f18-5866-9415-cf319b5dab3b",
        //         "tradeIv": "",
        //         "blockTradeId": "",
        //         "markPrice": "1183.54",
        //         "execPrice": "1190.15",
        //         "markIv": "",
        //         "orderQty": "0.1",
        //         "orderPrice": "1236.9",
        //         "execValue": "119.015",
        //         "execType": "Trade",
        //         "execQty": "0.1"
        //     }
        //
        object marketId = this.safeString(liquidation, "symbol");
        object timestamp = this.safeInteger(liquidation, "execTime");
        object contractsString = this.safeString(liquidation, "execQty");
        object contractSizeString = this.safeString(market, "contractSize");
        object priceString = this.safeString(liquidation, "execPrice");
        object baseValueString = Precise.stringMul(contractsString, contractSizeString);
        object quoteValueString = Precise.stringMul(baseValueString, priceString);
        return this.safeLiquidation(new Dictionary<string, object>() {
            { "info", liquidation },
            { "symbol", this.safeSymbol(marketId, market, null, "contract") },
            { "contracts", this.parseNumber(contractsString) },
            { "contractSize", this.parseNumber(contractSizeString) },
            { "price", this.parseNumber(priceString) },
            { "baseValue", this.parseNumber(baseValueString) },
            { "quoteValue", this.parseNumber(quoteValueString) },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        });
    }

    public async virtual Task<object> getLeverageTiersPaginated(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
        }
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "getLeverageTiersPaginated", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("getLeverageTiersPaginated", symbol, null, null, parameters, "nextPageCursor", "cursor", null, 100);
        }
        object subType = null;
        var subTypeparametersVariable = this.handleSubTypeAndParams("getLeverageTiersPaginated", market, parameters, "linear");
        subType = ((IList<object>)subTypeparametersVariable)[0];
        parameters = ((IList<object>)subTypeparametersVariable)[1];
        object request = new Dictionary<string, object>() {
            { "category", subType },
        };
        object response = await this.publicGetV5MarketRiskLimit(this.extend(request, parameters));
        object result = this.addPaginationCursorToResult(response);
        object first = this.safeDict(result, 0);
        object total = getArrayLength(result);
        object lastIndex = subtract(total, 1);
        object last = this.safeDict(result, lastIndex);
        object cursorValue = this.safeString(first, "nextPageCursor");
        ((IDictionary<string,object>)last)["info"] = new Dictionary<string, object>() {
            { "nextPageCursor", cursorValue },
        };
        ((List<object>)result)[Convert.ToInt32(lastIndex)] = last;
        return result;
    }

    /**
     * @method
     * @name bybit#fetchLeverageTiers
     * @description retrieve information on the maximum leverage, for different trade sizes
     * @see https://bybit-exchange.github.io/docs/v5/market/risk-limit
     * @param {string[]} [symbols] a list of unified market symbols
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.subType] market subType, ['linear', 'inverse'], default is 'linear'
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [available parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {object} a dictionary of [leverage tiers structures]{@link https://docs.ccxt.com/#/?id=leverage-tiers-structure}, indexed by market symbols
     */
    public async override Task<object> fetchLeverageTiers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object symbol = null;
        if (isTrue(!isEqual(symbols, null)))
        {
            market = this.market(getValue(symbols, 0));
            if (isTrue(getValue(market, "spot")))
            {
                throw new NotSupported ((string)add(this.id, " fetchLeverageTiers() is not supported for spot market")) ;
            }
            symbol = getValue(market, "symbol");
        }
        object data = await this.getLeverageTiersPaginated(symbol, this.extend(new Dictionary<string, object>() {
            { "paginate", true },
            { "paginationCalls", 50 },
        }, parameters));
        symbols = this.marketSymbols(symbols);
        return this.parseLeverageTiers(data, symbols, "symbol");
    }

    public override object parseLeverageTiers(object response, object symbols = null, object marketIdKey = null)
    {
        //
        //  [
        //      {
        //          "id": 1,
        //          "symbol": "BTCUSD",
        //          "riskLimitValue": "150",
        //          "maintenanceMargin": "0.5",
        //          "initialMargin": "1",
        //          "isLowestRisk": 1,
        //          "maxLeverage": "100.00"
        //      }
        //  ]
        //
        object tiers = new Dictionary<string, object>() {};
        object marketIds = this.marketIds(symbols);
        object filteredResults = this.filterByArray(response, marketIdKey, marketIds, false);
        object grouped = this.groupBy(filteredResults, marketIdKey);
        object keys = new List<object>(((IDictionary<string,object>)grouped).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object marketId = getValue(keys, i);
            object entry = getValue(grouped, marketId);
            for (object j = 0; isLessThan(j, getArrayLength(entry)); postFixIncrement(ref j))
            {
                object id = this.safeInteger(getValue(entry, j), "id");
                ((IDictionary<string,object>)getValue(entry, j))["id"] = id;
            }
            object market = this.safeMarket(marketId, null, null, "contract");
            object symbol = getValue(market, "symbol");
            ((IDictionary<string,object>)tiers)[(string)symbol] = this.parseMarketLeverageTiers(this.sortBy(entry, "id"), market);
        }
        return tiers;
    }

    public override object parseMarketLeverageTiers(object info, object market = null)
    {
        //
        //  [
        //      {
        //          "id": 1,
        //          "symbol": "BTCUSD",
        //          "riskLimitValue": "150",
        //          "maintenanceMargin": "0.5",
        //          "initialMargin": "1",
        //          "isLowestRisk": 1,
        //          "maxLeverage": "100.00"
        //      }
        //  ]
        //
        object tiers = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(info)); postFixIncrement(ref i))
        {
            object tier = getValue(info, i);
            object marketId = this.safeString(info, "symbol");
            market = this.safeMarket(marketId);
            object minNotional = this.parseNumber("0");
            if (isTrue(!isEqual(i, 0)))
            {
                minNotional = this.safeNumber(getValue(info, subtract(i, 1)), "riskLimitValue");
            }
            ((IList<object>)tiers).Add(new Dictionary<string, object>() {
                { "tier", this.safeInteger(tier, "id") },
                { "symbol", this.safeSymbol(marketId, market) },
                { "currency", getValue(market, "settle") },
                { "minNotional", minNotional },
                { "maxNotional", this.safeNumber(tier, "riskLimitValue") },
                { "maintenanceMarginRate", this.safeNumber(tier, "maintenanceMargin") },
                { "maxLeverage", this.safeNumber(tier, "maxLeverage") },
                { "info", tier },
            });
        }
        return tiers;
    }

    /**
     * @method
     * @name bybit#fetchFundingHistory
     * @description fetch the history of funding payments paid and received on this account
     * @see https://bybit-exchange.github.io/docs/api-explorer/v5/position/execution
     * @param {string} [symbol] unified market symbol
     * @param {int} [since] the earliest time in ms to fetch funding history for
     * @param {int} [limit] the maximum number of funding history structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [available parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {object} a [funding history structure]{@link https://docs.ccxt.com/#/?id=funding-history-structure}
     */
    public async override Task<object> fetchFundingHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchFundingHistory", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallCursor("fetchFundingHistory", symbol, since, limit, parameters, "nextPageCursor", "cursor", null, 100);
        }
        object request = new Dictionary<string, object>() {
            { "execType", "Funding" },
        };
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchFundingHistory", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        ((IDictionary<string,object>)request)["category"] = type;
        if (isTrue(!isEqual(symbol, null)))
        {
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["size"] = limit;
        } else
        {
            ((IDictionary<string,object>)request)["size"] = 100;
        }
        var requestparametersVariable = this.handleUntilOption("endTime", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.privateGetV5ExecutionList(this.extend(request, parameters));
        object fundings = this.addPaginationCursorToResult(response);
        return this.parseIncomes(fundings, market, since, limit);
    }

    public override object parseIncome(object income, object market = null)
    {
        //
        // {
        //     "symbol": "XMRUSDT",
        //     "orderType": "UNKNOWN",
        //     "underlyingPrice": "",
        //     "orderLinkId": "",
        //     "orderId": "a11e5fe2-1dbf-4bab-a9b2-af80a14efc5d",
        //     "stopOrderType": "UNKNOWN",
        //     "execTime": "1710950400000",
        //     "feeCurrency": "",
        //     "createType": "",
        //     "feeRate": "-0.000761",
        //     "tradeIv": "",
        //     "blockTradeId": "",
        //     "markPrice": "136.79",
        //     "execPrice": "137.11",
        //     "markIv": "",
        //     "orderQty": "0",
        //     "orderPrice": "0",
        //     "execValue": "134.3678",
        //     "closedSize": "0",
        //     "execType": "Funding",
        //     "seq": "28097658790",
        //     "side": "Sell",
        //     "indexPrice": "",
        //     "leavesQty": "0",
        //     "isMaker": false,
        //     "execFee": "-0.10232512",
        //     "execId": "8d1ef156-4ec6-4445-9a6c-1c0c24dbd046",
        //     "marketUnit": "",
        //     "execQty": "0.98",
        //     "nextPageCursor": "5774437%3A0%2C5771289%3A0"
        // }
        //
        object marketId = this.safeString(income, "symbol");
        market = this.safeMarket(marketId, market, null, "contract");
        object code = "USDT";
        if (isTrue(getValue(market, "inverse")))
        {
            code = getValue(market, "quote");
        }
        object timestamp = this.safeInteger(income, "execTime");
        return new Dictionary<string, object>() {
            { "info", income },
            { "symbol", this.safeSymbol(marketId, market, "-", "swap") },
            { "code", code },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "id", this.safeString(income, "execId") },
            { "amount", this.safeNumber(income, "execFee") },
            { "rate", this.safeNumber(income, "feeRate") },
        };
    }

    /**
     * @method
     * @name bybit#fetchOption
     * @description fetches option data that is commonly found in an option chain
     * @see https://bybit-exchange.github.io/docs/v5/market/tickers
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [option chain structure]{@link https://docs.ccxt.com/#/?id=option-chain-structure}
     */
    public async override Task<object> fetchOption(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "category", "option" },
            { "symbol", getValue(market, "id") },
        };
        object response = await this.publicGetV5MarketTickers(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "SUCCESS",
        //         "result": {
        //             "category": "option",
        //             "list": [
        //                 {
        //                     "symbol": "BTC-27DEC24-55000-P",
        //                     "bid1Price": "0",
        //                     "bid1Size": "0",
        //                     "bid1Iv": "0",
        //                     "ask1Price": "0",
        //                     "ask1Size": "0",
        //                     "ask1Iv": "0",
        //                     "lastPrice": "10980",
        //                     "highPrice24h": "0",
        //                     "lowPrice24h": "0",
        //                     "markPrice": "11814.66756236",
        //                     "indexPrice": "63838.92",
        //                     "markIv": "0.8866",
        //                     "underlyingPrice": "71690.55303594",
        //                     "openInterest": "0.01",
        //                     "turnover24h": "0",
        //                     "volume24h": "0",
        //                     "totalVolume": "2",
        //                     "totalTurnover": "78719",
        //                     "delta": "-0.23284954",
        //                     "gamma": "0.0000055",
        //                     "vega": "191.70757975",
        //                     "theta": "-30.43617927",
        //                     "predictedDeliveryPrice": "0",
        //                     "change24h": "0"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1711162003672
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object resultList = this.safeList(result, "list", new List<object>() {});
        object chain = this.safeDict(resultList, 0, new Dictionary<string, object>() {});
        return this.parseOption(chain, null, market);
    }

    /**
     * @method
     * @name bybit#fetchOptionChain
     * @description fetches data for an underlying asset that is commonly found in an option chain
     * @see https://bybit-exchange.github.io/docs/v5/market/tickers
     * @param {string} code base currency to fetch an option chain for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [option chain structures]{@link https://docs.ccxt.com/#/?id=option-chain-structure}
     */
    public async override Task<object> fetchOptionChain(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "category", "option" },
            { "baseCoin", getValue(currency, "id") },
        };
        object response = await this.publicGetV5MarketTickers(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "SUCCESS",
        //         "result": {
        //             "category": "option",
        //             "list": [
        //                 {
        //                     "symbol": "BTC-27DEC24-55000-P",
        //                     "bid1Price": "0",
        //                     "bid1Size": "0",
        //                     "bid1Iv": "0",
        //                     "ask1Price": "0",
        //                     "ask1Size": "0",
        //                     "ask1Iv": "0",
        //                     "lastPrice": "10980",
        //                     "highPrice24h": "0",
        //                     "lowPrice24h": "0",
        //                     "markPrice": "11814.66756236",
        //                     "indexPrice": "63838.92",
        //                     "markIv": "0.8866",
        //                     "underlyingPrice": "71690.55303594",
        //                     "openInterest": "0.01",
        //                     "turnover24h": "0",
        //                     "volume24h": "0",
        //                     "totalVolume": "2",
        //                     "totalTurnover": "78719",
        //                     "delta": "-0.23284954",
        //                     "gamma": "0.0000055",
        //                     "vega": "191.70757975",
        //                     "theta": "-30.43617927",
        //                     "predictedDeliveryPrice": "0",
        //                     "change24h": "0"
        //                 },
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1711162003672
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object resultList = this.safeList(result, "list", new List<object>() {});
        return this.parseOptionChain(resultList, null, "symbol");
    }

    public override object parseOption(object chain, object currency = null, object market = null)
    {
        //
        //     {
        //         "symbol": "BTC-27DEC24-55000-P",
        //         "bid1Price": "0",
        //         "bid1Size": "0",
        //         "bid1Iv": "0",
        //         "ask1Price": "0",
        //         "ask1Size": "0",
        //         "ask1Iv": "0",
        //         "lastPrice": "10980",
        //         "highPrice24h": "0",
        //         "lowPrice24h": "0",
        //         "markPrice": "11814.66756236",
        //         "indexPrice": "63838.92",
        //         "markIv": "0.8866",
        //         "underlyingPrice": "71690.55303594",
        //         "openInterest": "0.01",
        //         "turnover24h": "0",
        //         "volume24h": "0",
        //         "totalVolume": "2",
        //         "totalTurnover": "78719",
        //         "delta": "-0.23284954",
        //         "gamma": "0.0000055",
        //         "vega": "191.70757975",
        //         "theta": "-30.43617927",
        //         "predictedDeliveryPrice": "0",
        //         "change24h": "0"
        //     }
        //
        object marketId = this.safeString(chain, "symbol");
        market = this.safeMarket(marketId, market);
        return new Dictionary<string, object>() {
            { "info", chain },
            { "currency", null },
            { "symbol", getValue(market, "symbol") },
            { "timestamp", null },
            { "datetime", null },
            { "impliedVolatility", this.safeNumber(chain, "markIv") },
            { "openInterest", this.safeNumber(chain, "openInterest") },
            { "bidPrice", this.safeNumber(chain, "bid1Price") },
            { "askPrice", this.safeNumber(chain, "ask1Price") },
            { "midPrice", null },
            { "markPrice", this.safeNumber(chain, "markPrice") },
            { "lastPrice", this.safeNumber(chain, "lastPrice") },
            { "underlyingPrice", this.safeNumber(chain, "underlyingPrice") },
            { "change", this.safeNumber(chain, "change24h") },
            { "percentage", null },
            { "baseVolume", this.safeNumber(chain, "totalVolume") },
            { "quoteVolume", null },
        };
    }

    /**
     * @method
     * @name bybit#fetchPositionsHistory
     * @description fetches historical positions
     * @see https://bybit-exchange.github.io/docs/v5/position/close-pnl
     * @param {string[]} symbols a list of unified market symbols
     * @param {int} [since] timestamp in ms of the earliest position to fetch, params["until"] - since <= 7 days
     * @param {int} [limit] the maximum amount of records to fetch, default=50, max=100
     * @param {object} params extra parameters specific to the exchange api endpoint
     * @param {int} [params.until] timestamp in ms of the latest position to fetch, params["until"] - since <= 7 days
     * @param {string} [params.subType] 'linear' or 'inverse'
     * @returns {object[]} a list of [position structures]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPositionsHistory(object symbols = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object subType = null;
        object symbolsLength = 0;
        if (isTrue(!isEqual(symbols, null)))
        {
            symbolsLength = getArrayLength(symbols);
            if (isTrue(isGreaterThan(symbolsLength, 0)))
            {
                market = this.market(getValue(symbols, 0));
            }
        }
        object until = this.safeInteger(parameters, "until");
        var subTypeparametersVariable = this.handleSubTypeAndParams("fetchPositionsHistory", market, parameters, "linear");
        subType = ((IList<object>)subTypeparametersVariable)[0];
        parameters = ((IList<object>)subTypeparametersVariable)[1];
        parameters = this.omit(parameters, "until");
        object request = new Dictionary<string, object>() {
            { "category", subType },
        };
        if (isTrue(isTrue((!isEqual(symbols, null))) && isTrue((isEqual(symbolsLength, 1)))))
        {
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["endTime"] = until;
        }
        object response = await this.privateGetV5PositionClosedPnl(this.extend(request, parameters));
        //
        //    {
        //        retCode: '0',
        //        retMsg: 'OK',
        //        result: {
        //            nextPageCursor: '071749f3-a9fa-427b-b5ca-27b2f52b81de%3A1712717265566520788%2C071749f3-a9fa-427b-b5ca-27b2f52b81de%3A1712717265566520788',
        //            category: 'linear',
        //            list: [
        //                {
        //                    symbol: 'XRPUSDT',
        //                    orderType: 'Market',
        //                    leverage: '10',
        //                    updatedTime: '1712717265572',
        //                    side: 'Sell',
        //                    orderId: '071749f3-a9fa-427b-b5ca-27b2f52b81de',
        //                    closedPnl: '-0.00049568',
        //                    avgEntryPrice: '0.6045',
        //                    qty: '3',
        //                    cumEntryValue: '1.8135',
        //                    createdTime: '1712717265566',
        //                    orderPrice: '0.5744',
        //                    closedSize: '3',
        //                    avgExitPrice: '0.605',
        //                    execType: 'Trade',
        //                    fillCount: '1',
        //                    cumExitValue: '1.815'
        //                }
        //            ]
        //        },
        //        retExtInfo: {},
        //        time: '1712717286073'
        //    }
        //
        object result = this.safeDict(response, "result");
        object rawPositions = this.safeList(result, "list");
        object positions = this.parsePositions(rawPositions, symbols, parameters);
        return this.filterBySinceLimit(positions, since, limit);
    }

    /**
     * @method
     * @name bybit#fetchConvertCurrencies
     * @description fetches all available currencies that can be converted
     * @see https://bybit-exchange.github.io/docs/v5/asset/convert/convert-coin-list
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.accountType] eb_convert_uta, eb_convert_spot, eb_convert_funding, eb_convert_inverse, or eb_convert_contract
     * @returns {object} an associative dictionary of currencies
     */
    public async override Task<object> fetchConvertCurrencies(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object accountType = null;
        var enableUnifiedMarginenableUnifiedAccountVariable = await this.isUnifiedEnabled();
        var enableUnifiedMargin = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[0];
        var enableUnifiedAccount = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[1];
        object isUnifiedAccount = (isTrue(enableUnifiedMargin) || isTrue(enableUnifiedAccount));
        object accountTypeDefault = ((bool) isTrue(isUnifiedAccount)) ? "eb_convert_uta" : "eb_convert_spot";
        var accountTypeparametersVariable = this.handleOptionAndParams(parameters, "fetchConvertCurrencies", "accountType", accountTypeDefault);
        accountType = ((IList<object>)accountTypeparametersVariable)[0];
        parameters = ((IList<object>)accountTypeparametersVariable)[1];
        object request = new Dictionary<string, object>() {
            { "accountType", accountType },
        };
        object response = await this.privateGetV5AssetExchangeQueryCoinList(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "ok",
        //         "result": {
        //             "coins": [
        //                 {
        //                     "coin": "MATIC",
        //                     "fullName": "MATIC",
        //                     "icon": "https://s1.bycsi.com/app/assets/token/0552ae79c535c3095fa18f7b377dd2e9.svg",
        //                     "iconNight": "https://t1.bycsi.com/app/assets/token/f59301aef2d6ac2165c4c4603e672fb4.svg",
        //                     "accuracyLength": 8,
        //                     "coinType": "crypto",
        //                     "balance": "0",
        //                     "uBalance": "0",
        //                     "timePeriod": 0,
        //                     "singleFromMinLimit": "1.1",
        //                     "singleFromMaxLimit": "20001",
        //                     "singleToMinLimit": "0",
        //                     "singleToMaxLimit": "0",
        //                     "dailyFromMinLimit": "0",
        //                     "dailyFromMaxLimit": "0",
        //                     "dailyToMinLimit": "0",
        //                     "dailyToMaxLimit": "0",
        //                     "disableFrom": false,
        //                     "disableTo": false
        //                 },
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": 1727256416250
        //     }
        //
        object result = new Dictionary<string, object>() {};
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object coins = this.safeList(data, "coins", new List<object>() {});
        for (object i = 0; isLessThan(i, getArrayLength(coins)); postFixIncrement(ref i))
        {
            object entry = getValue(coins, i);
            object id = this.safeString(entry, "coin");
            object disableFrom = this.safeBool(entry, "disableFrom");
            object disableTo = this.safeBool(entry, "disableTo");
            object inactive = (isTrue(disableFrom) || isTrue(disableTo));
            object code = this.safeCurrencyCode(id);
            ((IDictionary<string,object>)result)[(string)code] = new Dictionary<string, object>() {
                { "info", entry },
                { "id", id },
                { "code", code },
                { "networks", null },
                { "type", this.safeString(entry, "coinType") },
                { "name", this.safeString(entry, "fullName") },
                { "active", !isTrue(inactive) },
                { "deposit", null },
                { "withdraw", this.safeNumber(entry, "balance") },
                { "fee", null },
                { "precision", null },
                { "limits", new Dictionary<string, object>() {
                    { "amount", new Dictionary<string, object>() {
                        { "min", this.safeNumber(entry, "singleFromMinLimit") },
                        { "max", this.safeNumber(entry, "singleFromMaxLimit") },
                    } },
                    { "withdraw", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "deposit", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                } },
                { "created", null },
            };
        }
        return result;
    }

    /**
     * @method
     * @name bybit#fetchConvertQuote
     * @description fetch a quote for converting from one currency to another
     * @see https://bybit-exchange.github.io/docs/v5/asset/convert/apply-quote
     * @param {string} fromCode the currency that you want to sell and convert from
     * @param {string} toCode the currency that you want to buy and convert into
     * @param {float} [amount] how much you want to trade in units of the from currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.accountType] eb_convert_uta, eb_convert_spot, eb_convert_funding, eb_convert_inverse, or eb_convert_contract
     * @returns {object} a [conversion structure]{@link https://docs.ccxt.com/#/?id=conversion-structure}
     */
    public async override Task<object> fetchConvertQuote(object fromCode, object toCode, object amount = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object accountType = null;
        var enableUnifiedMarginenableUnifiedAccountVariable = await this.isUnifiedEnabled();
        var enableUnifiedMargin = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[0];
        var enableUnifiedAccount = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[1];
        object isUnifiedAccount = (isTrue(enableUnifiedMargin) || isTrue(enableUnifiedAccount));
        object accountTypeDefault = ((bool) isTrue(isUnifiedAccount)) ? "eb_convert_uta" : "eb_convert_spot";
        var accountTypeparametersVariable = this.handleOptionAndParams(parameters, "fetchConvertQuote", "accountType", accountTypeDefault);
        accountType = ((IList<object>)accountTypeparametersVariable)[0];
        parameters = ((IList<object>)accountTypeparametersVariable)[1];
        object request = new Dictionary<string, object>() {
            { "fromCoin", fromCode },
            { "toCoin", toCode },
            { "requestAmount", this.numberToString(amount) },
            { "requestCoin", fromCode },
            { "accountType", accountType },
        };
        object response = await this.privatePostV5AssetExchangeQuoteApply(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "ok",
        //         "result": {
        //             "quoteTxId": "1010020692439481682687668224",
        //             "exchangeRate": "0.000015330836780000",
        //             "fromCoin": "USDT",
        //             "fromCoinType": "crypto",
        //             "toCoin": "BTC",
        //             "toCoinType": "crypto",
        //             "fromAmount": "10",
        //             "toAmount": "0.000153308367800000",
        //             "expiredTime": "*************",
        //             "requestId": ""
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object fromCurrencyId = this.safeString(data, "fromCoin", fromCode);
        object fromCurrency = this.currency(fromCurrencyId);
        object toCurrencyId = this.safeString(data, "toCoin", toCode);
        object toCurrency = this.currency(toCurrencyId);
        return this.parseConversion(data, fromCurrency, toCurrency);
    }

    /**
     * @method
     * @name bybit#createConvertTrade
     * @description convert from one currency to another
     * @see https://bybit-exchange.github.io/docs/v5/asset/convert/confirm-quote
     * @param {string} id the id of the trade that you want to make
     * @param {string} fromCode the currency that you want to sell and convert from
     * @param {string} toCode the currency that you want to buy and convert into
     * @param {float} amount how much you want to trade in units of the from currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [conversion structure]{@link https://docs.ccxt.com/#/?id=conversion-structure}
     */
    public async override Task<object> createConvertTrade(object id, object fromCode, object toCode, object amount = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "quoteTxId", id },
        };
        object response = await this.privatePostV5AssetExchangeConvertExecute(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "ok",
        //         "result": {
        //             "exchangeStatus": "processing",
        //             "quoteTxId": "1010020692439483803499737088"
        //         },
        //         "retExtInfo": {},
        //         "time": 1727257904969
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseConversion(data);
    }

    /**
     * @method
     * @name bybit#fetchConvertTrade
     * @description fetch the data for a conversion trade
     * @see https://bybit-exchange.github.io/docs/v5/asset/convert/get-convert-result
     * @param {string} id the id of the trade that you want to fetch
     * @param {string} [code] the unified currency code of the conversion trade
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.accountType] eb_convert_uta, eb_convert_spot, eb_convert_funding, eb_convert_inverse, or eb_convert_contract
     * @returns {object} a [conversion structure]{@link https://docs.ccxt.com/#/?id=conversion-structure}
     */
    public async override Task<object> fetchConvertTrade(object id, object code = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object accountType = null;
        var enableUnifiedMarginenableUnifiedAccountVariable = await this.isUnifiedEnabled();
        var enableUnifiedMargin = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[0];
        var enableUnifiedAccount = ((IList<object>) enableUnifiedMarginenableUnifiedAccountVariable)[1];
        object isUnifiedAccount = (isTrue(enableUnifiedMargin) || isTrue(enableUnifiedAccount));
        object accountTypeDefault = ((bool) isTrue(isUnifiedAccount)) ? "eb_convert_uta" : "eb_convert_spot";
        var accountTypeparametersVariable = this.handleOptionAndParams(parameters, "fetchConvertQuote", "accountType", accountTypeDefault);
        accountType = ((IList<object>)accountTypeparametersVariable)[0];
        parameters = ((IList<object>)accountTypeparametersVariable)[1];
        object request = new Dictionary<string, object>() {
            { "quoteTxId", id },
            { "accountType", accountType },
        };
        object response = await this.privateGetV5AssetExchangeConvertResultQuery(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "ok",
        //         "result": {
        //             "result": {
        //                 "accountType": "eb_convert_uta",
        //                 "exchangeTxId": "1010020692439483803499737088",
        //                 "userId": "*********",
        //                 "fromCoin": "USDT",
        //                 "fromCoinType": "crypto",
        //                 "fromAmount": "10",
        //                 "toCoin": "BTC",
        //                 "toCoinType": "crypto",
        //                 "toAmount": "0.***********",
        //                 "exchangeStatus": "success",
        //                 "extInfo": {},
        //                 "convertRate": "0.0***********",
        //                 "createdAt": "*************"
        //             }
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object result = this.safeDict(data, "result", new Dictionary<string, object>() {});
        object fromCurrencyId = this.safeString(result, "fromCoin");
        object toCurrencyId = this.safeString(result, "toCoin");
        object fromCurrency = null;
        object toCurrency = null;
        if (isTrue(!isEqual(fromCurrencyId, null)))
        {
            fromCurrency = this.currency(fromCurrencyId);
        }
        if (isTrue(!isEqual(toCurrencyId, null)))
        {
            toCurrency = this.currency(toCurrencyId);
        }
        return this.parseConversion(result, fromCurrency, toCurrency);
    }

    /**
     * @method
     * @name bybit#fetchConvertTradeHistory
     * @description fetch the users history of conversion trades
     * @see https://bybit-exchange.github.io/docs/v5/asset/convert/get-convert-history
     * @param {string} [code] the unified currency code
     * @param {int} [since] the earliest time in ms to fetch conversions for
     * @param {int} [limit] the maximum number of conversion structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.accountType] eb_convert_uta, eb_convert_spot, eb_convert_funding, eb_convert_inverse, or eb_convert_contract
     * @returns {object[]} a list of [conversion structures]{@link https://docs.ccxt.com/#/?id=conversion-structure}
     */
    public async override Task<object> fetchConvertTradeHistory(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.privateGetV5AssetExchangeQueryConvertHistory(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "ok",
        //         "result": {
        //             "list": [
        //                 {
        //                     "accountType": "eb_convert_uta",
        //                     "exchangeTxId": "1010020692439483803499737088",
        //                     "userId": "*********",
        //                     "fromCoin": "USDT",
        //                     "fromCoinType": "crypto",
        //                     "fromAmount": "10",
        //                     "toCoin": "BTC",
        //                     "toCoinType": "crypto",
        //                     "toAmount": "0.***********",
        //                     "exchangeStatus": "success",
        //                     "extInfo": {},
        //                     "convertRate": "0.0***********",
        //                     "createdAt": "*************"
        //                 }
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object dataList = this.safeList(data, "list", new List<object>() {});
        return this.parseConversions(dataList, code, "fromCoin", "toCoin", since, limit);
    }

    public override object parseConversion(object conversion, object fromCurrency = null, object toCurrency = null)
    {
        //
        // fetchConvertQuote
        //
        //     {
        //         "quoteTxId": "1010020692439481682687668224",
        //         "exchangeRate": "0.000015330836780000",
        //         "fromCoin": "USDT",
        //         "fromCoinType": "crypto",
        //         "toCoin": "BTC",
        //         "toCoinType": "crypto",
        //         "fromAmount": "10",
        //         "toAmount": "0.000153308367800000",
        //         "expiredTime": "*************",
        //         "requestId": ""
        //     }
        //
        // createConvertTrade
        //
        //     {
        //         "exchangeStatus": "processing",
        //         "quoteTxId": "1010020692439483803499737088"
        //     }
        //
        // fetchConvertTrade, fetchConvertTradeHistory
        //
        //     {
        //         "accountType": "eb_convert_uta",
        //         "exchangeTxId": "1010020692439483803499737088",
        //         "userId": "*********",
        //         "fromCoin": "USDT",
        //         "fromCoinType": "crypto",
        //         "fromAmount": "10",
        //         "toCoin": "BTC",
        //         "toCoinType": "crypto",
        //         "toAmount": "0.***********",
        //         "exchangeStatus": "success",
        //         "extInfo": {},
        //         "convertRate": "0.0***********",
        //         "createdAt": "*************"
        //     }
        //
        object timestamp = this.safeInteger2(conversion, "expiredTime", "createdAt");
        object fromCoin = this.safeString(conversion, "fromCoin");
        object fromCode = this.safeCurrencyCode(fromCoin, fromCurrency);
        object to = this.safeString(conversion, "toCoin");
        object toCode = this.safeCurrencyCode(to, toCurrency);
        return new Dictionary<string, object>() {
            { "info", conversion },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "id", this.safeString2(conversion, "quoteTxId", "exchangeTxId") },
            { "fromCurrency", fromCode },
            { "fromAmount", this.safeNumber(conversion, "fromAmount") },
            { "toCurrency", toCode },
            { "toAmount", this.safeNumber(conversion, "toAmount") },
            { "price", null },
            { "fee", null },
        };
    }

    /**
     * @method
     * @name bybit#fetchLongShortRatioHistory
     * @description fetches the long short ratio history for a unified market symbol
     * @see https://bybit-exchange.github.io/docs/v5/market/long-short-ratio
     * @param {string} symbol unified symbol of the market to fetch the long short ratio for
     * @param {string} [timeframe] the period for the ratio, default is 24 hours
     * @param {int} [since] the earliest time in ms to fetch ratios for
     * @param {int} [limit] the maximum number of long short ratio structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of [long short ratio structures]{@link https://docs.ccxt.com/#/?id=long-short-ratio-structure}
     */
    public async override Task<object> fetchLongShortRatioHistory(object symbol = null, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object type = null;
        var typeparametersVariable = this.getBybitType("fetchLongShortRatioHistory", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        if (isTrue(isTrue(isEqual(type, "spot")) || isTrue(isEqual(type, "option"))))
        {
            throw new NotSupported ((string)add(this.id, " fetchLongShortRatioHistory() only support linear and inverse markets")) ;
        }
        if (isTrue(isEqual(timeframe, null)))
        {
            timeframe = "1d";
        }
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "period", timeframe },
            { "category", type },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.publicGetV5MarketAccountRatio(this.extend(request, parameters));
        //
        //     {
        //         "retCode": 0,
        //         "retMsg": "OK",
        //         "result": {
        //             "list": [
        //                 {
        //                     "symbol": "BTCUSDT",
        //                     "buyRatio": "0.5707",
        //                     "sellRatio": "0.4293",
        //                     "timestamp": "*************"
        //                 },
        //             ]
        //         },
        //         "retExtInfo": {},
        //         "time": *************
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "list", new List<object>() {});
        return this.parseLongShortRatioHistory(data, market);
    }

    public override object parseLongShortRatio(object info, object market = null)
    {
        //
        //     {
        //         "symbol": "BTCUSDT",
        //         "buyRatio": "0.5707",
        //         "sellRatio": "0.4293",
        //         "timestamp": "*************"
        //     }
        //
        object marketId = this.safeString(info, "symbol");
        object timestamp = this.safeIntegerOmitZero(info, "timestamp");
        object longString = this.safeString(info, "buyRatio");
        object shortString = this.safeString(info, "sellRatio");
        return new Dictionary<string, object>() {
            { "info", info },
            { "symbol", this.safeSymbol(marketId, market, null, "contract") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "timeframe", null },
            { "longShortRatio", this.parseToNumeric(Precise.stringDiv(longString, shortString)) },
        };
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object url = add(add(this.implodeHostname(getValue(getValue(this.urls, "api"), api)), "/"), path);
        if (isTrue(isEqual(api, "public")))
        {
            if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)parameters).Keys))))
            {
                url = add(url, add("?", this.rawencode(parameters)));
            }
        } else if (isTrue(isEqual(api, "private")))
        {
            this.checkRequiredCredentials();
            object isOpenapi = isGreaterThanOrEqual(getIndexOf(url, "openapi"), 0);
            object isV3UnifiedMargin = isGreaterThanOrEqual(getIndexOf(url, "unified/v3"), 0);
            object isV3Contract = isGreaterThanOrEqual(getIndexOf(url, "contract/v3"), 0);
            object isV5UnifiedAccount = isGreaterThanOrEqual(getIndexOf(url, "v5"), 0);
            object timestamp = ((object)this.nonce()).ToString();
            if (isTrue(isOpenapi))
            {
                if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)parameters).Keys))))
                {
                    body = this.json(parameters);
                } else
                {
                    // this fix for PHP is required otherwise it generates
                    // '[]' on empty arrays even when forced to use objects
                    body = "{}";
                }
                object payload = add(add(timestamp, this.apiKey), body);
                object signature = this.hmac(this.encode(payload), this.encode(this.secret), sha256, "hex");
                headers = new Dictionary<string, object>() {
                    { "Content-Type", "application/json" },
                    { "X-BAPI-API-KEY", this.apiKey },
                    { "X-BAPI-TIMESTAMP", timestamp },
                    { "X-BAPI-SIGN", signature },
                };
            } else if (isTrue(isTrue(isTrue(isV3UnifiedMargin) || isTrue(isV3Contract)) || isTrue(isV5UnifiedAccount)))
            {
                headers = new Dictionary<string, object>() {
                    { "Content-Type", "application/json" },
                    { "X-BAPI-API-KEY", this.apiKey },
                    { "X-BAPI-TIMESTAMP", timestamp },
                    { "X-BAPI-RECV-WINDOW", ((object)getValue(this.options, "recvWindow")).ToString() },
                };
                if (isTrue(isTrue(isV3UnifiedMargin) || isTrue(isV3Contract)))
                {
                    ((IDictionary<string,object>)headers)["X-BAPI-SIGN-TYPE"] = "2";
                }
                object query = this.extend(new Dictionary<string, object>() {}, parameters);
                object queryEncoded = this.rawencode(query);
                object auth_base = add(add(((object)timestamp).ToString(), this.apiKey), ((object)getValue(this.options, "recvWindow")).ToString());
                object authFull = null;
                if (isTrue(isEqual(method, "POST")))
                {
                    body = this.json(query);
                    authFull = add(auth_base, body);
                } else
                {
                    authFull = add(auth_base, queryEncoded);
                    url = add(url, add("?", queryEncoded));
                }
                object signature = null;
                if (isTrue(isGreaterThan(getIndexOf(this.secret, "PRIVATE KEY"), -1)))
                {
                    signature = rsa(authFull, this.secret, sha256);
                } else
                {
                    signature = this.hmac(this.encode(authFull), this.encode(this.secret), sha256);
                }
                ((IDictionary<string,object>)headers)["X-BAPI-SIGN"] = signature;
            } else
            {
                object query = this.extend(parameters, new Dictionary<string, object>() {
                    { "api_key", this.apiKey },
                    { "recv_window", getValue(this.options, "recvWindow") },
                    { "timestamp", timestamp },
                });
                object sortedQuery = this.keysort(query);
                object auth = this.rawencode(sortedQuery, true);
                object signature = null;
                if (isTrue(isGreaterThan(getIndexOf(this.secret, "PRIVATE KEY"), -1)))
                {
                    signature = rsa(auth, this.secret, sha256);
                } else
                {
                    signature = this.hmac(this.encode(auth), this.encode(this.secret), sha256);
                }
                if (isTrue(isEqual(method, "POST")))
                {
                    object isSpot = isGreaterThanOrEqual(getIndexOf(url, "spot"), 0);
                    object extendedQuery = this.extend(query, new Dictionary<string, object>() {
                        { "sign", signature },
                    });
                    if (isTrue(isSpot))
                    {
                        body = this.urlencode(extendedQuery);
                        headers = new Dictionary<string, object>() {
                            { "Content-Type", "application/x-www-form-urlencoded" },
                        };
                    } else
                    {
                        body = this.json(extendedQuery);
                        headers = new Dictionary<string, object>() {
                            { "Content-Type", "application/json" },
                        };
                    }
                } else
                {
                    url = add(url, add("?", this.rawencode(sortedQuery, true)));
                    url = add(url, add("&sign=", signature));
                }
            }
        }
        if (isTrue(isEqual(method, "POST")))
        {
            object brokerId = this.safeString(this.options, "brokerId");
            if (isTrue(!isEqual(brokerId, null)))
            {
                ((IDictionary<string,object>)headers)["Referer"] = brokerId;
            }
        }
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object httpCode, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        if (!isTrue(response))
        {
            return null;  // fallback to default error handler
        }
        //
        //     {
        //         "ret_code": 10001,
        //         "ret_msg": "ReadMapCB: expect { or n, but found \u0000, error " +
        //         "found in #0 byte of ...||..., bigger context " +
        //         "...||...",
        //         "ext_code": '',
        //         "ext_info": '',
        //         "result": null,
        //         "time_now": "1583934106.590436"
        //     }
        //
        //     {
        //         "retCode":10001,
        //         "retMsg":"symbol params err",
        //         "result":{"symbol":"","bid":"","bidIv":"","bidSize":"","ask":"","askIv":"","askSize":"","lastPrice":"","openInterest":"","indexPrice":"","markPrice":"","markPriceIv":"","change24h":"","high24h":"","low24h":"","volume24h":"","turnover24h":"","totalVolume":"","totalTurnover":"","fundingRate":"","predictedFundingRate":"","nextFundingTime":"","countdownHour":"0","predictedDeliveryPrice":"","underlyingPrice":"","delta":"","gamma":"","vega":"","theta":""}
        //     }
        //
        object errorCode = this.safeString2(response, "ret_code", "retCode");
        if (isTrue(!isEqual(errorCode, "0")))
        {
            if (isTrue(isEqual(errorCode, "30084")))
            {
                // not an error
                // https://github.com/ccxt/ccxt/issues/11268
                // https://github.com/ccxt/ccxt/pull/11624
                // POST https://api.bybit.com/v2/private/position/switch-isolated 200 OK
                // {"ret_code":30084,"ret_msg":"Isolated not modified","ext_code":"","ext_info":"","result":null,"time_now":"**********.937988","rate_limit_status":73,"rate_limit_reset_ms":**********894,"rate_limit":75}
                return null;
            }
            object feedback = null;
            if (isTrue(isTrue(isEqual(errorCode, "10005")) && isTrue(isLessThan(getIndexOf(url, "order"), 0))))
            {
                feedback = add(add(this.id, " private api uses /user/v3/private/query-api to check if you have a unified account. The API key of user id must own one of permissions: \"Account Transfer\", \"Subaccount Transfer\", \"Withdrawal\" "), body);
            } else
            {
                feedback = add(add(this.id, " "), body);
            }
            if (isTrue(isGreaterThan(getIndexOf(body, "Withdraw address chain or destination tag are not equal"), -1)))
            {
                feedback = add(feedback, "; You might also need to ensure the address is whitelisted");
            }
            this.throwBroadlyMatchedException(getValue(this.exceptions, "broad"), body, feedback);
            this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), errorCode, feedback);
            throw new ExchangeError ((string)feedback) ;
        }
        return null;
    }
}
