namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class cryptocom : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "cryptocom" },
            { "name", "Crypto.com" },
            { "countries", new List<object>() {"MT"} },
            { "version", "v2" },
            { "rateLimit", 10 },
            { "certified", true },
            { "pro", true },
            { "has", new Dictionary<string, object>() {
                { "CORS", false },
                { "spot", true },
                { "margin", true },
                { "swap", true },
                { "future", true },
                { "option", true },
                { "addMargin", false },
                { "cancelAllOrders", true },
                { "cancelOrder", true },
                { "cancelOrders", true },
                { "cancelOrdersForSymbols", true },
                { "closeAllPositions", false },
                { "closePosition", true },
                { "createMarketBuyOrderWithCost", false },
                { "createMarketOrderWithCost", false },
                { "createMarketSellOrderWithCost", false },
                { "createOrder", true },
                { "createOrders", true },
                { "createStopOrder", true },
                { "createTriggerOrder", true },
                { "editOrder", true },
                { "fetchAccounts", true },
                { "fetchBalance", true },
                { "fetchBidsAsks", false },
                { "fetchBorrowInterest", false },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchClosedOrders", "emulated" },
                { "fetchCrossBorrowRate", false },
                { "fetchCrossBorrowRates", false },
                { "fetchCurrencies", true },
                { "fetchDepositAddress", true },
                { "fetchDepositAddresses", false },
                { "fetchDepositAddressesByNetwork", true },
                { "fetchDeposits", true },
                { "fetchDepositsWithdrawals", false },
                { "fetchDepositWithdrawFee", "emulated" },
                { "fetchDepositWithdrawFees", true },
                { "fetchFundingHistory", false },
                { "fetchFundingRate", true },
                { "fetchFundingRateHistory", true },
                { "fetchFundingRates", false },
                { "fetchGreeks", false },
                { "fetchIndexOHLCV", false },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchLedger", true },
                { "fetchLeverage", false },
                { "fetchLeverageTiers", false },
                { "fetchMarginAdjustmentHistory", false },
                { "fetchMarginMode", false },
                { "fetchMarketLeverageTiers", false },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMySettlementHistory", false },
                { "fetchMyTrades", true },
                { "fetchOHLCV", true },
                { "fetchOpenOrders", true },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchOrders", true },
                { "fetchPosition", true },
                { "fetchPositionHistory", false },
                { "fetchPositionMode", false },
                { "fetchPositions", true },
                { "fetchPositionsHistory", false },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchSettlementHistory", true },
                { "fetchStatus", false },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTime", false },
                { "fetchTrades", true },
                { "fetchTradingFee", true },
                { "fetchTradingFees", true },
                { "fetchTransactionFees", false },
                { "fetchTransactions", false },
                { "fetchTransfers", false },
                { "fetchUnderlyingAssets", false },
                { "fetchVolatilityHistory", false },
                { "fetchWithdrawals", true },
                { "reduceMargin", false },
                { "repayCrossMargin", false },
                { "repayIsolatedMargin", false },
                { "sandbox", true },
                { "setLeverage", false },
                { "setMarginMode", false },
                { "setPositionMode", false },
                { "transfer", false },
                { "withdraw", true },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", "1m" },
                { "5m", "5m" },
                { "15m", "15m" },
                { "30m", "30m" },
                { "1h", "1h" },
                { "4h", "4h" },
                { "6h", "6h" },
                { "12h", "12h" },
                { "1d", "1D" },
                { "1w", "7D" },
                { "2w", "14D" },
                { "1M", "1M" },
            } },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://user-images.githubusercontent.com/1294454/147792121-38ed5e36-c229-48d6-b49a-48d05fc19ed4.jpeg" },
                { "test", new Dictionary<string, object>() {
                    { "v1", "https://uat-api.3ona.co/exchange/v1" },
                    { "v2", "https://uat-api.3ona.co/v2" },
                    { "derivatives", "https://uat-api.3ona.co/v2" },
                } },
                { "api", new Dictionary<string, object>() {
                    { "base", "https://api.crypto.com" },
                    { "v1", "https://api.crypto.com/exchange/v1" },
                    { "v2", "https://api.crypto.com/v2" },
                    { "derivatives", "https://deriv-api.crypto.com/v1" },
                } },
                { "www", "https://crypto.com/" },
                { "referral", new Dictionary<string, object>() {
                    { "url", "https://crypto.com/exch/kdacthrnxt" },
                    { "discount", 0.75 },
                } },
                { "doc", new List<object>() {"https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html", "https://exchange-docs.crypto.com/spot/index.html", "https://exchange-docs.crypto.com/derivatives/index.html"} },
                { "fees", "https://crypto.com/exchange/document/fees-limits" },
            } },
            { "api", new Dictionary<string, object>() {
                { "base", new Dictionary<string, object>() {
                    { "public", new Dictionary<string, object>() {
                        { "get", new Dictionary<string, object>() {
                            { "v1/public/get-announcements", 1 },
                        } },
                    } },
                } },
                { "v1", new Dictionary<string, object>() {
                    { "public", new Dictionary<string, object>() {
                        { "get", new Dictionary<string, object>() {
                            { "public/auth", divide(10, 3) },
                            { "public/get-instruments", divide(10, 3) },
                            { "public/get-book", 1 },
                            { "public/get-candlestick", 1 },
                            { "public/get-trades", 1 },
                            { "public/get-tickers", 1 },
                            { "public/get-valuations", 1 },
                            { "public/get-expired-settlement-price", divide(10, 3) },
                            { "public/get-insurance", 1 },
                            { "public/get-risk-parameters", 1 },
                        } },
                        { "post", new Dictionary<string, object>() {
                            { "public/staking/get-conversion-rate", 2 },
                        } },
                    } },
                    { "private", new Dictionary<string, object>() {
                        { "post", new Dictionary<string, object>() {
                            { "private/set-cancel-on-disconnect", divide(10, 3) },
                            { "private/get-cancel-on-disconnect", divide(10, 3) },
                            { "private/user-balance", divide(10, 3) },
                            { "private/user-balance-history", divide(10, 3) },
                            { "private/get-positions", divide(10, 3) },
                            { "private/create-order", divide(2, 3) },
                            { "private/amend-order", divide(4, 3) },
                            { "private/create-order-list", divide(10, 3) },
                            { "private/cancel-order", divide(2, 3) },
                            { "private/cancel-order-list", divide(10, 3) },
                            { "private/cancel-all-orders", divide(2, 3) },
                            { "private/close-position", divide(10, 3) },
                            { "private/get-order-history", 100 },
                            { "private/get-open-orders", divide(10, 3) },
                            { "private/get-order-detail", divide(1, 3) },
                            { "private/get-trades", 100 },
                            { "private/change-account-leverage", divide(10, 3) },
                            { "private/get-transactions", divide(10, 3) },
                            { "private/create-subaccount-transfer", divide(10, 3) },
                            { "private/get-subaccount-balances", divide(10, 3) },
                            { "private/get-order-list", divide(10, 3) },
                            { "private/create-withdrawal", divide(10, 3) },
                            { "private/get-currency-networks", divide(10, 3) },
                            { "private/get-deposit-address", divide(10, 3) },
                            { "private/get-accounts", divide(10, 3) },
                            { "private/get-withdrawal-history", divide(10, 3) },
                            { "private/get-deposit-history", divide(10, 3) },
                            { "private/get-fee-rate", 2 },
                            { "private/get-instrument-fee-rate", 2 },
                            { "private/staking/stake", 2 },
                            { "private/staking/unstake", 2 },
                            { "private/staking/get-staking-position", 2 },
                            { "private/staking/get-staking-instruments", 2 },
                            { "private/staking/get-open-stake", 2 },
                            { "private/staking/get-stake-history", 2 },
                            { "private/staking/get-reward-history", 2 },
                            { "private/staking/convert", 2 },
                            { "private/staking/get-open-convert", 2 },
                            { "private/staking/get-convert-history", 2 },
                        } },
                    } },
                } },
                { "v2", new Dictionary<string, object>() {
                    { "public", new Dictionary<string, object>() {
                        { "get", new Dictionary<string, object>() {
                            { "public/auth", 1 },
                            { "public/get-instruments", 1 },
                            { "public/get-book", 1 },
                            { "public/get-candlestick", 1 },
                            { "public/get-ticker", 1 },
                            { "public/get-trades", 1 },
                            { "public/margin/get-transfer-currencies", 1 },
                            { "public/margin/get-load-currenices", 1 },
                            { "public/respond-heartbeat", 1 },
                        } },
                    } },
                    { "private", new Dictionary<string, object>() {
                        { "post", new Dictionary<string, object>() {
                            { "private/set-cancel-on-disconnect", divide(10, 3) },
                            { "private/get-cancel-on-disconnect", divide(10, 3) },
                            { "private/create-withdrawal", divide(10, 3) },
                            { "private/get-withdrawal-history", divide(10, 3) },
                            { "private/get-currency-networks", divide(10, 3) },
                            { "private/get-deposit-history", divide(10, 3) },
                            { "private/get-deposit-address", divide(10, 3) },
                            { "private/export/create-export-request", divide(10, 3) },
                            { "private/export/get-export-requests", divide(10, 3) },
                            { "private/export/download-export-output", divide(10, 3) },
                            { "private/get-account-summary", divide(10, 3) },
                            { "private/create-order", divide(2, 3) },
                            { "private/cancel-order", divide(2, 3) },
                            { "private/cancel-all-orders", divide(2, 3) },
                            { "private/create-order-list", divide(10, 3) },
                            { "private/get-order-history", divide(10, 3) },
                            { "private/get-open-orders", divide(10, 3) },
                            { "private/get-order-detail", divide(1, 3) },
                            { "private/get-trades", 100 },
                            { "private/get-accounts", divide(10, 3) },
                            { "private/get-subaccount-balances", divide(10, 3) },
                            { "private/create-subaccount-transfer", divide(10, 3) },
                            { "private/otc/get-otc-user", divide(10, 3) },
                            { "private/otc/get-instruments", divide(10, 3) },
                            { "private/otc/request-quote", 100 },
                            { "private/otc/accept-quote", 100 },
                            { "private/otc/get-quote-history", divide(10, 3) },
                            { "private/otc/get-trade-history", divide(10, 3) },
                            { "private/otc/create-order", divide(10, 3) },
                        } },
                    } },
                } },
                { "derivatives", new Dictionary<string, object>() {
                    { "public", new Dictionary<string, object>() {
                        { "get", new Dictionary<string, object>() {
                            { "public/auth", divide(10, 3) },
                            { "public/get-instruments", divide(10, 3) },
                            { "public/get-book", 1 },
                            { "public/get-candlestick", 1 },
                            { "public/get-trades", 1 },
                            { "public/get-tickers", 1 },
                            { "public/get-valuations", 1 },
                            { "public/get-expired-settlement-price", divide(10, 3) },
                            { "public/get-insurance", 1 },
                        } },
                    } },
                    { "private", new Dictionary<string, object>() {
                        { "post", new Dictionary<string, object>() {
                            { "private/set-cancel-on-disconnect", divide(10, 3) },
                            { "private/get-cancel-on-disconnect", divide(10, 3) },
                            { "private/user-balance", divide(10, 3) },
                            { "private/user-balance-history", divide(10, 3) },
                            { "private/get-positions", divide(10, 3) },
                            { "private/create-order", divide(2, 3) },
                            { "private/create-order-list", divide(10, 3) },
                            { "private/cancel-order", divide(2, 3) },
                            { "private/cancel-order-list", divide(10, 3) },
                            { "private/cancel-all-orders", divide(2, 3) },
                            { "private/close-position", divide(10, 3) },
                            { "private/convert-collateral", divide(10, 3) },
                            { "private/get-order-history", 100 },
                            { "private/get-open-orders", divide(10, 3) },
                            { "private/get-order-detail", divide(1, 3) },
                            { "private/get-trades", 100 },
                            { "private/change-account-leverage", divide(10, 3) },
                            { "private/get-transactions", divide(10, 3) },
                            { "private/create-subaccount-transfer", divide(10, 3) },
                            { "private/get-subaccount-balances", divide(10, 3) },
                            { "private/get-order-list", divide(10, 3) },
                        } },
                    } },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "maker", this.parseNumber("0.004") },
                    { "taker", this.parseNumber("0.004") },
                    { "tiers", new Dictionary<string, object>() {
                        { "maker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.004")}, new List<object> {this.parseNumber("25000"), this.parseNumber("0.0035")}, new List<object> {this.parseNumber("50000"), this.parseNumber("0.0015")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.001")}, new List<object> {this.parseNumber("250000"), this.parseNumber("0.0009")}, new List<object> {this.parseNumber("1000000"), this.parseNumber("0.0008")}, new List<object> {this.parseNumber("********"), this.parseNumber("0.0007")}, new List<object> {this.parseNumber("*********"), this.parseNumber("0.0006")}, new List<object> {this.parseNumber("*********"), this.parseNumber("0.0004")}} },
                        { "taker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.004")}, new List<object> {this.parseNumber("25000"), this.parseNumber("0.0035")}, new List<object> {this.parseNumber("50000"), this.parseNumber("0.0025")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.0016")}, new List<object> {this.parseNumber("250000"), this.parseNumber("0.00015")}, new List<object> {this.parseNumber("1000000"), this.parseNumber("0.00014")}, new List<object> {this.parseNumber("********"), this.parseNumber("0.00013")}, new List<object> {this.parseNumber("*********"), this.parseNumber("0.00012")}, new List<object> {this.parseNumber("*********"), this.parseNumber("0.0001")}} },
                    } },
                } },
            } },
            { "options", new Dictionary<string, object>() {
                { "defaultType", "spot" },
                { "accountsById", new Dictionary<string, object>() {
                    { "funding", "SPOT" },
                    { "spot", "SPOT" },
                    { "margin", "MARGIN" },
                    { "derivatives", "DERIVATIVES" },
                    { "swap", "DERIVATIVES" },
                    { "future", "DERIVATIVES" },
                } },
                { "networks", new Dictionary<string, object>() {
                    { "BEP20", "BSC" },
                    { "ERC20", "ETH" },
                    { "TRC20", "TRON" },
                } },
                { "broker", "CCXT" },
            } },
            { "features", new Dictionary<string, object>() {
                { "default", new Dictionary<string, object>() {
                    { "sandbox", true },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", true },
                        { "triggerPrice", true },
                        { "triggerPriceType", new Dictionary<string, object>() {
                            { "last", true },
                            { "mark", true },
                            { "index", true },
                        } },
                        { "triggerDirection", false },
                        { "stopLossPrice", true },
                        { "takeProfitPrice", true },
                        { "attachedStopLossTakeProfit", null },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", true },
                            { "PO", true },
                            { "GTD", false },
                        } },
                        { "hedged", false },
                        { "selfTradePrevention", true },
                        { "trailing", false },
                        { "iceberg", false },
                        { "leverage", false },
                        { "marketBuyByCost", true },
                        { "marketBuyRequiresPrice", true },
                    } },
                    { "createOrders", new Dictionary<string, object>() {
                        { "max", 10 },
                    } },
                    { "fetchMyTrades", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 100 },
                        { "daysBack", null },
                        { "untilDays", 1 },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", true },
                        { "limit", 100 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 100 },
                        { "daysBack", null },
                        { "untilDays", 1 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchClosedOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 100 },
                        { "daysBack", null },
                        { "daysBackCanceled", null },
                        { "untilDays", 1 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 300 },
                    } },
                } },
                { "spot", new Dictionary<string, object>() {
                    { "extends", "default" },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                    { "inverse", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                    { "inverse", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                } },
            } },
            { "commonCurrencies", new Dictionary<string, object>() {
                { "USD_STABLE_COIN", "USDC" },
            } },
            { "precisionMode", TICK_SIZE },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "219", typeof(InvalidOrder) },
                    { "306", typeof(InsufficientFunds) },
                    { "314", typeof(InvalidOrder) },
                    { "325", typeof(InvalidOrder) },
                    { "415", typeof(InvalidOrder) },
                    { "10001", typeof(ExchangeError) },
                    { "10002", typeof(PermissionDenied) },
                    { "10003", typeof(PermissionDenied) },
                    { "10004", typeof(BadRequest) },
                    { "10005", typeof(PermissionDenied) },
                    { "10006", typeof(DDoSProtection) },
                    { "10007", typeof(InvalidNonce) },
                    { "10008", typeof(BadRequest) },
                    { "10009", typeof(BadRequest) },
                    { "20001", typeof(BadRequest) },
                    { "20002", typeof(InsufficientFunds) },
                    { "20005", typeof(AccountNotEnabled) },
                    { "30003", typeof(BadSymbol) },
                    { "30004", typeof(BadRequest) },
                    { "30005", typeof(BadRequest) },
                    { "30006", typeof(InvalidOrder) },
                    { "30007", typeof(InvalidOrder) },
                    { "30008", typeof(InvalidOrder) },
                    { "30009", typeof(InvalidOrder) },
                    { "30010", typeof(BadRequest) },
                    { "30013", typeof(InvalidOrder) },
                    { "30014", typeof(InvalidOrder) },
                    { "30016", typeof(InvalidOrder) },
                    { "30017", typeof(InvalidOrder) },
                    { "30023", typeof(InvalidOrder) },
                    { "30024", typeof(InvalidOrder) },
                    { "30025", typeof(InvalidOrder) },
                    { "40001", typeof(BadRequest) },
                    { "40002", typeof(BadRequest) },
                    { "40003", typeof(BadRequest) },
                    { "40004", typeof(BadRequest) },
                    { "40005", typeof(BadRequest) },
                    { "40006", typeof(BadRequest) },
                    { "40007", typeof(BadRequest) },
                    { "40101", typeof(AuthenticationError) },
                    { "40102", typeof(InvalidNonce) },
                    { "40103", typeof(AuthenticationError) },
                    { "40104", typeof(AuthenticationError) },
                    { "40107", typeof(BadRequest) },
                    { "40401", typeof(OrderNotFound) },
                    { "40801", typeof(RequestTimeout) },
                    { "42901", typeof(RateLimitExceeded) },
                    { "43005", typeof(InvalidOrder) },
                    { "43003", typeof(InvalidOrder) },
                    { "43004", typeof(InvalidOrder) },
                    { "43012", typeof(BadRequest) },
                    { "50001", typeof(ExchangeError) },
                    { "9010001", typeof(OnMaintenance) },
                } },
                { "broad", new Dictionary<string, object>() {} },
            } },
        });
    }

    /**
     * @method
     * @name cryptocom#fetchCurrencies
     * @description fetches all available currencies on an exchange
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-currency-networks
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an associative dictionary of currencies
     */
    public async override Task<object> fetchCurrencies(object parameters = null)
    {
        // this endpoint requires authentication
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(this.checkRequiredCredentials(false)))
        {
            return null;
        }
        object skipFetchCurrencies = false;
        var skipFetchCurrenciesparametersVariable = this.handleOptionAndParams(parameters, "fetchCurrencies", "skipFetchCurrencies", false);
        skipFetchCurrencies = ((IList<object>)skipFetchCurrenciesparametersVariable)[0];
        parameters = ((IList<object>)skipFetchCurrenciesparametersVariable)[1];
        if (isTrue(skipFetchCurrencies))
        {
            // sub-accounts can't access this endpoint
            return null;
        }
        object response = new Dictionary<string, object>() {};
        try
        {
            response = await this.v1PrivatePostPrivateGetCurrencyNetworks(parameters);
        } catch(Exception e)
        {
            if (isTrue(e is ExchangeError))
            {
                // sub-accounts can't access this endpoint
                // {"code":"10001","msg":"SYS_ERROR"}
                return null;
            }
            throw e;
        }
        //
        //    {
        //        "id": "*************",
        //        "method": "private/get-currency-networks",
        //        "code": "0",
        //        "result": {
        //            "update_time": "*************",
        //            "currency_map": {
        //                "USDT": {
        //                    "full_name": "Tether USD",
        //                    "default_network": "ETH",
        //                    "network_list": [
        //                        {
        //                            "network_id": "ETH",
        //                            "withdrawal_fee": "10.********",
        //                            "withdraw_enabled": true,
        //                            "min_withdrawal_amount": "20.0",
        //                            "deposit_enabled": true,
        //                            "confirmation_required": "32"
        //                        },
        //                        {
        //                            "network_id": "CRONOS",
        //                            "withdrawal_fee": "0.18000000",
        //                            "withdraw_enabled": true,
        //                            "min_withdrawal_amount": "0.35",
        //                            "deposit_enabled": true,
        //                            "confirmation_required": "15"
        //                        },
        //                        {
        //                            "network_id": "SOL",
        //                            "withdrawal_fee": "5.31000000",
        //                            "withdraw_enabled": true,
        //                            "min_withdrawal_amount": "10.62",
        //                            "deposit_enabled": true,
        //                            "confirmation_required": "1"
        //                        }
        //                    ]
        //                }
        //            }
        //        }
        //    }
        //
        object resultData = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object currencyMap = this.safeDict(resultData, "currency_map", new Dictionary<string, object>() {});
        object keys = new List<object>(((IDictionary<string,object>)currencyMap).Keys);
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object key = getValue(keys, i);
            object currency = getValue(currencyMap, key);
            object id = key;
            object code = this.safeCurrencyCode(id);
            object networks = new Dictionary<string, object>() {};
            object chains = this.safeList(currency, "network_list", new List<object>() {});
            for (object j = 0; isLessThan(j, getArrayLength(chains)); postFixIncrement(ref j))
            {
                object chain = getValue(chains, j);
                object networkId = this.safeString(chain, "network_id");
                object network = this.networkIdToCode(networkId);
                ((IDictionary<string,object>)networks)[(string)network] = new Dictionary<string, object>() {
                    { "info", chain },
                    { "id", networkId },
                    { "network", network },
                    { "active", null },
                    { "deposit", this.safeBool(chain, "deposit_enabled", false) },
                    { "withdraw", this.safeBool(chain, "withdraw_enabled", false) },
                    { "fee", this.safeNumber(chain, "withdrawal_fee") },
                    { "precision", null },
                    { "limits", new Dictionary<string, object>() {
                        { "withdraw", new Dictionary<string, object>() {
                            { "min", this.safeNumber(chain, "min_withdrawal_amount") },
                            { "max", null },
                        } },
                    } },
                };
            }
            ((IDictionary<string,object>)result)[(string)code] = this.safeCurrencyStructure(new Dictionary<string, object>() {
                { "info", currency },
                { "id", id },
                { "code", code },
                { "name", this.safeString(currency, "full_name") },
                { "active", null },
                { "deposit", null },
                { "withdraw", null },
                { "fee", null },
                { "precision", null },
                { "limits", new Dictionary<string, object>() {
                    { "amount", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                } },
                { "type", "crypto" },
                { "networks", networks },
            });
        }
        return result;
    }

    /**
     * @method
     * @name cryptocom#fetchMarkets
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-instruments
     * @description retrieves data on all markets for cryptocom
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.v1PublicGetPublicGetInstruments(parameters);
        //
        //     {
        //         "id": 1,
        //         "method": "public/get-instruments",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "symbol": "BTC_USDT",
        //                     "inst_type": "CCY_PAIR",
        //                     "display_name": "BTC/USDT",
        //                     "base_ccy": "BTC",
        //                     "quote_ccy": "USDT",
        //                     "quote_decimals": 2,
        //                     "quantity_decimals": 5,
        //                     "price_tick_size": "0.01",
        //                     "qty_tick_size": "0.00001",
        //                     "max_leverage": "50",
        //                     "tradable": true,
        //                     "expiry_timestamp_ms": 0,
        //                     "beta_product": false,
        //                     "margin_buy_enabled": false,
        //                     "margin_sell_enabled": true
        //                 },
        //                 {
        //                     "symbol": "RUNEUSD-PERP",
        //                     "inst_type": "PERPETUAL_SWAP",
        //                     "display_name": "RUNEUSD Perpetual",
        //                     "base_ccy": "RUNE",
        //                     "quote_ccy": "USD",
        //                     "quote_decimals": 3,
        //                     "quantity_decimals": 1,
        //                     "price_tick_size": "0.001",
        //                     "qty_tick_size": "0.1",
        //                     "max_leverage": "50",
        //                     "tradable": true,
        //                     "expiry_timestamp_ms": 0,
        //                     "beta_product": false,
        //                     "underlying_symbol": "RUNEUSD-INDEX",
        //                     "contract_size": "1",
        //                     "margin_buy_enabled": false,
        //                     "margin_sell_enabled": false
        //                 },
        //                 {
        //                     "symbol": "ETHUSD-230825",
        //                     "inst_type": "FUTURE",
        //                     "display_name": "ETHUSD Futures 20230825",
        //                     "base_ccy": "ETH",
        //                     "quote_ccy": "USD",
        //                     "quote_decimals": 2,
        //                     "quantity_decimals": 4,
        //                     "price_tick_size": "0.01",
        //                     "qty_tick_size": "0.0001",
        //                     "max_leverage": "100",
        //                     "tradable": true,
        //                     "expiry_timestamp_ms": 1692950400000,
        //                     "beta_product": false,
        //                     "underlying_symbol": "ETHUSD-INDEX",
        //                     "contract_size": "1",
        //                     "margin_buy_enabled": false,
        //                     "margin_sell_enabled": false
        //                 },
        //                 {
        //                     "symbol": "BTCUSD-230630-CW30000",
        //                     "inst_type": "WARRANT",
        //                     "display_name": "BTCUSD-230630-CW30000",
        //                     "base_ccy": "BTC",
        //                     "quote_ccy": "USD",
        //                     "quote_decimals": 3,
        //                     "quantity_decimals": 0,
        //                     "price_tick_size": "0.001",
        //                     "qty_tick_size": "10",
        //                     "max_leverage": "50",
        //                     "tradable": true,
        //                     "expiry_timestamp_ms": 1688112000000,
        //                     "beta_product": false,
        //                     "underlying_symbol": "BTCUSD-INDEX",
        //                     "put_call": "CALL",
        //                     "strike": "30000",
        //                     "contract_size": "0.0001",
        //                     "margin_buy_enabled": false,
        //                     "margin_sell_enabled": false
        //                 },
        //             ]
        //         }
        //     }
        //
        object resultResponse = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(resultResponse, "data", new List<object>() {});
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(data)); postFixIncrement(ref i))
        {
            object market = getValue(data, i);
            object inst_type = this.safeString(market, "inst_type");
            object spot = isEqual(inst_type, "CCY_PAIR");
            object swap = isEqual(inst_type, "PERPETUAL_SWAP");
            object future = isEqual(inst_type, "FUTURE");
            object option = isEqual(inst_type, "WARRANT");
            object baseId = this.safeString(market, "base_ccy");
            object quoteId = this.safeString(market, "quote_ccy");
            object settleId = ((bool) isTrue(spot)) ? null : quoteId;
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object settle = ((bool) isTrue(spot)) ? null : this.safeCurrencyCode(settleId);
            object optionType = this.safeStringLower(market, "put_call");
            object strike = this.safeString(market, "strike");
            object marginBuyEnabled = this.safeBool(market, "margin_buy_enabled");
            object marginSellEnabled = this.safeBool(market, "margin_sell_enabled");
            object expiryString = this.omitZero(this.safeString(market, "expiry_timestamp_ms"));
            object expiry = ((bool) isTrue((!isEqual(expiryString, null)))) ? parseInt(expiryString) : null;
            object symbol = add(add(bs, "/"), quote);
            object type = null;
            object contract = null;
            if (isTrue(isEqual(inst_type, "CCY_PAIR")))
            {
                type = "spot";
                contract = false;
            } else if (isTrue(isEqual(inst_type, "PERPETUAL_SWAP")))
            {
                type = "swap";
                symbol = add(add(symbol, ":"), quote);
                contract = true;
            } else if (isTrue(isEqual(inst_type, "FUTURE")))
            {
                type = "future";
                symbol = add(add(add(add(symbol, ":"), quote), "-"), this.yymmdd(expiry));
                contract = true;
            } else if (isTrue(isEqual(inst_type, "WARRANT")))
            {
                type = "option";
                object symbolOptionType = ((bool) isTrue((isEqual(optionType, "call")))) ? "C" : "P";
                symbol = add(add(add(add(add(add(add(add(symbol, ":"), quote), "-"), this.yymmdd(expiry)), "-"), strike), "-"), symbolOptionType);
                contract = true;
            }
            ((IList<object>)result).Add(new Dictionary<string, object>() {
                { "id", this.safeString(market, "symbol") },
                { "symbol", symbol },
                { "base", bs },
                { "quote", quote },
                { "settle", settle },
                { "baseId", baseId },
                { "quoteId", quoteId },
                { "settleId", settleId },
                { "type", type },
                { "spot", spot },
                { "margin", (isTrue((marginBuyEnabled)) || isTrue((marginSellEnabled))) },
                { "swap", swap },
                { "future", future },
                { "option", option },
                { "active", this.safeBool(market, "tradable") },
                { "contract", contract },
                { "linear", ((bool) isTrue((contract))) ? true : null },
                { "inverse", ((bool) isTrue((contract))) ? false : null },
                { "contractSize", this.safeNumber(market, "contract_size") },
                { "expiry", expiry },
                { "expiryDatetime", this.iso8601(expiry) },
                { "strike", this.parseNumber(strike) },
                { "optionType", optionType },
                { "precision", new Dictionary<string, object>() {
                    { "price", this.parseNumber(this.safeString(market, "price_tick_size")) },
                    { "amount", this.parseNumber(this.safeString(market, "qty_tick_size")) },
                } },
                { "limits", new Dictionary<string, object>() {
                    { "leverage", new Dictionary<string, object>() {
                        { "min", this.parseNumber("1") },
                        { "max", this.safeNumber(market, "max_leverage") },
                    } },
                    { "amount", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "price", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "cost", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                } },
                { "created", null },
                { "info", market },
            });
        }
        return result;
    }

    /**
     * @method
     * @name cryptocom#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-tickers
     * @see https://exchange-docs.crypto.com/derivatives/index.html#public-get-tickers
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbols, null)))
        {
            object symbol = null;
            if (isTrue(((symbols is IList<object>) || (symbols.GetType().IsGenericType && symbols.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
            {
                object symbolsLength = getArrayLength(symbols);
                if (isTrue(isGreaterThan(symbolsLength, 1)))
                {
                    throw new BadRequest ((string)add(this.id, " fetchTickers() symbols argument cannot contain more than 1 symbol")) ;
                }
                symbol = getValue(symbols, 0);
            } else
            {
                symbol = symbols;
            }
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["instrument_name"] = getValue(market, "id");
        }
        object response = await this.v1PublicGetPublicGetTickers(this.extend(request, parameters));
        //
        //     {
        //         "id": -1,
        //         "method": "public/get-tickers",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "i": "AVAXUSD-PERP",
        //                     "h": "13.209",
        //                     "l": "12.148",
        //                     "a": "13.209",
        //                     "v": "1109.8",
        //                     "vv": "14017.33",
        //                     "c": "0.0732",
        //                     "b": "13.210",
        //                     "k": "13.230",
        //                     "oi": "10888.9",
        //                     "t": 1687402657575
        //                 },
        //             ]
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "data", new List<object>() {});
        return this.parseTickers(data, symbols);
    }

    /**
     * @method
     * @name cryptocom#fetchTicker
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-tickers
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbol = this.symbol(symbol);
        object tickers = await this.fetchTickers(new List<object>() {symbol}, parameters);
        return this.safeValue(tickers, symbol);
    }

    /**
     * @method
     * @name cryptocom#fetchOrders
     * @description fetches information on multiple orders made by the user
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-order-history
     * @param {string} symbol unified market symbol of the market the orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for, max date range is one day
     * @param {int} [limit] the maximum number of order structures to retrieve, default 100 max 100
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms for the ending date filter, default is the current time
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOrders", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchOrders", symbol, since, limit, parameters);
        }
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["instrument_name"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start_time"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["end_time"] = until;
        }
        object response = await this.v1PrivatePostPrivateGetOrderHistory(this.extend(request, parameters));
        //
        //     {
        //         "id": *************,
        //         "method": "private/get-order-history",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "account_id": "ce075bef-1234-4321-bd6g-ff9007252e63",
        //                     "order_id": "6142909895014042762",
        //                     "client_oid": "4e918597-1234-4321-8201-a7577e1e1d91",
        //                     "order_type": "MARKET",
        //                     "time_in_force": "GOOD_TILL_CANCEL",
        //                     "side": "SELL",
        //                     "exec_inst": [ ],
        //                     "quantity": "0.00024",
        //                     "order_value": "5.7054672",
        //                     "maker_fee_rate": "0",
        //                     "taker_fee_rate": "0",
        //                     "avg_price": "25023.97",
        //                     "trigger_price": "0",
        //                     "ref_price": "0",
        //                     "ref_price_type": "NULL_VAL",
        //                     "cumulative_quantity": "0.00024",
        //                     "cumulative_value": "6.0057528",
        //                     "cumulative_fee": "0.001501438200",
        //                     "status": "FILLED",
        //                     "update_user_id": "ce075bef-1234-4321-bd6g-ff9007252e63",
        //                     "order_date": "2023-06-15",
        //                     "instrument_name": "BTC_USD",
        //                     "fee_instrument_name": "USD",
        //                     "create_time": 1686805465891,
        //                     "create_time_ns": "1686805465891812578",
        //                     "update_time": 1686805465891
        //                 }
        //             ]
        //         }
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object orders = this.safeList(data, "data", new List<object>() {});
        return this.parseOrders(orders, market, since, limit);
    }

    /**
     * @method
     * @name cryptocom#fetchTrades
     * @description get a list of the most recent trades for a particular symbol
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-trades
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch, maximum date range is one day
     * @param {int} [limit] the maximum number of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms for the ending date filter, default is the current time
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchTrades", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchTrades", symbol, since, limit, parameters);
        }
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start_ts"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["count"] = limit;
        }
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["end_ts"] = until;
        }
        object response = await this.v1PublicGetPublicGetTrades(this.extend(request, parameters));
        //
        //     {
        //         "id": -1,
        //         "method": "public/get-trades",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "s": "sell",
        //                     "p": "26386.00",
        //                     "q": "0.00453",
        //                     "t": *************,
        //                     "tn" : 1704476468851524373,
        //                     "d": "4611686018455979970",
        //                     "i": "BTC_USD"
        //                 },
        //             ]
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object trades = this.safeList(result, "data", new List<object>() {});
        return this.parseTrades(trades, market, since, limit);
    }

    /**
     * @method
     * @name cryptocom#fetchOHLCV
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-candlestick
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms for the ending date filter, default is the current time
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchOHLCV", "paginate", false);
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDeterministic("fetchOHLCV", symbol, since, limit, timeframe, parameters, 300);
        }
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
            { "timeframe", this.safeString(this.timeframes, timeframe, timeframe) },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            if (isTrue(isGreaterThan(limit, 300)))
            {
                limit = 300;
            }
            ((IDictionary<string,object>)request)["count"] = limit;
        }
        object now = this.microseconds();
        object duration = this.parseTimeframe(timeframe);
        object until = this.safeInteger(parameters, "until", now);
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start_ts"] = subtract(since, multiply(duration, 1000));
            if (isTrue(!isEqual(limit, null)))
            {
                ((IDictionary<string,object>)request)["end_ts"] = this.sum(since, multiply(multiply(duration, limit), 1000));
            } else
            {
                ((IDictionary<string,object>)request)["end_ts"] = until;
            }
        } else
        {
            ((IDictionary<string,object>)request)["end_ts"] = until;
        }
        object response = await this.v1PublicGetPublicGetCandlestick(this.extend(request, parameters));
        //
        //     {
        //         "id": -1,
        //         "method": "public/get-candlestick",
        //         "code": 0,
        //         "result": {
        //             "interval": "1m",
        //             "data": [
        //                 {
        //                     "o": "26949.89",
        //                     "h": "26957.64",
        //                     "l": "26948.24",
        //                     "c": "26950.00",
        //                     "v": "0.0670",
        //                     "t": 1687237080000
        //                 },
        //             ],
        //             "instrument_name": "BTC_USD"
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "data", new List<object>() {});
        return this.parseOHLCVs(data, market, timeframe, since, limit);
    }

    /**
     * @method
     * @name cryptocom#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-book
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the number of order book entries to return, max 50
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
        };
        if (isTrue(limit))
        {
            ((IDictionary<string,object>)request)["depth"] = mathMin(limit, 50); // max 50
        }
        object response = await this.v1PublicGetPublicGetBook(this.extend(request, parameters));
        //
        //     {
        //         "id": -1,
        //         "method": "public/get-book",
        //         "code": 0,
        //         "result": {
        //             "depth": 3,
        //             "data": [
        //                 {
        //                     "bids": [ [ "30025.00", "0.00004", "1" ], [ "30020.15", "0.02498", "1" ], [ "30020.00", "0.00004", "1" ] ],
        //                     "asks": [ [ "30025.01", "0.04090", "1" ], [ "30025.70", "0.01000", "1" ], [ "30026.94", "0.02681", "1" ] ],
        //                     "t": 1687491287380
        //                 }
        //             ],
        //             "instrument_name": "BTC_USD"
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "data", new List<object>() {});
        object orderBook = this.safeValue(data, 0);
        object timestamp = this.safeInteger(orderBook, "t");
        return this.parseOrderBook(orderBook, symbol, timestamp);
    }

    public override object parseBalance(object response)
    {
        object responseResult = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(responseResult, "data", new List<object>() {});
        object positionBalances = this.safeValue(getValue(data, 0), "position_balances", new List<object>() {});
        object result = new Dictionary<string, object>() {
            { "info", response },
        };
        for (object i = 0; isLessThan(i, getArrayLength(positionBalances)); postFixIncrement(ref i))
        {
            object balance = getValue(positionBalances, i);
            object currencyId = this.safeString(balance, "instrument_name");
            object code = this.safeCurrencyCode(currencyId);
            object account = this.account();
            ((IDictionary<string,object>)account)["total"] = this.safeString(balance, "quantity");
            ((IDictionary<string,object>)account)["used"] = this.safeString(balance, "reserved_qty");
            ((IDictionary<string,object>)result)[(string)code] = account;
        }
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name cryptocom#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-user-balance
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.v1PrivatePostPrivateUserBalance(parameters);
        //
        //     {
        //         "id": *************,
        //         "method": "private/user-balance",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "total_available_balance": "5.84684368",
        //                     "total_margin_balance": "5.84684368",
        //                     "total_initial_margin": "0",
        //                     "total_maintenance_margin": "0",
        //                     "total_position_cost": "0",
        //                     "total_cash_balance": "6.44412101",
        //                     "total_collateral_value": "5.846843685",
        //                     "total_session_unrealized_pnl": "0",
        //                     "instrument_name": "USD",
        //                     "total_session_realized_pnl": "0",
        //                     "position_balances": [
        //                         {
        //                             "quantity": "0.0002119875",
        //                             "reserved_qty": "0",
        //                             "collateral_weight": "0.9",
        //                             "collateral_amount": "5.37549592",
        //                             "market_value": "5.97277325",
        //                             "max_withdrawal_balance": "0.00021198",
        //                             "instrument_name": "BTC",
        //                             "hourly_interest_rate": "0"
        //                         },
        //                     ],
        //                     "total_effective_leverage": "0",
        //                     "position_limit": "3000000",
        //                     "used_position_limit": "0",
        //                     "total_borrow": "0",
        //                     "margin_score": "0",
        //                     "is_liquidating": false,
        //                     "has_risk": false,
        //                     "terminatable": true
        //                 }
        //             ]
        //         }
        //     }
        //
        return this.parseBalance(response);
    }

    /**
     * @method
     * @name cryptocom#fetchOrder
     * @description fetches information on an order made by the user
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-order-detail
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
        }
        object request = new Dictionary<string, object>() {
            { "order_id", id },
        };
        object response = await this.v1PrivatePostPrivateGetOrderDetail(this.extend(request, parameters));
        //
        //     {
        //         "id": *************,
        //         "method": "private/get-order-detail",
        //         "code": 0,
        //         "result": {
        //             "account_id": "ae075bef-1234-4321-bd6g-bb9007252a63",
        //             "order_id": "6142909895025252686",
        //             "client_oid": "CCXT_c2d2152cc32d40a3ae7fbf",
        //             "order_type": "LIMIT",
        //             "time_in_force": "GOOD_TILL_CANCEL",
        //             "side": "BUY",
        //             "exec_inst": [ ],
        //             "quantity": "0.00020",
        //             "limit_price": "20000.00",
        //             "order_value": "4",
        //             "avg_price": "0",
        //             "trigger_price": "0",
        //             "ref_price": "0",
        //             "cumulative_quantity": "0",
        //             "cumulative_value": "0",
        //             "cumulative_fee": "0",
        //             "status": "ACTIVE",
        //             "update_user_id": "ae075bef-1234-4321-bd6g-bb9007252a63",
        //             "order_date": "2023-06-15",
        //             "instrument_name": "BTC_USD",
        //             "fee_instrument_name": "BTC",
        //             "create_time": 1686870220684,
        //             "create_time_ns": "1686870220684239675",
        //             "update_time": 1686870220684
        //         }
        //     }
        //
        object order = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseOrder(order, market);
    }

    public virtual object createOrderRequest(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        object uppercaseType = ((string)type).ToUpper();
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
            { "side", ((string)side).ToUpper() },
            { "quantity", this.amountToPrecision(symbol, amount) },
        };
        if (isTrue(isTrue(isTrue((isEqual(uppercaseType, "LIMIT"))) || isTrue((isEqual(uppercaseType, "STOP_LIMIT")))) || isTrue((isEqual(uppercaseType, "TAKE_PROFIT_LIMIT")))))
        {
            ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
        }
        object broker = this.safeString(this.options, "broker", "CCXT");
        ((IDictionary<string,object>)request)["broker_id"] = broker;
        object marketType = null;
        object marginMode = null;
        var marketTypeparametersVariable = this.handleMarketTypeAndParams("createOrder", market, parameters);
        marketType = ((IList<object>)marketTypeparametersVariable)[0];
        parameters = ((IList<object>)marketTypeparametersVariable)[1];
        var marginModeparametersVariable = this.customHandleMarginModeAndParams("createOrder", parameters);
        marginMode = ((IList<object>)marginModeparametersVariable)[0];
        parameters = ((IList<object>)marginModeparametersVariable)[1];
        if (isTrue(isTrue((isEqual(marketType, "margin"))) || isTrue((!isEqual(marginMode, null)))))
        {
            ((IDictionary<string,object>)request)["spot_margin"] = "MARGIN";
        } else if (isTrue(isEqual(marketType, "spot")))
        {
            ((IDictionary<string,object>)request)["spot_margin"] = "SPOT";
        }
        object timeInForce = this.safeStringUpper2(parameters, "timeInForce", "time_in_force");
        if (isTrue(!isEqual(timeInForce, null)))
        {
            if (isTrue(isEqual(timeInForce, "GTC")))
            {
                ((IDictionary<string,object>)request)["time_in_force"] = "GOOD_TILL_CANCEL";
            } else if (isTrue(isEqual(timeInForce, "IOC")))
            {
                ((IDictionary<string,object>)request)["time_in_force"] = "IMMEDIATE_OR_CANCEL";
            } else if (isTrue(isEqual(timeInForce, "FOK")))
            {
                ((IDictionary<string,object>)request)["time_in_force"] = "FILL_OR_KILL";
            } else
            {
                ((IDictionary<string,object>)request)["time_in_force"] = timeInForce;
            }
        }
        object postOnly = this.safeBool(parameters, "postOnly", false);
        if (isTrue(isTrue((postOnly)) || isTrue((isEqual(timeInForce, "PO")))))
        {
            ((IDictionary<string,object>)request)["exec_inst"] = new List<object>() {"POST_ONLY"};
            ((IDictionary<string,object>)request)["time_in_force"] = "GOOD_TILL_CANCEL";
        }
        object triggerPrice = this.safeStringN(parameters, new List<object>() {"stopPrice", "triggerPrice", "ref_price"});
        object stopLossPrice = this.safeNumber(parameters, "stopLossPrice");
        object takeProfitPrice = this.safeNumber(parameters, "takeProfitPrice");
        object isTrigger = (!isEqual(triggerPrice, null));
        object isStopLossTrigger = (!isEqual(stopLossPrice, null));
        object isTakeProfitTrigger = (!isEqual(takeProfitPrice, null));
        if (isTrue(isTrigger))
        {
            ((IDictionary<string,object>)request)["ref_price"] = this.priceToPrecision(symbol, triggerPrice);
            object priceString = this.numberToString(price);
            if (isTrue(isTrue(isTrue((isEqual(uppercaseType, "LIMIT"))) || isTrue((isEqual(uppercaseType, "STOP_LIMIT")))) || isTrue((isEqual(uppercaseType, "TAKE_PROFIT_LIMIT")))))
            {
                if (isTrue(isEqual(side, "buy")))
                {
                    if (isTrue(Precise.stringLt(priceString, triggerPrice)))
                    {
                        ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT_LIMIT";
                    } else
                    {
                        ((IDictionary<string,object>)request)["type"] = "STOP_LIMIT";
                    }
                } else
                {
                    if (isTrue(Precise.stringLt(priceString, triggerPrice)))
                    {
                        ((IDictionary<string,object>)request)["type"] = "STOP_LIMIT";
                    } else
                    {
                        ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT_LIMIT";
                    }
                }
            } else
            {
                if (isTrue(isEqual(side, "buy")))
                {
                    if (isTrue(Precise.stringLt(priceString, triggerPrice)))
                    {
                        ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT";
                    } else
                    {
                        ((IDictionary<string,object>)request)["type"] = "STOP_LOSS";
                    }
                } else
                {
                    if (isTrue(Precise.stringLt(priceString, triggerPrice)))
                    {
                        ((IDictionary<string,object>)request)["type"] = "STOP_LOSS";
                    } else
                    {
                        ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT";
                    }
                }
            }
        } else if (isTrue(isStopLossTrigger))
        {
            if (isTrue(isTrue((isEqual(uppercaseType, "LIMIT"))) || isTrue((isEqual(uppercaseType, "STOP_LIMIT")))))
            {
                ((IDictionary<string,object>)request)["type"] = "STOP_LIMIT";
            } else
            {
                ((IDictionary<string,object>)request)["type"] = "STOP_LOSS";
            }
            ((IDictionary<string,object>)request)["ref_price"] = this.priceToPrecision(symbol, stopLossPrice);
        } else if (isTrue(isTakeProfitTrigger))
        {
            if (isTrue(isTrue((isEqual(uppercaseType, "LIMIT"))) || isTrue((isEqual(uppercaseType, "TAKE_PROFIT_LIMIT")))))
            {
                ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT_LIMIT";
            } else
            {
                ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT";
            }
            ((IDictionary<string,object>)request)["ref_price"] = this.priceToPrecision(symbol, takeProfitPrice);
        } else
        {
            ((IDictionary<string,object>)request)["type"] = uppercaseType;
        }
        parameters = this.omit(parameters, new List<object>() {"postOnly", "clientOrderId", "timeInForce", "stopPrice", "triggerPrice", "stopLossPrice", "takeProfitPrice"});
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name cryptocom#createOrder
     * @description create a trade order
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-create-order
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market', 'limit', 'stop_loss', 'stop_limit', 'take_profit', 'take_profit_limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.timeInForce] 'GTC', 'IOC', 'FOK' or 'PO'
     * @param {string} [params.ref_price_type] 'MARK_PRICE', 'INDEX_PRICE', 'LAST_PRICE' which trigger price type to use, default is MARK_PRICE
     * @param {float} [params.triggerPrice] price to trigger a trigger order
     * @param {float} [params.stopLossPrice] price to trigger a stop-loss trigger order
     * @param {float} [params.takeProfitPrice] price to trigger a take-profit trigger order
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = this.createOrderRequest(symbol, type, side, amount, price, parameters);
        object response = await this.v1PrivatePostPrivateCreateOrder(request);
        //
        //     {
        //         "id": 1686804664362,
        //         "method": "private/create-order",
        //         "code" : 0,
        //         "result": {
        //             "order_id": "6540219377766741832",
        //             "client_oid": "CCXT_d6ef7c3db6c1495aa8b757"
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseOrder(result, market);
    }

    /**
     * @method
     * @name cryptocom#createOrders
     * @description create a list of trade orders
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-create-order-list-list
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-create-order-list-oco
     * @param {Array} orders list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrders(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object ordersRequests = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(orders)); postFixIncrement(ref i))
        {
            object rawOrder = getValue(orders, i);
            object marketId = this.safeString(rawOrder, "symbol");
            object type = this.safeString(rawOrder, "type");
            object side = this.safeString(rawOrder, "side");
            object amount = this.safeValue(rawOrder, "amount");
            object price = this.safeValue(rawOrder, "price");
            object orderParams = this.safeDict(rawOrder, "params", new Dictionary<string, object>() {});
            object orderRequest = this.createAdvancedOrderRequest(marketId, type, side, amount, price, orderParams);
            ((IList<object>)ordersRequests).Add(orderRequest);
        }
        object contigency = this.safeString(parameters, "contingency_type", "LIST");
        object request = new Dictionary<string, object>() {
            { "contingency_type", contigency },
            { "order_list", ordersRequests },
        };
        object response = await this.v1PrivatePostPrivateCreateOrderList(this.extend(request, parameters));
        //
        // {
        //     "id": 12,
        //     "method": "private/create-order-list",
        //     "code": 10001,
        //     "result": {
        //       "result_list": [
        //         {
        //           "index": 0,
        //           "code": 0,
        //           "order_id": "2015106383706015873",
        //           "client_oid": "my_order_0001"
        //         },
        //         {
        //           "index": 1,
        //           "code": 20007,
        //           "message": "INVALID_REQUEST",
        //           "client_oid": "my_order_0002"
        //         }
        //       ]
        //     }
        // }
        //
        //   {
        //       "id" : 1698068111133,
        //       "method" : "private/create-order-list",
        //       "code" : 0,
        //       "result" : [ {
        //         "code" : 0,
        //         "index" : 0,
        //         "client_oid" : "1698068111133_0",
        //         "order_id" : "6142909896519488206"
        //       }, {
        //         "code" : 306,
        //         "index" : 1,
        //         "client_oid" : "1698068111133_1",
        //         "message" : "INSUFFICIENT_AVAILABLE_BALANCE",
        //         "order_id" : "6142909896519488207"
        //       } ]
        //   }
        //
        object result = this.safeValue(response, "result", new List<object>() {});
        object listId = this.safeString(result, "list_id");
        if (isTrue(!isEqual(listId, null)))
        {
            object ocoOrders = new List<object>() {new Dictionary<string, object>() {
    { "order_id", listId },
}};
            return this.parseOrders(ocoOrders);
        }
        return this.parseOrders(result);
    }

    public virtual object createAdvancedOrderRequest(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        // differs slightly from createOrderRequest
        // since the advanced order endpoint requires a different set of parameters
        // namely here we don't support ref_price or spot_margin
        // and market-buy orders need to send notional instead of quantity
        parameters ??= new Dictionary<string, object>();
        object market = this.market(symbol);
        object uppercaseType = ((string)type).ToUpper();
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
            { "side", ((string)side).ToUpper() },
        };
        if (isTrue(isTrue(isTrue((isEqual(uppercaseType, "LIMIT"))) || isTrue((isEqual(uppercaseType, "STOP_LIMIT")))) || isTrue((isEqual(uppercaseType, "TAKE_PROFIT_LIMIT")))))
        {
            ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
        }
        object broker = this.safeString(this.options, "broker", "CCXT");
        ((IDictionary<string,object>)request)["broker_id"] = broker;
        object timeInForce = this.safeStringUpper2(parameters, "timeInForce", "time_in_force");
        if (isTrue(!isEqual(timeInForce, null)))
        {
            if (isTrue(isEqual(timeInForce, "GTC")))
            {
                ((IDictionary<string,object>)request)["time_in_force"] = "GOOD_TILL_CANCEL";
            } else if (isTrue(isEqual(timeInForce, "IOC")))
            {
                ((IDictionary<string,object>)request)["time_in_force"] = "IMMEDIATE_OR_CANCEL";
            } else if (isTrue(isEqual(timeInForce, "FOK")))
            {
                ((IDictionary<string,object>)request)["time_in_force"] = "FILL_OR_KILL";
            } else
            {
                ((IDictionary<string,object>)request)["time_in_force"] = timeInForce;
            }
        }
        object postOnly = this.safeBool(parameters, "postOnly", false);
        if (isTrue(isTrue((postOnly)) || isTrue((isEqual(timeInForce, "PO")))))
        {
            ((IDictionary<string,object>)request)["exec_inst"] = new List<object>() {"POST_ONLY"};
            ((IDictionary<string,object>)request)["time_in_force"] = "GOOD_TILL_CANCEL";
        }
        object triggerPrice = this.safeStringN(parameters, new List<object>() {"stopPrice", "triggerPrice", "ref_price"});
        object stopLossPrice = this.safeNumber(parameters, "stopLossPrice");
        object takeProfitPrice = this.safeNumber(parameters, "takeProfitPrice");
        object isTrigger = (!isEqual(triggerPrice, null));
        object isStopLossTrigger = (!isEqual(stopLossPrice, null));
        object isTakeProfitTrigger = (!isEqual(takeProfitPrice, null));
        if (isTrue(isTrigger))
        {
            object priceString = this.numberToString(price);
            if (isTrue(isTrue(isTrue((isEqual(uppercaseType, "LIMIT"))) || isTrue((isEqual(uppercaseType, "STOP_LIMIT")))) || isTrue((isEqual(uppercaseType, "TAKE_PROFIT_LIMIT")))))
            {
                if (isTrue(isEqual(side, "buy")))
                {
                    if (isTrue(Precise.stringLt(priceString, triggerPrice)))
                    {
                        ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT_LIMIT";
                    } else
                    {
                        ((IDictionary<string,object>)request)["type"] = "STOP_LIMIT";
                    }
                } else
                {
                    if (isTrue(Precise.stringLt(priceString, triggerPrice)))
                    {
                        ((IDictionary<string,object>)request)["type"] = "STOP_LIMIT";
                    } else
                    {
                        ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT_LIMIT";
                    }
                }
            } else
            {
                if (isTrue(isEqual(side, "buy")))
                {
                    if (isTrue(Precise.stringLt(priceString, triggerPrice)))
                    {
                        ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT";
                    } else
                    {
                        ((IDictionary<string,object>)request)["type"] = "STOP_LOSS";
                    }
                } else
                {
                    if (isTrue(Precise.stringLt(priceString, triggerPrice)))
                    {
                        ((IDictionary<string,object>)request)["type"] = "STOP_LOSS";
                    } else
                    {
                        ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT";
                    }
                }
            }
        } else if (isTrue(isStopLossTrigger))
        {
            if (isTrue(isTrue((isEqual(uppercaseType, "LIMIT"))) || isTrue((isEqual(uppercaseType, "STOP_LIMIT")))))
            {
                ((IDictionary<string,object>)request)["type"] = "STOP_LIMIT";
            } else
            {
                ((IDictionary<string,object>)request)["type"] = "STOP_LOSS";
            }
        } else if (isTrue(isTakeProfitTrigger))
        {
            if (isTrue(isTrue((isEqual(uppercaseType, "LIMIT"))) || isTrue((isEqual(uppercaseType, "TAKE_PROFIT_LIMIT")))))
            {
                ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT_LIMIT";
            } else
            {
                ((IDictionary<string,object>)request)["type"] = "TAKE_PROFIT";
            }
        } else
        {
            ((IDictionary<string,object>)request)["type"] = uppercaseType;
        }
        if (isTrue(isTrue((isEqual(side, "buy"))) && isTrue((isTrue(isTrue((isEqual(uppercaseType, "MARKET"))) || isTrue((isEqual(uppercaseType, "STOP_LOSS")))) || isTrue((isEqual(uppercaseType, "TAKE_PROFIT")))))))
        {
            // use createmarketBuy logic here
            object quoteAmount = null;
            object createMarketBuyOrderRequiresPrice = true;
            var createMarketBuyOrderRequiresPriceparametersVariable = this.handleOptionAndParams(parameters, "createOrder", "createMarketBuyOrderRequiresPrice", true);
            createMarketBuyOrderRequiresPrice = ((IList<object>)createMarketBuyOrderRequiresPriceparametersVariable)[0];
            parameters = ((IList<object>)createMarketBuyOrderRequiresPriceparametersVariable)[1];
            object cost = this.safeNumber2(parameters, "cost", "notional");
            parameters = this.omit(parameters, "cost");
            if (isTrue(!isEqual(cost, null)))
            {
                quoteAmount = this.costToPrecision(symbol, cost);
            } else if (isTrue(createMarketBuyOrderRequiresPrice))
            {
                if (isTrue(isEqual(price, null)))
                {
                    throw new InvalidOrder ((string)add(this.id, " createOrder() requires the price argument for market buy orders to calculate the total cost to spend (amount * price), alternatively set the createMarketBuyOrderRequiresPrice option or param to false and pass the cost to spend (quote quantity) in the amount argument")) ;
                } else
                {
                    object amountString = this.numberToString(amount);
                    object priceString = this.numberToString(price);
                    object costRequest = Precise.stringMul(amountString, priceString);
                    quoteAmount = this.costToPrecision(symbol, costRequest);
                }
            } else
            {
                quoteAmount = this.costToPrecision(symbol, amount);
            }
            ((IDictionary<string,object>)request)["notional"] = quoteAmount;
        } else
        {
            ((IDictionary<string,object>)request)["quantity"] = this.amountToPrecision(symbol, amount);
        }
        parameters = this.omit(parameters, new List<object>() {"postOnly", "clientOrderId", "timeInForce", "stopPrice", "triggerPrice", "stopLossPrice", "takeProfitPrice"});
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name cryptocom#editOrder
     * @description edit a trade order
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-amend-order
     * @param {string} id order id
     * @param {string} symbol unified market symbol of the order to edit
     * @param {string} [type] not used by cryptocom editOrder
     * @param {string} [side] not used by cryptocom editOrder
     * @param {float} amount (mandatory) how much of the currency you want to trade in units of the base currency
     * @param {float} price (mandatory) the price for the order, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.clientOrderId] the original client order id of the order to edit, required if id is not provided
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> editOrder(object id, object symbol, object type, object side, object amount = null, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = this.editOrderRequest(id, symbol, amount, price, parameters);
        object response = await this.v1PrivatePostPrivateAmendOrder(request);
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseOrder(result);
    }

    public virtual object editOrderRequest(object id, object symbol, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(id, null)))
        {
            ((IDictionary<string,object>)request)["order_id"] = id;
        } else
        {
            object originalClientOrderId = this.safeString2(parameters, "orig_client_oid", "clientOrderId");
            if (isTrue(isEqual(originalClientOrderId, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " editOrder() requires an id argument or orig_client_oid parameter")) ;
            } else
            {
                ((IDictionary<string,object>)request)["orig_client_oid"] = originalClientOrderId;
                parameters = this.omit(parameters, new List<object>() {"orig_client_oid", "clientOrderId"});
            }
        }
        if (isTrue(isTrue((isEqual(amount, null))) || isTrue((isEqual(price, null)))))
        {
            throw new ArgumentsRequired ((string)add(this.id, " editOrder() requires both amount and price arguments. If you do not want to change the amount or price, you should pass the original values")) ;
        }
        ((IDictionary<string,object>)request)["new_quantity"] = this.amountToPrecision(symbol, amount);
        ((IDictionary<string,object>)request)["new_price"] = this.priceToPrecision(symbol, price);
        return this.extend(request, parameters);
    }

    /**
     * @method
     * @name cryptocom#cancelAllOrders
     * @description cancel all open orders
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-cancel-all-orders
     * @param {string} symbol unified market symbol of the orders to cancel
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} Returns exchange raw message{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["instrument_name"] = getValue(market, "id");
        }
        object response = await this.v1PrivatePostPrivateCancelAllOrders(this.extend(request, parameters));
        return new List<object> {this.safeOrder(new Dictionary<string, object>() {
    { "info", response },
})};
    }

    /**
     * @method
     * @name cryptocom#cancelOrder
     * @description cancels an open order
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-cancel-order
     * @param {string} id the order id of the order to cancel
     * @param {string} [symbol] unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
        }
        object request = new Dictionary<string, object>() {
            { "order_id", id },
        };
        object response = await this.v1PrivatePostPrivateCancelOrder(this.extend(request, parameters));
        //
        //     {
        //         "id": 1686882846638,
        //         "method": "private/cancel-order",
        //         "code": 0,
        //         "message": "NO_ERROR",
        //         "result": {
        //             "client_oid": "CCXT_c2d2152cc32d40a3ae7fbf",
        //             "order_id": "6142909895025252686"
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseOrder(result, market);
    }

    /**
     * @method
     * @name cryptocom#cancelOrders
     * @description cancel multiple orders
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-cancel-order-list-list
     * @param {string[]} ids order ids
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> cancelOrders(object ids, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object orderRequests = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(ids)); postFixIncrement(ref i))
        {
            object id = getValue(ids, i);
            object order = new Dictionary<string, object>() {
                { "instrument_name", getValue(market, "id") },
                { "order_id", ((object)id).ToString() },
            };
            ((IList<object>)orderRequests).Add(order);
        }
        object request = new Dictionary<string, object>() {
            { "contingency_type", "LIST" },
            { "order_list", orderRequests },
        };
        object response = await this.v1PrivatePostPrivateCancelOrderList(this.extend(request, parameters));
        object result = this.safeList(response, "result", new List<object>() {});
        return this.parseOrders(result, market, null, null, parameters);
    }

    /**
     * @method
     * @name cryptocom#cancelOrdersForSymbols
     * @description cancel multiple orders for multiple symbols
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-cancel-order-list-list
     * @param {CancellationRequest[]} orders each order should contain the parameters required by cancelOrder namely id and symbol, example [{"id": "a", "symbol": "BTC/USDT"}, {"id": "b", "symbol": "ETH/USDT"}]
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrdersForSymbols(object orders, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object orderRequests = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(orders)); postFixIncrement(ref i))
        {
            object order = getValue(orders, i);
            object id = this.safeString(order, "id");
            object symbol = this.safeString(order, "symbol");
            object market = this.market(symbol);
            object orderItem = new Dictionary<string, object>() {
                { "instrument_name", getValue(market, "id") },
                { "order_id", ((object)id).ToString() },
            };
            ((IList<object>)orderRequests).Add(orderItem);
        }
        object request = new Dictionary<string, object>() {
            { "contingency_type", "LIST" },
            { "order_list", orderRequests },
        };
        object response = await this.v1PrivatePostPrivateCancelOrderList(this.extend(request, parameters));
        object result = this.safeList(response, "result", new List<object>() {});
        return this.parseOrders(result, null, null, null, parameters);
    }

    /**
     * @method
     * @name cryptocom#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-open-orders
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of open order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["instrument_name"] = getValue(market, "id");
        }
        object response = await this.v1PrivatePostPrivateGetOpenOrders(this.extend(request, parameters));
        //
        //     {
        //         "id": *************,
        //         "method": "private/get-open-orders",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "account_id": "ce075bef-1234-4321-bd6g-ff9007252e63",
        //                     "order_id": "6530219477767564494",
        //                     "client_oid": "CCXT_7ce730f0388441df9bc218",
        //                     "order_type": "LIMIT",
        //                     "time_in_force": "GOOD_TILL_CANCEL",
        //                     "side": "BUY",
        //                     "exec_inst": [ ],
        //                     "quantity": "0.00020",
        //                     "limit_price": "20000.00",
        //                     "order_value": "4",
        //                     "avg_price": "0",
        //                     "trigger_price": "0",
        //                     "ref_price": "0",
        //                     "cumulative_quantity": "0",
        //                     "cumulative_value": "0",
        //                     "cumulative_fee": "0",
        //                     "status": "ACTIVE",
        //                     "update_user_id": "ce075bef-1234-4321-bd6g-gg9007252e63",
        //                     "order_date": "2023-06-15",
        //                     "instrument_name": "BTC_USD",
        //                     "fee_instrument_name": "BTC",
        //                     "create_time": 1686806053992,
        //                     "create_time_ns": "1686806053992921880",
        //                     "update_time": 1686806053993
        //                 }
        //             ]
        //         }
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object orders = this.safeList(data, "data", new List<object>() {});
        return this.parseOrders(orders, market, since, limit);
    }

    /**
     * @method
     * @name cryptocom#fetchMyTrades
     * @description fetch all trades made by the user
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-trades
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for, maximum date range is one day
     * @param {int} [limit] the maximum number of trade structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms for the ending date filter, default is the current time
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchMyTrades", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDynamic("fetchMyTrades", symbol, since, limit, parameters, 100);
        }
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["instrument_name"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start_time"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["end_time"] = until;
        }
        object response = await this.v1PrivatePostPrivateGetTrades(this.extend(request, parameters));
        //
        //     {
        //         "id": *************,
        //         "method": "private/get-trades",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "account_id": "ds075abc-1234-4321-bd6g-ff9007252r63",
        //                     "event_date": "2023-06-16",
        //                     "journal_type": "TRADING",
        //                     "side": "BUY",
        //                     "instrument_name": "BTC_USD",
        //                     "fees": "-0.**********",
        //                     "trade_id": "6142909898247428343",
        //                     "trade_match_id": "4611686018455978480",
        //                     "create_time": *************,
        //                     "traded_price": "26347.16",
        //                     "traded_quantity": "0.00021",
        //                     "fee_instrument_name": "BTC",
        //                     "client_oid": "d1c70a60-810e-4c92-b2a0-72b931cb31e0",
        //                     "taker_side": "TAKER",
        //                     "order_id": "6142909895036331486",
        //                     "create_time_ns": "*************207066"
        //                 }
        //             ]
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object trades = this.safeList(result, "data", new List<object>() {});
        return this.parseTrades(trades, market, since, limit);
    }

    public virtual object parseAddress(object addressString)
    {
        object address = null;
        object tag = null;
        object rawTag = null;
        if (isTrue(isGreaterThan(getIndexOf(addressString, "?"), 0)))
        {
            var addressrawTagVariable = ((string)addressString).Split(new [] {((string)"?")}, StringSplitOptions.None).ToList<object>();
            address = ((IList<object>)addressrawTagVariable)[0];
            rawTag = ((IList<object>)addressrawTagVariable)[1];
            object splitted = ((string)rawTag).Split(new [] {((string)"=")}, StringSplitOptions.None).ToList<object>();
            tag = getValue(splitted, 1);
        } else
        {
            address = addressString;
        }
        return new List<object>() {address, tag};
    }

    /**
     * @method
     * @name cryptocom#withdraw
     * @description make a withdrawal
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-create-withdrawal
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address the address to withdraw to
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        var tagparametersVariable = this.handleWithdrawTagAndParams(tag, parameters);
        tag = ((IList<object>)tagparametersVariable)[0];
        parameters = ((IList<object>)tagparametersVariable)[1];
        await this.loadMarkets();
        object currency = this.safeCurrency(code); // for instance, USDC is not inferred from markets but it's still available
        object request = new Dictionary<string, object>() {
            { "currency", getValue(currency, "id") },
            { "amount", amount },
            { "address", address },
        };
        if (isTrue(!isEqual(tag, null)))
        {
            ((IDictionary<string,object>)request)["address_tag"] = tag;
        }
        object networkCode = null;
        var networkCodeparametersVariable = this.handleNetworkCodeAndParams(parameters);
        networkCode = ((IList<object>)networkCodeparametersVariable)[0];
        parameters = ((IList<object>)networkCodeparametersVariable)[1];
        object networkId = this.networkCodeToId(networkCode);
        if (isTrue(!isEqual(networkId, null)))
        {
            ((IDictionary<string,object>)request)["network_id"] = networkId;
        }
        object response = await this.v1PrivatePostPrivateCreateWithdrawal(this.extend(request, parameters));
        //
        //    {
        //        "id":-1,
        //        "method":"private/create-withdrawal",
        //        "code":0,
        //        "result": {
        //            "id": 2220,
        //            "amount": 1,
        //            "fee": 0.0004,
        //            "symbol": "BTC",
        //            "address": "2NBqqD5GRJ8wHy1PYyCXTe9ke5226FhavBf",
        //            "client_wid": "my_withdrawal_002",
        //            "create_time":1607063412000
        //        }
        //     }
        //
        object result = this.safeDict(response, "result");
        return this.parseTransaction(result, currency);
    }

    /**
     * @method
     * @name cryptocom#fetchDepositAddressesByNetwork
     * @description fetch a dictionary of addresses for a currency, indexed by network
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-deposit-address
     * @param {string} code unified currency code of the currency for the deposit address
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [address structures]{@link https://docs.ccxt.com/#/?id=address-structure} indexed by the network
     */
    public async override Task<object> fetchDepositAddressesByNetwork(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.safeCurrency(code);
        object request = new Dictionary<string, object>() {
            { "currency", getValue(currency, "id") },
        };
        object response = await this.v1PrivatePostPrivateGetDepositAddress(this.extend(request, parameters));
        //
        //     {
        //         "id": 1234555011221,
        //         "method": "private/get-deposit-address",
        //         "code": 0,
        //         "result": {
        //             "deposit_address_list": [
        //                 {
        //                     "currency": "BTC",
        //                     "create_time": 1686730755000,
        //                     "id": "3737377",
        //                     "address": "**********************************",
        //                     "status":"1",
        //                     "network": "BTC"
        //                 },
        //             ]
        //         }
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object addresses = this.safeList(data, "deposit_address_list", new List<object>() {});
        object addressesLength = getArrayLength(addresses);
        if (isTrue(isEqual(addressesLength, 0)))
        {
            throw new ExchangeError ((string)add(this.id, " fetchDepositAddressesByNetwork() generating address...")) ;
        }
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, addressesLength); postFixIncrement(ref i))
        {
            object value = this.safeDict(addresses, i);
            object addressString = this.safeString(value, "address");
            object currencyId = this.safeString(value, "currency");
            object responseCode = this.safeCurrencyCode(currencyId);
            var addresstagVariable = this.parseAddress(addressString);
            var address = ((IList<object>) addresstagVariable)[0];
            var tag = ((IList<object>) addresstagVariable)[1];
            this.checkAddress(address);
            object networkId = this.safeString(value, "network");
            object network = this.networkIdToCode(networkId, responseCode);
            ((IDictionary<string,object>)result)[(string)network] = new Dictionary<string, object>() {
                { "info", value },
                { "currency", responseCode },
                { "network", network },
                { "address", address },
                { "tag", tag },
            };
        }
        return result;
    }

    /**
     * @method
     * @name cryptocom#fetchDepositAddress
     * @description fetch the deposit address for a currency associated with this account
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-deposit-address
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> fetchDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object network = this.safeStringUpper(parameters, "network");
        parameters = this.omit(parameters, new List<object>() {"network"});
        object depositAddresses = await this.fetchDepositAddressesByNetwork(code, parameters);
        if (isTrue(inOp(depositAddresses, network)))
        {
            return getValue(depositAddresses, network);
        } else
        {
            object keys = new List<object>(((IDictionary<string,object>)depositAddresses).Keys);
            return getValue(depositAddresses, getValue(keys, 0));
        }
    }

    /**
     * @method
     * @name cryptocom#fetchDeposits
     * @description fetch all deposits made to an account
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-deposit-history
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for
     * @param {int} [limit] the maximum number of deposits structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms for the ending date filter, default is the current time
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDeposits(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.safeCurrency(code);
            ((IDictionary<string,object>)request)["currency"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            // 90 days date range
            ((IDictionary<string,object>)request)["start_ts"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["page_size"] = limit;
        }
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["end_ts"] = until;
        }
        object response = await this.v1PrivatePostPrivateGetDepositHistory(this.extend(request, parameters));
        //
        //     {
        //         "id": 1688701375714,
        //         "method": "private/get-deposit-history",
        //         "code": 0,
        //         "result": {
        //             "deposit_list": [
        //                 {
        //                     "currency": "BTC",
        //                     "fee": 0,
        //                     "create_time": 1688023659000,
        //                     "id": "6201135",
        //                     "update_time": 1688178509000,
        //                     "amount": 0.00114571,
        //                     "address": "**********************************",
        //                     "status": "1",
        //                     "txid": "f0ae4202b76eb999c301eccdde44dc639bee42d1fdd5974105286ca3393f6065/2"
        //                 },
        //             ]
        //         }
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object depositList = this.safeList(data, "deposit_list", new List<object>() {});
        return this.parseTransactions(depositList, currency, since, limit);
    }

    /**
     * @method
     * @name cryptocom#fetchWithdrawals
     * @description fetch all withdrawals made from an account
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-withdrawal-history
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch withdrawals for
     * @param {int} [limit] the maximum number of withdrawals structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms for the ending date filter, default is the current time
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.safeCurrency(code);
            ((IDictionary<string,object>)request)["currency"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            // 90 days date range
            ((IDictionary<string,object>)request)["start_ts"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["page_size"] = limit;
        }
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["end_ts"] = until;
        }
        object response = await this.v1PrivatePostPrivateGetWithdrawalHistory(this.extend(request, parameters));
        //
        //     {
        //         "id": 1688613879534,
        //         "method": "private/get-withdrawal-history",
        //         "code": 0,
        //         "result": {
        //             "withdrawal_list": [
        //                 {
        //                     "currency": "BTC",
        //                     "client_wid": "",
        //                     "fee": 0.0005,
        //                     "create_time": 1688613850000,
        //                     "id": "5275977",
        //                     "update_time": 1688613850000,
        //                     "amount": 0.0005,
        //                     "address": "**********************************",
        //                     "status": "1",
        //                     "txid": "",
        //                     "network_id": "BTC"
        //                 }
        //             ]
        //         }
        //     }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object withdrawalList = this.safeList(data, "withdrawal_list", new List<object>() {});
        return this.parseTransactions(withdrawalList, currency, since, limit);
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        // fetchTicker
        //
        //     {
        //         "i": "BTC_USD",
        //         "h": "30821.45",
        //         "l": "28685.11",
        //         "a": "30446.00",
        //         "v": "1767.8734",
        //         "vv": "52436726.42",
        //         "c": "0.0583",
        //         "b": "30442.00",
        //         "k": "30447.66",
        //         "t": 1687403045415
        //     }
        //
        // fetchTickers
        //
        //     {
        //         "i": "AVAXUSD-PERP",
        //         "h": "13.209",
        //         "l": "12.148",
        //         "a": "13.209",
        //         "v": "1109.8",
        //         "vv": "14017.33",
        //         "c": "0.0732",
        //         "b": "13.210",
        //         "k": "13.230",
        //         "oi": "10888.9",
        //         "t": 1687402657575
        //     }
        //
        object timestamp = this.safeInteger(ticker, "t");
        object marketId = this.safeString(ticker, "i");
        market = this.safeMarket(marketId, market, "_");
        object quote = this.safeString(market, "quote");
        object last = this.safeString(ticker, "a");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", getValue(market, "symbol") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "high", this.safeNumber(ticker, "h") },
            { "low", this.safeNumber(ticker, "l") },
            { "bid", this.safeNumber(ticker, "b") },
            { "bidVolume", null },
            { "ask", this.safeNumber(ticker, "k") },
            { "askVolume", null },
            { "vwap", null },
            { "open", null },
            { "close", last },
            { "last", last },
            { "previousClose", null },
            { "change", null },
            { "percentage", this.safeString(ticker, "c") },
            { "average", null },
            { "baseVolume", this.safeString(ticker, "v") },
            { "quoteVolume", ((bool) isTrue((isEqual(quote, "USD")))) ? this.safeString(ticker, "vv") : null },
            { "info", ticker },
        }, market);
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        // fetchTrades
        //
        //     {
        //         "s": "sell",
        //         "p": "26386.00",
        //         "q": "0.00453",
        //         "tn": *************,
        //         "tn": 1704476468851524373,
        //         "d": "4611686018455979970",
        //         "i": "BTC_USD"
        //     }
        //
        // fetchMyTrades
        //
        //     {
        //         "account_id": "ds075abc-1234-4321-bd6g-ff9007252r63",
        //         "event_date": "2023-06-16",
        //         "journal_type": "TRADING",
        //         "side": "BUY",
        //         "instrument_name": "BTC_USD",
        //         "fees": "-0.**********",
        //         "trade_id": "6142909898247428343",
        //         "trade_match_id": "4611686018455978480",
        //         "create_time": *************,
        //         "traded_price": "26347.16",
        //         "traded_quantity": "0.00021",
        //         "fee_instrument_name": "BTC",
        //         "client_oid": "d1c70a60-1234-4c92-b2a0-72b931cb31e0",
        //         "taker_side": "TAKER",
        //         "order_id": "6142909895036331486",
        //         "create_time_ns": "*************207066"
        //     }
        //
        object timestamp = this.safeInteger2(trade, "t", "create_time");
        object marketId = this.safeString2(trade, "i", "instrument_name");
        market = this.safeMarket(marketId, market, "_");
        object feeCurrency = this.safeString(trade, "fee_instrument_name");
        object feeCostString = this.safeString(trade, "fees");
        return this.safeTrade(new Dictionary<string, object>() {
            { "info", trade },
            { "id", this.safeString2(trade, "d", "trade_id") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", getValue(market, "symbol") },
            { "order", this.safeString(trade, "order_id") },
            { "side", this.safeStringLower2(trade, "s", "side") },
            { "takerOrMaker", this.safeStringLower(trade, "taker_side") },
            { "price", this.safeNumber2(trade, "p", "traded_price") },
            { "amount", this.safeNumber2(trade, "q", "traded_quantity") },
            { "cost", null },
            { "type", null },
            { "fee", new Dictionary<string, object>() {
                { "currency", this.safeCurrencyCode(feeCurrency) },
                { "cost", this.parseNumber(Precise.stringNeg(feeCostString)) },
            } },
        }, market);
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        //
        //     {
        //         "o": "26949.89",
        //         "h": "26957.64",
        //         "l": "26948.24",
        //         "c": "26950.00",
        //         "v": "0.0670",
        //         "t": 1687237080000
        //     }
        //
        return new List<object> {this.safeInteger(ohlcv, "t"), this.safeNumber(ohlcv, "o"), this.safeNumber(ohlcv, "h"), this.safeNumber(ohlcv, "l"), this.safeNumber(ohlcv, "c"), this.safeNumber(ohlcv, "v")};
    }

    public virtual object parseOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "ACTIVE", "open" },
            { "CANCELED", "canceled" },
            { "FILLED", "closed" },
            { "REJECTED", "rejected" },
            { "EXPIRED", "expired" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object parseTimeInForce(object timeInForce)
    {
        object timeInForces = new Dictionary<string, object>() {
            { "GOOD_TILL_CANCEL", "GTC" },
            { "IMMEDIATE_OR_CANCEL", "IOC" },
            { "FILL_OR_KILL", "FOK" },
        };
        return this.safeString(timeInForces, timeInForce, timeInForce);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        // createOrder, cancelOrder
        //
        //     {
        //         "order_id": "6540219377766741832",
        //         "client_oid": "CCXT_d6ef7c3db6c1495aa8b757"
        //     }
        //
        // fetchOpenOrders, fetchOrder, fetchOrders
        //
        //     {
        //         "account_id": "ce075bef-1234-4321-bd6g-ff9007252e63",
        //         "order_id": "6530219477767564494",
        //         "client_oid": "CCXT_7ce730f0388441df9bc218",
        //         "order_type": "LIMIT",
        //         "time_in_force": "GOOD_TILL_CANCEL",
        //         "side": "BUY",
        //         "exec_inst": [ ],
        //         "quantity": "0.00020",
        //         "limit_price": "20000.00",
        //         "order_value": "4",
        //         "avg_price": "0",
        //         "trigger_price": "0",
        //         "ref_price": "0",
        //         "cumulative_quantity": "0",
        //         "cumulative_value": "0",
        //         "cumulative_fee": "0",
        //         "status": "ACTIVE",
        //         "update_user_id": "ce075bef-1234-4321-bd6g-gg9007252e63",
        //         "order_date": "2023-06-15",
        //         "instrument_name": "BTC_USD",
        //         "fee_instrument_name": "BTC",
        //         "create_time": 1686806053992,
        //         "create_time_ns": "1686806053992921880",
        //         "update_time": 1686806053993
        //     }
        //
        // createOrders
        //     {
        //             "code" : 306,
        //             "index" : 1,
        //             "client_oid" : "1698068111133_1",
        //             "message" : "INSUFFICIENT_AVAILABLE_BALANCE",
        //             "order_id" : "6142909896519488207"
        //     }
        //
        object code = this.safeInteger(order, "code");
        if (isTrue(isTrue((!isEqual(code, null))) && isTrue((!isEqual(code, 0)))))
        {
            return this.safeOrder(new Dictionary<string, object>() {
                { "id", this.safeString(order, "order_id") },
                { "clientOrderId", this.safeString(order, "client_oid") },
                { "info", order },
                { "status", "rejected" },
            });
        }
        object created = this.safeInteger(order, "create_time");
        object marketId = this.safeString(order, "instrument_name");
        object symbol = this.safeSymbol(marketId, market);
        object execInst = this.safeValue(order, "exec_inst");
        object postOnly = null;
        if (isTrue(!isEqual(execInst, null)))
        {
            postOnly = false;
            for (object i = 0; isLessThan(i, getArrayLength(execInst)); postFixIncrement(ref i))
            {
                object inst = getValue(execInst, i);
                if (isTrue(isEqual(inst, "POST_ONLY")))
                {
                    postOnly = true;
                    break;
                }
            }
        }
        object feeCurrency = this.safeString(order, "fee_instrument_name");
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", order },
            { "id", this.safeString(order, "order_id") },
            { "clientOrderId", this.safeString(order, "client_oid") },
            { "timestamp", created },
            { "datetime", this.iso8601(created) },
            { "lastTradeTimestamp", this.safeInteger(order, "update_time") },
            { "status", this.parseOrderStatus(this.safeString(order, "status")) },
            { "symbol", symbol },
            { "type", this.safeStringLower(order, "order_type") },
            { "timeInForce", this.parseTimeInForce(this.safeString(order, "time_in_force")) },
            { "postOnly", postOnly },
            { "side", this.safeStringLower(order, "side") },
            { "price", this.safeNumber(order, "limit_price") },
            { "amount", this.safeNumber(order, "quantity") },
            { "filled", this.safeNumber(order, "cumulative_quantity") },
            { "remaining", null },
            { "average", this.safeNumber(order, "avg_price") },
            { "cost", this.safeNumber(order, "cumulative_value") },
            { "fee", new Dictionary<string, object>() {
                { "currency", this.safeCurrencyCode(feeCurrency) },
                { "cost", this.safeNumber(order, "cumulative_fee") },
            } },
            { "trades", new List<object>() {} },
        }, market);
    }

    public virtual object parseDepositStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "0", "pending" },
            { "1", "ok" },
            { "2", "failed" },
            { "3", "pending" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object parseWithdrawalStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "0", "pending" },
            { "1", "pending" },
            { "2", "failed" },
            { "3", "pending" },
            { "4", "failed" },
            { "5", "ok" },
            { "6", "canceled" },
        };
        return this.safeString(statuses, status, status);
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        // fetchDeposits
        //
        //     {
        //         "currency": "BTC",
        //         "fee": 0,
        //         "create_time": 1688023659000,
        //         "id": "6201135",
        //         "update_time": 1688178509000,
        //         "amount": 0.00114571,
        //         "address": "**********************************",
        //         "status": "1",
        //         "txid": "f0ae4202b76eb999c301eccdde44dc639bee42d1fdd5974105286ca3393f6065/2"
        //     }
        //
        // fetchWithdrawals
        //
        //     {
        //         "currency": "BTC",
        //         "client_wid": "",
        //         "fee": 0.0005,
        //         "create_time": 1688613850000,
        //         "id": "5775977",
        //         "update_time": 1688613850000,
        //         "amount": 0.0005,
        //         "address": "**********************************",
        //         "status": "1",
        //         "txid": "",
        //         "network_id": "BTC"
        //     }
        //
        // withdraw
        //
        //     {
        //         "id": 2220,
        //         "amount": 1,
        //         "fee": 0.0004,
        //         "symbol": "BTC",
        //         "address": "2NBqqD5GRJ8wHy1PYyCXTe9ke5226FhavBf",
        //         "client_wid": "my_withdrawal_002",
        //         "create_time":1607063412000
        //     }
        //
        object type = null;
        object rawStatus = this.safeString(transaction, "status");
        object status = null;
        if (isTrue(inOp(transaction, "client_wid")))
        {
            type = "withdrawal";
            status = this.parseWithdrawalStatus(rawStatus);
        } else
        {
            type = "deposit";
            status = this.parseDepositStatus(rawStatus);
        }
        object addressString = this.safeString(transaction, "address");
        var addresstagVariable = this.parseAddress(addressString);
        var address = ((IList<object>) addresstagVariable)[0];
        var tag = ((IList<object>) addresstagVariable)[1];
        object currencyId = this.safeString(transaction, "currency");
        object code = this.safeCurrencyCode(currencyId, currency);
        object timestamp = this.safeInteger(transaction, "create_time");
        object feeCost = this.safeNumber(transaction, "fee");
        object fee = null;
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "currency", code },
                { "cost", feeCost },
            };
        }
        return new Dictionary<string, object>() {
            { "info", transaction },
            { "id", this.safeString(transaction, "id") },
            { "txid", this.safeString(transaction, "txid") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "network", null },
            { "address", address },
            { "addressTo", address },
            { "addressFrom", null },
            { "tag", tag },
            { "tagTo", tag },
            { "tagFrom", null },
            { "type", type },
            { "amount", this.safeNumber(transaction, "amount") },
            { "currency", code },
            { "status", status },
            { "updated", this.safeInteger(transaction, "update_time") },
            { "internal", null },
            { "comment", this.safeString(transaction, "client_wid") },
            { "fee", fee },
        };
    }

    public virtual object customHandleMarginModeAndParams(object methodName, object parameters = null)
    {
        /**
        * @ignore
        * @method
        * @description marginMode specified by params["marginMode"], this.options["marginMode"], this.options["defaultMarginMode"], params["margin"] = true or this.options["defaultType"] = 'margin'
        * @param {object} [params] extra parameters specific to the exchange API endpoint
        * @returns {Array} the marginMode in lowercase
        */
        parameters ??= new Dictionary<string, object>();
        object defaultType = this.safeString(this.options, "defaultType");
        object isMargin = this.safeBool(parameters, "margin", false);
        parameters = this.omit(parameters, "margin");
        object marginMode = null;
        var marginModeparametersVariable = this.handleMarginModeAndParams(methodName, parameters);
        marginMode = ((IList<object>)marginModeparametersVariable)[0];
        parameters = ((IList<object>)marginModeparametersVariable)[1];
        if (isTrue(!isEqual(marginMode, null)))
        {
            if (isTrue(!isEqual(marginMode, "cross")))
            {
                throw new NotSupported ((string)add(this.id, " only cross margin is supported")) ;
            }
        } else
        {
            if (isTrue(isTrue((isEqual(defaultType, "margin"))) || isTrue((isEqual(isMargin, true)))))
            {
                marginMode = "cross";
            }
        }
        return new List<object>() {marginMode, parameters};
    }

    public override object parseDepositWithdrawFee(object fee, object currency = null)
    {
        //
        //    {
        //        "full_name": "Alchemix",
        //        "default_network": "ETH",
        //        "network_list": [
        //          {
        //            "network_id": "ETH",
        //            "withdrawal_fee": "0.25000000",
        //            "withdraw_enabled": true,
        //            "min_withdrawal_amount": "0.5",
        //            "deposit_enabled": true,
        //            "confirmation_required": "0"
        //          }
        //        ]
        //    }
        //
        object networkList = this.safeList(fee, "network_list", new List<object>() {});
        object networkListLength = getArrayLength(networkList);
        object result = new Dictionary<string, object>() {
            { "info", fee },
            { "withdraw", new Dictionary<string, object>() {
                { "fee", null },
                { "percentage", null },
            } },
            { "deposit", new Dictionary<string, object>() {
                { "fee", null },
                { "percentage", null },
            } },
            { "networks", new Dictionary<string, object>() {} },
        };
        if (isTrue(!isEqual(networkList, null)))
        {
            for (object i = 0; isLessThan(i, networkListLength); postFixIncrement(ref i))
            {
                object networkInfo = getValue(networkList, i);
                object networkId = this.safeString(networkInfo, "network_id");
                object currencyCode = this.safeString(currency, "code");
                object networkCode = this.networkIdToCode(networkId, currencyCode);
                ((IDictionary<string,object>)getValue(result, "networks"))[(string)networkCode] = new Dictionary<string, object>() {
                    { "deposit", new Dictionary<string, object>() {
                        { "fee", null },
                        { "percentage", null },
                    } },
                    { "withdraw", new Dictionary<string, object>() {
                        { "fee", this.safeNumber(networkInfo, "withdrawal_fee") },
                        { "percentage", false },
                    } },
                };
                if (isTrue(isEqual(networkListLength, 1)))
                {
                    ((IDictionary<string,object>)getValue(result, "withdraw"))["fee"] = this.safeNumber(networkInfo, "withdrawal_fee");
                    ((IDictionary<string,object>)getValue(result, "withdraw"))["percentage"] = false;
                }
            }
        }
        return result;
    }

    /**
     * @method
     * @name cryptocom#fetchDepositWithdrawFees
     * @description fetch deposit and withdraw fees
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-currency-networks
     * @param {string[]|undefined} codes list of unified currency codes
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchDepositWithdrawFees(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.v1PrivatePostPrivateGetCurrencyNetworks(parameters);
        object data = this.safeValue(response, "result");
        object currencyMap = this.safeList(data, "currency_map");
        return this.parseDepositWithdrawFees(currencyMap, codes, "full_name");
    }

    /**
     * @method
     * @name cryptocom#fetchLedger
     * @description fetch the history of changes, actions done by the user or operations that altered the balance of the user
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-transactions
     * @param {string} [code] unified currency code
     * @param {int} [since] timestamp in ms of the earliest ledger entry
     * @param {int} [limit] max number of ledger entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms for the ending date filter, default is the current time
     * @returns {object} a [ledger structure]{@link https://docs.ccxt.com/#/?id=ledger}
     */
    public async override Task<object> fetchLedger(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.safeCurrency(code);
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start_time"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["end_time"] = until;
        }
        object response = await this.v1PrivatePostPrivateGetTransactions(this.extend(request, parameters));
        //
        //     {
        //         "id": *************,
        //         "method": "private/get-transactions",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "account_id": "ce075cef-1234-4321-bd6e-gf9007351e64",
        //                     "event_date": "2023-06-15",
        //                     "journal_type": "TRADING",
        //                     "journal_id": "6530219460124075091",
        //                     "transaction_qty": "6.0091224",
        //                     "transaction_cost": "6.0091224",
        //                     "realized_pnl": "0",
        //                     "order_id": "6530219477766741833",
        //                     "trade_id": "6530219495775954765",
        //                     "trade_match_id": "4611686018455865176",
        //                     "event_timestamp_ms": *************,
        //                     "event_timestamp_ns": "*************642422",
        //                     "client_oid": "CCXT_d6ea7c5db6c1495aa8b758",
        //                     "taker_side": "",
        //                     "side": "BUY",
        //                     "instrument_name": "USD"
        //                 },
        //             ]
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object ledger = this.safeList(result, "data", new List<object>() {});
        return this.parseLedger(ledger, currency, since, limit);
    }

    public override object parseLedgerEntry(object item, object currency = null)
    {
        //
        //     {
        //         "account_id": "ce075cef-1234-4321-bd6e-gf9007351e64",
        //         "event_date": "2023-06-15",
        //         "journal_type": "TRADING",
        //         "journal_id": "6530219460124075091",
        //         "transaction_qty": "6.0091224",
        //         "transaction_cost": "6.0091224",
        //         "realized_pnl": "0",
        //         "order_id": "6530219477766741833",
        //         "trade_id": "6530219495775954765",
        //         "trade_match_id": "4611686018455865176",
        //         "event_timestamp_ms": *************,
        //         "event_timestamp_ns": "*************642422",
        //         "client_oid": "CCXT_d6ea7c5db6c1495aa8b758",
        //         "taker_side": "",
        //         "side": "BUY",
        //         "instrument_name": "USD"
        //     }
        //
        object timestamp = this.safeInteger(item, "event_timestamp_ms");
        object currencyId = this.safeString(item, "instrument_name");
        object code = this.safeCurrencyCode(currencyId, currency);
        currency = this.safeCurrency(currencyId, currency);
        object amount = this.safeString(item, "transaction_qty");
        object direction = null;
        if (isTrue(Precise.stringLt(amount, "0")))
        {
            direction = "out";
            amount = Precise.stringAbs(amount);
        } else
        {
            direction = "in";
        }
        return this.safeLedgerEntry(new Dictionary<string, object>() {
            { "info", item },
            { "id", this.safeString(item, "order_id") },
            { "direction", direction },
            { "account", this.safeString(item, "account_id") },
            { "referenceId", this.safeString(item, "trade_id") },
            { "referenceAccount", this.safeString(item, "trade_match_id") },
            { "type", this.parseLedgerEntryType(this.safeString(item, "journal_type")) },
            { "currency", code },
            { "amount", this.parseNumber(amount) },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "before", null },
            { "after", null },
            { "status", null },
            { "fee", new Dictionary<string, object>() {
                { "currency", null },
                { "cost", null },
            } },
        }, currency);
    }

    public virtual object parseLedgerEntryType(object type)
    {
        object ledgerType = new Dictionary<string, object>() {
            { "TRADING", "trade" },
            { "TRADE_FEE", "fee" },
            { "WITHDRAW_FEE", "fee" },
            { "WITHDRAW", "withdrawal" },
            { "DEPOSIT", "deposit" },
            { "ROLLBACK_WITHDRAW", "rollback" },
            { "ROLLBACK_DEPOSIT", "rollback" },
            { "FUNDING", "fee" },
            { "REALIZED_PNL", "trade" },
            { "INSURANCE_FUND", "insurance" },
            { "SOCIALIZED_LOSS", "trade" },
            { "LIQUIDATION_FEE", "fee" },
            { "SESSION_RESET", "reset" },
            { "ADJUSTMENT", "adjustment" },
            { "SESSION_SETTLE", "settlement" },
            { "UNCOVERED_LOSS", "trade" },
            { "ADMIN_ADJUSTMENT", "adjustment" },
            { "DELIST", "delist" },
            { "SETTLEMENT_FEE", "fee" },
            { "AUTO_CONVERSION", "conversion" },
            { "MANUAL_CONVERSION", "conversion" },
        };
        return this.safeString(ledgerType, type, type);
    }

    /**
     * @method
     * @name cryptocom#fetchAccounts
     * @description fetch all the accounts associated with a profile
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-accounts
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [account structures]{@link https://docs.ccxt.com/#/?id=account-structure} indexed by the account type
     */
    public async override Task<object> fetchAccounts(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.v1PrivatePostPrivateGetAccounts(parameters);
        //
        //     {
        //         "id": *************,
        //         "method": "private/get-accounts",
        //         "code": 0,
        //         "result": {
        //             "master_account": {
        //                 "uuid": "a1234abc-1234-4321-q5r7-b1ab0a0b12b",
        //                 "user_uuid": "a1234abc-1234-4321-q5r7-b1ab0a0b12b",
        //                 "enabled": true,
        //                 "tradable": true,
        //                 "name": "YOUR_NAME",
        //                 "country_code": "CAN",
        //                 "phone_country_code": "CAN",
        //                 "incorp_country_code": "",
        //                 "margin_access": "DEFAULT",
        //                 "derivatives_access": "DEFAULT",
        //                 "create_time": *************,
        //                 "update_time": *************,
        //                 "two_fa_enabled": true,
        //                 "kyc_level": "ADVANCED",
        //                 "suspended": false,
        //                 "terminated": false,
        //                 "spot_enabled": false,
        //                 "margin_enabled": false,
        //                 "derivatives_enabled": false
        //             },
        //             "sub_account_list": []
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object masterAccount = this.safeDict(result, "master_account", new Dictionary<string, object>() {});
        object accounts = this.safeList(result, "sub_account_list", new List<object>() {});
        ((IList<object>)accounts).Add(masterAccount);
        return this.parseAccounts(accounts, parameters);
    }

    public override object parseAccount(object account)
    {
        //
        //     {
        //         "uuid": "a1234abc-1234-4321-q5r7-b1ab0a0b12b",
        //         "user_uuid": "a1234abc-1234-4321-q5r7-b1ab0a0b12b",
        //         "master_account_uuid": "a1234abc-1234-4321-q5r7-b1ab0a0b12b",
        //         "label": "FORMER_MASTER_MARGIN",
        //         "enabled": true,
        //         "tradable": true,
        //         "name": "YOUR_NAME",
        //         "country_code": "YOUR_COUNTRY_CODE",
        //         "incorp_country_code": "",
        //         "margin_access": "DEFAULT",
        //         "derivatives_access": "DEFAULT",
        //         "create_time": *************,
        //         "update_time": *************,
        //         "two_fa_enabled": false,
        //         "kyc_level": "ADVANCED",
        //         "suspended": false,
        //         "terminated": false,
        //         "spot_enabled": false,
        //         "margin_enabled": false,
        //         "derivatives_enabled": false,
        //         "system_label": "FORMER_MASTER_MARGIN"
        //     }
        //
        return new Dictionary<string, object>() {
            { "id", this.safeString(account, "uuid") },
            { "type", this.safeString(account, "label") },
            { "code", null },
            { "info", account },
        };
    }

    /**
     * @method
     * @name cryptocom#fetchSettlementHistory
     * @description fetches historical settlement records
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-expired-settlement-price
     * @param {string} symbol unified market symbol of the settlement history
     * @param {int} [since] timestamp in ms
     * @param {int} [limit] number of records
     * @param {object} [params] exchange specific params
     * @param {int} [params.type] 'future', 'option'
     * @returns {object[]} a list of [settlement history objects]{@link https://docs.ccxt.com/#/?id=settlement-history-structure}
     */
    public async virtual Task<object> fetchSettlementHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
        }
        object type = null;
        var typeparametersVariable = this.handleMarketTypeAndParams("fetchSettlementHistory", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        this.checkRequiredArgument("fetchSettlementHistory", type, "type", new List<object>() {"future", "option", "WARRANT", "FUTURE"});
        if (isTrue(isEqual(type, "option")))
        {
            type = "WARRANT";
        }
        object request = new Dictionary<string, object>() {
            { "instrument_type", ((string)type).ToUpper() },
        };
        object response = await this.v1PublicGetPublicGetExpiredSettlementPrice(this.extend(request, parameters));
        //
        //     {
        //         "id": -1,
        //         "method": "public/get-expired-settlement-price",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "i": "BTCUSD-230526",
        //                     "x": 1685088000000,
        //                     "v": "26464.1",
        //                     "t": 1685087999500
        //                 }
        //             ]
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "data", new List<object>() {});
        object settlements = this.parseSettlements(data, market);
        object sorted = this.sortBy(settlements, "timestamp");
        return this.filterBySymbolSinceLimit(sorted, symbol, since, limit);
    }

    public virtual object parseSettlement(object settlement, object market)
    {
        //
        //     {
        //         "i": "BTCUSD-230526",
        //         "x": 1685088000000,
        //         "v": "26464.1",
        //         "t": 1685087999500
        //     }
        //
        object timestamp = this.safeInteger(settlement, "x");
        object marketId = this.safeString(settlement, "i");
        return new Dictionary<string, object>() {
            { "info", settlement },
            { "symbol", this.safeSymbol(marketId, market) },
            { "price", this.safeNumber(settlement, "v") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        };
    }

    public virtual object parseSettlements(object settlements, object market)
    {
        //
        //     [
        //         {
        //             "i": "BTCUSD-230526",
        //             "x": 1685088000000,
        //             "v": "26464.1",
        //             "t": 1685087999500
        //         }
        //     ]
        //
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(settlements)); postFixIncrement(ref i))
        {
            ((IList<object>)result).Add(this.parseSettlement(getValue(settlements, i), market));
        }
        return result;
    }

    /**
     * @method
     * @name cryptocom#fetchFundingRate
     * @description fetches historical funding rates
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-valuations
     * @param {string} symbol unified symbol of the market to fetch the funding rate history for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-history-structure}
     */
    public async override Task<object> fetchFundingRate(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "swap")))
        {
            throw new BadSymbol ((string)add(this.id, " fetchFundingRate() supports swap contracts only")) ;
        }
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
            { "valuation_type", "estimated_funding_rate" },
            { "count", 1 },
        };
        object response = await this.v1PublicGetPublicGetValuations(this.extend(request, parameters));
        //
        //     {
        //         "id": -1,
        //         "method": "public/get-valuations",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "v": "-0.000001884",
        //                     "t": 1687892400000
        //                 },
        //             ],
        //             "instrument_name": "BTCUSD-PERP"
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "data", new List<object>() {});
        object entry = this.safeDict(data, 0, new Dictionary<string, object>() {});
        return this.parseFundingRate(entry, market);
    }

    public override object parseFundingRate(object contract, object market = null)
    {
        //
        //                 {
        //                     "v": "-0.000001884",
        //                     "t": 1687892400000
        //                 },
        //
        object timestamp = this.safeInteger(contract, "t");
        object fundingTimestamp = null;
        if (isTrue(!isEqual(timestamp, null)))
        {
            fundingTimestamp = multiply(Math.Ceiling(Convert.ToDouble(divide(timestamp, 3600000))), 3600000); // end of the next hour
        }
        return new Dictionary<string, object>() {
            { "info", contract },
            { "symbol", this.safeSymbol(null, market) },
            { "markPrice", null },
            { "indexPrice", null },
            { "interestRate", null },
            { "estimatedSettlePrice", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "fundingRate", this.safeNumber(contract, "v") },
            { "fundingTimestamp", fundingTimestamp },
            { "fundingDatetime", this.iso8601(fundingTimestamp) },
            { "nextFundingRate", null },
            { "nextFundingTimestamp", null },
            { "nextFundingDatetime", null },
            { "previousFundingRate", null },
            { "previousFundingTimestamp", null },
            { "previousFundingDatetime", null },
            { "interval", "1h" },
        };
    }

    /**
     * @method
     * @name cryptocom#fetchFundingRateHistory
     * @description fetches historical funding rates
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#public-get-valuations
     * @param {string} symbol unified symbol of the market to fetch the funding rate history for
     * @param {int} [since] timestamp in ms of the earliest funding rate to fetch
     * @param {int} [limit] the maximum amount of [funding rate structures] to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms for the ending date filter, default is the current time
     * @param {boolean} [params.paginate] default false, when true will automatically paginate by calling this endpoint multiple times. See in the docs all the [availble parameters](https://github.com/ccxt/ccxt/wiki/Manual#pagination-params)
     * @returns {object[]} a list of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-history-structure}
     */
    public async override Task<object> fetchFundingRateHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchFundingRateHistory() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object paginate = false;
        var paginateparametersVariable = this.handleOptionAndParams(parameters, "fetchFundingRateHistory", "paginate");
        paginate = ((IList<object>)paginateparametersVariable)[0];
        parameters = ((IList<object>)paginateparametersVariable)[1];
        if (isTrue(paginate))
        {
            return await this.fetchPaginatedCallDeterministic("fetchFundingRateHistory", symbol, since, limit, "8h", parameters);
        }
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "swap")))
        {
            throw new BadSymbol ((string)add(this.id, " fetchFundingRateHistory() supports swap contracts only")) ;
        }
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
            { "valuation_type", "funding_hist" },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["start_ts"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["count"] = limit;
        }
        object until = this.safeInteger(parameters, "until");
        parameters = this.omit(parameters, new List<object>() {"until"});
        if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["end_ts"] = until;
        }
        object response = await this.v1PublicGetPublicGetValuations(this.extend(request, parameters));
        //
        //     {
        //         "id": -1,
        //         "method": "public/get-valuations",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "v": "-0.000001884",
        //                     "t": 1687892400000
        //                 },
        //             ],
        //             "instrument_name": "BTCUSD-PERP"
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "data", new List<object>() {});
        object marketId = this.safeString(result, "instrument_name");
        object rates = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(data)); postFixIncrement(ref i))
        {
            object entry = getValue(data, i);
            object timestamp = this.safeInteger(entry, "t");
            ((IList<object>)rates).Add(new Dictionary<string, object>() {
                { "info", entry },
                { "symbol", this.safeSymbol(marketId, market) },
                { "fundingRate", this.safeNumber(entry, "v") },
                { "timestamp", timestamp },
                { "datetime", this.iso8601(timestamp) },
            });
        }
        object sorted = this.sortBy(rates, "timestamp");
        return this.filterBySymbolSinceLimit(sorted, getValue(market, "symbol"), since, limit);
    }

    /**
     * @method
     * @name cryptocom#fetchPosition
     * @description fetch data on a single open contract trade position
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-positions
     * @param {string} symbol unified market symbol of the market the position is held in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPosition(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
        };
        object response = await this.v1PrivatePostPrivateGetPositions(this.extend(request, parameters));
        //
        //     {
        //         "id": *************,
        //         "method": "private/get-positions",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "account_id": "ce075bef-b600-4277-bd6e-ff9007251e63",
        //                     "quantity": "0.0001",
        //                     "cost": "3.02392",
        //                     "open_pos_cost": "3.02392",
        //                     "open_position_pnl": "-0.**********",
        //                     "session_pnl": "-0.**********",
        //                     "update_timestamp_ms": *************,
        //                     "instrument_name": "BTCUSD-PERP",
        //                     "type": "PERPETUAL_SWAP"
        //                 }
        //             ]
        //         }
        //     }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object data = this.safeList(result, "data", new List<object>() {});
        return this.parsePosition(this.safeDict(data, 0), market);
    }

    /**
     * @method
     * @name cryptocom#fetchPositions
     * @description fetch all open positions
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-positions
     * @param {string[]|undefined} symbols list of unified market symbols
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPositions(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbols, null)))
        {
            object symbol = null;
            if (isTrue(((symbols is IList<object>) || (symbols.GetType().IsGenericType && symbols.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
            {
                object symbolsLength = getArrayLength(symbols);
                if (isTrue(isGreaterThan(symbolsLength, 1)))
                {
                    throw new BadRequest ((string)add(this.id, " fetchPositions() symbols argument cannot contain more than 1 symbol")) ;
                }
                symbol = getValue(symbols, 0);
            } else
            {
                symbol = symbols;
            }
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["instrument_name"] = getValue(market, "id");
        }
        object response = await this.v1PrivatePostPrivateGetPositions(this.extend(request, parameters));
        //
        //     {
        //         "id": *************,
        //         "method": "private/get-positions",
        //         "code": 0,
        //         "result": {
        //             "data": [
        //                 {
        //                     "account_id": "ce075bef-b600-4277-bd6e-ff9007251e63",
        //                     "quantity": "0.0001",
        //                     "cost": "3.02392",
        //                     "open_pos_cost": "3.02392",
        //                     "open_position_pnl": "-0.**********",
        //                     "session_pnl": "-0.**********",
        //                     "update_timestamp_ms": *************,
        //                     "instrument_name": "BTCUSD-PERP",
        //                     "type": "PERPETUAL_SWAP"
        //                 }
        //             ]
        //         }
        //     }
        //
        object responseResult = this.safeDict(response, "result", new Dictionary<string, object>() {});
        object positions = this.safeList(responseResult, "data", new List<object>() {});
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(positions)); postFixIncrement(ref i))
        {
            object entry = getValue(positions, i);
            object marketId = this.safeString(entry, "instrument_name");
            object marketInner = this.safeMarket(marketId, null, null, "contract");
            ((IList<object>)result).Add(this.parsePosition(entry, marketInner));
        }
        return this.filterByArrayPositions(result, "symbol", null, false);
    }

    public override object parsePosition(object position, object market = null)
    {
        //
        //     {
        //         "account_id": "ce075bef-b600-4277-bd6e-ff9007251e63",
        //         "quantity": "0.0001",
        //         "cost": "3.02392",
        //         "open_pos_cost": "3.02392",
        //         "open_position_pnl": "-0.**********",
        //         "session_pnl": "-0.**********",
        //         "update_timestamp_ms": *************,
        //         "instrument_name": "BTCUSD-PERP",
        //         "type": "PERPETUAL_SWAP"
        //     }
        //
        object marketId = this.safeString(position, "instrument_name");
        market = this.safeMarket(marketId, market, null, "contract");
        object symbol = this.safeSymbol(marketId, market, null, "contract");
        object timestamp = this.safeInteger(position, "update_timestamp_ms");
        object amount = this.safeString(position, "quantity");
        return this.safePosition(new Dictionary<string, object>() {
            { "info", position },
            { "id", null },
            { "symbol", symbol },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "hedged", null },
            { "side", ((bool) isTrue(Precise.stringGt(amount, "0"))) ? "buy" : "sell" },
            { "contracts", Precise.stringAbs(amount) },
            { "contractSize", getValue(market, "contractSize") },
            { "entryPrice", null },
            { "markPrice", null },
            { "notional", null },
            { "leverage", null },
            { "collateral", this.safeNumber(position, "open_pos_cost") },
            { "initialMargin", this.safeNumber(position, "cost") },
            { "maintenanceMargin", null },
            { "initialMarginPercentage", null },
            { "maintenanceMarginPercentage", null },
            { "unrealizedPnl", this.safeNumber(position, "open_position_pnl") },
            { "liquidationPrice", null },
            { "marginMode", null },
            { "percentage", null },
            { "marginRatio", null },
            { "stopLossPrice", null },
            { "takeProfitPrice", null },
        });
    }

    public override object nonce()
    {
        return this.milliseconds();
    }

    public virtual object paramsToString(object obj, object level)
    {
        object maxLevel = 3;
        if (isTrue(isGreaterThanOrEqual(level, maxLevel)))
        {
            return ((object)obj).ToString();
        }
        if (isTrue((obj is string)))
        {
            return obj;
        }
        object returnString = "";
        object paramsKeys = null;
        if (isTrue(((obj is IList<object>) || (obj.GetType().IsGenericType && obj.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            paramsKeys = obj;
        } else
        {
            object sorted = this.keysort(obj);
            paramsKeys = new List<object>(((IDictionary<string,object>)sorted).Keys);
        }
        for (object i = 0; isLessThan(i, getArrayLength(paramsKeys)); postFixIncrement(ref i))
        {
            object key = getValue(paramsKeys, i);
            returnString = add(returnString, key);
            object value = getValue(obj, key);
            if (isTrue(isEqual(value, "undefined")))
            {
                returnString = add(returnString, "null");
            } else if (isTrue(((value is IList<object>) || (value.GetType().IsGenericType && value.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
            {
                for (object j = 0; isLessThan(j, getArrayLength(value)); postFixIncrement(ref j))
                {
                    returnString = add(returnString, this.paramsToString(getValue(value, j), add(level, 1)));
                }
            } else
            {
                returnString = add(returnString, ((object)value).ToString());
            }
        }
        return returnString;
    }

    /**
     * @method
     * @name cryptocom#closePositions
     * @description closes open positions for a market
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-close-position
     * @param {string} symbol Unified CCXT market symbol
     * @param {string} [side] not used by cryptocom.closePositions
     * @param {object} [params] extra parameters specific to the okx api endpoint
     *
     * EXCHANGE SPECIFIC PARAMETERS
     * @param {string} [params.type] LIMIT or MARKET
     * @param {number} [params.price] for limit orders only
     * @returns {object[]} [A list of position structures]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> closePosition(object symbol, object side = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
            { "type", "MARKET" },
        };
        object type = this.safeStringUpper(parameters, "type");
        object price = this.safeString(parameters, "price");
        if (isTrue(!isEqual(type, null)))
        {
            ((IDictionary<string,object>)request)["type"] = type;
        }
        if (isTrue(!isEqual(price, null)))
        {
            ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(getValue(market, "symbol"), price);
        }
        object response = await this.v1PrivatePostPrivateClosePosition(this.extend(request, parameters));
        //
        //    {
        //        "id" : 1700830813298,
        //        "method" : "private/close-position",
        //        "code" : 0,
        //        "result" : {
        //            "client_oid" : "179a909d-5614-655b-0d0e-9e85c9a25c85",
        //            "order_id" : "6142909897021751347"
        //        }
        //    }
        //
        object result = this.safeDict(response, "result");
        return this.parseOrder(result, market);
    }

    /**
     * @method
     * @name cryptocom#fetchTradingFee
     * @description fetch the trading fees for a market
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-instrument-fee-rate
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [fee structure]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchTradingFee(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "instrument_name", getValue(market, "id") },
        };
        object response = await this.v1PrivatePostPrivateGetInstrumentFeeRate(this.extend(request, parameters));
        //
        //    {
        //        "id": 1,
        //        "code": 0,
        //        "method": "private/staking/unstake",
        //        "result": {
        //          "staking_id": "1",
        //          "instrument_name": "SOL.staked",
        //          "status": "NEW",
        //          "quantity": "1",
        //          "underlying_inst_name": "SOL",
        //          "reason": "NO_ERROR"
        //        }
        //    }
        //
        object data = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseTradingFee(data, market);
    }

    /**
     * @method
     * @name cryptocom#fetchTradingFees
     * @see https://exchange-docs.crypto.com/exchange/v1/rest-ws/index.html#private-get-fee-rate
     * @description fetch the trading fees for multiple markets
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
     */
    public async override Task<object> fetchTradingFees(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.v1PrivatePostPrivateGetFeeRate(parameters);
        //
        //   {
        //       "id": 1,
        //       "method": "/private/get-fee-rate",
        //       "code": 0,
        //       "result": {
        //         "spot_tier": "3",
        //         "deriv_tier": "3",
        //         "effective_spot_maker_rate_bps": "6.5",
        //         "effective_spot_taker_rate_bps": "6.9",
        //         "effective_deriv_maker_rate_bps": "1.1",
        //         "effective_deriv_taker_rate_bps": "3"
        //       }
        //   }
        //
        object result = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseTradingFees(result);
    }

    public virtual object parseTradingFees(object response)
    {
        //
        // {
        //         "spot_tier": "3",
        //         "deriv_tier": "3",
        //         "effective_spot_maker_rate_bps": "6.5",
        //         "effective_spot_taker_rate_bps": "6.9",
        //         "effective_deriv_maker_rate_bps": "1.1",
        //         "effective_deriv_taker_rate_bps": "3"
        //  }
        //
        object result = new Dictionary<string, object>() {};
        ((IDictionary<string,object>)result)["info"] = response;
        for (object i = 0; isLessThan(i, getArrayLength(this.symbols)); postFixIncrement(ref i))
        {
            object symbol = getValue(this.symbols, i);
            object market = this.market(symbol);
            object isSwap = getValue(market, "swap");
            object takerFeeKey = ((bool) isTrue(isSwap)) ? "effective_deriv_taker_rate_bps" : "effective_spot_taker_rate_bps";
            object makerFeeKey = ((bool) isTrue(isSwap)) ? "effective_deriv_maker_rate_bps" : "effective_spot_maker_rate_bps";
            object tradingFee = new Dictionary<string, object>() {
                { "info", response },
                { "symbol", symbol },
                { "maker", this.parseNumber(Precise.stringDiv(this.safeString(response, makerFeeKey), "10000")) },
                { "taker", this.parseNumber(Precise.stringDiv(this.safeString(response, takerFeeKey), "10000")) },
                { "percentage", null },
                { "tierBased", null },
            };
            ((IDictionary<string,object>)result)[(string)symbol] = tradingFee;
        }
        return result;
    }

    public virtual object parseTradingFee(object fee, object market = null)
    {
        //
        // {
        //      "instrument_name": "BTC_USD",
        //      "effective_maker_rate_bps": "6.5",
        //      "effective_taker_rate_bps": "6.9"
        // }
        //
        object marketId = this.safeString(fee, "instrument_name");
        object symbol = this.safeSymbol(marketId, market);
        return new Dictionary<string, object>() {
            { "info", fee },
            { "symbol", symbol },
            { "maker", this.parseNumber(Precise.stringDiv(this.safeString(fee, "effective_maker_rate_bps"), "10000")) },
            { "taker", this.parseNumber(Precise.stringDiv(this.safeString(fee, "effective_taker_rate_bps"), "10000")) },
            { "percentage", null },
            { "tierBased", null },
        };
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object type = this.safeString(api, 0);
        object access = this.safeString(api, 1);
        object url = add(add(getValue(getValue(this.urls, "api"), type), "/"), path);
        object query = this.omit(parameters, this.extractParams(path));
        if (isTrue(isEqual(access, "public")))
        {
            if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)query).Keys))))
            {
                url = add(url, add("?", this.urlencode(query)));
            }
        } else
        {
            this.checkRequiredCredentials();
            object nonce = ((object)this.nonce()).ToString();
            object requestParams = this.extend(new Dictionary<string, object>() {}, parameters);
            object paramsKeys = new List<object>(((IDictionary<string,object>)requestParams).Keys);
            object strSortKey = this.paramsToString(requestParams, 0);
            object payload = add(add(add(add(path, nonce), this.apiKey), strSortKey), nonce);
            object signature = this.hmac(this.encode(payload), this.encode(this.secret), sha256);
            object paramsKeysLength = getArrayLength(paramsKeys);
            body = this.json(new Dictionary<string, object>() {
                { "id", nonce },
                { "method", path },
                { "params", parameters },
                { "api_key", this.apiKey },
                { "sig", signature },
                { "nonce", nonce },
            });
            // fix issue https://github.com/ccxt/ccxt/issues/11179
            // php always encodes dictionaries as arrays
            // if an array is empty, php will put it in square brackets
            // python and js will put it in curly brackets
            // the code below checks and replaces those brackets in empty requests
            if (isTrue(isEqual(paramsKeysLength, 0)))
            {
                object paramsString = "{}";
                object arrayString = "[]";
                body = ((string)body).Replace((string)arrayString, (string)paramsString);
            }
            headers = new Dictionary<string, object>() {
                { "Content-Type", "application/json" },
            };
        }
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object code, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        object errorCode = this.safeString(response, "code");
        if (isTrue(!isEqual(errorCode, "0")))
        {
            object feedback = add(add(this.id, " "), body);
            this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), errorCode, feedback);
            throw new ExchangeError ((string)add(add(this.id, " "), body)) ;
        }
        return null;
    }
}
