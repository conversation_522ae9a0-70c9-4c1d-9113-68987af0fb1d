namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class whitebit : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "whitebit" },
            { "name", "WhiteBit" },
            { "version", "v4" },
            { "countries", new List<object>() {"EE"} },
            { "rateLimit", 50 },
            { "pro", true },
            { "has", new Dictionary<string, object>() {
                { "CORS", null },
                { "spot", true },
                { "margin", true },
                { "swap", true },
                { "future", false },
                { "option", false },
                { "cancelAllOrders", true },
                { "cancelAllOrdersAfter", true },
                { "cancelOrder", true },
                { "cancelOrders", false },
                { "createConvertTrade", true },
                { "createDepositAddress", true },
                { "createMarketBuyOrderWithCost", true },
                { "createMarketOrderWithCost", false },
                { "createMarketSellOrderWithCost", false },
                { "createOrder", true },
                { "createPostOnlyOrder", true },
                { "createStopLimitOrder", true },
                { "createStopMarketOrder", true },
                { "createStopOrder", true },
                { "createTriggerOrder", true },
                { "editOrder", false },
                { "fetchBalance", true },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchClosedOrders", true },
                { "fetchConvertQuote", true },
                { "fetchConvertTrade", false },
                { "fetchConvertTradeHistory", true },
                { "fetchCrossBorrowRate", true },
                { "fetchCrossBorrowRates", false },
                { "fetchCurrencies", true },
                { "fetchDeposit", true },
                { "fetchDepositAddress", true },
                { "fetchDepositAddresses", false },
                { "fetchDepositAddressesByNetwork", false },
                { "fetchDeposits", true },
                { "fetchDepositsWithdrawals", true },
                { "fetchDepositWithdrawFee", "emulated" },
                { "fetchDepositWithdrawFees", true },
                { "fetchFundingHistory", true },
                { "fetchFundingRate", true },
                { "fetchFundingRateHistory", false },
                { "fetchFundingRates", true },
                { "fetchIndexOHLCV", false },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchMarginMode", false },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMyTrades", true },
                { "fetchOHLCV", true },
                { "fetchOpenInterestHistory", false },
                { "fetchOpenOrders", true },
                { "fetchOrderBook", true },
                { "fetchOrderTrades", true },
                { "fetchPosition", true },
                { "fetchPositionHistory", true },
                { "fetchPositionMode", false },
                { "fetchPositions", true },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchStatus", true },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTime", true },
                { "fetchTrades", true },
                { "fetchTradingFee", false },
                { "fetchTradingFees", true },
                { "fetchTransactionFees", true },
                { "repayCrossMargin", false },
                { "repayIsolatedMargin", false },
                { "setLeverage", true },
                { "transfer", true },
                { "withdraw", true },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", "1m" },
                { "3m", "3m" },
                { "5m", "5m" },
                { "15m", "15m" },
                { "30m", "30m" },
                { "1h", "1h" },
                { "2h", "2h" },
                { "4h", "4h" },
                { "6h", "6h" },
                { "8h", "8h" },
                { "12h", "12h" },
                { "1d", "1d" },
                { "3d", "3d" },
                { "1w", "1w" },
                { "1M", "1M" },
            } },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://user-images.githubusercontent.com/1294454/66732963-8eb7dd00-ee66-11e9-849b-10d9282bb9e0.jpg" },
                { "api", new Dictionary<string, object>() {
                    { "v1", new Dictionary<string, object>() {
                        { "public", "https://whitebit.com/api/v1/public" },
                        { "private", "https://whitebit.com/api/v1" },
                    } },
                    { "v2", new Dictionary<string, object>() {
                        { "public", "https://whitebit.com/api/v2/public" },
                    } },
                    { "v4", new Dictionary<string, object>() {
                        { "public", "https://whitebit.com/api/v4/public" },
                        { "private", "https://whitebit.com/api/v4" },
                    } },
                } },
                { "www", "https://www.whitebit.com" },
                { "doc", "https://github.com/whitebit-exchange/api-docs" },
                { "fees", "https://whitebit.com/fee-schedule" },
                { "referral", "https://whitebit.com/referral/d9bdf40e-28f2-4b52-b2f9-cd1415d82963" },
            } },
            { "api", new Dictionary<string, object>() {
                { "web", new Dictionary<string, object>() {
                    { "get", new List<object>() {"v1/healthcheck"} },
                } },
                { "v1", new Dictionary<string, object>() {
                    { "public", new Dictionary<string, object>() {
                        { "get", new List<object>() {"markets", "tickers", "ticker", "symbols", "depth/result", "history", "kline"} },
                    } },
                    { "private", new Dictionary<string, object>() {
                        { "post", new List<object>() {"account/balance", "order/new", "order/cancel", "orders", "account/order_history", "account/executed_history", "account/executed_history/all", "account/order"} },
                    } },
                } },
                { "v2", new Dictionary<string, object>() {
                    { "public", new Dictionary<string, object>() {
                        { "get", new List<object>() {"markets", "ticker", "assets", "fee", "depth/{market}", "trades/{market}"} },
                    } },
                } },
                { "v4", new Dictionary<string, object>() {
                    { "public", new Dictionary<string, object>() {
                        { "get", new List<object>() {"assets", "collateral/markets", "fee", "orderbook/depth/{market}", "orderbook/{market}", "ticker", "trades/{market}", "time", "ping", "markets", "futures", "platform/status", "mining-pool"} },
                    } },
                    { "private", new Dictionary<string, object>() {
                        { "post", new List<object>() {"collateral-account/balance", "collateral-account/balance-summary", "collateral-account/positions/history", "collateral-account/leverage", "collateral-account/positions/open", "collateral-account/summary", "collateral-account/funding-history", "main-account/address", "main-account/balance", "main-account/create-new-address", "main-account/codes", "main-account/codes/apply", "main-account/codes/my", "main-account/codes/history", "main-account/fiat-deposit-url", "main-account/history", "main-account/withdraw", "main-account/withdraw-pay", "main-account/transfer", "main-account/smart/plans", "main-account/smart/investment", "main-account/smart/investment/close", "main-account/smart/investments", "main-account/fee", "main-account/smart/interest-payment-history", "trade-account/balance", "trade-account/executed-history", "trade-account/order", "trade-account/order/history", "order/collateral/limit", "order/collateral/market", "order/collateral/stop-limit", "order/collateral/trigger-market", "order/collateral/bulk", "order/new", "order/market", "order/stock_market", "order/stop_limit", "order/stop_market", "order/cancel", "order/cancel/all", "order/kill-switch", "order/kill-switch/status", "order/bulk", "order/modify", "order/conditional-cancel", "orders", "oco-orders", "order/collateral/oco", "order/oco-cancel", "order/oto-cancel", "profile/websocket_token", "convert/estimate", "convert/confirm", "convert/history", "sub-account/create", "sub-account/delete", "sub-account/edit", "sub-account/list", "sub-account/transfer", "sub-account/block", "sub-account/unblock", "sub-account/balances", "sub-account/transfer/history", "sub-account/api-key/create", "sub-account/api-key/edit", "sub-account/api-key/delete", "sub-account/api-key/list", "sub-account/api-key/reset", "sub-account/api-key/ip-address/list", "sub-account/api-key/ip-address/create", "sub-account/api-key/ip-address/delete", "mining/rewards", "market/fee", "conditional-orders"} },
                    } },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "tierBased", false },
                    { "percentage", true },
                    { "taker", this.parseNumber("0.001") },
                    { "maker", this.parseNumber("0.001") },
                } },
            } },
            { "options", new Dictionary<string, object>() {
                { "timeDifference", 0 },
                { "adjustForTimeDifference", false },
                { "fiatCurrencies", new List<object>() {"EUR", "USD", "RUB", "UAH"} },
                { "fetchBalance", new Dictionary<string, object>() {
                    { "account", "spot" },
                } },
                { "accountsByType", new Dictionary<string, object>() {
                    { "funding", "main" },
                    { "main", "main" },
                    { "spot", "spot" },
                    { "margin", "collateral" },
                    { "trade", "spot" },
                } },
                { "networksById", new Dictionary<string, object>() {
                    { "BEP20", "BSC" },
                } },
                { "defaultType", "spot" },
                { "brokerId", "ccxt" },
            } },
            { "features", new Dictionary<string, object>() {
                { "default", new Dictionary<string, object>() {
                    { "sandbox", false },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", true },
                        { "triggerPrice", true },
                        { "triggerDirection", false },
                        { "triggerPriceType", null },
                        { "stopLossPrice", false },
                        { "takeProfitPrice", false },
                        { "attachedStopLossTakeProfit", null },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", false },
                            { "PO", true },
                            { "GTD", false },
                        } },
                        { "hedged", false },
                        { "trailing", false },
                        { "leverage", false },
                        { "marketBuyByCost", true },
                        { "marketBuyRequiresPrice", false },
                        { "selfTradePrevention", false },
                        { "iceberg", false },
                    } },
                    { "createOrders", null },
                    { "fetchMyTrades", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 100 },
                        { "daysBack", null },
                        { "untilDays", null },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrder", null },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 100 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrders", null },
                    { "fetchClosedOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 100 },
                        { "daysBack", null },
                        { "daysBackCanceled", null },
                        { "untilDays", null },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 1440 },
                    } },
                } },
                { "spot", new Dictionary<string, object>() {
                    { "extends", "default" },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                    { "inverse", new Dictionary<string, object>() {
                        { "extends", "default" },
                    } },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
            } },
            { "precisionMode", TICK_SIZE },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "Unauthorized request.", typeof(AuthenticationError) },
                    { "The market format is invalid.", typeof(BadSymbol) },
                    { "Market is not available", typeof(BadSymbol) },
                    { "Invalid payload.", typeof(BadRequest) },
                    { "Amount must be greater than 0", typeof(InvalidOrder) },
                    { "Not enough balance.", typeof(InsufficientFunds) },
                    { "The order id field is required.", typeof(InvalidOrder) },
                    { "Not enough balance", typeof(InsufficientFunds) },
                    { "This action is unauthorized.", typeof(PermissionDenied) },
                    { "This API Key is not authorized to perform this action.", typeof(PermissionDenied) },
                    { "Unexecuted order was not found.", typeof(OrderNotFound) },
                    { "The selected from is invalid.", typeof(BadRequest) },
                    { "503", typeof(ExchangeNotAvailable) },
                    { "422", typeof(OrderNotFound) },
                } },
                { "broad", new Dictionary<string, object>() {
                    { "This action is unauthorized", typeof(PermissionDenied) },
                    { "Given amount is less than min amount", typeof(InvalidOrder) },
                    { "Min amount step", typeof(InvalidOrder) },
                    { "Total is less than", typeof(InvalidOrder) },
                    { "fee must be no less than", typeof(InvalidOrder) },
                    { "Enable your key in API settings", typeof(PermissionDenied) },
                    { "You don't have such amount for transfer", typeof(InsufficientFunds) },
                } },
            } },
        });
    }

    /**
     * @method
     * @name whitebit#fetchMarkets
     * @description retrieves data on all markets for whitebit
     * @see https://docs.whitebit.com/public/http-v4/#market-info
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(getValue(this.options, "adjustForTimeDifference")))
        {
            await this.loadTimeDifference();
        }
        object markets = await this.v4PublicGetMarkets();
        //
        //    [
        //        {
        //          "name": "SON_USD",         // Market pair name
        //          "stock": "SON",            // Ticker of stock currency
        //          "money": "USD",            // Ticker of money currency
        //          "stockPrec": "3",          // Stock currency precision
        //          "moneyPrec": "2",          // Precision of money currency
        //          "feePrec": "4",            // Fee precision
        //          "makerFee": "0.1",         // Default maker fee ratio
        //          "takerFee": "0.1",         // Default taker fee ratio
        //          "minAmount": "0.001",      // Minimal amount of stock to trade
        //          "minTotal": "0.001",       // Minimal amount of money to trade
        //          "tradesEnabled": true,     // Is trading enabled
        //          "isCollateral": true,      // Is margin trading enabled
        //          "type": "spot",            // Market type. Possible values: "spot", "futures"
        //          "maxTotal": "1000000000"   // Maximum total(amount * price) of money to trade
        //        },
        //        {
        //          ...
        //        }
        //    ]
        //
        return this.parseMarkets(markets);
    }

    public override object parseMarket(object market)
    {
        object id = this.safeString(market, "name");
        object baseId = this.safeString(market, "stock");
        object quoteId = this.safeString(market, "money");
        quoteId = ((bool) isTrue((isEqual(quoteId, "PERP")))) ? "USDT" : quoteId;
        object bs = this.safeCurrencyCode(baseId);
        object quote = this.safeCurrencyCode(quoteId);
        object active = this.safeValue(market, "tradesEnabled");
        object isCollateral = this.safeValue(market, "isCollateral");
        object typeId = this.safeString(market, "type");
        object type = null;
        object settle = null;
        object settleId = null;
        object symbol = add(add(bs, "/"), quote);
        object swap = isEqual(typeId, "futures");
        object margin = isTrue(isCollateral) && !isTrue(swap);
        object contract = false;
        object amountPrecision = this.parseNumber(this.parsePrecision(this.safeString(market, "stockPrec")));
        object contractSize = amountPrecision;
        object linear = null;
        object inverse = null;
        if (isTrue(swap))
        {
            settleId = quoteId;
            settle = this.safeCurrencyCode(settleId);
            symbol = add(add(symbol, ":"), settle);
            type = "swap";
            contract = true;
            linear = true;
            inverse = false;
        } else
        {
            type = "spot";
        }
        object takerFeeRate = this.safeString(market, "takerFee");
        object taker = Precise.stringDiv(takerFeeRate, "100");
        object makerFeeRate = this.safeString(market, "makerFee");
        object maker = Precise.stringDiv(makerFeeRate, "100");
        return new Dictionary<string, object>() {
            { "id", id },
            { "symbol", symbol },
            { "base", bs },
            { "quote", quote },
            { "settle", settle },
            { "baseId", baseId },
            { "quoteId", quoteId },
            { "settleId", settleId },
            { "type", type },
            { "spot", !isTrue(swap) },
            { "margin", margin },
            { "swap", swap },
            { "future", false },
            { "option", false },
            { "active", active },
            { "contract", contract },
            { "linear", linear },
            { "inverse", inverse },
            { "taker", this.parseNumber(taker) },
            { "maker", this.parseNumber(maker) },
            { "contractSize", contractSize },
            { "expiry", null },
            { "expiryDatetime", null },
            { "strike", null },
            { "optionType", null },
            { "precision", new Dictionary<string, object>() {
                { "amount", amountPrecision },
                { "price", this.parseNumber(this.parsePrecision(this.safeString(market, "moneyPrec"))) },
            } },
            { "limits", new Dictionary<string, object>() {
                { "leverage", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "amount", new Dictionary<string, object>() {
                    { "min", this.safeNumber(market, "minAmount") },
                    { "max", null },
                } },
                { "price", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "cost", new Dictionary<string, object>() {
                    { "min", this.safeNumber(market, "minTotal") },
                    { "max", this.safeNumber(market, "maxTotal") },
                } },
            } },
            { "created", null },
            { "info", market },
        };
    }

    /**
     * @method
     * @name whitebit#fetchCurrencies
     * @description fetches all available currencies on an exchange
     * @see https://docs.whitebit.com/public/http-v4/#asset-status-list
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an associative dictionary of currencies
     */
    public async override Task<object> fetchCurrencies(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.v4PublicGetAssets(parameters);
        //
        // {
        //   BTC: {
        //     name: "Bitcoin",
        //     unified_cryptoasset_id: "1",
        //     can_withdraw: true,
        //     can_deposit: true,
        //     min_withdraw: "0.0003",
        //     max_withdraw: "0",
        //     maker_fee: "0.1",
        //     taker_fee: "0.1",
        //     min_deposit: "0.0001",
        //     max_deposit: "0",
        //     networks: {
        //         deposits: [ "BTC", ],
        //         withdraws: [ "BTC", ],
        //         default: "BTC",
        //     },
        //     confirmations: {
        //         BTC: "2",
        //     },
        //     limits: {
        //         deposit: {
        //            BTC: { min: "0.0001", },
        //         },
        //         withdraw: {
        //            BTC: { min: "0.0003", },
        //         },
        //     },
        //     currency_precision: "8",
        //     is_memo: false,
        //   },
        //   USD: {
        //         name: "United States Dollar",
        //         unified_cryptoasset_id: "6955",
        //         can_withdraw: true,
        //         can_deposit: true,
        //         min_withdraw: "10",
        //         max_withdraw: "10000",
        //         maker_fee: "0.1",
        //         taker_fee: "0.1",
        //         min_deposit: "10",
        //         max_deposit: "10000",
        //         networks: {
        //           deposits: [ "USD", ],
        //           withdraws: [ "USD", ],
        //           default: "USD",
        //         },
        //         providers: {
        //           deposits: [ "ADVCASH", ],
        //           withdraws: [ "ADVCASH", ],
        //         },
        //         limits: {
        //           deposit: {
        //             USD: {  max: "10000", min: "10", },
        //           },
        //           withdraw: {
        //             USD: { max: "10000",  min: "10", },
        //           },
        //         },
        //         currency_precision: "2",
        //         is_memo: false,
        //   }
        // }
        //
        object ids = new List<object>(((IDictionary<string,object>)response).Keys);
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(ids)); postFixIncrement(ref i))
        {
            object id = getValue(ids, i);
            object currency = getValue(response, id);
            // const name = this.safeString (currency, 'name'); // breaks down in Python due to utf8 encoding issues on the exchange side
            object code = this.safeCurrencyCode(id);
            object hasProvider = (inOp(currency, "providers"));
            object networks = new Dictionary<string, object>() {};
            object rawNetworks = this.safeDict(currency, "networks", new Dictionary<string, object>() {});
            object depositsNetworks = this.safeList(rawNetworks, "deposits", new List<object>() {});
            object withdrawsNetworks = this.safeList(rawNetworks, "withdraws", new List<object>() {});
            object networkLimits = this.safeDict(currency, "limits", new Dictionary<string, object>() {});
            object depositLimits = this.safeDict(networkLimits, "deposit", new Dictionary<string, object>() {});
            object withdrawLimits = this.safeDict(networkLimits, "withdraw", new Dictionary<string, object>() {});
            object allNetworks = this.arrayConcat(depositsNetworks, withdrawsNetworks);
            for (object j = 0; isLessThan(j, getArrayLength(allNetworks)); postFixIncrement(ref j))
            {
                object networkId = getValue(allNetworks, j);
                object networkCode = this.networkIdToCode(networkId);
                ((IDictionary<string,object>)networks)[(string)networkCode] = new Dictionary<string, object>() {
                    { "id", networkId },
                    { "network", networkCode },
                    { "active", null },
                    { "deposit", this.inArray(networkId, depositsNetworks) },
                    { "withdraw", this.inArray(networkId, withdrawsNetworks) },
                    { "fee", null },
                    { "precision", null },
                    { "limits", new Dictionary<string, object>() {
                        { "deposit", new Dictionary<string, object>() {
                            { "min", this.safeNumber(depositLimits, "min", null) },
                            { "max", this.safeNumber(depositLimits, "max", null) },
                        } },
                        { "withdraw", new Dictionary<string, object>() {
                            { "min", this.safeNumber(withdrawLimits, "min", null) },
                            { "max", this.safeNumber(withdrawLimits, "max", null) },
                        } },
                    } },
                };
            }
            ((IDictionary<string,object>)result)[(string)code] = this.safeCurrencyStructure(new Dictionary<string, object>() {
                { "id", id },
                { "code", code },
                { "info", currency },
                { "name", null },
                { "active", null },
                { "deposit", this.safeBool(currency, "can_deposit") },
                { "withdraw", this.safeBool(currency, "can_withdraw") },
                { "fee", null },
                { "networks", null },
                { "type", ((bool) isTrue(hasProvider)) ? "fiat" : "crypto" },
                { "precision", this.parseNumber(this.parsePrecision(this.safeString(currency, "currency_precision"))) },
                { "limits", new Dictionary<string, object>() {
                    { "amount", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "withdraw", new Dictionary<string, object>() {
                        { "min", this.safeNumber(currency, "min_withdraw") },
                        { "max", this.safeNumber(currency, "max_withdraw") },
                    } },
                    { "deposit", new Dictionary<string, object>() {
                        { "min", this.safeNumber(currency, "min_deposit") },
                        { "max", this.safeNumber(currency, "max_deposit") },
                    } },
                } },
            });
        }
        return result;
    }

    /**
     * @method
     * @name whitebit#fetchTransactionFees
     * @deprecated
     * @description please use fetchDepositWithdrawFees instead
     * @see https://docs.whitebit.com/public/http-v4/#fee
     * @param {string[]|undefined} codes not used by fetchTransactionFees ()
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchTransactionFees(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.v4PublicGetFee(parameters);
        //
        //      {
        //          "1INCH":{
        //              "is_depositable":true,
        //              "is_withdrawal":true,
        //              "ticker":"1INCH",
        //              "name":"1inch",
        //              "providers":[
        //              ],
        //              "withdraw":{
        //                   "max_amount":"0",
        //                  "min_amount":"21.5",
        //                  "fixed":"17.5",
        //                  "flex":null
        //              },
        //              "deposit":{
        //                  "max_amount":"0",
        //                  "min_amount":"19.5",
        //                  "fixed":null,
        //                  "flex":null
        //               }
        //          },
        //           {...}
        //      }
        //
        object currenciesIds = new List<object>(((IDictionary<string,object>)response).Keys);
        object withdrawFees = new Dictionary<string, object>() {};
        object depositFees = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(currenciesIds)); postFixIncrement(ref i))
        {
            object currency = getValue(currenciesIds, i);
            object data = getValue(response, currency);
            object code = this.safeCurrencyCode(currency);
            object withdraw = this.safeValue(data, "withdraw", new Dictionary<string, object>() {});
            ((IDictionary<string,object>)withdrawFees)[(string)code] = this.safeString(withdraw, "fixed");
            object deposit = this.safeValue(data, "deposit", new Dictionary<string, object>() {});
            ((IDictionary<string,object>)depositFees)[(string)code] = this.safeString(deposit, "fixed");
        }
        return new Dictionary<string, object>() {
            { "withdraw", withdrawFees },
            { "deposit", depositFees },
            { "info", response },
        };
    }

    /**
     * @method
     * @name whitebit#fetchDepositWithdrawFees
     * @description fetch deposit and withdraw fees
     * @see https://docs.whitebit.com/public/http-v4/#fee
     * @param {string[]|undefined} codes not used by fetchDepositWithdrawFees ()
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchDepositWithdrawFees(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.v4PublicGetFee(parameters);
        //
        //    {
        //        "1INCH": {
        //            "is_depositable": true,
        //            "is_withdrawal": true,
        //            "ticker": "1INCH",
        //            "name": "1inch",
        //            "providers": [],
        //            "withdraw": {
        //                "max_amount": "0",
        //                "min_amount": "21.5",
        //                "fixed": "17.5",
        //                "flex": null
        //            },
        //            "deposit": {
        //                "max_amount": "0",
        //                "min_amount": "19.5",
        //                "fixed": null,
        //                "flex": null
        //            }
        //        },
        //        "WBT (ERC20)": {
        //            "is_depositable": true,
        //            "is_withdrawal": true,
        //            "ticker": "WBT",
        //            "name": "WhiteBIT Token",
        //            "providers": [],
        //            "withdraw": { max_amount: "0", min_amount: '0.7', fixed: "0.253", flex: null },
        //            "deposit": { max_amount: "0", min_amount: "0.35", fixed: null, flex: null }
        //        },
        //        "WBT (TRC20)": {
        //            "is_depositable": true,
        //            "is_withdrawal": true,
        //            "ticker": "WBT",
        //            "name": "WhiteBIT Token",
        //            "providers": [],
        //            "withdraw": { max_amount: "0", min_amount: "1.5", fixed: "0.075", flex: null },
        //            "deposit": { max_amount: "0", min_amount: "0.75", fixed: null, flex: null }
        //        },
        //        ...
        //    }
        //
        return this.parseDepositWithdrawFees(response, codes);
    }

    public override object parseDepositWithdrawFees(object response, object codes = null, object currencyIdKey = null)
    {
        //
        //    {
        //        "1INCH": {
        //            "is_depositable": true,
        //            "is_withdrawal": true,
        //            "ticker": "1INCH",
        //            "name": "1inch",
        //            "providers": [],
        //            "withdraw": {
        //                "max_amount": "0",
        //                "min_amount": "21.5",
        //                "fixed": "17.5",
        //                "flex": null
        //            },
        //            "deposit": {
        //                "max_amount": "0",
        //                "min_amount": "19.5",
        //                "fixed": null,
        //                "flex": null
        //            }
        //        },
        //        "WBT (ERC20)": {
        //            "is_depositable": true,
        //            "is_withdrawal": true,
        //            "ticker": "WBT",
        //            "name": "WhiteBIT Token",
        //            "providers": [],
        //            "withdraw": { max_amount: "0", min_amount: "0.7", fixed: "0.253", flex: null },
        //            "deposit": { max_amount: "0", min_amount: "0.35", fixed: null, flex: null }
        //        },
        //        "WBT (TRC20)": {
        //            "is_depositable": true,
        //            "is_withdrawal": true,
        //            "ticker": "WBT",
        //            "name": "WhiteBIT Token",
        //            "providers": [],
        //            "withdraw": { max_amount: "0", min_amount: "1.5", fixed: "0.075", flex: null },
        //            "deposit": { max_amount: "0", min_amount: "0.75", fixed: null, flex: null }
        //        },
        //        ...
        //    }
        //
        object depositWithdrawFees = new Dictionary<string, object>() {};
        codes = this.marketCodes(codes);
        object currencyIds = new List<object>(((IDictionary<string,object>)response).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(currencyIds)); postFixIncrement(ref i))
        {
            object entry = getValue(currencyIds, i);
            object splitEntry = ((string)entry).Split(new [] {((string)" ")}, StringSplitOptions.None).ToList<object>();
            object currencyId = getValue(splitEntry, 0);
            object feeInfo = getValue(response, entry);
            object code = this.safeCurrencyCode(currencyId);
            if (isTrue(isTrue((isEqual(codes, null))) || isTrue((this.inArray(code, codes)))))
            {
                object depositWithdrawFee = this.safeValue(depositWithdrawFees, code);
                if (isTrue(isEqual(depositWithdrawFee, null)))
                {
                    ((IDictionary<string,object>)depositWithdrawFees)[(string)code] = this.depositWithdrawFee(new Dictionary<string, object>() {});
                }
                ((IDictionary<string,object>)getValue(getValue(depositWithdrawFees, code), "info"))[(string)entry] = feeInfo;
                object networkId = this.safeString(splitEntry, 1);
                object withdraw = this.safeValue(feeInfo, "withdraw");
                object deposit = this.safeValue(feeInfo, "deposit");
                object withdrawFee = this.safeNumber(withdraw, "fixed");
                object depositFee = this.safeNumber(deposit, "fixed");
                object withdrawResult = new Dictionary<string, object>() {
                    { "fee", withdrawFee },
                    { "percentage", ((bool) isTrue((!isEqual(withdrawFee, null)))) ? false : null },
                };
                object depositResult = new Dictionary<string, object>() {
                    { "fee", depositFee },
                    { "percentage", ((bool) isTrue((!isEqual(depositFee, null)))) ? false : null },
                };
                if (isTrue(!isEqual(networkId, null)))
                {
                    object networkLength = ((string)networkId).Length;
                    networkId = slice(networkId, 1, subtract(networkLength, 1));
                    object networkCode = this.networkIdToCode(networkId);
                    ((IDictionary<string,object>)getValue(getValue(depositWithdrawFees, code), "networks"))[(string)networkCode] = new Dictionary<string, object>() {
                        { "withdraw", withdrawResult },
                        { "deposit", depositResult },
                    };
                } else
                {
                    ((IDictionary<string,object>)getValue(depositWithdrawFees, code))["withdraw"] = withdrawResult;
                    ((IDictionary<string,object>)getValue(depositWithdrawFees, code))["deposit"] = depositResult;
                }
            }
        }
        object depositWithdrawCodes = new List<object>(((IDictionary<string,object>)depositWithdrawFees).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(depositWithdrawCodes)); postFixIncrement(ref i))
        {
            object code = getValue(depositWithdrawCodes, i);
            object currency = this.currency(code);
            ((IDictionary<string,object>)depositWithdrawFees)[(string)code] = this.assignDefaultDepositWithdrawFees(getValue(depositWithdrawFees, code), currency);
        }
        return depositWithdrawFees;
    }

    /**
     * @method
     * @name whitebit#fetchTradingFees
     * @description fetch the trading fees for multiple markets
     * @see https://docs.whitebit.com/public/http-v4/#asset-status-list
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
     */
    public async override Task<object> fetchTradingFees(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.v4PublicGetAssets(parameters);
        //
        //      {
        //          "1INCH": {
        //              "name": "1inch",
        //              "unified_cryptoasset_id": "8104",
        //              "can_withdraw": true,
        //              "can_deposit": true,
        //              "min_withdraw": "33",
        //              "max_withdraw": "0",
        //              "maker_fee": "0.1",
        //              "taker_fee": "0.1",
        //              "min_deposit": "30",
        //              "max_deposit": "0"
        //            },
        //            ...
        //      }
        //
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(this.symbols)); postFixIncrement(ref i))
        {
            object symbol = getValue(this.symbols, i);
            object market = this.market(symbol);
            object fee = this.safeValue(response, getValue(market, "baseId"), new Dictionary<string, object>() {});
            object makerFee = this.safeString(fee, "maker_fee");
            object takerFee = this.safeString(fee, "taker_fee");
            makerFee = Precise.stringDiv(makerFee, "100");
            takerFee = Precise.stringDiv(takerFee, "100");
            ((IDictionary<string,object>)result)[(string)symbol] = new Dictionary<string, object>() {
                { "info", fee },
                { "symbol", getValue(market, "symbol") },
                { "percentage", true },
                { "tierBased", false },
                { "maker", this.parseNumber(makerFee) },
                { "taker", this.parseNumber(takerFee) },
            };
        }
        return result;
    }

    /**
     * @method
     * @name whitebit#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://docs.whitebit.com/public/http-v4/#market-activity
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        object response = await this.v1PublicGetTicker(this.extend(request, parameters));
        //
        //      {
        //         "success":true,
        //         "message":"",
        //         "result": {
        //             "bid":"0.021979",
        //             "ask":"0.021996",
        //             "open":"0.02182",
        //             "high":"0.022039",
        //             "low":"0.02161",
        //             "last":"0.021987",
        //             "volume":"2810.267",
        //             "deal":"61.383565474",
        //             "change":"0.76",
        //         },
        //     }
        //
        object ticker = this.safeDict(response, "result", new Dictionary<string, object>() {});
        return this.parseTicker(ticker, market);
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        //  FetchTicker (v1)
        //
        //    {
        //        "bid": "0.021979",
        //        "ask": "0.021996",
        //        "open": "0.02182",
        //        "high": "0.022039",
        //        "low": "0.02161",
        //        "last": "0.021987",
        //        "volume": "2810.267",
        //        "deal": "61.383565474",
        //        "change": "0.76",
        //    }
        //
        // FetchTickers (v4)
        //
        //    "BCH_RUB": {
        //        "base_id": 1831,
        //        "quote_id": 0,
        //        "last_price": "32830.21",
        //        "quote_volume": "1494659.8024096",
        //        "base_volume": "46.1083",
        //        "isFrozen": false,
        //        "change": "2.12" // in percent
        //    }
        //
        // WS market_update
        //
        //     {
        //         "open": "52853.04",
        //         "close": "55913.88",
        //         "high": "56272",
        //         "low": "49549.67",
        //         "volume": "57331.067185",
        //         "deal": "3063860382.42985338",
        //         "last": "55913.88",
        //         "period": 86400
        //     }
        // v2
        //   {
        //       lastUpdateTimestamp: '2025-01-02T09:16:36.000Z',
        //       tradingPairs: 'ARB_USDC',
        //       lastPrice: '0.7727',
        //       lowestAsk: '0.7735',
        //       highestBid: '0.7732',
        //       baseVolume24h: '1555793.74',
        //       quoteVolume24h: '1157602.622406',
        //       tradesEnabled: true
        //   }
        //
        object marketId = this.safeString(ticker, "tradingPairs");
        market = this.safeMarket(marketId, market);
        // last price is provided as "last" or "last_price"
        object last = this.safeStringN(ticker, new List<object>() {"last", "last_price", "lastPrice"});
        // if "close" is provided, use it, otherwise use <last>
        object close = this.safeString(ticker, "close", last);
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", getValue(market, "symbol") },
            { "timestamp", null },
            { "datetime", null },
            { "high", this.safeString(ticker, "high") },
            { "low", this.safeString(ticker, "low") },
            { "bid", this.safeString2(ticker, "bid", "highestBid") },
            { "bidVolume", null },
            { "ask", this.safeString2(ticker, "ask", "lowestAsk") },
            { "askVolume", null },
            { "vwap", null },
            { "open", this.safeString(ticker, "open") },
            { "close", close },
            { "last", last },
            { "previousClose", null },
            { "change", null },
            { "percentage", this.safeString(ticker, "change") },
            { "average", null },
            { "baseVolume", this.safeStringN(ticker, new List<object>() {"base_volume", "volume", "baseVolume24h"}) },
            { "quoteVolume", this.safeStringN(ticker, new List<object>() {"quote_volume", "deal", "quoteVolume24h"}) },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name whitebit#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://docs.whitebit.com/public/http-v4/#market-activity
     * @param {string[]} [symbols] unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.method] either v2PublicGetTicker or v4PublicGetTicker default is v4PublicGetTicker
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        object method = "v4PublicGetTicker";
        var methodparametersVariable = this.handleOptionAndParams(parameters, "fetchTickers", "method", method);
        method = ((IList<object>)methodparametersVariable)[0];
        parameters = ((IList<object>)methodparametersVariable)[1];
        object response = null;
        if (isTrue(isEqual(method, "v4PublicGetTicker")))
        {
            response = await this.v4PublicGetTicker(parameters);
        } else
        {
            response = await this.v2PublicGetTicker(parameters);
        }
        //
        //      "BCH_RUB": {
        //          "base_id":1831,
        //          "quote_id":0,
        //          "last_price":"32830.21",
        //          "quote_volume":"1494659.8024096",
        //          "base_volume":"46.1083",
        //          "isFrozen":false,
        //          "change":"2.12"
        //      },
        //
        object resultList = this.safeList(response, "result");
        if (isTrue(!isEqual(resultList, null)))
        {
            return this.parseTickers(resultList, symbols);
        }
        object marketIds = new List<object>(((IDictionary<string,object>)response).Keys);
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
        {
            object marketId = getValue(marketIds, i);
            object market = this.safeMarket(marketId);
            object ticker = this.parseTicker(getValue(response, marketId), market);
            object symbol = getValue(ticker, "symbol");
            ((IDictionary<string,object>)result)[(string)symbol] = ticker;
        }
        return this.filterByArrayTickers(result, "symbol", symbols);
    }

    /**
     * @method
     * @name whitebit#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://docs.whitebit.com/public/http-v4/#orderbook
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // default = 100, maximum = 100
        }
        object response = await this.v4PublicGetOrderbookMarket(this.extend(request, parameters));
        //
        //      {
        //          "timestamp": 1594391413,
        //          "asks": [
        //              [
        //                  "9184.41",
        //                  "0.773162"
        //              ],
        //              [ ... ]
        //          ],
        //          "bids": [
        //              [
        //                  "9181.19",
        //                  "0.010873"
        //              ],
        //              [ ... ]
        //          ]
        //      }
        //
        object timestamp = this.safeTimestamp(response, "timestamp");
        return this.parseOrderBook(response, symbol, timestamp);
    }

    /**
     * @method
     * @name whitebit#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://docs.whitebit.com/public/http-v4/#recent-trades
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        object response = await this.v4PublicGetTradesMarket(this.extend(request, parameters));
        //
        //      [
        //          {
        //              "tradeID": 158056419,
        //              "price": "9186.13",
        //              "quote_volume": "0.0021",
        //              "base_volume": "9186.13",
        //              "trade_timestamp": 1594391747,
        //              "type": "sell"
        //          },
        //      ],
        //
        return this.parseTrades(response, market, since, limit);
    }

    /**
     * @method
     * @name whitebit#fetchMyTrades
     * @description fetch all trades made by the user
     * @see https://docs.whitebit.com/private/http-trade-v4/#query-executed-order-history
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        }
        object response = await this.v4PrivatePostTradeAccountExecutedHistory(this.extend(request, parameters));
        //
        // when no symbol is provided
        //
        //   {
        //       "USDC_USDT":[
        //          {
        //             "id":"**********",
        //             "clientOrderId":"",
        //             "time":"**********.532965",
        //             "side":"sell",
        //             "role":"2",
        //             "amount":"9.986",
        //             "price":"0.9995",
        //             "deal":"9.981007",
        //             "fee":"0.*********",
        //             "orderId":"***********"
        //          },
        //       ]
        //   }
        //
        // when a symbol is provided
        //
        //     [
        //         {
        //             "id": **********,
        //             "clientOrderId": '',
        //             "time": **********.532965,
        //             "side": "sell",
        //             "role": 2,
        //             "amount": "9.986",
        //             "price": "0.9995",
        //             "deal": "9.981007",
        //             "fee": "0.*********",
        //             "orderId": ***********,
        //         },
        //     ]
        //
        if (isTrue(((response is IList<object>) || (response.GetType().IsGenericType && response.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            return this.parseTrades(response, market, since, limit);
        } else
        {
            object results = new List<object>() {};
            object keys = new List<object>(((IDictionary<string,object>)response).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
            {
                object marketId = getValue(keys, i);
                object marketNew = this.safeMarket(marketId, null, "_");
                object rawTrades = this.safeValue(response, marketId, new List<object>() {});
                object parsed = this.parseTrades(rawTrades, marketNew, since, limit);
                results = this.arrayConcat(results, parsed);
            }
            results = this.sortBy2(results, "timestamp", "id");
            return this.filterBySinceLimit(results, since, limit, "timestamp");
        }
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        // fetchTradesV4
        //
        //     {
        //       "tradeID": 158056419,
        //       "price": "9186.13",
        //       "quote_volume": "0.0021",
        //       "base_volume": "9186.13",
        //       "trade_timestamp": 1594391747,
        //       "type": "sell"
        //     }
        //
        // orderTrades (v4Private)
        //
        //     {
        //         "time": **********.613711,
        //         "fee": "0.000********",
        //         "price": "0.********",
        //         "amount": "598",
        //         "id": *********, // trade id
        //         "dealOrderId": **********, // orderId
        //         "clientOrderId": "customId11",
        //         "role": 2, // 1 = maker, 2 = taker
        //         "deal": "0.********" // amount in money
        //         "feeAsset": "USDT"
        //     }
        //
        // fetchMyTrades
        //
        //      {
        //          "id": **********,
        //          "clientOrderId": '',
        //          "time": **********.532965,
        //          "side": "sell",
        //          "role": 2,
        //          "amount": "9.986",
        //          "price": "0.9995",
        //          "deal": "9.981007",
        //          "fee": "0.*********",
        //          "orderId": ***********,
        //          "feeAsset": "USDT"
        //      }
        //
        market = this.safeMarket(null, market);
        object timestamp = this.safeTimestamp2(trade, "time", "trade_timestamp");
        object orderId = this.safeString2(trade, "dealOrderId", "orderId");
        object cost = this.safeString(trade, "deal");
        object price = this.safeString(trade, "price");
        object amount = this.safeString2(trade, "amount", "quote_volume");
        object id = this.safeString2(trade, "id", "tradeID");
        object side = this.safeString2(trade, "type", "side");
        object symbol = getValue(market, "symbol");
        object role = this.safeInteger(trade, "role");
        object takerOrMaker = null;
        if (isTrue(!isEqual(role, null)))
        {
            takerOrMaker = ((bool) isTrue((isEqual(role, 1)))) ? "maker" : "taker";
        }
        object fee = null;
        object feeCost = this.safeString(trade, "fee");
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "cost", feeCost },
                { "currency", this.safeCurrencyCode(this.safeString(trade, "feeAsset")) },
            };
        }
        return this.safeTrade(new Dictionary<string, object>() {
            { "info", trade },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", symbol },
            { "id", id },
            { "order", orderId },
            { "type", null },
            { "takerOrMaker", takerOrMaker },
            { "side", side },
            { "price", price },
            { "amount", amount },
            { "cost", cost },
            { "fee", fee },
        }, market);
    }

    /**
     * @method
     * @name whitebit#fetchOHLCV
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @see https://docs.whitebit.com/public/http-v1/#kline
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
            { "interval", this.safeString(this.timeframes, timeframe, timeframe) },
        };
        if (isTrue(!isEqual(since, null)))
        {
            object maxLimit = 1440;
            if (isTrue(isEqual(limit, null)))
            {
                limit = maxLimit;
            }
            limit = mathMin(limit, maxLimit);
            object start = this.parseToInt(divide(since, 1000));
            ((IDictionary<string,object>)request)["start"] = start;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = mathMin(limit, 1440);
        }
        object response = await this.v1PublicGetKline(this.extend(request, parameters));
        //
        //     {
        //         "success":true,
        //         "message":"",
        //         "result":[
        //             [1591488000,"0.025025","0.025025","0.025029","0.025023","6.181","0.154686629"],
        //             [1591488060,"0.025028","0.025033","0.025035","0.025026","8.067","0.201921167"],
        //             [1591488120,"0.025034","0.02505","0.02505","0.025034","20.089","0.503114696"],
        //         ]
        //     }
        //
        object result = this.safeList(response, "result", new List<object>() {});
        return this.parseOHLCVs(result, market, timeframe, since, limit);
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        //
        //     [
        //         1591488000,
        //         "0.025025",
        //         "0.025025",
        //         "0.025029",
        //         "0.025023",
        //         "6.181",
        //         "0.154686629"
        //     ]
        //
        return new List<object> {this.safeTimestamp(ohlcv, 0), this.safeNumber(ohlcv, 1), this.safeNumber(ohlcv, 3), this.safeNumber(ohlcv, 4), this.safeNumber(ohlcv, 2), this.safeNumber(ohlcv, 5)};
    }

    /**
     * @method
     * @name whitebit#fetchStatus
     * @description the latest known information on the availability of the exchange API
     * @see https://docs.whitebit.com/public/http-v4/#server-status
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [status structure]{@link https://docs.ccxt.com/#/?id=exchange-status-structure}
     */
    public async override Task<object> fetchStatus(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.v4PublicGetPing(parameters);
        //
        //      [
        //          "pong"
        //      ]
        //
        object status = this.safeString(response, 0);
        return new Dictionary<string, object>() {
            { "status", ((bool) isTrue((isEqual(status, "pong")))) ? "ok" : status },
            { "updated", null },
            { "eta", null },
            { "url", null },
            { "info", response },
        };
    }

    /**
     * @method
     * @name whitebit#fetchTime
     * @description fetches the current integer timestamp in milliseconds from the exchange server
     * @see https://docs.whitebit.com/public/http-v4/#server-time
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int} the current integer timestamp in milliseconds from the exchange server
     */
    public async override Task<object> fetchTime(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.v4PublicGetTime(parameters);
        //
        //     {
        //         "time":1737380046
        //     }
        //
        return this.safeInteger(response, "time");
    }

    /**
     * @method
     * @name whitebit#createMarketOrderWithCost
     * @description create a market order by providing the symbol, side and cost
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} side 'buy' or 'sell'
     * @param {float} cost how much you want to trade in units of the quote currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createMarketOrderWithCost(object symbol, object side, object cost, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object req = new Dictionary<string, object>() {
            { "cost", cost },
        };
        // only buy side is supported
        return await this.createOrder(symbol, "market", side, 0, null, this.extend(req, parameters));
    }

    /**
     * @method
     * @name whitebit#createMarketBuyOrderWithCost
     * @description create a market buy order by providing the symbol and cost
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {float} cost how much you want to trade in units of the quote currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createMarketBuyOrderWithCost(object symbol, object cost, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.createMarketOrderWithCost(symbol, "buy", cost, parameters);
    }

    /**
     * @method
     * @name whitebit#createOrder
     * @description create a trade order
     * @see https://docs.whitebit.com/private/http-trade-v4/#create-limit-order
     * @see https://docs.whitebit.com/private/http-trade-v4/#create-market-order
     * @see https://docs.whitebit.com/private/http-trade-v4/#create-buy-stock-market-order
     * @see https://docs.whitebit.com/private/http-trade-v4/#create-stop-limit-order
     * @see https://docs.whitebit.com/private/http-trade-v4/#create-stop-market-order
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {float} [params.cost] *market orders only* the cost of the order in units of the base currency
     * @param {float} [params.triggerPrice] The price at which a trigger order is triggered at
     * @param {bool} [params.postOnly] If true, the order will only be posted to the order book and not executed immediately
     * @param {string} [params.clientOrderId] a unique id for the order
     * @param {string} [params.marginMode] 'cross' or 'isolated', for margin trading, uses this.options.defaultMarginMode if not passed, defaults to undefined/None/null
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
            { "side", side },
        };
        object cost = null;
        var costparametersVariable = this.handleParamString(parameters, "cost");
        cost = ((IList<object>)costparametersVariable)[0];
        parameters = ((IList<object>)costparametersVariable)[1];
        if (isTrue(!isEqual(cost, null)))
        {
            if (isTrue(isTrue((!isEqual(side, "buy"))) || isTrue((!isEqual(type, "market")))))
            {
                throw new InvalidOrder ((string)add(this.id, " createOrder() cost is only supported for market buy orders")) ;
            }
            ((IDictionary<string,object>)request)["amount"] = this.costToPrecision(symbol, cost);
        } else
        {
            ((IDictionary<string,object>)request)["amount"] = this.amountToPrecision(symbol, amount);
        }
        object clientOrderId = this.safeString2(parameters, "clOrdId", "clientOrderId");
        if (isTrue(isEqual(clientOrderId, null)))
        {
            object brokerId = this.safeString(this.options, "brokerId");
            if (isTrue(!isEqual(brokerId, null)))
            {
                ((IDictionary<string,object>)request)["clientOrderId"] = add(brokerId, this.uuid16());
            }
        } else
        {
            ((IDictionary<string,object>)request)["clientOrderId"] = clientOrderId;
            parameters = this.omit(parameters, new List<object>() {"clientOrderId"});
        }
        object marketType = this.safeString(market, "type");
        object isLimitOrder = isEqual(type, "limit");
        object isMarketOrder = isEqual(type, "market");
        object triggerPrice = this.safeNumberN(parameters, new List<object>() {"triggerPrice", "stopPrice", "activation_price"});
        object isStopOrder = (!isEqual(triggerPrice, null));
        object postOnly = this.isPostOnly(isMarketOrder, false, parameters);
        var marginModequeryVariable = this.handleMarginModeAndParams("createOrder", parameters);
        var marginMode = ((IList<object>) marginModequeryVariable)[0];
        var query = ((IList<object>) marginModequeryVariable)[1];
        if (isTrue(postOnly))
        {
            ((IDictionary<string,object>)request)["postOnly"] = true;
        }
        if (isTrue(isTrue(!isEqual(marginMode, null)) && isTrue(!isEqual(marginMode, "cross"))))
        {
            throw new NotSupported ((string)add(this.id, " createOrder() is only available for cross margin")) ;
        }
        parameters = this.omit(query, new List<object>() {"postOnly", "triggerPrice", "stopPrice"});
        object useCollateralEndpoint = isTrue(!isEqual(marginMode, null)) || isTrue(isEqual(marketType, "swap"));
        object response = null;
        if (isTrue(isStopOrder))
        {
            ((IDictionary<string,object>)request)["activation_price"] = this.priceToPrecision(symbol, triggerPrice);
            if (isTrue(isLimitOrder))
            {
                // stop limit order
                ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
                response = await this.v4PrivatePostOrderStopLimit(this.extend(request, parameters));
            } else
            {
                // stop market order
                if (isTrue(useCollateralEndpoint))
                {
                    response = await this.v4PrivatePostOrderCollateralTriggerMarket(this.extend(request, parameters));
                } else
                {
                    response = await this.v4PrivatePostOrderStopMarket(this.extend(request, parameters));
                }
            }
        } else
        {
            if (isTrue(isLimitOrder))
            {
                // limit order
                ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
                if (isTrue(useCollateralEndpoint))
                {
                    response = await this.v4PrivatePostOrderCollateralLimit(this.extend(request, parameters));
                } else
                {
                    response = await this.v4PrivatePostOrderNew(this.extend(request, parameters));
                }
            } else
            {
                // market order
                if (isTrue(useCollateralEndpoint))
                {
                    response = await this.v4PrivatePostOrderCollateralMarket(this.extend(request, parameters));
                } else
                {
                    if (isTrue(!isEqual(cost, null)))
                    {
                        response = await this.v4PrivatePostOrderMarket(this.extend(request, parameters));
                    } else
                    {
                        response = await this.v4PrivatePostOrderStockMarket(this.extend(request, parameters));
                    }
                }
            }
        }
        return this.parseOrder(response);
    }

    /**
     * @method
     * @name whitebit#editOrder
     * @description edit a trade order
     * @see https://docs.whitebit.com/private/http-trade-v4/#modify-order
     * @param {string} id cancel order id
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} price the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> editOrder(object id, object symbol, object type, object side, object amount = null, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(id, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " editOrder() requires a id argument")) ;
        }
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " editOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "orderId", id },
            { "market", getValue(market, "id") },
        };
        object clientOrderId = this.safeString2(parameters, "clOrdId", "clientOrderId");
        if (isTrue(!isEqual(clientOrderId, null)))
        {
            // Update clientOrderId of the order
            ((IDictionary<string,object>)request)["clientOrderId"] = clientOrderId;
        }
        object isLimitOrder = isEqual(type, "limit");
        object triggerPrice = this.safeNumberN(parameters, new List<object>() {"triggerPrice", "stopPrice", "activation_price"});
        object isStopOrder = (!isEqual(triggerPrice, null));
        parameters = this.omit(parameters, new List<object>() {"clOrdId", "clientOrderId", "triggerPrice", "stopPrice"});
        if (isTrue(isStopOrder))
        {
            ((IDictionary<string,object>)request)["activation_price"] = this.priceToPrecision(symbol, triggerPrice);
            if (isTrue(isLimitOrder))
            {
                // stop limit order
                ((IDictionary<string,object>)request)["amount"] = this.amountToPrecision(symbol, amount);
                ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
            } else
            {
                // stop market order
                if (isTrue(isEqual(side, "buy")))
                {
                    // Use total parameter instead of amount for modify buy stop market order
                    ((IDictionary<string,object>)request)["total"] = this.amountToPrecision(symbol, amount);
                } else
                {
                    ((IDictionary<string,object>)request)["amount"] = this.amountToPrecision(symbol, amount);
                }
            }
        } else
        {
            ((IDictionary<string,object>)request)["amount"] = this.amountToPrecision(symbol, amount);
            if (isTrue(isLimitOrder))
            {
                // limit order
                ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
            }
        }
        object response = await this.v4PrivatePostOrderModify(this.extend(request, parameters));
        return this.parseOrder(response);
    }

    /**
     * @method
     * @name whitebit#cancelOrder
     * @description cancels an open order
     * @see https://docs.whitebit.com/private/http-trade-v4/#cancel-order
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
            { "orderId", parseInt(id) },
        };
        object response = await this.v4PrivatePostOrderCancel(this.extend(request, parameters));
        //
        //    {
        //        "orderId": 4180284841, // order id
        //        "clientOrderId": "customId11", // custom order identifier; "clientOrderId": "" - if not specified.
        //        "market": "BTC_USDT", // deal market
        //        "side": "buy", // order side
        //        "type": "stop market", // order type
        //        "timestamp": 1595792396.165973, // current timestamp
        //        "dealMoney": "0", // if order finished - amount in money currency that is finished
        //        "dealStock": "0", // if order finished - amount in stock currency that is finished
        //        "amount": "0.001", // amount
        //        "takerFee": "0.001", // maker fee ratio. If the number less than 0.0001 - it will be rounded to zero
        //        "makerFee": "0.001", // maker fee ratio. If the number less than 0.0001 - it will be rounded to zero
        //        "left": "0.001", // if order not finished - rest of the amount that must be finished
        //        "dealFee": "0", // fee in money that you pay if order is finished
        //        "price": "40000", // price if price isset
        //        "activation_price": "40000" // activation price if activation price is set
        //    }
        //
        return this.parseOrder(response);
    }

    /**
     * @method
     * @name whitebit#cancelAllOrders
     * @description cancel all open orders
     * @see https://docs.whitebit.com/private/http-trade-v4/#cancel-all-orders
     * @param {string} symbol unified market symbol, only orders in the market of this symbol are cancelled when symbol is not undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.type] market type, ['swap', 'spot']
     * @param {boolean} [params.isMargin] cancel all margin orders
     * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        }
        object type = null;
        var typeparametersVariable = this.handleMarketTypeAndParams("cancelAllOrders", market, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        object requestType = new List<object>() {};
        if (isTrue(isEqual(type, "spot")))
        {
            object isMargin = null;
            var isMarginparametersVariable = this.handleOptionAndParams(parameters, "cancelAllOrders", "isMargin", false);
            isMargin = ((IList<object>)isMarginparametersVariable)[0];
            parameters = ((IList<object>)isMarginparametersVariable)[1];
            if (isTrue(isMargin))
            {
                ((IList<object>)requestType).Add("margin");
            } else
            {
                ((IList<object>)requestType).Add("spot");
            }
        } else if (isTrue(isEqual(type, "swap")))
        {
            ((IList<object>)requestType).Add("futures");
        } else
        {
            throw new NotSupported ((string)add(add(add(this.id, " cancelAllOrders() does not support "), type), " type")) ;
        }
        ((IDictionary<string,object>)request)["type"] = requestType;
        object response = await this.v4PrivatePostOrderCancelAll(this.extend(request, parameters));
        //
        // []
        //
        return this.parseOrders(response, market);
    }

    /**
     * @method
     * @name whitebit#cancelAllOrdersAfter
     * @description dead man's switch, cancel all orders after the given timeout
     * @see https://docs.whitebit.com/private/http-trade-v4/#sync-kill-switch-timer
     * @param {number} timeout time in milliseconds, 0 represents cancel the timer
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.types] Order types value. Example: "spot", "margin", "futures" or null
     * @param {string} [params.symbol] symbol unified symbol of the market the order was made in
     * @returns {object} the api result
     */
    public async override Task<object> cancelAllOrdersAfter(object timeout, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object symbol = this.safeString(parameters, "symbol");
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelAllOrdersAfter() requires a symbol argument in params")) ;
        }
        object market = this.market(symbol);
        parameters = this.omit(parameters, "symbol");
        object isBiggerThanZero = (isGreaterThan(timeout, 0));
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        if (isTrue(isBiggerThanZero))
        {
            ((IDictionary<string,object>)request)["timeout"] = this.numberToString(divide(timeout, 1000));
        } else
        {
            ((IDictionary<string,object>)request)["timeout"] = "null";
        }
        object response = await this.v4PrivatePostOrderKillSwitch(this.extend(request, parameters));
        //
        //     {
        //         "market": "BTC_USDT", // currency market,
        //         "startTime": 1662478154, // now timestamp,
        //         "cancellationTime": 1662478154, // now + timer_value,
        //         "types": ["spot", "margin"]
        //     }
        //
        return response;
    }

    public override object parseBalance(object response)
    {
        object balanceKeys = new List<object>(((IDictionary<string,object>)response).Keys);
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(balanceKeys)); postFixIncrement(ref i))
        {
            object id = getValue(balanceKeys, i);
            object code = this.safeCurrencyCode(id);
            object balance = getValue(response, id);
            if (isTrue(isTrue((balance is IDictionary<string, object>)) && isTrue(!isEqual(balance, null))))
            {
                object account = this.account();
                ((IDictionary<string,object>)account)["free"] = this.safeString2(balance, "available", "main_balance");
                ((IDictionary<string,object>)account)["used"] = this.safeString(balance, "freeze");
                ((IDictionary<string,object>)account)["total"] = this.safeString(balance, "main_balance");
                ((IDictionary<string,object>)result)[(string)code] = account;
            } else
            {
                object account = this.account();
                ((IDictionary<string,object>)account)["total"] = balance;
                ((IDictionary<string,object>)result)[(string)code] = account;
            }
        }
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name whitebit#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://docs.whitebit.com/private/http-main-v4/#main-balance
     * @see https://docs.whitebit.com/private/http-trade-v4/#trading-balance
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object marketType = null;
        var marketTypeparametersVariable = this.handleMarketTypeAndParams("fetchBalance", null, parameters);
        marketType = ((IList<object>)marketTypeparametersVariable)[0];
        parameters = ((IList<object>)marketTypeparametersVariable)[1];
        object response = null;
        if (isTrue(isEqual(marketType, "swap")))
        {
            response = await this.v4PrivatePostCollateralAccountBalance(parameters);
        } else
        {
            object options = this.safeValue(this.options, "fetchBalance", new Dictionary<string, object>() {});
            object defaultAccount = this.safeString(options, "account");
            object account = this.safeString2(parameters, "account", "type", defaultAccount);
            parameters = this.omit(parameters, new List<object>() {"account", "type"});
            if (isTrue(isTrue(isEqual(account, "main")) || isTrue(isEqual(account, "funding"))))
            {
                response = await this.v4PrivatePostMainAccountBalance(parameters);
            } else
            {
                response = await this.v4PrivatePostTradeAccountBalance(parameters);
            }
        }
        //
        // main account
        //
        //     {
        //         "BTC":{"main_balance":"0.****************"},
        //         "ETH":{"main_balance":"0.************"},
        //     }
        //
        // spot trade account
        //
        //     {
        //         "BTC": { "available": "0.123", "freeze": "1" },
        //         "XMR": { "available": "3013", "freeze": "100" },
        //     }
        //
        // swap
        //
        //     {
        //          "BTC": 1,
        //          "USDT": 1000
        //     }
        //
        return this.parseBalance(response);
    }

    /**
     * @method
     * @name whitebit#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://docs.whitebit.com/private/http-trade-v4/#query-unexecutedactive-orders
     * @param {string} [symbol] unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of open order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = mathMin(limit, 100);
        }
        object response = await this.v4PrivatePostOrders(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "orderId": 3686033640,
        //             "clientOrderId": "customId11",
        //             "market": "BTC_USDT",
        //             "side": "buy",
        //             "type": "limit",
        //             "timestamp": 1594605801.49815,    // current timestamp of unexecuted order
        //             "dealMoney": "0",                 // executed amount in money
        //             "dealStock": "0",                 // executed amount in stock
        //             "amount": "2.241379",             // active order amount
        //             "takerFee": "0.001",
        //             "makerFee": "0.001",
        //             "left": "2.241379",               // unexecuted amount in stock
        //             "dealFee": "0",                   // executed fee by deal
        //             "price": "40000"
        //         },
        //     ]
        //
        return this.parseOrders(response, market, since, limit, new Dictionary<string, object>() {
            { "status", "open" },
        });
    }

    /**
     * @method
     * @name whitebit#fetchClosedOrders
     * @description fetches information on multiple closed orders made by the user
     * @see https://docs.whitebit.com/private/http-trade-v4/#query-executed-orders
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            symbol = getValue(market, "symbol");
            ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = mathMin(limit, 100); // default 50 max 100
        }
        object response = await this.v4PrivatePostTradeAccountOrderHistory(this.extend(request, parameters));
        //
        //     {
        //         "BTC_USDT": [
        //             {
        //                 "id": *********,
        //                 "clientOrderId": "customId11",
        //                 "time": **********.724403,
        //                 "side": "sell",
        //                 "role": 2, // 1 = maker, 2 = taker
        //                 "amount": "0.000076",
        //                 "price": "9264.21",
        //                 "deal": "0.********",
        //                 "fee": "0.***********"
        //             },
        //         ],
        //     }
        //
        object marketIds = new List<object>(((IDictionary<string,object>)response).Keys);
        object results = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
        {
            object marketId = getValue(marketIds, i);
            object marketNew = this.safeMarket(marketId, null, "_");
            object orders = getValue(response, marketId);
            for (object j = 0; isLessThan(j, getArrayLength(orders)); postFixIncrement(ref j))
            {
                object order = this.parseOrder(getValue(orders, j), marketNew);
                ((IList<object>)results).Add(this.extend(order, new Dictionary<string, object>() {
                    { "status", "closed" },
                }));
            }
        }
        results = this.sortBy(results, "timestamp");
        results = this.filterBySymbolSinceLimit(results, symbol, since, limit);
        return results;
    }

    public virtual object parseOrderType(object type)
    {
        object types = new Dictionary<string, object>() {
            { "limit", "limit" },
            { "market", "market" },
            { "stop market", "market" },
            { "stop limit", "limit" },
            { "stock market", "market" },
            { "margin limit", "limit" },
            { "margin market", "market" },
        };
        return this.safeString(types, type, type);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        // createOrder, fetchOpenOrders, cancelOrder
        //
        //      {
        //          "orderId":105687928629,
        //          "clientOrderId":"",
        //          "market":"DOGE_USDT",
        //          "side":"sell",
        //          "type":"stop market",
        //          "timestamp":1659091079.729576,
        //          "dealMoney":"0",                // executed amount in quote
        //          "dealStock":"0",                // base filled amount
        //          "amount":"100",
        //          "takerFee":"0.001",
        //          "makerFee":"0",
        //          "left":"100",
        //          "price": "40000", // price if price isset
        //          "dealFee":"0",
        //          "activation_price":"0.065"      // stop price (if stop limit or stop market)
        //      }
        //
        // fetchClosedOrders
        //
        //      {
        //          "id":105531094719,
        //          "clientOrderId":"",
        //          "ctime":1659045334.550127,
        //          "ftime":1659045334.550127,
        //          "side":"buy",
        //          "amount":"5.9940059",           // cost in terms of quote for regular market orders, amount in terms or base for all other order types
        //          "price":"0",
        //          "type":"market",
        //          "takerFee":"0.001",
        //          "makerFee":"0",
        //          "dealFee":"0.0059375815",
        //          "dealStock":"85",               // base filled amount
        //          "dealMoney":"5.9375815",        // executed amount in quote
        //      }
        //
        object marketId = this.safeString(order, "market");
        market = this.safeMarket(marketId, market, "_");
        object symbol = getValue(market, "symbol");
        object side = this.safeString(order, "side");
        object filled = this.safeString(order, "dealStock");
        object remaining = this.safeString(order, "left");
        object clientOrderId = this.safeString(order, "clientOrderId");
        if (isTrue(isEqual(clientOrderId, "")))
        {
            clientOrderId = null;
        }
        object price = this.safeString(order, "price");
        object triggerPrice = this.safeNumber(order, "activation_price");
        object orderId = this.safeString2(order, "orderId", "id");
        object type = this.safeString(order, "type");
        object orderType = this.parseOrderType(type);
        if (isTrue(isEqual(orderType, "market")))
        {
            remaining = null;
        }
        object amount = this.safeString(order, "amount");
        object cost = this.safeString(order, "dealMoney");
        if (isTrue(isTrue((isEqual(side, "buy"))) && isTrue((isTrue((isEqual(type, "market"))) || isTrue((isEqual(type, "stop market")))))))
        {
            amount = filled;
        }
        object dealFee = this.safeString(order, "dealFee");
        object fee = null;
        if (isTrue(!isEqual(dealFee, null)))
        {
            fee = new Dictionary<string, object>() {
                { "cost", this.parseNumber(dealFee) },
                { "currency", getValue(market, "quote") },
            };
        }
        object timestamp = this.safeTimestamp2(order, "ctime", "timestamp");
        object lastTradeTimestamp = this.safeTimestamp(order, "ftime");
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", order },
            { "id", orderId },
            { "symbol", symbol },
            { "clientOrderId", clientOrderId },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastTradeTimestamp", lastTradeTimestamp },
            { "timeInForce", null },
            { "postOnly", null },
            { "status", null },
            { "side", side },
            { "price", price },
            { "type", orderType },
            { "triggerPrice", triggerPrice },
            { "amount", amount },
            { "filled", filled },
            { "remaining", remaining },
            { "average", null },
            { "cost", cost },
            { "fee", fee },
            { "trades", null },
        }, market);
    }

    /**
     * @method
     * @name whitebit#fetchOrderTrades
     * @description fetch all the trades made from a single order
     * @see https://docs.whitebit.com/private/http-trade-v4/#query-executed-order-deals
     * @param {string} id order id
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchOrderTrades(object id, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "orderId", parseInt(id) },
        };
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = mathMin(limit, 100);
        }
        object response = await this.v4PrivatePostTradeAccountOrder(this.extend(request, parameters));
        //
        //     {
        //         "records": [
        //             {
        //                 "time": **********.613711,
        //                 "fee": "0.000********",
        //                 "price": "0.********",
        //                 "amount": "598",
        //                 "id": *********, // trade id
        //                 "dealOrderId": **********, // orderId
        //                 "clientOrderId": "customId11", // empty string if not specified
        //                 "role": 2, // 1 = maker, 2 = taker
        //                 "deal": "0.********"
        //             }
        //         ],
        //         "offset": 0,
        //         "limit": 100
        //     }
        //
        object data = this.safeList(response, "records", new List<object>() {});
        return this.parseTrades(data, market);
    }

    /**
     * @method
     * @name whitebit#fetchDepositAddress
     * @description fetch the deposit address for a currency associated with this account
     * @see https://docs.whitebit.com/private/http-main-v4/#get-fiat-deposit-address
     * @see https://docs.whitebit.com/private/http-main-v4/#get-cryptocurrency-deposit-address
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> fetchDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "ticker", getValue(currency, "id") },
        };
        object response = null;
        if (isTrue(this.isFiat(code)))
        {
            object provider = this.safeString(parameters, "provider");
            if (isTrue(isEqual(provider, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " fetchDepositAddress() requires a provider when the ticker is fiat")) ;
            }
            ((IDictionary<string,object>)request)["provider"] = provider;
            object amount = this.safeNumber(parameters, "amount");
            if (isTrue(isEqual(amount, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " fetchDepositAddress() requires an amount when the ticker is fiat")) ;
            }
            ((IDictionary<string,object>)request)["amount"] = amount;
            object uniqueId = this.safeValue(parameters, "uniqueId");
            if (isTrue(isEqual(uniqueId, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " fetchDepositAddress() requires an uniqueId when the ticker is fiat")) ;
            }
            response = await this.v4PrivatePostMainAccountFiatDepositUrl(this.extend(request, parameters));
        } else
        {
            response = await this.v4PrivatePostMainAccountAddress(this.extend(request, parameters));
        }
        //
        // fiat
        //
        //     {
        //         "url": "https://someaddress.com"
        //     }
        //
        // crypto
        //
        //     {
        //         "account": {
        //             "address": "GDTSOI56XNVAKJNJBLJGRNZIVOCIZJRBIDKTWSCYEYNFAZEMBLN75RMN",
        //             "memo": "**************"
        //         },
        //         "required": {
        //             "fixedFee": "0",
        //             "flexFee": {
        //                 "maxFee": "0",
        //                 "minFee": "0",
        //                 "percent": "0"
        //             },
        //             "maxAmount": "0",
        //             "minAmount": "1"
        //         }
        //     }
        //
        object url = this.safeString(response, "url");
        object account = this.safeValue(response, "account", new Dictionary<string, object>() {});
        object address = this.safeString(account, "address", url);
        object tag = this.safeString(account, "memo");
        this.checkAddress(address);
        return new Dictionary<string, object>() {
            { "info", response },
            { "currency", code },
            { "network", null },
            { "address", address },
            { "tag", tag },
        };
    }

    /**
     * @method
     * @name whitebit#createDepositAddress
     * @description create a currency deposit address
     * @see https://docs.whitebit.com/private/http-main-v4/#create-new-address-for-deposit
     * @param {string} code unified currency code of the currency for the deposit address
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.network] the blockchain network to create a deposit address on
     * @param {string} [params.type] address type, available for specific currencies
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> createDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "ticker", getValue(currency, "id") },
        };
        object response = await this.v4PrivatePostMainAccountCreateNewAddress(this.extend(request, parameters));
        //
        //     {
        //         "account": {
        //             "address": "GDTSOI56XNVAKJNJBLJGRNZIVOCIZJRBIDKTWSCYEYNFAZEMBLN75RMN",
        //             "memo": "**************"
        //         },
        //         "required": {
        //             "maxAmount": "0",
        //             "minAmount": "1",
        //             "fixedFee": "0",
        //             "flexFee": {
        //                 "maxFee": "0",
        //                 "minFee": "0",
        //                 "percent": "0"
        //             }
        //         }
        //     }
        //
        object data = this.safeDict(response, "account", new Dictionary<string, object>() {});
        return this.parseDepositAddress(data, currency);
    }

    public override object parseDepositAddress(object depositAddress, object currency = null)
    {
        //
        //     {
        //         "address": "GDTSOI56XNVAKJNJBLJGRNZIVOCIZJRBIDKTWSCYEYNFAZEMBLN75RMN",
        //         "memo": "**************"
        //     },
        //
        return new Dictionary<string, object>() {
            { "info", depositAddress },
            { "currency", this.safeCurrencyCode(null, currency) },
            { "network", null },
            { "address", this.safeString(depositAddress, "address") },
            { "tag", this.safeString(depositAddress, "memo") },
        };
    }

    /**
     * @method
     * @name whitebit#setLeverage
     * @description set the level of leverage for a market
     * @see https://docs.whitebit.com/private/http-trade-v4/#change-collateral-account-leverage
     * @param {float} leverage the rate of leverage
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} response from the exchange
     */
    public async override Task<object> setLeverage(object leverage, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        if (isTrue(!isEqual(symbol, null)))
        {
            throw new NotSupported ((string)add(this.id, " setLeverage() does not allow to set per symbol")) ;
        }
        if (isTrue(isTrue((isLessThan(leverage, 1))) || isTrue((isGreaterThan(leverage, 20)))))
        {
            throw new BadRequest ((string)add(this.id, " setLeverage() leverage should be between 1 and 20")) ;
        }
        object request = new Dictionary<string, object>() {
            { "leverage", leverage },
        };
        return await this.v4PrivatePostCollateralAccountLeverage(this.extend(request, parameters));
    }

    /**
     * @method
     * @name whitebit#transfer
     * @description transfer currency internally between wallets on the same account
     * @see https://docs.whitebit.com/private/http-main-v4/#transfer-between-main-and-trade-balances
     * @param {string} code unified currency code
     * @param {float} amount amount to transfer
     * @param {string} fromAccount account to transfer from - main, spot, collateral
     * @param {string} toAccount account to transfer to - main, spot, collateral
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transfer structure]{@link https://docs.ccxt.com/#/?id=transfer-structure}
     */
    public async override Task<object> transfer(object code, object amount, object fromAccount, object toAccount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object accountsByType = this.safeValue(this.options, "accountsByType");
        object fromAccountId = this.safeString(accountsByType, fromAccount, fromAccount);
        object toAccountId = this.safeString(accountsByType, toAccount, toAccount);
        object amountString = this.currencyToPrecision(code, amount);
        object request = new Dictionary<string, object>() {
            { "ticker", getValue(currency, "id") },
            { "amount", amountString },
            { "from", fromAccountId },
            { "to", toAccountId },
        };
        object response = await this.v4PrivatePostMainAccountTransfer(this.extend(request, parameters));
        //
        //    []
        //
        return this.parseTransfer(response, currency);
    }

    public override object parseTransfer(object transfer, object currency = null)
    {
        //
        //    []
        //
        return new Dictionary<string, object>() {
            { "info", transfer },
            { "id", null },
            { "timestamp", null },
            { "datetime", null },
            { "currency", this.safeCurrencyCode(null, currency) },
            { "amount", null },
            { "fromAccount", null },
            { "toAccount", null },
            { "status", null },
        };
    }

    /**
     * @method
     * @name whitebit#withdraw
     * @description make a withdrawal
     * @see https://docs.whitebit.com/private/http-main-v4/#create-withdraw-request
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address the address to withdraw to
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code); // check if it has canDeposit
        object request = new Dictionary<string, object>() {
            { "ticker", getValue(currency, "id") },
            { "amount", this.currencyToPrecision(code, amount) },
            { "address", address },
        };
        object uniqueId = this.safeValue(parameters, "uniqueId");
        if (isTrue(isEqual(uniqueId, null)))
        {
            uniqueId = this.uuid22();
        }
        ((IDictionary<string,object>)request)["uniqueId"] = uniqueId;
        if (isTrue(!isEqual(tag, null)))
        {
            ((IDictionary<string,object>)request)["memo"] = tag;
        }
        if (isTrue(this.isFiat(code)))
        {
            object provider = this.safeValue(parameters, "provider");
            if (isTrue(isEqual(provider, null)))
            {
                throw new ArgumentsRequired ((string)add(this.id, " withdraw() requires a provider when the ticker is fiat")) ;
            }
            ((IDictionary<string,object>)request)["provider"] = provider;
        }
        object response = await this.v4PrivatePostMainAccountWithdraw(this.extend(request, parameters));
        //
        // empty array with a success status
        // go to deposit/withdraw history and check you request status by uniqueId
        //
        //     []
        //
        return this.extend(new Dictionary<string, object>() {
            { "id", uniqueId },
        }, this.parseTransaction(response, currency));
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        //     {
        //         "address": "**********************************",                                              // deposit address
        //         "uniqueId": null,                                                                             // unique Id of deposit
        //         "transactionId": "a6d71d69-2b17-4ad8-8b15-2d686c54a1a5",
        //         "createdAt": **********,                                                                      // timestamp of deposit
        //         "currency": "Bitcoin",                                                                        // deposit currency
        //         "ticker": "BTC",                                                                              // deposit currency ticker
        //         "method": 1,                                                                                  // called method 1 - deposit, 2 - withdraw
        //         "amount": "0.0006",                                                                           // amount of deposit
        //         "description": "",                                                                            // deposit description
        //         "memo": "",                                                                                   // deposit memo
        //         "fee": "0",                                                                                   // deposit fee
        //         "status": 15,                                                                                 // transactions status
        //         "network": null,                                                                              // if currency is multinetwork
        //         "transactionHash": "a275a514013e4e0f927fd0d1bed215e7f6f2c4c6ce762836fe135ec22529d886",        // deposit transaction hash
        //         "details": {
        //             "partial": {                                                                              // details about partially successful withdrawals
        //                 "requestAmount": "50000",                                                             // requested withdrawal amount
        //                 "processedAmount": "39000",                                                           // processed withdrawal amount
        //                 "processedFee": "273",                                                                // fee for processed withdrawal amount
        //                 "normalizeTransaction": ""                                                            // deposit id
        //             }
        //         },
        //         "confirmations": {                                                                            // if transaction status == 15 you can see this object
        //             "actual": 1,                                                                              // current block confirmations
        //             "required": 2                                                                             // required block confirmation for successful deposit
        //         }
        //         "centralized": false,
        //     }
        //
        currency = this.safeCurrency(null, currency);
        object address = this.safeString(transaction, "address");
        object timestamp = this.safeTimestamp(transaction, "createdAt");
        object currencyId = this.safeString(transaction, "ticker");
        object status = this.safeString(transaction, "status");
        object method = this.safeString(transaction, "method");
        return new Dictionary<string, object>() {
            { "id", this.safeString(transaction, "uniqueId") },
            { "txid", this.safeString(transaction, "transactionId") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "network", this.safeString(transaction, "network") },
            { "addressFrom", ((bool) isTrue((isEqual(method, "1")))) ? address : null },
            { "address", address },
            { "addressTo", ((bool) isTrue((isEqual(method, "2")))) ? address : null },
            { "amount", this.safeNumber(transaction, "amount") },
            { "type", ((bool) isTrue((isEqual(method, "1")))) ? "deposit" : "withdrawal" },
            { "currency", this.safeCurrencyCode(currencyId, currency) },
            { "status", this.parseTransactionStatus(status) },
            { "updated", null },
            { "tagFrom", null },
            { "tag", null },
            { "tagTo", null },
            { "comment", this.safeString(transaction, "description") },
            { "internal", null },
            { "fee", new Dictionary<string, object>() {
                { "cost", this.safeNumber(transaction, "fee") },
                { "currency", this.safeCurrencyCode(currencyId, currency) },
            } },
            { "info", transaction },
        };
    }

    public virtual object parseTransactionStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "1", "pending" },
            { "2", "pending" },
            { "3", "ok" },
            { "4", "canceled" },
            { "5", "pending" },
            { "6", "pending" },
            { "7", "ok" },
            { "9", "canceled" },
            { "10", "pending" },
            { "11", "pending" },
            { "12", "pending" },
            { "13", "pending" },
            { "14", "pending" },
            { "15", "pending" },
            { "16", "pending" },
            { "17", "pending" },
        };
        return this.safeString(statuses, status, status);
    }

    /**
     * @method
     * @name whitebit#fetchDeposit
     * @description fetch information on a deposit
     * @see https://docs.whitebit.com/private/http-main-v4/#get-depositwithdraw-history
     * @param {string} id deposit id
     * @param {string} code not used by whitebit fetchDeposit ()
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async virtual Task<object> fetchDeposit(object id, object code = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = null;
        object request = new Dictionary<string, object>() {
            { "transactionMethod", 1 },
            { "uniqueId", id },
            { "limit", 1 },
            { "offset", 0 },
        };
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["ticker"] = getValue(currency, "id");
        }
        object response = await this.v4PrivatePostMainAccountHistory(this.extend(request, parameters));
        //
        //     {
        //         "limit": 100,
        //         "offset": 0,
        //         "records": [
        //             {
        //                 "address": "**********************************",                                              // deposit address
        //                 "uniqueId": null,                                                                             // unique Id of deposit
        //                 "createdAt": **********,                                                                      // timestamp of deposit
        //                 "currency": "Bitcoin",                                                                        // deposit currency
        //                 "ticker": "BTC",                                                                              // deposit currency ticker
        //                 "method": 1,                                                                                  // called method 1 - deposit, 2 - withdraw
        //                 "amount": "0.0006",                                                                           // amount of deposit
        //                 "description": "",                                                                            // deposit description
        //                 "memo": "",                                                                                   // deposit memo
        //                 "fee": "0",                                                                                   // deposit fee
        //                 "status": 15,                                                                                 // transactions status
        //                 "network": null,                                                                              // if currency is multinetwork
        //                 "transactionHash": "a275a514013e4e0f927fd0d1bed215e7f6f2c4c6ce762836fe135ec22529d886",        // deposit transaction hash
        //                 "details": {
        //                     "partial": {                                                                              // details about partially successful withdrawals
        //                         "requestAmount": "50000",                                                             // requested withdrawal amount
        //                         "processedAmount": "39000",                                                           // processed withdrawal amount
        //                         "processedFee": "273",                                                                // fee for processed withdrawal amount
        //                         "normalizeTransaction": ""                                                            // deposit id
        //                     }
        //                 },
        //                 "confirmations": {                                                                            // if transaction status == 15 you can see this object
        //                     "actual": 1,                                                                              // current block confirmations
        //                     "required": 2                                                                             // required block confirmation for successful deposit
        //                 }
        //             },
        //             {...},
        //         ],
        //         "total": 300                                                                                             // total number of  transactions, use this for calculating ‘limit’ and ‘offset'
        //     }
        //
        object records = this.safeValue(response, "records", new List<object>() {});
        object first = this.safeDict(records, 0, new Dictionary<string, object>() {});
        return this.parseTransaction(first, currency);
    }

    /**
     * @method
     * @name whitebit#fetchDeposits
     * @description fetch all deposits made to an account
     * @see https://docs.whitebit.com/private/http-main-v4/#get-depositwithdraw-history
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for
     * @param {int} [limit] the maximum number of deposits structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDeposits(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = null;
        object request = new Dictionary<string, object>() {
            { "transactionMethod", 1 },
            { "limit", 100 },
            { "offset", 0 },
        };
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["ticker"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = mathMin(limit, 100);
        }
        object response = await this.v4PrivatePostMainAccountHistory(this.extend(request, parameters));
        //
        //     {
        //         "limit": 100,
        //         "offset": 0,
        //         "records": [
        //             {
        //                 "address": "**********************************",                                              // deposit address
        //                 "uniqueId": null,                                                                             // unique Id of deposit
        //                 "createdAt": **********,                                                                      // timestamp of deposit
        //                 "currency": "Bitcoin",                                                                        // deposit currency
        //                 "ticker": "BTC",                                                                              // deposit currency ticker
        //                 "method": 1,                                                                                  // called method 1 - deposit, 2 - withdraw
        //                 "amount": "0.0006",                                                                           // amount of deposit
        //                 "description": "",                                                                            // deposit description
        //                 "memo": "",                                                                                   // deposit memo
        //                 "fee": "0",                                                                                   // deposit fee
        //                 "status": 15,                                                                                 // transactions status
        //                 "network": null,                                                                              // if currency is multinetwork
        //                 "transactionHash": "a275a514013e4e0f927fd0d1bed215e7f6f2c4c6ce762836fe135ec22529d886",        // deposit transaction hash
        //                 "details": {
        //                     "partial": {                                                                              // details about partially successful withdrawals
        //                         "requestAmount": "50000",                                                             // requested withdrawal amount
        //                         "processedAmount": "39000",                                                           // processed withdrawal amount
        //                         "processedFee": "273",                                                                // fee for processed withdrawal amount
        //                         "normalizeTransaction": ""                                                            // deposit id
        //                     }
        //                 },
        //                 "confirmations": {                                                                            // if transaction status == 15 you can see this object
        //                     "actual": 1,                                                                              // current block confirmations
        //                     "required": 2                                                                             // required block confirmation for successful deposit
        //                 }
        //             },
        //             {...},
        //         ],
        //         "total": 300                                                                                             // total number of  transactions, use this for calculating ‘limit’ and ‘offset'
        //     }
        //
        object records = this.safeList(response, "records", new List<object>() {});
        return this.parseTransactions(records, currency, since, limit);
    }

    /**
     * @method
     * @name whitebit#fetchBorrowInterest
     * @description fetch the interest owed by the user for borrowing currency for margin trading
     * @see https://docs.whitebit.com/private/http-trade-v4/#open-positions
     * @param {string} code unified currency code
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch borrrow interest for
     * @param {int} [limit] the maximum number of structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [borrow interest structures]{@link https://docs.ccxt.com/#/?id=borrow-interest-structure}
     */
    public async override Task<object> fetchBorrowInterest(object code = null, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["market"] = getValue(market, "id");
        }
        object response = await this.v4PrivatePostCollateralAccountPositionsOpen(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "positionId": 191823,
        //             "market": "BTC_USDT",
        //             "openDate": **********.027163,
        //             "modifyDate": **********.027163,
        //             "amount": "0.003075",
        //             "basePrice": "24149.24512",
        //             "liquidationPrice": "7059.02",
        //             "leverage": "5",
        //             "pnl": "-0.15",
        //             "pnlPercent": "-0.20",
        //             "margin": "14.86",
        //             "freeMargin": "44.99",
        //             "funding": "0",
        //             "unrealizedFunding": "0.****************",
        //             "liquidationState": null
        //         }
        //     ]
        //
        object interest = this.parseBorrowInterests(response, market);
        return this.filterByCurrencySinceLimit(interest, code, since, limit);
    }

    public override object parseBorrowInterest(object info, object market = null)
    {
        //
        //     {
        //         "positionId": 191823,
        //         "market": "BTC_USDT",
        //         "openDate": **********.027163,
        //         "modifyDate": **********.027163,
        //         "amount": "0.003075",
        //         "basePrice": "24149.24512",
        //         "liquidationPrice": "7059.02",
        //         "leverage": "5",
        //         "pnl": "-0.15",
        //         "pnlPercent": "-0.20",
        //         "margin": "14.86",
        //         "freeMargin": "44.99",
        //         "funding": "0",
        //         "unrealizedFunding": "0.****************",
        //         "liquidationState": null
        //     }
        //
        object marketId = this.safeString(info, "market");
        object symbol = this.safeSymbol(marketId, market, "_");
        object timestamp = this.safeTimestamp(info, "modifyDate");
        return new Dictionary<string, object>() {
            { "info", info },
            { "symbol", symbol },
            { "currency", "USDT" },
            { "interest", this.safeNumber(info, "unrealizedFunding") },
            { "interestRate", 0.00098 },
            { "amountBorrowed", this.safeNumber(info, "amount") },
            { "marginMode", "cross" },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        };
    }

    /**
     * @method
     * @name whitebit#fetchFundingRate
     * @description fetch the current funding rate
     * @see https://docs.whitebit.com/public/http-v4/#available-futures-markets-list
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [funding rate structure]{@link https://docs.ccxt.com/#/?id=funding-rate-structure}
     */
    public async override Task<object> fetchFundingRate(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbol = this.symbol(symbol);
        object response = await this.fetchFundingRates(new List<object>() {symbol}, parameters);
        return this.safeValue(response, symbol);
    }

    /**
     * @method
     * @name whitebit#fetchFundingRates
     * @description fetch the funding rate for multiple markets
     * @see https://docs.whitebit.com/public/http-v4/#available-futures-markets-list
     * @param {string[]|undefined} symbols list of unified market symbols
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rates-structure}, indexed by market symbols
     */
    public async override Task<object> fetchFundingRates(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        object response = await this.v4PublicGetFutures(parameters);
        //
        //    [
        //        {
        //            "name": "BTC_USDT",
        //            "type": "direct",
        //            "quanto_multiplier": "0.0001",
        //            "ref_discount_rate": "0",
        //            "order_price_deviate": "0.5",
        //            "maintenance_rate": "0.005",
        //            "mark_type": "index",
        //            "last_price": "38026",
        //            "mark_price": "37985.6",
        //            "index_price": "37954.92",
        //            "funding_rate_indicative": "0.000219",
        //            "mark_price_round": "0.01",
        //            "funding_offset": 0,
        //            "in_delisting": false,
        //            "risk_limit_base": "1000000",
        //            "interest_rate": "0.0003",
        //            "order_price_round": "0.1",
        //            "order_size_min": 1,
        //            "ref_rebate_rate": "0.2",
        //            "funding_interval": 28800,
        //            "risk_limit_step": "1000000",
        //            "leverage_min": "1",
        //            "leverage_max": "100",
        //            "risk_limit_max": "8000000",
        //            "maker_fee_rate": "-0.00025",
        //            "taker_fee_rate": "0.00075",
        //            "funding_rate": "0.002053",
        //            "order_size_max": 1000000,
        //            "funding_next_apply": 1610035200,
        //            "short_users": 977,
        //            "config_change_time": 1609899548,
        //            "trade_size": 28530850594,
        //            "position_size": 5223816,
        //            "long_users": 455,
        //            "funding_impact_value": "60000",
        //            "orders_limit": 50,
        //            "trade_id": 10851092,
        //            "orderbook_id": 2129638396
        //        }
        //    ]
        //
        object data = this.safeList(response, "result", new List<object>() {});
        return this.parseFundingRates(data, symbols);
    }

    public override object parseFundingRate(object contract, object market = null)
    {
        //
        // {
        //     "ticker_id":"ADA_PERP",
        //     "stock_currency":"ADA",
        //     "money_currency":"USDT",
        //     "last_price":"0.296708",
        //     "stock_volume":"7982130",
        //     "money_volume":"2345758.29189",
        //     "bid":"0.296608",
        //     "ask":"0.296758",
        //     "high":"0.298338",
        //     "low":"0.290171",
        //     "product_type":"Perpetual",
        //     "open_interest":"46533000",
        //     "index_price":"0.29659",
        //     "index_name":"Cardano",
        //     "index_currency":"ADA",
        //     "funding_rate":"0.0001",
        //     "next_funding_rate_timestamp":"1691193600000",
        //     "brackets":{
        //        "1":"0",
        //        "2":"0",
        //        "3":"0",
        //        "5":"0",
        //        "10":"0",
        //        "20":"0",
        //        "50":"-10000",
        //        "100":"-5000"
        //     },
        //     "max_leverage":"100"
        //  }
        //
        object marketId = this.safeString(contract, "ticker_id");
        object symbol = this.safeSymbol(marketId, market);
        object markPrice = this.safeNumber(contract, "markPrice");
        object indexPrice = this.safeNumber(contract, "indexPrice");
        object interestRate = this.safeNumber(contract, "interestRate");
        object fundingRate = this.safeNumber(contract, "funding_rate");
        object fundingTime = this.safeInteger(contract, "next_funding_rate_timestamp");
        return new Dictionary<string, object>() {
            { "info", contract },
            { "symbol", symbol },
            { "markPrice", markPrice },
            { "indexPrice", indexPrice },
            { "interestRate", interestRate },
            { "timestamp", null },
            { "datetime", null },
            { "fundingRate", fundingRate },
            { "fundingTimestamp", fundingTime },
            { "fundingDatetime", this.iso8601(fundingTime) },
            { "nextFundingRate", null },
            { "nextFundingTimestamp", null },
            { "nextFundingDatetime", null },
            { "previousFundingRate", null },
            { "previousFundingTimestamp", null },
            { "previousFundingDatetime", null },
            { "interval", null },
        };
    }

    /**
     * @method
     * @name whitebit#fetchFundingHistory
     * @description fetch the history of funding payments paid and received on this account
     * @see https://docs.whitebit.com/private/http-trade-v4/#funding-history
     * @param {string} [symbol] unified market symbol
     * @param {int} [since] the starting timestamp in milliseconds
     * @param {int} [limit] the number of entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch funding history for
     * @returns {object[]} a list of [funding history structures]{@link https://docs.ccxt.com/#/?id=funding-history-structure}
     */
    public async override Task<object> fetchFundingHistory(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchFundingHistory() requires a symbol argument")) ;
        }
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startDate"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = since;
        }
        var requestparametersVariable = this.handleUntilOption("endDate", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.v4PrivatePostCollateralAccountFundingHistory(request);
        //
        //     {
        //         "records": [
        //             {
        //                 "market": "BTC_PERP",
        //                 "fundingTime": "*************",
        //                 "fundingRate": "0.********",
        //                 "fundingAmount": "-0.************",
        //                 "positionAmount": "0.019",
        //                 "settlementPrice": "50938.2",
        //                 "rateCalculatedTime": "*************"
        //             },
        //         ],
        //         "limit": 100,
        //         "offset": 0
        //     }
        //
        object data = this.safeList(response, "records", new List<object>() {});
        return this.parseFundingHistories(data, market, since, limit);
    }

    public virtual object parseFundingHistory(object contract, object market = null)
    {
        //
        //     {
        //         "market": "BTC_PERP",
        //         "fundingTime": "*************",
        //         "fundingRate": "0.********",
        //         "fundingAmount": "-0.************",
        //         "positionAmount": "0.019",
        //         "settlementPrice": "50938.2",
        //         "rateCalculatedTime": "*************"
        //     }
        //
        object marketId = this.safeString(contract, "market");
        object timestamp = this.safeInteger(contract, "fundingTime");
        return new Dictionary<string, object>() {
            { "info", contract },
            { "symbol", this.safeSymbol(marketId, market, null, "swap") },
            { "code", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "id", null },
            { "amount", this.safeNumber(contract, "fundingAmount") },
        };
    }

    public virtual object parseFundingHistories(object contracts, object market = null, object since = null, object limit = null)
    {
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(contracts)); postFixIncrement(ref i))
        {
            object contract = getValue(contracts, i);
            ((IList<object>)result).Add(this.parseFundingHistory(contract, market));
        }
        object sorted = this.sortBy(result, "timestamp");
        return this.filterBySinceLimit(sorted, since, limit);
    }

    /**
     * @method
     * @name whitebit#fetchDepositsWithdrawals
     * @description fetch history of deposits and withdrawals
     * @see https://github.com/whitebit-exchange/api-docs/blob/main/pages/private/http-main-v4.md#get-depositwithdraw-history
     * @param {string} [code] unified currency code for the currency of the deposit/withdrawals, default is undefined
     * @param {int} [since] timestamp in ms of the earliest deposit/withdrawal, default is undefined
     * @param {int} [limit] max number of deposit/withdrawals to return, default = 50, Min: 1, Max: 100
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     *
     * EXCHANGE SPECIFIC PARAMETERS
     * @param {number} [params.transactionMethod] Method. Example: 1 to display deposits / 2 to display withdraws. Do not send this parameter in order to receive both deposits and withdraws.
     * @param {string} [params.address] Can be used for filtering transactions by specific address or memo.
     * @param {string[]} [params.addresses] Can be used for filtering transactions by specific addresses or memos (max: 20).
     * @param {string} [params.uniqueId] Can be used for filtering transactions by specific unique id
     * @param {int} [params.offset] If you want the request to return entries starting from a particular line, you can use OFFSET clause to tell it where it should start. Default: 0, Min: 0, Max: 10000
     * @param {string[]} [params.status] Can be used for filtering transactions by status codes. Caution: You must use this parameter with appropriate transactionMethod and use valid status codes for this method. You can find them below. Example: "status": [3,7]
     * @returns {object} a list of [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDepositsWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["ticker"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // default 1000
        }
        object response = await this.v4PrivatePostMainAccountHistory(this.extend(request, parameters));
        //
        //    {
        //        "limit": 100,
        //        "offset": 0,
        //        "records": [
        //            {
        //                "address": "**********************************",                                        // deposit address
        //                "uniqueId": null,                                                                       // unique Id of deposit
        //                "createdAt": **********,                                                                // timestamp of deposit
        //                "currency": "Bitcoin",                                                                  // deposit currency
        //                "ticker": "BTC",                                                                        // deposit currency ticker
        //                "method": 1,                                                                            // called method 1 - deposit, 2 - withdraw
        //                "amount": "0.0006",                                                                     // amount of deposit
        //                "description": "",                                                                      // deposit description
        //                "memo": "",                                                                             // deposit memo
        //                "fee": "0",                                                                             // deposit fee
        //                "status": 15,                                                                           // transactions status
        //                "network": null,                                                                        // if currency is multinetwork
        //                "transactionHash": "a275a514013e4e0f927fd0d1bed215e7f6f2c4c6ce762836fe135ec22529d886",  // deposit transaction hash
        //                "transactionId": "5e112b38-9652-11ed-a1eb-0242ac120002",                                // transaction id
        //                "details": {
        //                    "partial": {                                                                        // details about partially successful withdrawals
        //                        "requestAmount": "50000",                                                       // requested withdrawal amount
        //                        "processedAmount": "39000",                                                     // processed withdrawal amount
        //                        "processedFee": "273",                                                          // fee for processed withdrawal amount
        //                        "normalizeTransaction": ""                                                      // deposit id
        //                    }
        //                },
        //                "confirmations": {                                                                      // if transaction status == 15 (Pending) you can see this object
        //                    "actual": 1,                                                                        // current block confirmations
        //                    "required": 2                                                                       // required block confirmation for successful deposit
        //                }
        //            },
        //            {...},
        //        ],
        //        "total": 300                                                                                    // total number of  transactions, use this for calculating ‘limit’ and ‘offset'
        //    }
        //
        object records = this.safeList(response, "records");
        return this.parseTransactions(records, currency, since, limit);
    }

    /**
     * @method
     * @name whitebit#fetchConvertQuote
     * @description fetch a quote for converting from one currency to another
     * @see https://docs.whitebit.com/private/http-trade-v4/#convert-estimate
     * @param {string} fromCode the currency that you want to sell and convert from
     * @param {string} toCode the currency that you want to buy and convert into
     * @param {float} amount how much you want to trade in units of the from currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [conversion structure]{@link https://docs.ccxt.com/#/?id=conversion-structure}
     */
    public async override Task<object> fetchConvertQuote(object fromCode, object toCode, object amount = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object fromCurrency = this.currency(fromCode);
        object toCurrency = this.currency(toCode);
        object request = new Dictionary<string, object>() {
            { "from", fromCode },
            { "to", toCode },
            { "amount", this.numberToString(amount) },
            { "direction", "from" },
        };
        object response = await this.v4PrivatePostConvertEstimate(this.extend(request, parameters));
        //
        //     {
        //         "give": "4",
        //         "receive": "0.00004762",
        //         "rate": "0.0000119",
        //         "id": "1740889",
        //         "expireAt": 1741090147,
        //         "from": "USDT",
        //         "to": "BTC"
        //     }
        //
        return this.parseConversion(response, fromCurrency, toCurrency);
    }

    /**
     * @method
     * @name whitebit#createConvertTrade
     * @description convert from one currency to another
     * @see https://docs.whitebit.com/private/http-trade-v4/#convert-confirm
     * @param {string} id the id of the trade that you want to make
     * @param {string} fromCode the currency that you want to sell and convert from
     * @param {string} toCode the currency that you want to buy and convert into
     * @param {float} [amount] how much you want to trade in units of the from currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [conversion structure]{@link https://docs.ccxt.com/#/?id=conversion-structure}
     */
    public async override Task<object> createConvertTrade(object id, object fromCode, object toCode, object amount = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object fromCurrency = this.currency(fromCode);
        object toCurrency = this.currency(toCode);
        object request = new Dictionary<string, object>() {
            { "quoteId", id },
        };
        object response = await this.v4PrivatePostConvertConfirm(this.extend(request, parameters));
        //
        //     {
        //         "finalGive": "4",
        //         "finalReceive": "0.00004772"
        //     }
        //
        return this.parseConversion(response, fromCurrency, toCurrency);
    }

    /**
     * @method
     * @name whitebit#fetchConvertTradeHistory
     * @description fetch the users history of conversion trades
     * @see https://docs.whitebit.com/private/http-trade-v4/#convert-history
     * @param {string} [code] the unified currency code
     * @param {int} [since] the earliest time in ms to fetch conversions for
     * @param {int} [limit] the maximum number of conversion structures to retrieve, default 20, max 200
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.until] the end time in ms
     * @param {string} [params.fromTicker] the currency that you sold and converted from
     * @param {string} [params.toTicker] the currency that you bought and converted into
     * @param {string} [params.quoteId] the quote id of the conversion
     * @returns {object[]} a list of [conversion structures]{@link https://docs.ccxt.com/#/?id=conversion-structure}
     */
    public async override Task<object> fetchConvertTradeHistory(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(code, null)))
        {
            ((IDictionary<string,object>)request)["fromTicker"] = code;
        }
        if (isTrue(!isEqual(since, null)))
        {
            object start = this.parseToInt(divide(since, 1000));
            ((IDictionary<string,object>)request)["from"] = this.numberToString(start);
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        var requestparametersVariable = this.handleUntilOption("to", request, parameters, 0.001);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.v4PrivatePostConvertHistory(this.extend(request, parameters));
        //
        //     {
        //         "records": [
        //             {
        //                 "id": "1741105",
        //                 "path": [
        //                     {
        //                         "from": "USDT",
        //                         "to": "BTC",
        //                         "rate": "0.00001193"
        //                     }
        //                 ],
        //                 "date": 1741090757,
        //                 "give": "4",
        //                 "receive": "0.00004772",
        //                 "rate": "0.00001193"
        //             }
        //         ],
        //         "total": 1,
        //         "limit": 100,
        //         "offset": 0
        //     }
        //
        object rows = this.safeList(response, "records", new List<object>() {});
        return this.parseConversions(rows, code, "fromCurrency", "toCurrency", since, limit);
    }

    public override object parseConversion(object conversion, object fromCurrency = null, object toCurrency = null)
    {
        //
        // fetchConvertQuote
        //
        //     {
        //         "give": "4",
        //         "receive": "0.00004762",
        //         "rate": "0.0000119",
        //         "id": "1740889",
        //         "expireAt": 1741090147,
        //         "from": "USDT",
        //         "to": "BTC"
        //     }
        //
        // createConvertTrade
        //
        //     {
        //         "finalGive": "4",
        //         "finalReceive": "0.00004772"
        //     }
        //
        // fetchConvertTradeHistory
        //
        //     {
        //         "id": "1741105",
        //         "path": [
        //             {
        //                 "from": "USDT",
        //                 "to": "BTC",
        //                 "rate": "0.00001193"
        //             }
        //         ],
        //         "date": 1741090757,
        //         "give": "4",
        //         "receive": "0.00004772",
        //         "rate": "0.00001193"
        //     }
        //
        object path = this.safeList(conversion, "path", new List<object>() {});
        object first = this.safeDict(path, 0, new Dictionary<string, object>() {});
        object fromPath = this.safeString(first, "from");
        object toPath = this.safeString(first, "to");
        object timestamp = this.safeTimestamp2(conversion, "date", "expireAt");
        object fromCoin = this.safeString(conversion, "from", fromPath);
        object fromCode = this.safeCurrencyCode(fromCoin, fromCurrency);
        object toCoin = this.safeString(conversion, "to", toPath);
        object toCode = this.safeCurrencyCode(toCoin, toCurrency);
        return new Dictionary<string, object>() {
            { "info", conversion },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "id", this.safeString(conversion, "id") },
            { "fromCurrency", fromCode },
            { "fromAmount", this.safeNumber2(conversion, "give", "finalGive") },
            { "toCurrency", toCode },
            { "toAmount", this.safeNumber2(conversion, "receive", "finalReceive") },
            { "price", this.safeNumber(conversion, "rate") },
            { "fee", null },
        };
    }

    /**
     * @method
     * @name whitebit#fetchPositionHistory
     * @description fetches historical positions
     * @see https://docs.whitebit.com/private/http-trade-v4/#positions-history
     * @param {string} symbol unified contract symbol
     * @param {int} [since] the earliest time in ms to fetch positions for
     * @param {int} [limit] the maximum amount of records to fetch
     * @param {object} [params] extra parameters specific to the exchange api endpoint
     * @param {int} [params.positionId] the id of the requested position
     * @returns {object[]} a list of [position structures]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPositionHistory(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "market", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startDate"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = since;
        }
        var requestparametersVariable = this.handleUntilOption("endDate", request, parameters);
        request = ((IList<object>)requestparametersVariable)[0];
        parameters = ((IList<object>)requestparametersVariable)[1];
        object response = await this.v4PrivatePostCollateralAccountPositionsHistory(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "positionId": *********,
        //             "market": "BTC_PERP",
        //             "openDate": **********.309887,
        //             "modifyDate": **********.309887,
        //             "amount": "0.001",
        //             "basePrice": "82498.7",
        //             "realizedFunding": "0",
        //             "liquidationPrice": "0",
        //             "liquidationState": null,
        //             "orderDetail": {
        //                 "id": *************,
        //                 "tradeAmount": "0.001",
        //                 "price": "82498.7",
        //                 "tradeFee": "0.*********",
        //                 "fundingFee": "0",
        //                 "realizedPnl": "-0.*********"
        //             }
        //         }
        //     ]
        //
        object positions = this.parsePositions(response);
        return this.filterBySymbolSinceLimit(positions, symbol, since, limit);
    }

    /**
     * @method
     * @name whitebit#fetchPositions
     * @description fetch all open positions
     * @see https://docs.whitebit.com/private/http-trade-v4/#open-positions
     * @param {string[]} [symbols] list of unified market symbols
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [position structures]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPositions(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        object response = await this.v4PrivatePostCollateralAccountPositionsOpen(parameters);
        //
        //     [
        //         {
        //             "positionId": *********,
        //             "market": "BTC_PERP",
        //             "openDate": **********.3098869,
        //             "modifyDate": **********.3098869,
        //             "amount": "0.001",
        //             "basePrice": "82498.7",
        //             "liquidationPrice": "70177.2",
        //             "pnl": "0",
        //             "pnlPercent": "0.00",
        //             "margin": "4.2",
        //             "freeMargin": "9.9",
        //             "funding": "0",
        //             "unrealizedFunding": "0",
        //             "liquidationState": null,
        //             "tpsl": null
        //         }
        //     ]
        //
        return this.parsePositions(response, symbols);
    }

    /**
     * @method
     * @name whitebit#fetchPosition
     * @description fetch data on a single open contract trade position
     * @see https://docs.whitebit.com/private/http-trade-v4/#open-positions
     * @param {string} symbol unified market symbol of the market the position is held in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
     */
    public async override Task<object> fetchPosition(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.v4PrivatePostCollateralAccountPositionsOpen(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "positionId": *********,
        //             "market": "BTC_PERP",
        //             "openDate": **********.3098869,
        //             "modifyDate": **********.3098869,
        //             "amount": "0.001",
        //             "basePrice": "82498.7",
        //             "liquidationPrice": "70177.2",
        //             "pnl": "0",
        //             "pnlPercent": "0.00",
        //             "margin": "4.2",
        //             "freeMargin": "9.9",
        //             "funding": "0",
        //             "unrealizedFunding": "0",
        //             "liquidationState": null,
        //             "tpsl": null
        //         }
        //     ]
        //
        object data = this.safeDict(response, 0, new Dictionary<string, object>() {});
        return this.parsePosition(data, market);
    }

    public override object parsePosition(object position, object market = null)
    {
        //
        // fetchPosition, fetchPositions
        //
        //     {
        //         "positionId": *********,
        //         "market": "BTC_PERP",
        //         "openDate": **********.3098869,
        //         "modifyDate": **********.3098869,
        //         "amount": "0.001",
        //         "basePrice": "82498.7",
        //         "liquidationPrice": "70177.2",
        //         "pnl": "0",
        //         "pnlPercent": "0.00",
        //         "margin": "4.2",
        //         "freeMargin": "9.9",
        //         "funding": "0",
        //         "unrealizedFunding": "0",
        //         "liquidationState": null,
        //         "tpsl": null
        //     }
        //
        // fetchPositionHistory
        //
        //     {
        //         "positionId": *********,
        //         "market": "BTC_PERP",
        //         "openDate": **********.309887,
        //         "modifyDate": **********.309887,
        //         "amount": "0.001",
        //         "basePrice": "82498.7",
        //         "realizedFunding": "0",
        //         "liquidationPrice": "0",
        //         "liquidationState": null,
        //         "orderDetail": {
        //             "id": *************,
        //             "tradeAmount": "0.001",
        //             "price": "82498.7",
        //             "tradeFee": "0.*********",
        //             "fundingFee": "0",
        //             "realizedPnl": "-0.*********"
        //         }
        //     }
        //
        object marketId = this.safeString(position, "market");
        object timestamp = this.safeTimestamp(position, "openDate");
        object tpsl = this.safeDict(position, "tpsl", new Dictionary<string, object>() {});
        object orderDetail = this.safeDict(position, "orderDetail", new Dictionary<string, object>() {});
        return this.safePosition(new Dictionary<string, object>() {
            { "info", position },
            { "id", this.safeString(position, "positionId") },
            { "symbol", this.safeSymbol(marketId, market) },
            { "notional", null },
            { "marginMode", null },
            { "liquidationPrice", this.safeNumber(position, "liquidationPrice") },
            { "entryPrice", this.safeNumber(position, "basePrice") },
            { "unrealizedPnl", this.safeNumber(position, "pnl") },
            { "realizedPnl", this.safeNumber(orderDetail, "realizedPnl") },
            { "percentage", this.safeNumber(position, "pnlPercent") },
            { "contracts", null },
            { "contractSize", null },
            { "markPrice", null },
            { "lastPrice", null },
            { "side", null },
            { "hedged", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastUpdateTimestamp", this.safeTimestamp(position, "modifyDate") },
            { "maintenanceMargin", null },
            { "maintenanceMarginPercentage", null },
            { "collateral", this.safeNumber(position, "margin") },
            { "initialMargin", null },
            { "initialMarginPercentage", null },
            { "leverage", null },
            { "marginRatio", null },
            { "stopLossPrice", this.safeNumber(tpsl, "stopLoss") },
            { "takeProfitPrice", this.safeNumber(tpsl, "takeProfit") },
        });
    }

    /**
     * @method
     * @name whitebit#fetchCrossBorrowRate
     * @description fetch the rate of interest to borrow a currency for margin trading
     * @see https://docs.whitebit.com/private/http-main-v4/#get-plans
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [borrow rate structure]{@link https://docs.ccxt.com/#/?id=borrow-rate-structure}
     */
    public async override Task<object> fetchCrossBorrowRate(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "ticker", getValue(currency, "id") },
        };
        object response = await this.v4PrivatePostMainAccountSmartPlans(this.extend(request, parameters));
        //
        //
        object data = this.safeList(response, 0, new List<object>() {});
        return this.parseBorrowRate(data, currency);
    }

    public override object parseBorrowRate(object info, object currency = null)
    {
        //
        //
        object currencyId = this.safeString(info, "ticker");
        object percent = this.safeString(info, "percent");
        return new Dictionary<string, object>() {
            { "currency", this.safeCurrencyCode(currencyId, currency) },
            { "rate", this.parseNumber(Precise.stringDiv(percent, "100")) },
            { "period", this.safeInteger(info, "duration") },
            { "timestamp", null },
            { "datetime", null },
            { "info", info },
        };
    }

    public virtual object isFiat(object currency)
    {
        object fiatCurrencies = this.safeValue(this.options, "fiatCurrencies", new List<object>() {});
        return this.inArray(currency, fiatCurrencies);
    }

    public override object nonce()
    {
        return subtract(this.milliseconds(), getValue(this.options, "timeDifference"));
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object query = this.omit(parameters, this.extractParams(path));
        object version = this.safeValue(((object)api), 0);
        object accessibility = this.safeValue(((object)api), 1);
        object pathWithParams = add("/", this.implodeParams(path, parameters));
        object url = add(getValue(getValue(getValue(this.urls, "api"), version), accessibility), pathWithParams);
        if (isTrue(isEqual(accessibility, "public")))
        {
            if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)query).Keys))))
            {
                url = add(url, add("?", this.urlencode(query)));
            }
        }
        if (isTrue(isEqual(accessibility, "private")))
        {
            this.checkRequiredCredentials();
            object nonce = ((object)this.nonce()).ToString();
            object secret = this.encode(this.secret);
            object request = add(add(add(add("/", "api"), "/"), version), pathWithParams);
            body = this.json(this.extend(new Dictionary<string, object>() {
                { "request", request },
                { "nonce", nonce },
            }, parameters));
            object payload = this.stringToBase64(body);
            object signature = this.hmac(this.encode(payload), secret, sha512);
            headers = new Dictionary<string, object>() {
                { "Content-Type", "application/json" },
                { "X-TXC-APIKEY", this.apiKey },
                { "X-TXC-PAYLOAD", payload },
                { "X-TXC-SIGNATURE", signature },
            };
        }
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object code, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        if (isTrue(isTrue((isEqual(code, 418))) || isTrue((isEqual(code, 429)))))
        {
            throw new DDoSProtection ((string)add(add(add(add(add(add(this.id, " "), ((object)code).ToString()), " "), reason), " "), body)) ;
        }
        if (isTrue(isEqual(code, 404)))
        {
            throw new ExchangeError ((string)add(add(add(this.id, " "), ((object)code).ToString()), " endpoint not found")) ;
        }
        if (isTrue(!isEqual(response, null)))
        {
            // For cases where we have a meaningful status
            // {"response":null,"status":422,"errors":{"orderId":["Finished order id ************ not found on your account"]},"notification":null,"warning":"Finished order id ************ not found on your account","_token":null}
            object status = this.safeString(response, "status");
            object errors = this.safeValue(response, "errors");
            // {"code":10,"message":"Unauthorized request."}
            object message = this.safeString(response, "message");
            // For these cases where we have a generic code variable error key
            // {"code":0,"message":"Validation failed","errors":{"amount":["Amount must be greater than 0"]}}
            object codeNew = this.safeInteger(response, "code");
            object hasErrorStatus = isTrue(isTrue(!isEqual(status, null)) && isTrue(!isEqual(status, "200"))) && isTrue(!isEqual(errors, null));
            if (isTrue(isTrue(hasErrorStatus) || isTrue(!isEqual(codeNew, null))))
            {
                object feedback = add(add(this.id, " "), body);
                object errorInfo = message;
                if (isTrue(hasErrorStatus))
                {
                    errorInfo = status;
                } else
                {
                    object errorObject = this.safeDict(response, "errors", new Dictionary<string, object>() {});
                    object errorKeys = new List<object>(((IDictionary<string,object>)errorObject).Keys);
                    object errorsLength = getArrayLength(errorKeys);
                    if (isTrue(isGreaterThan(errorsLength, 0)))
                    {
                        object errorKey = getValue(errorKeys, 0);
                        object errorMessageArray = this.safeValue(errorObject, errorKey, new List<object>() {});
                        object errorMessageLength = getArrayLength(errorMessageArray);
                        errorInfo = ((bool) isTrue((isGreaterThan(errorMessageLength, 0)))) ? getValue(errorMessageArray, 0) : body;
                    }
                }
                this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), errorInfo, feedback);
                this.throwBroadlyMatchedException(getValue(this.exceptions, "broad"), body, feedback);
                throw new ExchangeError ((string)feedback) ;
            }
        }
        return null;
    }
}
