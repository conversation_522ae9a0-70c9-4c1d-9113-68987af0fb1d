namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class blockchaincom : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "blockchaincom" },
            { "secret", null },
            { "name", "Blockchain.com" },
            { "countries", new List<object>() {"LX"} },
            { "rateLimit", 500 },
            { "version", "v3" },
            { "pro", true },
            { "has", new Dictionary<string, object>() {
                { "CORS", false },
                { "spot", true },
                { "margin", null },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "cancelAllOrders", true },
                { "cancelOrder", true },
                { "createOrder", true },
                { "createStopLimitOrder", true },
                { "createStopMarketOrder", true },
                { "createStopOrder", true },
                { "fetchBalance", true },
                { "fetchCanceledOrders", true },
                { "fetchClosedOrders", true },
                { "fetchDeposit", true },
                { "fetchDepositAddress", true },
                { "fetchDepositAddresses", false },
                { "fetchDepositAddressesByNetwork", false },
                { "fetchDeposits", true },
                { "fetchFundingHistory", false },
                { "fetchFundingRate", false },
                { "fetchFundingRateHistory", false },
                { "fetchFundingRates", false },
                { "fetchIndexOHLCV", false },
                { "fetchL2OrderBook", true },
                { "fetchL3OrderBook", true },
                { "fetchLedger", false },
                { "fetchMarginMode", false },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMyTrades", true },
                { "fetchOHLCV", false },
                { "fetchOpenInterestHistory", false },
                { "fetchOpenOrders", true },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchPositionMode", false },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTrades", false },
                { "fetchTradingFee", false },
                { "fetchTradingFees", true },
                { "fetchTransfer", false },
                { "fetchTransfers", false },
                { "fetchWithdrawal", true },
                { "fetchWithdrawals", true },
                { "fetchWithdrawalWhitelist", true },
                { "transfer", false },
                { "withdraw", true },
            } },
            { "timeframes", null },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://github.com/user-attachments/assets/975e3054-3399-4363-bcee-ec3c6d63d4e8" },
                { "test", new Dictionary<string, object>() {
                    { "public", "https://testnet-api.delta.exchange" },
                    { "private", "https://testnet-api.delta.exchange" },
                } },
                { "api", new Dictionary<string, object>() {
                    { "public", "https://api.blockchain.com/v3/exchange" },
                    { "private", "https://api.blockchain.com/v3/exchange" },
                } },
                { "www", "https://blockchain.com" },
                { "doc", new List<object>() {"https://api.blockchain.com/v3"} },
                { "fees", "https://exchange.blockchain.com/fees" },
            } },
            { "api", new Dictionary<string, object>() {
                { "public", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "tickers", 1 },
                        { "tickers/{symbol}", 1 },
                        { "symbols", 1 },
                        { "symbols/{symbol}", 1 },
                        { "l2/{symbol}", 1 },
                        { "l3/{symbol}", 1 },
                    } },
                } },
                { "private", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "fees", 1 },
                        { "orders", 1 },
                        { "orders/{orderId}", 1 },
                        { "trades", 1 },
                        { "fills", 1 },
                        { "deposits", 1 },
                        { "deposits/{depositId}", 1 },
                        { "accounts", 1 },
                        { "accounts/{account}/{currency}", 1 },
                        { "whitelist", 1 },
                        { "whitelist/{currency}", 1 },
                        { "withdrawals", 1 },
                        { "withdrawals/{withdrawalId}", 1 },
                    } },
                    { "post", new Dictionary<string, object>() {
                        { "orders", 1 },
                        { "deposits/{currency}", 1 },
                        { "withdrawals", 1 },
                    } },
                    { "delete", new Dictionary<string, object>() {
                        { "orders", 1 },
                        { "orders/{orderId}", 1 },
                    } },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "feeSide", "get" },
                    { "tierBased", true },
                    { "percentage", true },
                    { "tiers", new Dictionary<string, object>() {
                        { "taker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.0045")}, new List<object> {this.parseNumber("10000"), this.parseNumber("0.0035")}, new List<object> {this.parseNumber("50000"), this.parseNumber("0.0018")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.0018")}, new List<object> {this.parseNumber("500000"), this.parseNumber("0.0018")}, new List<object> {this.parseNumber("1000000"), this.parseNumber("0.0018")}, new List<object> {this.parseNumber("2500000"), this.parseNumber("0.0018")}, new List<object> {this.parseNumber("5000000"), this.parseNumber("0.0016")}, new List<object> {this.parseNumber("25000000"), this.parseNumber("0.0014")}, new List<object> {this.parseNumber("100000000"), this.parseNumber("0.0011")}, new List<object> {this.parseNumber("500000000"), this.parseNumber("0.0008")}, new List<object> {this.parseNumber("1000000000"), this.parseNumber("0.0006")}} },
                        { "maker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.004")}, new List<object> {this.parseNumber("10000"), this.parseNumber("0.0017")}, new List<object> {this.parseNumber("50000"), this.parseNumber("0.0015")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.0008")}, new List<object> {this.parseNumber("500000"), this.parseNumber("0.0007")}, new List<object> {this.parseNumber("1000000"), this.parseNumber("0.0006")}, new List<object> {this.parseNumber("2500000"), this.parseNumber("0.0005")}, new List<object> {this.parseNumber("5000000"), this.parseNumber("0.0004")}, new List<object> {this.parseNumber("25000000"), this.parseNumber("0.0003")}, new List<object> {this.parseNumber("100000000"), this.parseNumber("0.0002")}, new List<object> {this.parseNumber("500000000"), this.parseNumber("0.0001")}, new List<object> {this.parseNumber("1000000000"), this.parseNumber("0")}} },
                    } },
                } },
            } },
            { "requiredCredentials", new Dictionary<string, object>() {
                { "apiKey", false },
                { "secret", true },
            } },
            { "options", new Dictionary<string, object>() {
                { "networks", new Dictionary<string, object>() {
                    { "ERC20", "ETH" },
                    { "TRC20", "TRX" },
                    { "ALGO", "ALGO" },
                    { "ADA", "ADA" },
                    { "AR", "AR" },
                    { "ATOM", "ATOM" },
                    { "AVAXC", "AVAX" },
                    { "BCH", "BCH" },
                    { "BSV", "BSV" },
                    { "BTC", "BTC" },
                    { "DCR", "DCR" },
                    { "DESO", "DESO" },
                    { "DASH", "DASH" },
                    { "CELO", "CELO" },
                    { "CHZ", "CHZ" },
                    { "MATIC", "MATIC" },
                    { "SOL", "SOL" },
                    { "DOGE", "DOGE" },
                    { "DOT", "DOT" },
                    { "EOS", "EOS" },
                    { "ETC", "ETC" },
                    { "FIL", "FIL" },
                    { "KAVA", "KAVA" },
                    { "LTC", "LTC" },
                    { "IOTA", "MIOTA" },
                    { "NEAR", "NEAR" },
                    { "STX", "STX" },
                    { "XLM", "XLM" },
                    { "XMR", "XMR" },
                    { "XRP", "XRP" },
                    { "XTZ", "XTZ" },
                    { "ZEC", "ZEC" },
                    { "ZIL", "ZIL" },
                } },
            } },
            { "features", new Dictionary<string, object>() {
                { "spot", new Dictionary<string, object>() {
                    { "sandbox", false },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "triggerPrice", true },
                        { "triggerPriceType", null },
                        { "triggerDirection", false },
                        { "stopLossPrice", false },
                        { "takeProfitPrice", false },
                        { "attachedStopLossTakeProfit", null },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", true },
                            { "PO", false },
                            { "GTD", true },
                        } },
                        { "hedged", false },
                        { "leverage", false },
                        { "marketBuyRequiresPrice", false },
                        { "marketBuyByCost", false },
                        { "selfTradePrevention", false },
                        { "trailing", false },
                        { "iceberg", false },
                    } },
                    { "createOrders", null },
                    { "fetchMyTrades", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", 100000 },
                        { "untilDays", 100000 },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", false },
                        { "symbolRequired", false },
                        { "trailing", false },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrders", null },
                    { "fetchClosedOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", 100000 },
                        { "daysBackCanceled", 1 },
                        { "untilDays", 100000 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOHLCV", null },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
            } },
            { "precisionMode", TICK_SIZE },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "401", typeof(AuthenticationError) },
                    { "404", typeof(OrderNotFound) },
                } },
                { "broad", new Dictionary<string, object>() {} },
            } },
        });
    }

    /**
     * @method
     * @name blockchaincom#fetchMarkets
     * @description retrieves data on all markets for blockchaincom
     * @see https://api.blockchain.com/v3/#getsymbols
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        //
        //     "USDC-GBP": {
        //         "base_currency": "USDC",
        //         "base_currency_scale": 6,
        //         "counter_currency": "GBP",
        //         "counter_currency_scale": 2,
        //         "min_price_increment": 10000,
        //         "min_price_increment_scale": 8,
        //         "min_order_size": 500000000,
        //         "min_order_size_scale": 8,
        //         "max_order_size": 0,
        //         "max_order_size_scale": 8,
        //         "lot_size": 10000,
        //         "lot_size_scale": 8,
        //         "status": "open",
        //         "id": 68,
        //         "auction_price": 0,
        //         "auction_size": 0,
        //         "auction_time": "",
        //         "imbalance": 0
        //     }
        //
        parameters ??= new Dictionary<string, object>();
        object markets = await this.publicGetSymbols(parameters);
        object marketIds = new List<object>(((IDictionary<string,object>)markets).Keys);
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
        {
            object marketId = getValue(marketIds, i);
            object market = this.safeValue(markets, marketId);
            object baseId = this.safeString(market, "base_currency");
            object quoteId = this.safeString(market, "counter_currency");
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object numericId = this.safeNumber(market, "id");
            object active = null;
            object marketState = this.safeString(market, "status");
            if (isTrue(isEqual(marketState, "open")))
            {
                active = true;
            } else
            {
                active = false;
            }
            // price precision
            object minPriceIncrementString = this.safeString(market, "min_price_increment");
            object minPriceIncrementScaleString = this.safeString(market, "min_price_increment_scale");
            object minPriceScalePrecisionString = this.parsePrecision(minPriceIncrementScaleString);
            object pricePrecisionString = Precise.stringMul(minPriceIncrementString, minPriceScalePrecisionString);
            // amount precision
            object lotSizeString = this.safeString(market, "lot_size");
            object lotSizeScaleString = this.safeString(market, "lot_size_scale");
            object lotSizeScalePrecisionString = this.parsePrecision(lotSizeScaleString);
            object amountPrecisionString = Precise.stringMul(lotSizeString, lotSizeScalePrecisionString);
            // minimum order size
            object minOrderSizeString = this.safeString(market, "min_order_size");
            object minOrderSizeScaleString = this.safeString(market, "min_order_size_scale");
            object minOrderSizeScalePrecisionString = this.parsePrecision(minOrderSizeScaleString);
            object minOrderSizePreciseString = Precise.stringMul(minOrderSizeString, minOrderSizeScalePrecisionString);
            object minOrderSize = this.parseNumber(minOrderSizePreciseString);
            // maximum order size
            object maxOrderSize = null;
            maxOrderSize = this.safeString(market, "max_order_size");
            if (isTrue(!isEqual(maxOrderSize, "0")))
            {
                object maxOrderSizeScaleString = this.safeString(market, "max_order_size_scale");
                object maxOrderSizeScalePrecisionString = this.parsePrecision(maxOrderSizeScaleString);
                object maxOrderSizeString = Precise.stringMul(maxOrderSize, maxOrderSizeScalePrecisionString);
                maxOrderSize = this.parseNumber(maxOrderSizeString);
            } else
            {
                maxOrderSize = null;
            }
            ((IList<object>)result).Add(new Dictionary<string, object>() {
                { "info", market },
                { "id", marketId },
                { "numericId", numericId },
                { "symbol", add(add(bs, "/"), quote) },
                { "base", bs },
                { "quote", quote },
                { "settle", null },
                { "baseId", baseId },
                { "quoteId", quoteId },
                { "settleId", null },
                { "type", "spot" },
                { "spot", true },
                { "margin", false },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "active", active },
                { "contract", false },
                { "linear", null },
                { "inverse", null },
                { "contractSize", null },
                { "expiry", null },
                { "expiryDatetime", null },
                { "strike", null },
                { "optionType", null },
                { "precision", new Dictionary<string, object>() {
                    { "amount", this.parseNumber(amountPrecisionString) },
                    { "price", this.parseNumber(pricePrecisionString) },
                } },
                { "limits", new Dictionary<string, object>() {
                    { "leverage", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "amount", new Dictionary<string, object>() {
                        { "min", minOrderSize },
                        { "max", maxOrderSize },
                    } },
                    { "price", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "cost", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                } },
                { "created", null },
            });
        }
        return result;
    }

    /**
     * @method
     * @name blockchaincom#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://api.blockchain.com/v3/#getl3orderbook
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.fetchL3OrderBook(symbol, limit, parameters);
    }

    /**
     * @method
     * @name blockchaincom#fetchL3OrderBook
     * @description fetches level 3 information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://api.blockchain.com/v3/#getl3orderbook
     * @param {string} symbol unified market symbol
     * @param {int} [limit] max number of orders to return, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order book structure]{@link https://docs.ccxt.com/#/?id=order-book-structure}
     */
    public async override Task<object> fetchL3OrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["depth"] = limit;
        }
        object response = await this.publicGetL3Symbol(this.extend(request, parameters));
        return this.parseOrderBook(response, getValue(market, "symbol"), null, "bids", "asks", "px", "qty");
    }

    public async override Task<object> fetchL2OrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["depth"] = limit;
        }
        object response = await this.publicGetL2Symbol(this.extend(request, parameters));
        return this.parseOrderBook(response, getValue(market, "symbol"), null, "bids", "asks", "px", "qty");
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        //     {
        //     "symbol": "BTC-USD",
        //     "price_24h": 47791.86,
        //     "volume_24h": 362.88635738,
        //     "last_trade_price": 47587.75
        //     }
        //
        object marketId = this.safeString(ticker, "symbol");
        object symbol = this.safeSymbol(marketId, market, "-");
        object last = this.safeString(ticker, "last_trade_price");
        object baseVolume = this.safeString(ticker, "volume_24h");
        object open = this.safeString(ticker, "price_24h");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", symbol },
            { "timestamp", null },
            { "datetime", null },
            { "high", null },
            { "low", null },
            { "bid", null },
            { "bidVolume", null },
            { "ask", null },
            { "askVolume", null },
            { "vwap", null },
            { "open", open },
            { "close", null },
            { "last", last },
            { "previousClose", null },
            { "change", null },
            { "percentage", null },
            { "average", null },
            { "baseVolume", baseVolume },
            { "quoteVolume", null },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name blockchaincom#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://api.blockchain.com/v3/#gettickerbysymbol
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.publicGetTickersSymbol(this.extend(request, parameters));
        return this.parseTicker(response, market);
    }

    /**
     * @method
     * @name blockchaincom#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://api.blockchain.com/v3/#gettickers
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object tickers = await this.publicGetTickers(parameters);
        return this.parseTickers(tickers, symbols);
    }

    public virtual object parseOrderState(object state)
    {
        object states = new Dictionary<string, object>() {
            { "OPEN", "open" },
            { "REJECTED", "rejected" },
            { "FILLED", "closed" },
            { "CANCELED", "canceled" },
            { "PART_FILLED", "open" },
            { "EXPIRED", "expired" },
        };
        return this.safeString(states, state, state);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        //     {
        //         "clOrdId": "00001",
        //         "ordType": "MARKET",
        //         "ordStatus": "FILLED",
        //         "side": "BUY",
        //         "symbol": "USDC-USDT",
        //         "exOrdId": "281775861306290",
        //         "price": null,
        //         "text": "Fill",
        //         "lastShares": "30.0",
        //         "lastPx": "0.9999",
        //         "leavesQty": "0.0",
        //         "cumQty": "30.0",
        //         "avgPx": "0.9999",
        //         "timestamp": "1633940339619"
        //     }
        //
        object clientOrderId = this.safeString(order, "clOrdId");
        object type = this.safeStringLower(order, "ordType");
        object statusId = this.safeString(order, "ordStatus");
        object state = this.parseOrderState(statusId);
        object side = this.safeStringLower(order, "side");
        object marketId = this.safeString(order, "symbol");
        object symbol = this.safeSymbol(marketId, market, "-");
        object exchangeOrderId = this.safeString(order, "exOrdId");
        object price = ((bool) isTrue((!isEqual(type, "market")))) ? this.safeString(order, "price") : null;
        object average = this.safeNumber(order, "avgPx");
        object timestamp = this.safeInteger(order, "timestamp");
        object datetime = this.iso8601(timestamp);
        object filled = this.safeString(order, "cumQty");
        object remaining = this.safeString(order, "leavesQty");
        object result = this.safeOrder(new Dictionary<string, object>() {
            { "id", exchangeOrderId },
            { "clientOrderId", clientOrderId },
            { "datetime", datetime },
            { "timestamp", timestamp },
            { "lastTradeTimestamp", null },
            { "status", state },
            { "symbol", symbol },
            { "type", type },
            { "timeInForce", null },
            { "side", side },
            { "price", price },
            { "average", average },
            { "amount", null },
            { "filled", filled },
            { "remaining", remaining },
            { "cost", null },
            { "trades", new List<object>() {} },
            { "fees", new List<object>() {} },
            { "info", order },
        });
        return result;
    }

    /**
     * @method
     * @name blockchaincom#createOrder
     * @description create a trade order
     * @see https://api.blockchain.com/v3/#createorder
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object orderType = this.safeString(parameters, "ordType", type);
        object uppercaseOrderType = ((string)orderType).ToUpper();
        object clientOrderId = this.safeString2(parameters, "clientOrderId", "clOrdId", this.uuid16());
        parameters = this.omit(parameters, new List<object>() {"ordType", "clientOrderId", "clOrdId"});
        object request = new Dictionary<string, object>() {
            { "ordType", uppercaseOrderType },
            { "symbol", getValue(market, "id") },
            { "side", ((string)side).ToUpper() },
            { "orderQty", this.amountToPrecision(symbol, amount) },
            { "clOrdId", clientOrderId },
        };
        object triggerPrice = this.safeValueN(parameters, new List<object>() {"triggerPrice", "stopPx", "stopPrice"});
        parameters = this.omit(parameters, new List<object>() {"triggerPrice", "stopPx", "stopPrice"});
        if (isTrue(isTrue(isEqual(uppercaseOrderType, "STOP")) || isTrue(isEqual(uppercaseOrderType, "STOPLIMIT"))))
        {
            if (isTrue(isEqual(triggerPrice, null)))
            {
                throw new ArgumentsRequired ((string)add(add(add(this.id, " createOrder() requires a stopPx or triggerPrice param for a "), uppercaseOrderType), " order")) ;
            }
        }
        if (isTrue(!isEqual(triggerPrice, null)))
        {
            if (isTrue(isEqual(uppercaseOrderType, "MARKET")))
            {
                ((IDictionary<string,object>)request)["ordType"] = "STOP";
            } else if (isTrue(isEqual(uppercaseOrderType, "LIMIT")))
            {
                ((IDictionary<string,object>)request)["ordType"] = "STOPLIMIT";
            }
        }
        object priceRequired = false;
        object stopPriceRequired = false;
        if (isTrue(isTrue(isEqual(getValue(request, "ordType"), "LIMIT")) || isTrue(isEqual(getValue(request, "ordType"), "STOPLIMIT"))))
        {
            priceRequired = true;
        }
        if (isTrue(isTrue(isEqual(getValue(request, "ordType"), "STOP")) || isTrue(isEqual(getValue(request, "ordType"), "STOPLIMIT"))))
        {
            stopPriceRequired = true;
        }
        if (isTrue(priceRequired))
        {
            ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
        }
        if (isTrue(stopPriceRequired))
        {
            ((IDictionary<string,object>)request)["stopPx"] = this.priceToPrecision(symbol, triggerPrice);
        }
        object response = await this.privatePostOrders(this.extend(request, parameters));
        return this.parseOrder(response, market);
    }

    /**
     * @method
     * @name blockchaincom#cancelOrder
     * @description cancels an open order
     * @see https://api.blockchain.com/v3/#deleteorder
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object request = new Dictionary<string, object>() {
            { "orderId", id },
        };
        object response = await this.privateDeleteOrdersOrderId(this.extend(request, parameters));
        return this.safeOrder(new Dictionary<string, object>() {
            { "id", id },
            { "info", response },
        });
    }

    /**
     * @method
     * @name blockchaincom#cancelAllOrders
     * @description cancel all open orders
     * @see https://api.blockchain.com/v3/#deleteallorders
     * @param {string} symbol unified market symbol of the market to cancel orders in, all markets are used if undefined, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        // cancels all open orders if no symbol specified
        // cancels all open orders of specified symbol, if symbol is specified
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            object marketId = this.marketId(symbol);
            ((IDictionary<string,object>)request)["symbol"] = marketId;
        }
        object response = await this.privateDeleteOrders(this.extend(request, parameters));
        //
        // {}
        //
        return new List<object> {this.safeOrder(new Dictionary<string, object>() {
    { "info", response },
})};
    }

    /**
     * @method
     * @name blockchaincom#fetchTradingFees
     * @description fetch the trading fees for multiple markets
     * @see https://api.blockchain.com/v3/#getfees
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
     */
    public async override Task<object> fetchTradingFees(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.privateGetFees(parameters);
        //
        //     {
        //         "makerRate": "0.002",
        //         "takerRate": "0.004",
        //         "volumeInUSD": "0.0"
        //     }
        //
        object makerFee = this.safeNumber(response, "makerRate");
        object takerFee = this.safeNumber(response, "takerRate");
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(this.symbols)); postFixIncrement(ref i))
        {
            object symbol = getValue(this.symbols, i);
            ((IDictionary<string,object>)result)[(string)symbol] = new Dictionary<string, object>() {
                { "info", response },
                { "symbol", symbol },
                { "maker", makerFee },
                { "taker", takerFee },
            };
        }
        return result;
    }

    /**
     * @method
     * @name blockchaincom#fetchCanceledOrders
     * @description fetches information on multiple canceled orders made by the user
     * @see https://api.blockchain.com/v3/#getorders
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] timestamp in ms of the earliest order, default is undefined
     * @param {int} [limit] max number of orders to return, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async virtual Task<object> fetchCanceledOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object state = "CANCELED";
        return await this.fetchOrdersByState(state, symbol, since, limit, parameters);
    }

    /**
     * @method
     * @name blockchaincom#fetchClosedOrders
     * @description fetches information on multiple closed orders made by the user
     * @see https://api.blockchain.com/v3/#getorders
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object state = "FILLED";
        return await this.fetchOrdersByState(state, symbol, since, limit, parameters);
    }

    /**
     * @method
     * @name blockchaincom#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://api.blockchain.com/v3/#getorders
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of  open orders structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object state = "OPEN";
        return await this.fetchOrdersByState(state, symbol, since, limit, parameters);
    }

    public async virtual Task<object> fetchOrdersByState(object state, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "status", state },
            { "limit", 100 },
        };
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object response = await this.privateGetOrders(this.extend(request, parameters));
        return this.parseOrders(response, market, since, limit);
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        //     {
        //         "exOrdId":281685751028507,
        //         "tradeId":281685434947633,
        //         "execId":8847494003,
        //         "side":"BUY",
        //         "symbol":"AAVE-USDT",
        //         "price":405.34,
        //         "qty":0.1,
        //         "fee":0.162136,
        //         "timestamp":1634559249687
        //     }
        //
        object orderId = this.safeString(trade, "exOrdId");
        object tradeId = this.safeString(trade, "tradeId");
        object side = ((string)this.safeString(trade, "side")).ToLower();
        object marketId = this.safeString(trade, "symbol");
        object priceString = this.safeString(trade, "price");
        object amountString = this.safeString(trade, "qty");
        object timestamp = this.safeInteger(trade, "timestamp");
        object datetime = this.iso8601(timestamp);
        market = this.safeMarket(marketId, market, "-");
        object symbol = getValue(market, "symbol");
        object fee = null;
        object feeCostString = this.safeString(trade, "fee");
        if (isTrue(!isEqual(feeCostString, null)))
        {
            object feeCurrency = getValue(market, "quote");
            fee = new Dictionary<string, object>() {
                { "cost", feeCostString },
                { "currency", feeCurrency },
            };
        }
        return this.safeTrade(new Dictionary<string, object>() {
            { "id", tradeId },
            { "timestamp", timestamp },
            { "datetime", datetime },
            { "symbol", symbol },
            { "order", orderId },
            { "type", null },
            { "side", side },
            { "takerOrMaker", null },
            { "price", priceString },
            { "amount", amountString },
            { "cost", null },
            { "fee", fee },
            { "info", trade },
        }, market);
    }

    /**
     * @method
     * @name blockchaincom#fetchMyTrades
     * @description fetch all trades made by the user
     * @see https://api.blockchain.com/v3/#getfills
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object market = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            ((IDictionary<string,object>)request)["symbol"] = this.marketId(symbol);
            market = this.market(symbol);
        }
        object trades = await this.privateGetFills(this.extend(request, parameters));
        return this.parseTrades(trades, market, since, limit, parameters);  // need to define
    }

    /**
     * @method
     * @name blockchaincom#fetchDepositAddress
     * @description fetch the deposit address for a currency associated with this account
     * @see https://api.blockchain.com/v3/#getdepositaddress
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> fetchDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "currency", getValue(currency, "id") },
        };
        object response = await this.privatePostDepositsCurrency(this.extend(request, parameters));
        object rawAddress = this.safeString(response, "address");
        object tag = null;
        object address = null;
        if (isTrue(!isEqual(rawAddress, null)))
        {
            object addressParts = ((string)rawAddress).Split(new [] {((string)";")}, StringSplitOptions.None).ToList<object>();
            // if a tag or memo is used it is separated by a colon in the 'address' value
            tag = this.safeString(addressParts, 0);
            address = this.safeString(addressParts, 1);
        }
        return new Dictionary<string, object>() {
            { "info", response },
            { "currency", getValue(currency, "code") },
            { "network", null },
            { "address", address },
            { "tag", tag },
        };
    }

    public virtual object parseTransactionState(object state)
    {
        object states = new Dictionary<string, object>() {
            { "COMPLETED", "ok" },
            { "REJECTED", "failed" },
            { "PENDING", "pending" },
            { "FAILED", "failed" },
            { "REFUNDED", "refunded" },
        };
        return this.safeString(states, state, state);
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        // deposit
        //
        //     {
        //         "depositId":"748e9180-be0d-4a80-e175-0156150efc95",
        //         "amount":0.009,
        //         "currency":"ETH",
        //         "address":"******************************************",
        //         "state":"COMPLETED",
        //         "txHash":"582114562140e51a80b481c2dfebaf62b4ab9769b8ff54820bb67e34d4a3ab0c",
        //         "timestamp":1633697196241
        //     }
        //
        // withdrawal
        //
        //     {
        //         "amount":30.0,
        //         "currency":"USDT",
        //         "beneficiary":"cab00d11-6e7f-46b7-b453-2e8ef6f101fa", // blockchain specific id
        //         "withdrawalId":"99df5ef7-eab6-4033-be49-312930fbd1ea",
        //         "fee":34.005078,
        //         "state":"COMPLETED",
        //         "timestamp":1634218452549
        //     }
        //
        object type = null;
        object id = null;
        object amount = this.safeNumber(transaction, "amount");
        object timestamp = this.safeInteger(transaction, "timestamp");
        object currencyId = this.safeString(transaction, "currency");
        object code = this.safeCurrencyCode(currencyId, currency);
        object state = this.safeString(transaction, "state");
        if (isTrue(inOp(transaction, "depositId")))
        {
            type = "deposit";
            id = this.safeString(transaction, "depositId");
        } else if (isTrue(inOp(transaction, "withdrawalId")))
        {
            type = "withdrawal";
            id = this.safeString(transaction, "withdrawalId");
        }
        object feeCost = ((bool) isTrue((isEqual(type, "withdrawal")))) ? this.safeNumber(transaction, "fee") : null;
        object fee = null;
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "currency", code },
                { "cost", feeCost },
            };
        }
        object address = this.safeString(transaction, "address");
        object txid = this.safeString(transaction, "txhash");
        return new Dictionary<string, object>() {
            { "info", transaction },
            { "id", id },
            { "txid", txid },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "network", null },
            { "addressFrom", null },
            { "address", address },
            { "addressTo", address },
            { "tagFrom", null },
            { "tag", null },
            { "tagTo", null },
            { "type", type },
            { "amount", amount },
            { "currency", code },
            { "status", this.parseTransactionState(state) },
            { "updated", null },
            { "comment", null },
            { "internal", null },
            { "fee", fee },
        };
    }

    /**
     * @method
     * @name blockchaincom#withdraw
     * @description make a withdrawal
     * @see https://api.blockchain.com/v3/#createwithdrawal
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address the address to withdraw to
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "amount", amount },
            { "currency", getValue(currency, "id") },
            { "beneficiary", address },
            { "sendMax", false },
        };
        object response = await this.privatePostWithdrawals(this.extend(request, parameters));
        //
        //     {
        //         "amount": "30.0",
        //         "currency": "USDT",
        //         "beneficiary": "adcd43fb-9ba6-41f7-8c0d-7013482cb88f",
        //         "withdrawalId": "99df5ef7-eab6-4033-be49-312930fbd1ea",
        //         "fee": "34.005078",
        //         "state": "PENDING",
        //         "timestamp": "*************"
        //     },
        //
        return this.parseTransaction(response, currency);
    }

    /**
     * @method
     * @name blockchaincom#fetchWithdrawals
     * @description fetch all withdrawals made from an account
     * @see https://api.blockchain.com/v3/#getwithdrawals
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch withdrawals for
     * @param {int} [limit] the maximum number of withdrawals structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["from"] = since;
        }
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
        }
        object response = await this.privateGetWithdrawals(this.extend(request, parameters));
        return this.parseTransactions(response, currency, since, limit);
    }

    /**
     * @method
     * @name blockchaincom#fetchWithdrawal
     * @description fetch data on a currency withdrawal via the withdrawal id
     * @see https://api.blockchain.com/v3/#getwithdrawalbyid
     * @param {string} id withdrawal id
     * @param {string} code not used by blockchaincom.fetchWithdrawal
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async virtual Task<object> fetchWithdrawal(object id, object code = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "withdrawalId", id },
        };
        object response = await this.privateGetWithdrawalsWithdrawalId(this.extend(request, parameters));
        return this.parseTransaction(response);
    }

    /**
     * @method
     * @name blockchaincom#fetchDeposits
     * @description fetch all deposits made to an account
     * @see https://api.blockchain.com/v3/#getdeposits
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for
     * @param {int} [limit] the maximum number of deposits structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDeposits(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["from"] = since;
        }
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
        }
        object response = await this.privateGetDeposits(this.extend(request, parameters));
        return this.parseTransactions(response, currency, since, limit);
    }

    /**
     * @method
     * @name blockchaincom#fetchDeposit
     * @description fetch information on a deposit
     * @see https://api.blockchain.com/v3/#getdepositbyid
     * @param {string} id deposit id
     * @param {string} code not used by blockchaincom fetchDeposit ()
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async virtual Task<object> fetchDeposit(object id, object code = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object depositId = this.safeString(parameters, "depositId", id);
        object request = new Dictionary<string, object>() {
            { "depositId", depositId },
        };
        object deposit = await this.privateGetDepositsDepositId(this.extend(request, parameters));
        return this.parseTransaction(deposit);
    }

    /**
     * @method
     * @name blockchaincom#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://api.blockchain.com/v3/#getaccounts
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object accountName = this.safeString(parameters, "account", "primary");
        parameters = this.omit(parameters, "account");
        object request = new Dictionary<string, object>() {
            { "account", accountName },
        };
        object response = await this.privateGetAccounts(this.extend(request, parameters));
        //
        //     {
        //         "primary": [
        //             {
        //                 "currency":"ETH",
        //                 "balance":0.009,
        //                 "available":0.009,
        //                 "balance_local":30.82869,
        //                 "available_local":30.82869,
        //                 "rate":3425.41
        //             },
        //             ...
        //         ]
        //     }
        //
        object balances = this.safeValue(response, accountName);
        if (isTrue(isEqual(balances, null)))
        {
            throw new ExchangeError ((string)add(add(add(this.id, " fetchBalance() could not find the \""), accountName), "\" account")) ;
        }
        object result = new Dictionary<string, object>() {
            { "info", response },
        };
        for (object i = 0; isLessThan(i, getArrayLength(balances)); postFixIncrement(ref i))
        {
            object entry = getValue(balances, i);
            object currencyId = this.safeString(entry, "currency");
            object code = this.safeCurrencyCode(currencyId);
            object account = this.account();
            ((IDictionary<string,object>)account)["free"] = this.safeString(entry, "available");
            ((IDictionary<string,object>)account)["total"] = this.safeString(entry, "balance");
            ((IDictionary<string,object>)result)[(string)code] = account;
        }
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name blockchaincom#fetchOrder
     * @description fetches information on an order made by the user
     * @see https://api.blockchain.com/v3/#getorderbyid
     * @param {string} id the order id
     * @param {string} symbol not used by blockchaincom fetchOrder
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        // note: only works with exchange-order-id
        // does not work with clientOrderId
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "orderId", id },
        };
        object response = await this.privateGetOrdersOrderId(this.extend(request, parameters));
        //
        //     {
        //         "exOrdId": 11111111,
        //         "clOrdId": "ABC",
        //         "ordType": "MARKET",
        //         "ordStatus": "FILLED",
        //         "side": "BUY",
        //         "price": 0.12345,
        //         "text": "string",
        //         "symbol": "BTC-USD",
        //         "lastShares": 0.5678,
        //         "lastPx": 3500.12,
        //         "leavesQty": 10,
        //         "cumQty": 0.123345,
        //         "avgPx": 345.33,
        //         "timestamp": 1592830770594
        //     }
        //
        return this.parseOrder(response);
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object requestPath = add("/", this.implodeParams(path, parameters));
        object url = add(getValue(getValue(this.urls, "api"), api), requestPath);
        object query = this.omit(parameters, this.extractParams(path));
        if (isTrue(isEqual(api, "public")))
        {
            if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)query).Keys))))
            {
                url = add(url, add("?", this.urlencode(query)));
            }
        } else if (isTrue(isEqual(api, "private")))
        {
            this.checkRequiredCredentials();
            headers = new Dictionary<string, object>() {
                { "X-API-Token", this.secret },
            };
            if (isTrue((isEqual(method, "GET"))))
            {
                if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)query).Keys))))
                {
                    url = add(url, add("?", this.urlencode(query)));
                }
            } else
            {
                body = this.json(query);
                ((IDictionary<string,object>)headers)["Content-Type"] = "application/json";
            }
        }
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object code, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        // {"timestamp":"2021-10-21T15:13:58.837+00:00","status":404,"error":"Not Found","message":"","path":"/orders/505050"
        if (isTrue(isEqual(response, null)))
        {
            return null;
        }
        object text = this.safeString(response, "text");
        if (isTrue(!isEqual(text, null)))
        {
            if (isTrue(isEqual(text, "Insufficient Balance")))
            {
                throw new InsufficientFunds ((string)add(add(this.id, " "), body)) ;
            }
        }
        object errorCode = this.safeString(response, "status");
        object errorMessage = this.safeString(response, "error");
        if (isTrue(!isEqual(code, null)))
        {
            object feedback = add(add(this.id, " "), this.json(response));
            this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), errorCode, feedback);
            this.throwBroadlyMatchedException(getValue(this.exceptions, "broad"), errorMessage, feedback);
        }
        return null;
    }
}
