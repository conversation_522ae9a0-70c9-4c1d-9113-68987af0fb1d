namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class bitrue : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "bitrue" },
            { "name", "Bitrue" },
            { "countries", new List<object>() {"SG"} },
            { "rateLimit", 10 },
            { "certified", false },
            { "version", "v1" },
            { "pro", true },
            { "has", new Dictionary<string, object>() {
                { "CORS", null },
                { "spot", true },
                { "margin", false },
                { "swap", true },
                { "future", false },
                { "option", false },
                { "addMargin", false },
                { "borrowCrossMargin", false },
                { "borrowIsolatedMargin", false },
                { "borrowMargin", false },
                { "cancelAllOrders", true },
                { "cancelOrder", true },
                { "closeAllPositions", false },
                { "closePosition", false },
                { "createMarketBuyOrderWithCost", true },
                { "createMarketOrderWithCost", false },
                { "createMarketSellOrderWithCost", false },
                { "createOrder", true },
                { "createOrderWithTakeProfitAndStopLoss", false },
                { "createOrderWithTakeProfitAndStopLossWs", false },
                { "createReduceOnlyOrder", true },
                { "createStopLimitOrder", true },
                { "createStopMarketOrder", true },
                { "createStopOrder", true },
                { "fetchBalance", true },
                { "fetchBidsAsks", true },
                { "fetchBorrowInterest", false },
                { "fetchBorrowRate", false },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchBorrowRates", false },
                { "fetchBorrowRatesPerSymbol", false },
                { "fetchClosedOrders", true },
                { "fetchCrossBorrowRate", false },
                { "fetchCrossBorrowRates", false },
                { "fetchCurrencies", true },
                { "fetchDepositAddress", false },
                { "fetchDeposits", true },
                { "fetchDepositsWithdrawals", false },
                { "fetchDepositWithdrawFee", "emulated" },
                { "fetchDepositWithdrawFees", true },
                { "fetchFundingHistory", false },
                { "fetchFundingInterval", false },
                { "fetchFundingIntervals", false },
                { "fetchFundingRate", false },
                { "fetchFundingRateHistory", false },
                { "fetchFundingRates", false },
                { "fetchGreeks", false },
                { "fetchIndexOHLCV", false },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchIsolatedPositions", false },
                { "fetchLeverage", false },
                { "fetchLeverages", false },
                { "fetchLeverageTiers", false },
                { "fetchLiquidations", false },
                { "fetchLongShortRatio", false },
                { "fetchLongShortRatioHistory", false },
                { "fetchMarginAdjustmentHistory", false },
                { "fetchMarginMode", false },
                { "fetchMarginModes", false },
                { "fetchMarketLeverageTiers", false },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMarkPrices", false },
                { "fetchMyLiquidations", false },
                { "fetchMySettlementHistory", false },
                { "fetchMyTrades", true },
                { "fetchOHLCV", true },
                { "fetchOpenInterest", false },
                { "fetchOpenInterestHistory", false },
                { "fetchOpenInterests", false },
                { "fetchOpenOrders", true },
                { "fetchOption", false },
                { "fetchOptionChain", false },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchOrders", false },
                { "fetchPosition", false },
                { "fetchPositionHistory", false },
                { "fetchPositionMode", false },
                { "fetchPositions", false },
                { "fetchPositionsHistory", false },
                { "fetchPositionsRisk", false },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchSettlementHistory", false },
                { "fetchStatus", true },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTime", true },
                { "fetchTrades", true },
                { "fetchTradingFee", false },
                { "fetchTradingFees", false },
                { "fetchTransactionFees", false },
                { "fetchTransactions", false },
                { "fetchTransfers", true },
                { "fetchVolatilityHistory", false },
                { "fetchWithdrawals", true },
                { "reduceMargin", false },
                { "repayCrossMargin", false },
                { "repayIsolatedMargin", false },
                { "setLeverage", true },
                { "setMargin", true },
                { "setMarginMode", false },
                { "setPositionMode", false },
                { "transfer", true },
                { "withdraw", true },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", "1m" },
                { "5m", "5m" },
                { "15m", "15m" },
                { "30m", "30m" },
                { "1h", "1H" },
                { "2h", "2H" },
                { "4h", "4H" },
                { "1d", "1D" },
                { "1w", "1W" },
            } },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://github.com/user-attachments/assets/67abe346-1273-461a-bd7c-42fa32907c8e" },
                { "api", new Dictionary<string, object>() {
                    { "spot", "https://www.bitrue.com/api" },
                    { "fapi", "https://fapi.bitrue.com/fapi" },
                    { "dapi", "https://fapi.bitrue.com/dapi" },
                    { "kline", "https://www.bitrue.com/kline-api" },
                } },
                { "www", "https://www.bitrue.com" },
                { "referral", "https://www.bitrue.com/affiliate/landing?cn=600000&inviteCode=EZWETQE" },
                { "doc", new List<object>() {"https://github.com/Bitrue-exchange/bitrue-official-api-docs", "https://www.bitrue.com/api-docs"} },
                { "fees", "https://bitrue.zendesk.com/hc/en-001/articles/4405479952537" },
            } },
            { "api", new Dictionary<string, object>() {
                { "spot", new Dictionary<string, object>() {
                    { "kline", new Dictionary<string, object>() {
                        { "public", new Dictionary<string, object>() {
                            { "get", new Dictionary<string, object>() {
                                { "public.json", 0.24 },
                                { "public{currency}.json", 0.24 },
                            } },
                        } },
                    } },
                    { "v1", new Dictionary<string, object>() {
                        { "public", new Dictionary<string, object>() {
                            { "get", new Dictionary<string, object>() {
                                { "ping", 0.24 },
                                { "time", 0.24 },
                                { "exchangeInfo", 0.24 },
                                { "depth", new Dictionary<string, object>() {
                                    { "cost", 1 },
                                    { "byLimit", new List<object>() {new List<object>() {100, 0.24}, new List<object>() {500, 1.2}, new List<object>() {1000, 2.4}} },
                                } },
                                { "trades", 0.24 },
                                { "historicalTrades", 1.2 },
                                { "aggTrades", 0.24 },
                                { "ticker/24hr", new Dictionary<string, object>() {
                                    { "cost", 0.24 },
                                    { "noSymbol", 9.6 },
                                } },
                                { "ticker/price", 0.24 },
                                { "ticker/bookTicker", 0.24 },
                                { "market/kline", 0.24 },
                            } },
                        } },
                        { "private", new Dictionary<string, object>() {
                            { "get", new Dictionary<string, object>() {
                                { "order", 5 },
                                { "openOrders", 5 },
                                { "allOrders", 25 },
                                { "account", 25 },
                                { "myTrades", 25 },
                                { "etf/net-value/{symbol}", 0.24 },
                                { "withdraw/history", 120 },
                                { "deposit/history", 120 },
                            } },
                            { "post", new Dictionary<string, object>() {
                                { "order", 5 },
                                { "withdraw/commit", 120 },
                            } },
                            { "delete", new Dictionary<string, object>() {
                                { "order", 5 },
                            } },
                        } },
                    } },
                    { "v2", new Dictionary<string, object>() {
                        { "private", new Dictionary<string, object>() {
                            { "get", new Dictionary<string, object>() {
                                { "myTrades", 1.2 },
                            } },
                        } },
                    } },
                } },
                { "fapi", new Dictionary<string, object>() {
                    { "v1", new Dictionary<string, object>() {
                        { "public", new Dictionary<string, object>() {
                            { "get", new Dictionary<string, object>() {
                                { "ping", 0.24 },
                                { "time", 0.24 },
                                { "contracts", 0.24 },
                                { "depth", 0.24 },
                                { "ticker", 0.24 },
                                { "klines", 0.24 },
                            } },
                        } },
                    } },
                    { "v2", new Dictionary<string, object>() {
                        { "private", new Dictionary<string, object>() {
                            { "get", new Dictionary<string, object>() {
                                { "myTrades", 5 },
                                { "openOrders", 5 },
                                { "order", 5 },
                                { "account", 5 },
                                { "leverageBracket", 5 },
                                { "commissionRate", 5 },
                                { "futures_transfer_history", 5 },
                                { "forceOrdersHistory", 5 },
                            } },
                            { "post", new Dictionary<string, object>() {
                                { "positionMargin", 5 },
                                { "level_edit", 5 },
                                { "cancel", 5 },
                                { "order", 25 },
                                { "allOpenOrders", 5 },
                                { "futures_transfer", 5 },
                            } },
                        } },
                    } },
                } },
                { "dapi", new Dictionary<string, object>() {
                    { "v1", new Dictionary<string, object>() {
                        { "public", new Dictionary<string, object>() {
                            { "get", new Dictionary<string, object>() {
                                { "ping", 0.24 },
                                { "time", 0.24 },
                                { "contracts", 0.24 },
                                { "depth", 0.24 },
                                { "ticker", 0.24 },
                                { "klines", 0.24 },
                            } },
                        } },
                    } },
                    { "v2", new Dictionary<string, object>() {
                        { "private", new Dictionary<string, object>() {
                            { "get", new Dictionary<string, object>() {
                                { "myTrades", 5 },
                                { "openOrders", 5 },
                                { "order", 5 },
                                { "account", 5 },
                                { "leverageBracket", 5 },
                                { "commissionRate", 5 },
                                { "futures_transfer_history", 5 },
                                { "forceOrdersHistory", 5 },
                            } },
                            { "post", new Dictionary<string, object>() {
                                { "positionMargin", 5 },
                                { "level_edit", 5 },
                                { "cancel", 5 },
                                { "order", 5 },
                                { "allOpenOrders", 5 },
                                { "futures_transfer", 5 },
                            } },
                        } },
                    } },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "feeSide", "get" },
                    { "tierBased", false },
                    { "percentage", true },
                    { "taker", this.parseNumber("0.00098") },
                    { "maker", this.parseNumber("0.00098") },
                } },
                { "future", new Dictionary<string, object>() {
                    { "trading", new Dictionary<string, object>() {
                        { "feeSide", "quote" },
                        { "tierBased", true },
                        { "percentage", true },
                        { "taker", this.parseNumber("0.000400") },
                        { "maker", this.parseNumber("0.000200") },
                        { "tiers", new Dictionary<string, object>() {
                            { "taker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.000400")}, new List<object> {this.parseNumber("250"), this.parseNumber("0.000400")}, new List<object> {this.parseNumber("2500"), this.parseNumber("0.000350")}, new List<object> {this.parseNumber("7500"), this.parseNumber("0.000320")}, new List<object> {this.parseNumber("22500"), this.parseNumber("0.000300")}, new List<object> {this.parseNumber("50000"), this.parseNumber("0.000270")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.000250")}, new List<object> {this.parseNumber("200000"), this.parseNumber("0.000220")}, new List<object> {this.parseNumber("400000"), this.parseNumber("0.000200")}, new List<object> {this.parseNumber("750000"), this.parseNumber("0.000170")}} },
                            { "maker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.000200")}, new List<object> {this.parseNumber("250"), this.parseNumber("0.000160")}, new List<object> {this.parseNumber("2500"), this.parseNumber("0.000140")}, new List<object> {this.parseNumber("7500"), this.parseNumber("0.000120")}, new List<object> {this.parseNumber("22500"), this.parseNumber("0.000100")}, new List<object> {this.parseNumber("50000"), this.parseNumber("0.000080")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.000060")}, new List<object> {this.parseNumber("200000"), this.parseNumber("0.000040")}, new List<object> {this.parseNumber("400000"), this.parseNumber("0.000020")}, new List<object> {this.parseNumber("750000"), this.parseNumber("0")}} },
                        } },
                    } },
                } },
                { "delivery", new Dictionary<string, object>() {
                    { "trading", new Dictionary<string, object>() {
                        { "feeSide", "base" },
                        { "tierBased", true },
                        { "percentage", true },
                        { "taker", this.parseNumber("0.000500") },
                        { "maker", this.parseNumber("0.000100") },
                        { "tiers", new Dictionary<string, object>() {
                            { "taker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.000500")}, new List<object> {this.parseNumber("250"), this.parseNumber("0.000450")}, new List<object> {this.parseNumber("2500"), this.parseNumber("0.000400")}, new List<object> {this.parseNumber("7500"), this.parseNumber("0.000300")}, new List<object> {this.parseNumber("22500"), this.parseNumber("0.000250")}, new List<object> {this.parseNumber("50000"), this.parseNumber("0.000240")}, new List<object> {this.parseNumber("100000"), this.parseNumber("0.000240")}, new List<object> {this.parseNumber("200000"), this.parseNumber("0.000240")}, new List<object> {this.parseNumber("400000"), this.parseNumber("0.000240")}, new List<object> {this.parseNumber("750000"), this.parseNumber("0.000240")}} },
                            { "maker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.000100")}, new List<object> {this.parseNumber("250"), this.parseNumber("0.000080")}, new List<object> {this.parseNumber("2500"), this.parseNumber("0.000050")}, new List<object> {this.parseNumber("7500"), this.parseNumber("0.0000030")}, new List<object> {this.parseNumber("22500"), this.parseNumber("0")}, new List<object> {this.parseNumber("50000"), this.parseNumber("-0.000050")}, new List<object> {this.parseNumber("100000"), this.parseNumber("-0.000060")}, new List<object> {this.parseNumber("200000"), this.parseNumber("-0.000070")}, new List<object> {this.parseNumber("400000"), this.parseNumber("-0.000080")}, new List<object> {this.parseNumber("750000"), this.parseNumber("-0.000090")}} },
                        } },
                    } },
                } },
            } },
            { "options", new Dictionary<string, object>() {
                { "createMarketBuyOrderRequiresPrice", true },
                { "fetchMarkets", new Dictionary<string, object>() {
                    { "types", new List<object>() {"spot", "linear", "inverse"} },
                } },
                { "fetchMyTradesMethod", "v2PrivateGetMyTrades" },
                { "hasAlreadyAuthenticatedSuccessfully", false },
                { "currencyToPrecisionRoundingMode", TRUNCATE },
                { "recvWindow", multiply(5, 1000) },
                { "timeDifference", 0 },
                { "adjustForTimeDifference", false },
                { "parseOrderToPrecision", false },
                { "newOrderRespType", new Dictionary<string, object>() {
                    { "market", "FULL" },
                    { "limit", "FULL" },
                } },
                { "networks", new Dictionary<string, object>() {
                    { "ERC20", "ETH" },
                    { "TRC20", "TRX" },
                    { "AETERNITY", "Aeternity" },
                    { "AION", "AION" },
                    { "ALGO", "Algorand" },
                    { "ASK", "ASK" },
                    { "ATOM", "ATOM" },
                    { "AVAXC", "AVAX C-Chain" },
                    { "BCH", "BCH" },
                    { "BEP2", "BEP2" },
                    { "BEP20", "BEP20" },
                    { "Bitcoin", "Bitcoin" },
                    { "BRP20", "BRP20" },
                    { "ADA", "Cardano" },
                    { "CASINOCOIN", "CasinoCoin" },
                    { "CASINOCOIN-XRPL", "CasinoCoin XRPL" },
                    { "CONTENTOS", "Contentos" },
                    { "DASH", "Dash" },
                    { "DECOIN", "Decoin" },
                    { "DFI", "DeFiChain" },
                    { "DGB", "DGB" },
                    { "DIVI", "Divi" },
                    { "DOGE", "dogecoin" },
                    { "EOS", "EOS" },
                    { "ETC", "ETC" },
                    { "FILECOIN", "Filecoin" },
                    { "FREETON", "FREETON" },
                    { "HBAR", "HBAR" },
                    { "HEDERA", "Hedera Hashgraph" },
                    { "HRC20", "HRC20" },
                    { "ICON", "ICON" },
                    { "ICP", "ICP" },
                    { "IGNIS", "Ignis" },
                    { "INTERNETCOMPUTER", "Internet Computer" },
                    { "IOTA", "IOTA" },
                    { "KAVA", "KAVA" },
                    { "KSM", "KSM" },
                    { "LTC", "LiteCoin" },
                    { "LUNA", "Luna" },
                    { "MATIC", "MATIC" },
                    { "MOBILECOIN", "Mobile Coin" },
                    { "MONACOIN", "MonaCoin" },
                    { "XMR", "Monero" },
                    { "NEM", "NEM" },
                    { "NEP5", "NEP5" },
                    { "OMNI", "OMNI" },
                    { "PAC", "PAC" },
                    { "DOT", "Polkadot" },
                    { "RAVEN", "Ravencoin" },
                    { "SAFEX", "Safex" },
                    { "SOL", "SOLANA" },
                    { "SGB", "Songbird" },
                    { "XML", "Stellar Lumens" },
                    { "XYM", "Symbol" },
                    { "XTZ", "Tezos" },
                    { "theta", "theta" },
                    { "THETA", "THETA" },
                    { "VECHAIN", "VeChain" },
                    { "WANCHAIN", "Wanchain" },
                    { "XINFIN", "XinFin Network" },
                    { "XRP", "XRP" },
                    { "XRPL", "XRPL" },
                    { "ZIL", "ZIL" },
                } },
                { "defaultType", "spot" },
                { "timeframes", new Dictionary<string, object>() {
                    { "spot", new Dictionary<string, object>() {
                        { "1m", "1m" },
                        { "5m", "5m" },
                        { "15m", "15m" },
                        { "30m", "30m" },
                        { "1h", "1H" },
                        { "2h", "2H" },
                        { "4h", "4H" },
                        { "12h", "12H" },
                        { "1d", "1D" },
                        { "1w", "1W" },
                    } },
                    { "future", new Dictionary<string, object>() {
                        { "1m", "1min" },
                        { "5m", "5min" },
                        { "15m", "15min" },
                        { "30m", "30min" },
                        { "1h", "1h" },
                        { "1d", "1day" },
                        { "1w", "1week" },
                        { "1M", "1month" },
                    } },
                } },
                { "accountsByType", new Dictionary<string, object>() {
                    { "spot", "wallet" },
                    { "future", "contract" },
                    { "swap", "contract" },
                    { "funding", "wallet" },
                    { "fund", "wallet" },
                    { "contract", "contract" },
                } },
            } },
            { "commonCurrencies", new Dictionary<string, object>() {
                { "MIM", "MIM Swarm" },
            } },
            { "precisionMode", TICK_SIZE },
            { "features", new Dictionary<string, object>() {
                { "default", new Dictionary<string, object>() {
                    { "sandbox", false },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "triggerPrice", true },
                        { "triggerPriceType", null },
                        { "triggerDirection", null },
                        { "stopLossPrice", false },
                        { "takeProfitPrice", false },
                        { "attachedStopLossTakeProfit", null },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", true },
                            { "PO", true },
                            { "GTD", false },
                        } },
                        { "hedged", false },
                        { "trailing", false },
                        { "leverage", false },
                        { "marketBuyRequiresPrice", true },
                        { "marketBuyByCost", true },
                        { "selfTradePrevention", false },
                        { "iceberg", true },
                    } },
                    { "createOrders", null },
                    { "fetchMyTrades", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", 100000 },
                        { "untilDays", 100000 },
                        { "symbolRequired", true },
                    } },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", null },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOrders", null },
                    { "fetchClosedOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", 90 },
                        { "daysBackCanceled", 1 },
                        { "untilDays", 90 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 1440 },
                    } },
                } },
                { "spot", new Dictionary<string, object>() {
                    { "extends", "default" },
                } },
                { "forDerivatives", new Dictionary<string, object>() {
                    { "extends", "default" },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", true },
                        { "leverage", true },
                        { "marketBuyRequiresPrice", false },
                        { "marketBuyByCost", false },
                    } },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 300 },
                    } },
                    { "fetchClosedOrders", null },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", new Dictionary<string, object>() {
                        { "extends", "forDerivatives" },
                    } },
                    { "inverse", new Dictionary<string, object>() {
                        { "extends", "forDerivatives" },
                    } },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
            } },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "System is under maintenance.", typeof(OnMaintenance) },
                    { "System abnormality", typeof(ExchangeError) },
                    { "You are not authorized to execute this request.", typeof(PermissionDenied) },
                    { "API key does not exist", typeof(AuthenticationError) },
                    { "Order would trigger immediately.", typeof(OrderImmediatelyFillable) },
                    { "Stop price would trigger immediately.", typeof(OrderImmediatelyFillable) },
                    { "Order would immediately match and take.", typeof(OrderImmediatelyFillable) },
                    { "Account has insufficient balance for requested action.", typeof(InsufficientFunds) },
                    { "Rest API trading is not enabled.", typeof(ExchangeNotAvailable) },
                    { "You don't have permission.", typeof(PermissionDenied) },
                    { "Market is closed.", typeof(ExchangeNotAvailable) },
                    { "Too many requests. Please try again later.", typeof(DDoSProtection) },
                    { "-1000", typeof(ExchangeNotAvailable) },
                    { "-1001", typeof(ExchangeNotAvailable) },
                    { "-1002", typeof(AuthenticationError) },
                    { "-1003", typeof(RateLimitExceeded) },
                    { "-1013", typeof(InvalidOrder) },
                    { "-1015", typeof(RateLimitExceeded) },
                    { "-1016", typeof(ExchangeNotAvailable) },
                    { "-1020", typeof(BadRequest) },
                    { "-1021", typeof(InvalidNonce) },
                    { "-1022", typeof(AuthenticationError) },
                    { "-1100", typeof(BadRequest) },
                    { "-1101", typeof(BadRequest) },
                    { "-1102", typeof(BadRequest) },
                    { "-1103", typeof(BadRequest) },
                    { "-1104", typeof(BadRequest) },
                    { "-1105", typeof(BadRequest) },
                    { "-1106", typeof(BadRequest) },
                    { "-1111", typeof(BadRequest) },
                    { "-1112", typeof(InvalidOrder) },
                    { "-1114", typeof(BadRequest) },
                    { "-1115", typeof(BadRequest) },
                    { "-1116", typeof(BadRequest) },
                    { "-1117", typeof(BadRequest) },
                    { "-1166", typeof(InvalidOrder) },
                    { "-1118", typeof(BadRequest) },
                    { "-1119", typeof(BadRequest) },
                    { "-1120", typeof(BadRequest) },
                    { "-1121", typeof(BadSymbol) },
                    { "-1125", typeof(AuthenticationError) },
                    { "-1127", typeof(BadRequest) },
                    { "-1128", typeof(BadRequest) },
                    { "-1130", typeof(BadRequest) },
                    { "-1131", typeof(BadRequest) },
                    { "-1160", typeof(InvalidOrder) },
                    { "-1156", typeof(InvalidOrder) },
                    { "-2008", typeof(AuthenticationError) },
                    { "-2010", typeof(ExchangeError) },
                    { "-2011", typeof(OrderNotFound) },
                    { "-2013", typeof(OrderNotFound) },
                    { "-2014", typeof(AuthenticationError) },
                    { "-2015", typeof(AuthenticationError) },
                    { "-2017", typeof(InsufficientFunds) },
                    { "-2019", typeof(InsufficientFunds) },
                    { "-3005", typeof(InsufficientFunds) },
                    { "-3006", typeof(InsufficientFunds) },
                    { "-3008", typeof(InsufficientFunds) },
                    { "-3010", typeof(ExchangeError) },
                    { "-3015", typeof(ExchangeError) },
                    { "-3022", typeof(AccountSuspended) },
                    { "-4028", typeof(BadRequest) },
                    { "-3020", typeof(InsufficientFunds) },
                    { "-3041", typeof(InsufficientFunds) },
                    { "-5013", typeof(InsufficientFunds) },
                    { "-11008", typeof(InsufficientFunds) },
                    { "-4051", typeof(InsufficientFunds) },
                } },
                { "broad", new Dictionary<string, object>() {
                    { "Insufficient account balance", typeof(InsufficientFunds) },
                    { "has no operation privilege", typeof(PermissionDenied) },
                    { "MAX_POSITION", typeof(InvalidOrder) },
                } },
            } },
        });
    }

    public override object nonce()
    {
        return subtract(this.milliseconds(), getValue(this.options, "timeDifference"));
    }

    /**
     * @method
     * @name bitrue#fetchStatus
     * @description the latest known information on the availability of the exchange API
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#test-connectivity
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [status structure]{@link https://docs.ccxt.com/#/?id=exchange-status-structure}
     */
    public async override Task<object> fetchStatus(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.spotV1PublicGetPing(parameters);
        //
        // empty means working status.
        //
        //     {}
        //
        object keys = new List<object>(((IDictionary<string,object>)response).Keys);
        object keysLength = getArrayLength(keys);
        object formattedStatus = ((bool) isTrue(keysLength)) ? "maintenance" : "ok";
        return new Dictionary<string, object>() {
            { "status", formattedStatus },
            { "updated", null },
            { "eta", null },
            { "url", null },
            { "info", response },
        };
    }

    /**
     * @method
     * @name bitrue#fetchTime
     * @description fetches the current integer timestamp in milliseconds from the exchange server
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#check-server-time
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int} the current integer timestamp in milliseconds from the exchange server
     */
    public async override Task<object> fetchTime(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.spotV1PublicGetTime(parameters);
        //
        //     {
        //         "serverTime":1635467280514
        //     }
        //
        return this.safeInteger(response, "serverTime");
    }

    /**
     * @method
     * @name bitrue#fetchCurrencies
     * @description fetches all available currencies on an exchange
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an associative dictionary of currencies
     */
    public async override Task<object> fetchCurrencies(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.spotV1PublicGetExchangeInfo(parameters);
        //
        //     {
        //         "timezone":"CTT",
        //         "serverTime":1635464889117,
        //         "rateLimits":[
        //             {"rateLimitType":"REQUESTS_WEIGHT","interval":"MINUTES","limit":6000},
        //             {"rateLimitType":"ORDERS","interval":"SECONDS","limit":150},
        //             {"rateLimitType":"ORDERS","interval":"DAYS","limit":288000},
        //         ],
        //         "exchangeFilters":[],
        //         "symbols":[
        //             {
        //                 "symbol":"SHABTC",
        //                 "status":"TRADING",
        //                 "baseAsset":"sha",
        //                 "baseAssetPrecision":0,
        //                 "quoteAsset":"btc",
        //                 "quotePrecision":10,
        //                 "orderTypes":["MARKET","LIMIT"],
        //                 "icebergAllowed":false,
        //                 "filters":[
        //                     {"filterType":"PRICE_FILTER","minPrice":"0.00000001349","maxPrice":"0.00000017537","priceScale":10},
        //                     {"filterType":"LOT_SIZE","minQty":"1.0","minVal":"0.00020","maxQty":"1000000000","volumeScale":0},
        //                 ],
        //                 "defaultPrice":"0.0000006100",
        //             },
        //         ],
        //         "coins":[
        //           {
        //               "coin": "near",
        //               "coinFulName": "NEAR Protocol",
        //               "chains": [ "BEP20", ],
        //               "chainDetail": [
        //                 {
        //                     "chain": "BEP20",
        //                     "enableWithdraw": true,
        //                     "enableDeposit": true,
        //                     "withdrawFee": "0.2000",
        //                     "minWithdraw": "5.0000",
        //                     "maxWithdraw": "1000000000000000.0000",
        //                 },
        //               ],
        //           },
        //         ],
        //     }
        //
        object result = new Dictionary<string, object>() {};
        object coins = this.safeList(response, "coins", new List<object>() {});
        for (object i = 0; isLessThan(i, getArrayLength(coins)); postFixIncrement(ref i))
        {
            object currency = getValue(coins, i);
            object id = this.safeString(currency, "coin");
            object name = this.safeString(currency, "coinFulName");
            object code = this.safeCurrencyCode(id);
            object networkDetails = this.safeList(currency, "chainDetail", new List<object>() {});
            object networks = new Dictionary<string, object>() {};
            for (object j = 0; isLessThan(j, getArrayLength(networkDetails)); postFixIncrement(ref j))
            {
                object entry = getValue(networkDetails, j);
                object networkId = this.safeString(entry, "chain");
                object network = this.networkIdToCode(networkId, code);
                ((IDictionary<string,object>)networks)[(string)network] = new Dictionary<string, object>() {
                    { "info", entry },
                    { "id", networkId },
                    { "network", network },
                    { "deposit", this.safeBool(entry, "enableDeposit") },
                    { "withdraw", this.safeBool(entry, "enableWithdraw") },
                    { "active", null },
                    { "fee", this.safeNumber(entry, "withdrawFee") },
                    { "precision", null },
                    { "limits", new Dictionary<string, object>() {
                        { "withdraw", new Dictionary<string, object>() {
                            { "min", this.safeNumber(entry, "minWithdraw") },
                            { "max", this.safeNumber(entry, "maxWithdraw") },
                        } },
                    } },
                };
            }
            ((IDictionary<string,object>)result)[(string)code] = this.safeCurrencyStructure(new Dictionary<string, object>() {
                { "id", id },
                { "name", name },
                { "code", code },
                { "precision", null },
                { "info", currency },
                { "active", null },
                { "deposit", null },
                { "withdraw", null },
                { "networks", networks },
                { "fee", null },
                { "fees", null },
                { "type", "crypto" },
                { "limits", new Dictionary<string, object>() {
                    { "withdraw", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                } },
            });
        }
        return result;
    }

    /**
     * @method
     * @name bitrue#fetchMarkets
     * @description retrieves data on all markets for bitrue
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#exchangeInfo_endpoint
     * @see https://www.bitrue.com/api-docs#current-open-contract
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#current-open-contract
     * @param {object} [params] extra parameters specific to the exchange api endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object promisesRaw = new List<object>() {};
        object types = null;
        object defaultTypes = new List<object>() {"spot", "linear", "inverse"};
        object fetchMarketsOptions = this.safeDict(this.options, "fetchMarkets");
        if (isTrue(!isEqual(fetchMarketsOptions, null)))
        {
            types = this.safeList(fetchMarketsOptions, "types", defaultTypes);
        } else
        {
            // for backward-compatibility
            types = this.safeList(this.options, "fetchMarkets", defaultTypes);
        }
        for (object i = 0; isLessThan(i, getArrayLength(types)); postFixIncrement(ref i))
        {
            object marketType = getValue(types, i);
            if (isTrue(isEqual(marketType, "spot")))
            {
                ((IList<object>)promisesRaw).Add(this.spotV1PublicGetExchangeInfo(parameters));
            } else if (isTrue(isEqual(marketType, "linear")))
            {
                ((IList<object>)promisesRaw).Add(this.fapiV1PublicGetContracts(parameters));
            } else if (isTrue(isEqual(marketType, "inverse")))
            {
                ((IList<object>)promisesRaw).Add(this.dapiV1PublicGetContracts(parameters));
            } else
            {
                throw new ExchangeError ((string)add(add(add(this.id, " fetchMarkets() this.options fetchMarkets \""), marketType), "\" is not a supported market type")) ;
            }
        }
        object promises = await promiseAll(promisesRaw);
        object spotMarkets = this.safeValue(this.safeValue(promises, 0), "symbols", new List<object>() {});
        object futureMarkets = this.safeValue(promises, 1);
        object deliveryMarkets = this.safeValue(promises, 2);
        object markets = spotMarkets;
        markets = this.arrayConcat(markets, futureMarkets);
        markets = this.arrayConcat(markets, deliveryMarkets);
        //
        // spot
        //
        //     {
        //         "timezone":"CTT",
        //         "serverTime":1635464889117,
        //         "rateLimits":[
        //             {"rateLimitType":"REQUESTS_WEIGHT","interval":"MINUTES","limit":6000},
        //             {"rateLimitType":"ORDERS","interval":"SECONDS","limit":150},
        //             {"rateLimitType":"ORDERS","interval":"DAYS","limit":288000},
        //         ],
        //         "exchangeFilters":[],
        //         "symbols":[
        //             {
        //                 "symbol":"SHABTC",
        //                 "status":"TRADING",
        //                 "baseAsset":"sha",
        //                 "baseAssetPrecision":0,
        //                 "quoteAsset":"btc",
        //                 "quotePrecision":10,
        //                 "orderTypes":["MARKET","LIMIT"],
        //                 "icebergAllowed":false,
        //                 "filters":[
        //                     {"filterType":"PRICE_FILTER","minPrice":"0.00000001349","maxPrice":"0.00000017537","priceScale":10},
        //                     {"filterType":"LOT_SIZE","minQty":"1.0","minVal":"0.00020","maxQty":"1000000000","volumeScale":0},
        //                 ],
        //                 "defaultPrice":"0.0000006100",
        //             },
        //         ],
        //         "coins":[
        //             {
        //                 "coin":"sbr",
        //                 "coinFulName":"Saber",
        //                 "enableWithdraw":true,
        //                 "enableDeposit":true,
        //                 "chains":["SOLANA"],
        //                 "withdrawFee":"2.0",
        //                 "minWithdraw":"5.0",
        //                 "maxWithdraw":"1000000000000000",
        //             },
        //         ],
        //     }
        //
        // swap / delivery
        //
        //     [
        //         {
        //           "symbol": "H-HT-USDT",
        //           "pricePrecision": 8,
        //           "side": 1,
        //           "maxMarketVolume": 100000,
        //           "multiplier": 6,
        //           "minOrderVolume": 1,
        //           "maxMarketMoney": 10000000,
        //           "type": "H", // E: perpetual contract, S: test contract, others are mixed contract
        //           "maxLimitVolume": 1000000,
        //           "maxValidOrder": 20,
        //           "multiplierCoin": "HT",
        //           "minOrderMoney": 0.001,
        //           "maxLimitMoney": 1000000,
        //           "status": 1
        //         }
        //     ]
        //
        if (isTrue(getValue(this.options, "adjustForTimeDifference")))
        {
            await this.loadTimeDifference();
        }
        return this.parseMarkets(markets);
    }

    public override object parseMarket(object market)
    {
        object id = this.safeString(market, "symbol");
        object lowercaseId = this.safeStringLower(market, "symbol");
        object side = this.safeInteger(market, "side"); // 1 linear, 0 inverse, undefined spot
        object type = null;
        object isLinear = null;
        object isInverse = null;
        if (isTrue(isEqual(side, null)))
        {
            type = "spot";
        } else
        {
            type = "swap";
            isLinear = (isEqual(side, 1));
            isInverse = (isEqual(side, 0));
        }
        object isContract = (!isEqual(type, "spot"));
        object baseId = this.safeString(market, "baseAsset");
        object quoteId = this.safeString(market, "quoteAsset");
        object settleId = null;
        object settle = null;
        if (isTrue(isContract))
        {
            object symbolSplit = ((string)id).Split(new [] {((string)"-")}, StringSplitOptions.None).ToList<object>();
            baseId = this.safeString(symbolSplit, 1);
            quoteId = this.safeString(symbolSplit, 2);
            if (isTrue(isLinear))
            {
                settleId = quoteId;
            } else
            {
                settleId = baseId;
            }
            settle = this.safeCurrencyCode(settleId);
        }
        object bs = this.safeCurrencyCode(baseId);
        object quote = this.safeCurrencyCode(quoteId);
        object symbol = add(add(bs, "/"), quote);
        if (isTrue(!isEqual(settle, null)))
        {
            symbol = add(symbol, add(":", settle));
        }
        object filters = this.safeList(market, "filters", new List<object>() {});
        object filtersByType = this.indexBy(filters, "filterType");
        object status = this.safeString(market, "status");
        object priceFilter = this.safeDict(filtersByType, "PRICE_FILTER", new Dictionary<string, object>() {});
        object amountFilter = this.safeDict(filtersByType, "LOT_SIZE", new Dictionary<string, object>() {});
        object defaultPricePrecision = this.safeString(market, "pricePrecision");
        object defaultAmountPrecision = this.safeString(market, "quantityPrecision");
        object pricePrecision = this.safeString(priceFilter, "priceScale", defaultPricePrecision);
        object amountPrecision = this.safeString(amountFilter, "volumeScale", defaultAmountPrecision);
        object multiplier = this.safeString(market, "multiplier");
        object maxQuantity = this.safeNumber(amountFilter, "maxQty");
        if (isTrue(isEqual(maxQuantity, null)))
        {
            maxQuantity = this.safeNumber(market, "maxValidOrder");
        }
        object minCost = this.safeNumber(amountFilter, "minVal");
        if (isTrue(isEqual(minCost, null)))
        {
            minCost = this.safeNumber(market, "minOrderMoney");
        }
        return new Dictionary<string, object>() {
            { "id", id },
            { "lowercaseId", lowercaseId },
            { "symbol", symbol },
            { "base", bs },
            { "quote", quote },
            { "settle", settle },
            { "baseId", baseId },
            { "quoteId", quoteId },
            { "settleId", settleId },
            { "type", type },
            { "spot", (isEqual(type, "spot")) },
            { "margin", false },
            { "swap", isContract },
            { "future", false },
            { "option", false },
            { "active", (isEqual(status, "TRADING")) },
            { "contract", isContract },
            { "linear", isLinear },
            { "inverse", isInverse },
            { "contractSize", this.parseNumber(Precise.stringAbs(multiplier)) },
            { "expiry", null },
            { "expiryDatetime", null },
            { "strike", null },
            { "optionType", null },
            { "precision", new Dictionary<string, object>() {
                { "amount", this.parseNumber(this.parsePrecision(amountPrecision)) },
                { "price", this.parseNumber(this.parsePrecision(pricePrecision)) },
            } },
            { "limits", new Dictionary<string, object>() {
                { "leverage", new Dictionary<string, object>() {
                    { "min", null },
                    { "max", null },
                } },
                { "amount", new Dictionary<string, object>() {
                    { "min", this.safeNumber(amountFilter, "minQty") },
                    { "max", maxQuantity },
                } },
                { "price", new Dictionary<string, object>() {
                    { "min", this.safeNumber(priceFilter, "minPrice") },
                    { "max", this.safeNumber(priceFilter, "maxPrice") },
                } },
                { "cost", new Dictionary<string, object>() {
                    { "min", minCost },
                    { "max", null },
                } },
            } },
            { "created", null },
            { "info", market },
        };
    }

    public override object parseBalance(object response)
    {
        //
        // spot
        //
        //     {
        //         "makerCommission":0,
        //         "takerCommission":0,
        //         "buyerCommission":0,
        //         "sellerCommission":0,
        //         "updateTime":null,
        //         "balances":[
        //             {"asset":"sbr","free":"0","locked":"0"},
        //             {"asset":"ksm","free":"0","locked":"0"},
        //             {"asset":"neo3s","free":"0","locked":"0"},
        //         ],
        //         "canTrade":false,
        //         "canWithdraw":false,
        //         "canDeposit":false
        //     }
        //
        // swap
        //
        //     {
        //         "account":[
        //             {
        //                 "marginCoin":"USDT",
        //                 "coinPrecious":4,
        //                 "accountNormal":1010.****************,
        //                 "accountLock":2.****************,
        //                 "partPositionNormal":0,
        //                 "totalPositionNormal":0,
        //                 "achievedAmount":0,
        //                 "unrealizedAmount":0,
        //                 "totalMarginRate":0,
        //                 "totalEquity":1010.****************,
        //                 "partEquity":0,
        //                 "totalCost":0,
        //                 "sumMarginRate":0,
        //                 "sumOpenRealizedAmount":0,
        //                 "canUseTrialFund":0,
        //                 "sumMaintenanceMargin":null,
        //                 "futureModel":null,
        //                 "positionVos":[]
        //             }
        //         ]
        //     }
        //
        object result = new Dictionary<string, object>() {
            { "info", response },
        };
        object timestamp = this.safeInteger(response, "updateTime");
        object balances = this.safeValue2(response, "balances", "account", new List<object>() {});
        for (object i = 0; isLessThan(i, getArrayLength(balances)); postFixIncrement(ref i))
        {
            object balance = getValue(balances, i);
            object currencyId = this.safeString2(balance, "asset", "marginCoin");
            object code = this.safeCurrencyCode(currencyId);
            object account = this.account();
            ((IDictionary<string,object>)account)["free"] = this.safeString2(balance, "free", "accountNormal");
            ((IDictionary<string,object>)account)["used"] = this.safeString2(balance, "locked", "accountLock");
            ((IDictionary<string,object>)result)[(string)code] = account;
        }
        ((IDictionary<string,object>)result)["timestamp"] = timestamp;
        ((IDictionary<string,object>)result)["datetime"] = this.iso8601(timestamp);
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name bitrue#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#account-information-user_data
     * @see https://www.bitrue.com/api-docs#account-information-v2-user_data-hmac-sha256
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#account-information-v2-user_data-hmac-sha256
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.type] 'future', 'delivery', 'spot', 'swap'
     * @param {string} [params.subType] 'linear', 'inverse'
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object type = null;
        var typeparametersVariable = this.handleMarketTypeAndParams("fetchBalance", null, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        object subType = null;
        var subTypeparametersVariable = this.handleSubTypeAndParams("fetchBalance", null, parameters);
        subType = ((IList<object>)subTypeparametersVariable)[0];
        parameters = ((IList<object>)subTypeparametersVariable)[1];
        object response = null;
        object result = null;
        if (isTrue(isEqual(type, "swap")))
        {
            if (isTrue(isTrue(!isEqual(subType, null)) && isTrue(isEqual(subType, "inverse"))))
            {
                response = await this.dapiV2PrivateGetAccount(parameters);
                result = this.safeDict(response, "data", new Dictionary<string, object>() {});
            } else
            {
                response = await this.fapiV2PrivateGetAccount(parameters);
                result = this.safeDict(response, "data", new Dictionary<string, object>() {});
            }
        } else
        {
            response = await this.spotV1PrivateGetAccount(parameters);
            result = response;
        }
        return this.parseBalance(result);
    }

    /**
     * @method
     * @name bitrue#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#order-book
     * @see https://www.bitrue.com/api-docs#order-book
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#order-book
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object response = null;
        if (isTrue(getValue(market, "swap")))
        {
            object request = new Dictionary<string, object>() {
                { "contractName", getValue(market, "id") },
            };
            if (isTrue(!isEqual(limit, null)))
            {
                if (isTrue(isGreaterThan(limit, 100)))
                {
                    limit = 100;
                }
                ((IDictionary<string,object>)request)["limit"] = limit; // default 100, max 100, see https://www.bitrue.com/api-docs#order-book
            }
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV1PublicGetDepth(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV1PublicGetDepth(this.extend(request, parameters));
            }
        } else if (isTrue(getValue(market, "spot")))
        {
            object request = new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
            };
            if (isTrue(!isEqual(limit, null)))
            {
                if (isTrue(isGreaterThan(limit, 1000)))
                {
                    limit = 1000;
                }
                ((IDictionary<string,object>)request)["limit"] = limit; // default 100, max 1000, see https://github.com/Bitrue-exchange/bitrue-official-api-docs#order-book
            }
            response = await this.spotV1PublicGetDepth(this.extend(request, parameters));
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchOrderBook only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //     {
        //         "lastUpdateId":1635474910177,
        //         "bids":[
        //             ["61436.84","0.05",[]],
        //             ["61435.77","0.0124",[]],
        //             ["61434.88","0.012",[]],
        //         ],
        //         "asks":[
        //             ["61452.46","0.0001",[]],
        //             ["61452.47","0.0597",[]],
        //             ["61452.76","0.0713",[]],
        //         ]
        //     }
        //
        // swap
        //
        //     {
        //         "asks": [[34916.5, 2582], [34916.6, 2193], [34916.7, 2629], [34916.8, 3478], [34916.9, 2718]],
        //         "bids": [[34916.4, 92065], [34916.3, 25703], [34916.2, 37259], [34916.1, 26446], [34916, 44456]],
        //         "time": 1699338305000
        //     }
        //
        object timestamp = this.safeInteger2(response, "time", "lastUpdateId");
        object orderbook = this.parseOrderBook(response, symbol, timestamp);
        ((IDictionary<string,object>)orderbook)["nonce"] = this.safeInteger(response, "lastUpdateId");
        return orderbook;
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        // fetchBidsAsks
        //
        //     {
        //         "symbol": "LTCBTC",
        //         "bidPrice": "4.00000000",
        //         "bidQty": "431.00000000",
        //         "askPrice": "4.00000200",
        //         "askQty": "9.00000000"
        //     }
        //
        // fetchTicker
        //
        //     {
        //         "symbol": "BNBBTC",
        //         "priceChange": "0.000248",
        //         "priceChangePercent": "3.5500",
        //         "weightedAvgPrice": null,
        //         "prevClosePrice": null,
        //         "lastPrice": "0.007226",
        //         "lastQty": null,
        //         "bidPrice": "0.007208",
        //         "askPrice": "0.007240",
        //         "openPrice": "0.006978",
        //         "highPrice": "0.007295",
        //         "lowPrice": "0.006935",
        //         "volume": "11749.86",
        //         "quoteVolume": "84.1066211",
        //         "openTime": 0,
        //         "closeTime": 0,
        //         "firstId": 0,
        //         "lastId": 0,
        //         "count": 0
        //     }
        //
        object symbol = this.safeSymbol(null, market);
        object last = this.safeString2(ticker, "lastPrice", "last");
        object timestamp = this.safeInteger(ticker, "time");
        object percentage = null;
        if (isTrue(getValue(market, "swap")))
        {
            percentage = Precise.stringMul(this.safeString(ticker, "rose"), "100");
        } else
        {
            percentage = this.safeString(ticker, "priceChangePercent");
        }
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", symbol },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "high", this.safeString2(ticker, "highPrice", "high") },
            { "low", this.safeString2(ticker, "lowPrice", "low") },
            { "bid", this.safeString2(ticker, "bidPrice", "buy") },
            { "bidVolume", this.safeString(ticker, "bidQty") },
            { "ask", this.safeString2(ticker, "askPrice", "sell") },
            { "askVolume", this.safeString(ticker, "askQty") },
            { "vwap", this.safeString(ticker, "weightedAvgPrice") },
            { "open", this.safeString(ticker, "openPrice") },
            { "close", last },
            { "last", last },
            { "previousClose", null },
            { "change", this.safeString(ticker, "priceChange") },
            { "percentage", percentage },
            { "average", null },
            { "baseVolume", this.safeString2(ticker, "volume", "vol") },
            { "quoteVolume", this.safeString(ticker, "quoteVolume") },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name bitrue#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#24hr-ticker-price-change-statistics
     * @see https://www.bitrue.com/api-docs#ticker
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#ticker
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object response = null;
        object data = null;
        if (isTrue(getValue(market, "swap")))
        {
            object request = new Dictionary<string, object>() {
                { "contractName", getValue(market, "id") },
            };
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV1PublicGetTicker(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV1PublicGetTicker(this.extend(request, parameters));
            }
            data = response;
        } else if (isTrue(getValue(market, "spot")))
        {
            object request = new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
            };
            response = await this.spotV1PublicGetTicker24hr(this.extend(request, parameters));
            data = this.safeDict(response, 0, new Dictionary<string, object>() {});
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchTicker only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //     [{
        //         symbol: 'BTCUSDT',
        //         priceChange: '105.20',
        //         priceChangePercent: '0.3000',
        //         weightedAvgPrice: null,
        //         prevClosePrice: null,
        //         lastPrice: '34905.21',
        //         lastQty: null,
        //         bidPrice: '34905.21',
        //         askPrice: '34905.22',
        //         openPrice: '34800.01',
        //         highPrice: '35276.33',
        //         lowPrice: '34787.51',
        //         volume: '12549.6481',
        //         quoteVolume: '439390492.917',
        //         openTime: '0',
        //         closeTime: '0',
        //         firstId: '0',
        //         lastId: '0',
        //         count: '0'
        //     }]
        //
        // swap
        //
        //     {
        //         "high": "35296",
        //         "vol": "779308354",
        //         "last": "34884.1",
        //         "low": "34806.7",
        //         "buy": 34883.9,
        //         "sell": 34884,
        //         "rose": "-0.0027957315",
        //         "time": 1699348013000
        //     }
        //
        return this.parseTicker(data, market);
    }

    /**
     * @method
     * @name bitrue#fetchOHLCV
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @see https://www.bitrue.com/api_docs_includes_file/spot/index.html#kline-data
     * @see https://www.bitrue.com/api_docs_includes_file/futures/index.html#kline-candlestick-data
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch transfers for
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object timeframes = this.safeDict(this.options, "timeframes", new Dictionary<string, object>() {});
        object response = null;
        object data = null;
        if (isTrue(getValue(market, "swap")))
        {
            object timeframesFuture = this.safeDict(timeframes, "future", new Dictionary<string, object>() {});
            object request = new Dictionary<string, object>() {
                { "contractName", getValue(market, "id") },
                { "interval", this.safeString(timeframesFuture, timeframe, "1min") },
            };
            if (isTrue(!isEqual(limit, null)))
            {
                ((IDictionary<string,object>)request)["limit"] = limit;
            }
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV1PublicGetKlines(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV1PublicGetKlines(this.extend(request, parameters));
            }
            data = response;
        } else if (isTrue(getValue(market, "spot")))
        {
            object timeframesSpot = this.safeDict(timeframes, "spot", new Dictionary<string, object>() {});
            object request = new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
                { "scale", this.safeString(timeframesSpot, timeframe, "1m") },
            };
            if (isTrue(!isEqual(limit, null)))
            {
                ((IDictionary<string,object>)request)["limit"] = limit;
            }
            object until = this.safeInteger(parameters, "until");
            if (isTrue(!isEqual(until, null)))
            {
                parameters = this.omit(parameters, "until");
                ((IDictionary<string,object>)request)["fromIdx"] = until;
            }
            response = await this.spotV1PublicGetMarketKline(this.extend(request, parameters));
            data = this.safeList(response, "data", new List<object>() {});
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchOHLCV only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //       {
        //           "symbol":"BTCUSDT",
        //           "scale":"KLINE_1MIN",
        //           "data":[
        //                {
        //                   "i":"1660825020",
        //                   "a":"93458.778",
        //                   "v":"3.9774",
        //                   "c":"23494.99",
        //                   "h":"23509.63",
        //                   "l":"23491.93",
        //                   "o":"23508.34"
        //                }
        //           ]
        //       }
        //
        // swap
        //
        //     [
        //         {
        //           "high": "35360.7",
        //           "vol": "110288",
        //           "low": "35347.9",
        //           "idx": 1699411680000,
        //           "close": "35347.9",
        //           "open": "35349.4"
        //         }
        //     ]
        //
        return this.parseOHLCVs(data, market, timeframe, since, limit);
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        //
        // spot
        //
        //      {
        //         "i":"1660825020",
        //         "a":"93458.778",
        //         "v":"3.9774",
        //         "c":"23494.99",
        //         "h":"23509.63",
        //         "l":"23491.93",
        //         "o":"23508.34"
        //      }
        //
        // swap
        //
        //     {
        //         "high": "35360.7",
        //         "vol": "110288",
        //         "low": "35347.9",
        //         "idx": 1699411680000,
        //         "close": "35347.9",
        //         "open": "35349.4"
        //     }
        //
        object timestamp = this.safeTimestamp(ohlcv, "i");
        if (isTrue(isEqual(timestamp, null)))
        {
            timestamp = this.safeInteger(ohlcv, "idx");
        }
        return new List<object>() {timestamp, this.safeNumber2(ohlcv, "o", "open"), this.safeNumber2(ohlcv, "h", "high"), this.safeNumber2(ohlcv, "l", "low"), this.safeNumber2(ohlcv, "c", "close"), this.safeNumber2(ohlcv, "v", "vol")};
    }

    /**
     * @method
     * @name bitrue#fetchBidsAsks
     * @description fetches the bid and ask price and volume for multiple markets
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#symbol-order-book-ticker
     * @see https://www.bitrue.com/api-docs#ticker
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#ticker
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the bids and asks for, all markets are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchBidsAsks(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols, null, false);
        object first = this.safeString(symbols, 0);
        object market = this.market(first);
        object response = null;
        if (isTrue(getValue(market, "swap")))
        {
            object request = new Dictionary<string, object>() {
                { "contractName", getValue(market, "id") },
            };
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV1PublicGetTicker(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV1PublicGetTicker(this.extend(request, parameters));
            }
        } else if (isTrue(getValue(market, "spot")))
        {
            object request = new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
            };
            response = await this.spotV1PublicGetTickerBookTicker(this.extend(request, parameters));
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchBidsAsks only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //     {
        //         "symbol": "LTCBTC",
        //         "bidPrice": "4.00000000",
        //         "bidQty": "431.00000000",
        //         "askPrice": "4.00000200",
        //         "askQty": "9.00000000"
        //     }
        //
        // swap
        //
        //     {
        //         "high": "35296",
        //         "vol": "779308354",
        //         "last": "34884.1",
        //         "low": "34806.7",
        //         "buy": 34883.9,
        //         "sell": 34884,
        //         "rose": "-0.0027957315",
        //         "time": 1699348013000
        //     }
        //
        object data = new Dictionary<string, object>() {};
        ((IDictionary<string,object>)data)[(string)getValue(market, "id")] = response;
        return this.parseTickers(data, symbols);
    }

    /**
     * @method
     * @name bitrue#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#24hr-ticker-price-change-statistics
     * @see https://www.bitrue.com/api-docs#ticker
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#ticker
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        object response = null;
        object data = null;
        object request = new Dictionary<string, object>() {};
        object type = null;
        if (isTrue(!isEqual(symbols, null)))
        {
            object first = this.safeString(symbols, 0);
            object market = this.market(first);
            if (isTrue(getValue(market, "swap")))
            {
                throw new NotSupported ((string)add(this.id, " fetchTickers does not support swap markets, please use fetchTicker instead")) ;
            } else if (isTrue(getValue(market, "spot")))
            {
                response = await this.spotV1PublicGetTicker24hr(this.extend(request, parameters));
                data = response;
            } else
            {
                throw new NotSupported ((string)add(this.id, " fetchTickers only support spot & swap markets")) ;
            }
        } else
        {
            var typeparametersVariable = this.handleMarketTypeAndParams("fetchTickers", null, parameters);
            type = ((IList<object>)typeparametersVariable)[0];
            parameters = ((IList<object>)typeparametersVariable)[1];
            if (isTrue(!isEqual(type, "spot")))
            {
                throw new NotSupported ((string)add(this.id, " fetchTickers only support spot when symbols are not proved")) ;
            }
            response = await this.spotV1PublicGetTicker24hr(this.extend(request, parameters));
            data = response;
        }
        //
        // spot
        //
        //     [{
        //         symbol: 'BTCUSDT',
        //         priceChange: '105.20',
        //         priceChangePercent: '0.3000',
        //         weightedAvgPrice: null,
        //         prevClosePrice: null,
        //         lastPrice: '34905.21',
        //         lastQty: null,
        //         bidPrice: '34905.21',
        //         askPrice: '34905.22',
        //         openPrice: '34800.01',
        //         highPrice: '35276.33',
        //         lowPrice: '34787.51',
        //         volume: '12549.6481',
        //         quoteVolume: '439390492.917',
        //         openTime: '0',
        //         closeTime: '0',
        //         firstId: '0',
        //         lastId: '0',
        //         count: '0'
        //     }]
        //
        // swap
        //
        //     {
        //         "high": "35296",
        //         "vol": "779308354",
        //         "last": "34884.1",
        //         "low": "34806.7",
        //         "buy": 34883.9,
        //         "sell": 34884,
        //         "rose": "-0.0027957315",
        //         "time": 1699348013000
        //     }
        //
        // the exchange returns market ids with an underscore from the tickers endpoint
        // the market ids do not have an underscore, so it has to be removed
        // https://github.com/ccxt/ccxt/issues/13856
        object tickers = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(data)); postFixIncrement(ref i))
        {
            object ticker = this.safeDict(data, i, new Dictionary<string, object>() {});
            object market = this.safeMarket(this.safeString(ticker, "symbol"));
            ((IDictionary<string,object>)tickers)[(string)getValue(market, "id")] = ticker;
        }
        return this.parseTickers(tickers, symbols);
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        // fetchTrades
        //
        //     {
        //         "id": 28457,
        //         "price": "4.00000100",
        //         "qty": "12.00000000",
        //         "time": 1499865549590,  // Actual timestamp of trade
        //         "isBuyerMaker": true,
        //         "isBestMatch": true
        //     }
        //
        // fetchTrades - spot
        //
        //     {
        //         "symbol":"USDCUSDT",
        //         "id":20725156,
        //         "orderId":2880918576,
        //         "origClientOrderId":null,
        //         "price":"0.9996000000000000",
        //         "qty":"100.0000000000000000",
        //         "commission":null,
        //         "commissionAssert":null,
        //         "time":1635558511000,
        //         "isBuyer":false,
        //         "isMaker":false,
        //         "isBestMatch":true
        //     }
        //
        // fetchTrades - future
        //
        //     {
        //         "tradeId":12,
        //         "price":0.9,
        //         "qty":1,
        //         "amount":9,
        //         "contractName":"E-SAND-USDT",
        //         "side":"BUY",
        //         "fee":"0.0018",
        //         "bidId":1558124009467904992,
        //         "askId":1558124043827644908,
        //         "bidUserId":10294,
        //         "askUserId":10467,
        //         "isBuyer":true,
        //         "isMaker":true,
        //         "ctime":*************
        //     }
        //
        object timestamp = this.safeInteger2(trade, "ctime", "time");
        object priceString = this.safeString(trade, "price");
        object amountString = this.safeString(trade, "qty");
        object marketId = this.safeString2(trade, "symbol", "contractName");
        object symbol = this.safeSymbol(marketId, market);
        object orderId = this.safeString(trade, "orderId");
        object id = this.safeString2(trade, "id", "tradeId");
        object side = null;
        object buyerMaker = this.safeBool(trade, "isBuyerMaker"); // ignore "m" until Bitrue fixes api
        object isBuyer = this.safeBool(trade, "isBuyer");
        if (isTrue(!isEqual(buyerMaker, null)))
        {
            side = ((bool) isTrue(buyerMaker)) ? "sell" : "buy";
        }
        if (isTrue(!isEqual(isBuyer, null)))
        {
            side = ((bool) isTrue(isBuyer)) ? "buy" : "sell"; // this is a true side
        }
        object fee = null;
        if (isTrue(inOp(trade, "commission")))
        {
            fee = new Dictionary<string, object>() {
                { "cost", this.safeString2(trade, "commission", "fee") },
                { "currency", this.safeCurrencyCode(this.safeString(trade, "commissionAssert")) },
            };
        }
        object takerOrMaker = null;
        object isMaker = this.safeBool(trade, "isMaker");
        if (isTrue(!isEqual(isMaker, null)))
        {
            takerOrMaker = ((bool) isTrue(isMaker)) ? "maker" : "taker";
        }
        return this.safeTrade(new Dictionary<string, object>() {
            { "info", trade },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", symbol },
            { "id", id },
            { "order", orderId },
            { "type", null },
            { "side", side },
            { "takerOrMaker", takerOrMaker },
            { "price", priceString },
            { "amount", amountString },
            { "cost", null },
            { "fee", fee },
        }, market);
    }

    /**
     * @method
     * @name bitrue#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#recent-trades-list
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object response = null;
        if (isTrue(getValue(market, "spot")))
        {
            object request = new Dictionary<string, object>() {
                { "symbol", getValue(market, "id") },
            };
            if (isTrue(!isEqual(limit, null)))
            {
                ((IDictionary<string,object>)request)["limit"] = limit; // default 100, max 1000
            }
            response = await this.spotV1PublicGetTrades(this.extend(request, parameters));
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchTrades only support spot markets")) ;
        }
        //
        // spot
        //
        //     [
        //         {
        //             "id": 28457,
        //             "price": "4.00000100",
        //             "qty": "12.00000000",
        //             "time": 1499865549590,
        //             "isBuyerMaker": true,
        //             "isBestMatch": true
        //         }
        //     ]
        //
        return this.parseTrades(response, market, since, limit);
    }

    public virtual object parseOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "INIT", "open" },
            { "PENDING_CREATE", "open" },
            { "NEW", "open" },
            { "PARTIALLY_FILLED", "open" },
            { "FILLED", "closed" },
            { "CANCELED", "canceled" },
            { "PENDING_CANCEL", "canceling" },
            { "REJECTED", "rejected" },
            { "EXPIRED", "expired" },
        };
        return this.safeString(statuses, status, status);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        // createOrder - spot
        //
        //     {
        //         "symbol":"USDCUSDT",
        //         "orderId":2878854881,
        //         "clientOrderId":"",
        //         "transactTime":1635551031276
        //     }
        //
        // createOrder - future
        //
        //     {
        //         "orderId":1690615676032452985,
        //     }
        //
        // fetchOrders - spot
        //
        //     {
        //         "symbol":"USDCUSDT",
        //         "orderId":"2878854881",
        //         "clientOrderId":"",
        //         "price":"1.1000000000000000",
        //         "origQty":"100.0000000000000000",
        //         "executedQty":"0.0000000000000000",
        //         "cummulativeQuoteQty":"0.0000000000000000",
        //         "status":"NEW",
        //         "timeInForce":"",
        //         "type":"LIMIT",
        //         "side":"SELL",
        //         "stopPrice":"",
        //         "icebergQty":"",
        //         "time":1635551031000,
        //         "updateTime":1635551031000,
        //         "isWorking":false
        //     }
        //
        // fetchOrders - future
        //
        //     {
        //         "orderId":1917641,
        //         "price":100,
        //         "origQty":10,
        //         "origAmount":10,
        //         "executedQty":1,
        //         "avgPrice":10000,
        //         "status":"INIT",
        //         "type":"LIMIT",
        //         "side":"BUY",
        //         "action":"OPEN",
        //         "transactTime":1686716571425
        //         "clientOrderId":4949299210
        //     }
        //
        object status = this.parseOrderStatus(this.safeString2(order, "status", "orderStatus"));
        object marketId = this.safeString(order, "symbol");
        object symbol = this.safeSymbol(marketId, market);
        object filled = this.safeString(order, "executedQty");
        object timestamp = null;
        object lastTradeTimestamp = null;
        if (isTrue(inOp(order, "time")))
        {
            timestamp = this.safeInteger(order, "time");
        } else if (isTrue(inOp(order, "transactTime")))
        {
            timestamp = this.safeInteger(order, "transactTime");
        } else if (isTrue(inOp(order, "updateTime")))
        {
            if (isTrue(isEqual(status, "open")))
            {
                if (isTrue(Precise.stringGt(filled, "0")))
                {
                    lastTradeTimestamp = this.safeInteger(order, "updateTime");
                } else
                {
                    timestamp = this.safeInteger(order, "updateTime");
                }
            }
        }
        object average = this.safeString(order, "avgPrice");
        object price = this.safeString(order, "price");
        object amount = this.safeString(order, "origQty");
        // - Spot/Margin market: cummulativeQuoteQty
        // - Futures market: cumQuote.
        //   Note this is not the actual cost, since Binance futures uses leverage to calculate margins.
        object cost = this.safeString2(order, "cummulativeQuoteQty", "cumQuote");
        object id = this.safeString(order, "orderId");
        object type = this.safeStringLower(order, "type");
        object side = this.safeStringLower(order, "side");
        object fills = this.safeList(order, "fills", new List<object>() {});
        object clientOrderId = this.safeString(order, "clientOrderId");
        object timeInForce = this.safeString(order, "timeInForce");
        object postOnly = isTrue(isTrue((isEqual(type, "limit_maker"))) || isTrue((isEqual(timeInForce, "GTX")))) || isTrue((isEqual(type, "post_only")));
        if (isTrue(isEqual(type, "limit_maker")))
        {
            type = "limit";
        }
        object triggerPrice = this.parseNumber(this.omitZero(this.safeString(order, "stopPrice")));
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", order },
            { "id", id },
            { "clientOrderId", clientOrderId },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastTradeTimestamp", lastTradeTimestamp },
            { "symbol", symbol },
            { "type", type },
            { "timeInForce", timeInForce },
            { "postOnly", postOnly },
            { "side", side },
            { "price", price },
            { "triggerPrice", triggerPrice },
            { "amount", amount },
            { "cost", cost },
            { "average", average },
            { "filled", filled },
            { "remaining", null },
            { "status", status },
            { "fee", null },
            { "trades", fills },
        }, market);
    }

    /**
     * @method
     * @name bitrue#createMarketBuyOrderWithCost
     * @description create a market buy order by providing the symbol and cost
     * @see https://www.bitrue.com/api-docs#new-order-trade-hmac-sha256
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#new-order-trade-hmac-sha256
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {float} cost how much you want to trade in units of the quote currency
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createMarketBuyOrderWithCost(object symbol, object cost, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "swap")))
        {
            throw new NotSupported ((string)add(this.id, " createMarketBuyOrderWithCost() supports swap orders only")) ;
        }
        ((IDictionary<string,object>)parameters)["createMarketBuyOrderRequiresPrice"] = false;
        return await this.createOrder(symbol, "market", "buy", cost, null, parameters);
    }

    /**
     * @method
     * @name bitrue#createOrder
     * @description create a trade order
     * @see https://www.bitrue.com/api_docs_includes_file/spot/index.html#new-order-trade
     * @see https://www.bitrue.com/api_docs_includes_file/futures/index.html#new-order-trade-hmac-sha256
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {float} [params.triggerPrice] *spot only* the price at which a trigger order is triggered at
     * @param {string} [params.clientOrderId] a unique id for the order, automatically generated if not sent
     * @param {decimal} [params.leverage] in future order, the leverage value of the order should consistent with the user contract configuration, default is 1
     * @param {string} [params.timeInForce] 'fok', 'ioc' or 'po'
     * @param {bool} [params.postOnly] default false
     * @param {bool} [params.reduceOnly] default false
     * EXCHANGE SPECIFIC PARAMETERS
     * @param {decimal} [params.icebergQty]
     * @param {long} [params.recvWindow]
     * @param {float} [params.cost] *swap market buy only* the quote quantity that can be used as an alternative for the amount
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object response = null;
        object data = null;
        object uppercaseType = ((string)type).ToUpper();
        object request = new Dictionary<string, object>() {
            { "side", ((string)side).ToUpper() },
            { "type", uppercaseType },
        };
        if (isTrue(isEqual(uppercaseType, "LIMIT")))
        {
            if (isTrue(isEqual(price, null)))
            {
                throw new InvalidOrder ((string)add(this.id, " createOrder() requires a price argument")) ;
            }
            ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
        }
        if (isTrue(getValue(market, "swap")))
        {
            object isMarket = isEqual(uppercaseType, "MARKET");
            object timeInForce = this.safeStringLower(parameters, "timeInForce");
            object postOnly = this.isPostOnly(isMarket, null, parameters);
            if (isTrue(postOnly))
            {
                ((IDictionary<string,object>)request)["type"] = "POST_ONLY";
            } else if (isTrue(isEqual(timeInForce, "fok")))
            {
                ((IDictionary<string,object>)request)["type"] = "FOK";
            } else if (isTrue(isEqual(timeInForce, "ioc")))
            {
                ((IDictionary<string,object>)request)["type"] = "IOC";
            }
            ((IDictionary<string,object>)request)["contractName"] = getValue(market, "id");
            object createMarketBuyOrderRequiresPrice = true;
            var createMarketBuyOrderRequiresPriceparametersVariable = this.handleOptionAndParams(parameters, "createOrder", "createMarketBuyOrderRequiresPrice", true);
            createMarketBuyOrderRequiresPrice = ((IList<object>)createMarketBuyOrderRequiresPriceparametersVariable)[0];
            parameters = ((IList<object>)createMarketBuyOrderRequiresPriceparametersVariable)[1];
            if (isTrue(isTrue(isTrue(isMarket) && isTrue((isEqual(side, "buy")))) && isTrue(createMarketBuyOrderRequiresPrice)))
            {
                object cost = this.safeString(parameters, "cost");
                parameters = this.omit(parameters, "cost");
                if (isTrue(isTrue(isEqual(price, null)) && isTrue(isEqual(cost, null))))
                {
                    throw new InvalidOrder ((string)add(this.id, " createOrder() requires the price argument with swap market buy orders to calculate total order cost (amount to spend), where cost = amount * price. Supply a price argument to createOrder() call if you want the cost to be calculated for you from price and amount, or, alternatively, add .options[\"createMarketBuyOrderRequiresPrice\"] = false to supply the cost in the amount argument (the exchange-specific behaviour)")) ;
                } else
                {
                    object amountString = this.numberToString(amount);
                    object priceString = this.numberToString(price);
                    object quoteAmount = Precise.stringMul(amountString, priceString);
                    object requestAmount = ((bool) isTrue((!isEqual(cost, null)))) ? cost : quoteAmount;
                    ((IDictionary<string,object>)request)["amount"] = this.costToPrecision(symbol, requestAmount);
                    ((IDictionary<string,object>)request)["volume"] = this.costToPrecision(symbol, requestAmount);
                }
            } else
            {
                ((IDictionary<string,object>)request)["amount"] = this.parseToNumeric(amount);
                ((IDictionary<string,object>)request)["volume"] = this.parseToNumeric(amount);
            }
            ((IDictionary<string,object>)request)["positionType"] = 1;
            object reduceOnly = this.safeValue2(parameters, "reduceOnly", "reduce_only");
            ((IDictionary<string,object>)request)["open"] = ((bool) isTrue(reduceOnly)) ? "CLOSE" : "OPEN";
            object leverage = this.safeString(parameters, "leverage", "1");
            ((IDictionary<string,object>)request)["leverage"] = this.parseToNumeric(leverage);
            parameters = this.omit(parameters, new List<object>() {"leverage", "reduceOnly", "reduce_only", "timeInForce"});
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV2PrivatePostOrder(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV2PrivatePostOrder(this.extend(request, parameters));
            }
            data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        } else if (isTrue(getValue(market, "spot")))
        {
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
            ((IDictionary<string,object>)request)["quantity"] = this.amountToPrecision(symbol, amount);
            object validOrderTypes = this.safeValue(getValue(market, "info"), "orderTypes");
            if (!isTrue(this.inArray(uppercaseType, validOrderTypes)))
            {
                throw new InvalidOrder ((string)add(add(add(add(this.id, " "), type), " is not a valid order type in market "), symbol)) ;
            }
            object clientOrderId = this.safeString2(parameters, "newClientOrderId", "clientOrderId");
            if (isTrue(!isEqual(clientOrderId, null)))
            {
                parameters = this.omit(parameters, new List<object>() {"newClientOrderId", "clientOrderId"});
                ((IDictionary<string,object>)request)["newClientOrderId"] = clientOrderId;
            }
            object triggerPrice = this.safeValue2(parameters, "triggerPrice", "stopPrice");
            if (isTrue(!isEqual(triggerPrice, null)))
            {
                parameters = this.omit(parameters, new List<object>() {"triggerPrice", "stopPrice"});
                ((IDictionary<string,object>)request)["stopPrice"] = this.priceToPrecision(symbol, triggerPrice);
            }
            response = await this.spotV1PrivatePostOrder(this.extend(request, parameters));
            data = response;
        } else
        {
            throw new NotSupported ((string)add(this.id, " createOrder only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //     {
        //         "symbol": "BTCUSDT",
        //         "orderId": 307650651173648896,
        //         "orderIdStr": "307650651173648896",
        //         "clientOrderId": "6gCrw2kRUAF9CvJDGP16IP",
        //         "transactTime": 1507725176595
        //     }
        //
        // swap
        //
        //     {
        //         "code": "0",
        //         "msg": "Success",
        //         "data": {
        //             "orderId": 1690615676032452985
        //         }
        //     }
        //
        return this.parseOrder(data, market);
    }

    /**
     * @method
     * @name bitrue#fetchOrder
     * @description fetches information on an order made by the user
     * @see https://www.bitrue.com/api_docs_includes_file/spot/index.html#query-order-user_data
     * @see https://www.bitrue.com/api_docs_includes_file/futures/index.html#query-order-user_data-hmac-sha256
     * @param {string} id the order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object origClientOrderId = this.safeValue2(parameters, "origClientOrderId", "clientOrderId");
        parameters = this.omit(parameters, new List<object>() {"origClientOrderId", "clientOrderId"});
        object response = null;
        object data = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(isEqual(origClientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        } else
        {
            if (isTrue(getValue(market, "swap")))
            {
                ((IDictionary<string,object>)request)["clientOrderId"] = origClientOrderId;
            } else
            {
                ((IDictionary<string,object>)request)["origClientOrderId"] = origClientOrderId;
            }
        }
        if (isTrue(getValue(market, "swap")))
        {
            ((IDictionary<string,object>)request)["contractName"] = getValue(market, "id");
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV2PrivateGetOrder(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV2PrivateGetOrder(this.extend(request, parameters));
            }
            data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        } else if (isTrue(getValue(market, "spot")))
        {
            ((IDictionary<string,object>)request)["orderId"] = id; // spot market id is mandatory
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
            response = await this.spotV1PrivateGetOrder(this.extend(request, parameters));
            data = response;
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchOrder only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //     {
        //         "symbol": "LTCBTC",
        //         "orderId": 1,
        //         "clientOrderId": "myOrder1",
        //         "price": "0.1",
        //         "origQty": "1.0",
        //         "executedQty": "0.0",
        //         "cummulativeQuoteQty": "0.0",
        //         "status": "NEW",
        //         "timeInForce": "GTC",
        //         "type": "LIMIT",
        //         "side": "BUY",
        //         "stopPrice": "0.0",
        //         "icebergQty": "0.0",
        //         "time": 1499827319559,
        //         "updateTime": 1499827319559,
        //         "isWorking": true
        //     }
        //
        // swap
        //
        //     {
        //         "code":0,
        //         "msg":"success",
        //         "data":{
        //             "orderId":1917641,
        //             "price":100,
        //             "origQty":10,
        //             "origAmount":10,
        //             "executedQty":1,
        //             "avgPrice":10000,
        //             "status":"INIT",
        //             "type":"LIMIT",
        //             "side":"BUY",
        //             "action":"OPEN",
        //             "transactTime":1686716571425
        //             "clientOrderId":4949299210
        //         }
        //     }
        //
        return this.parseOrder(data, market);
    }

    /**
     * @method
     * @name bitrue#fetchClosedOrders
     * @description fetches information on multiple closed orders made by the user
     * @see https://www.bitrue.com/api_docs_includes_file/spot/index.html#all-orders-user_data
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchClosedOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "spot")))
        {
            throw new NotSupported ((string)add(this.id, " fetchClosedOrders only support spot markets")) ;
        }
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit; // default 100, max 1000
        }
        object response = await this.spotV1PrivateGetAllOrders(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "symbol": "LTCBTC",
        //             "orderId": 1,
        //             "clientOrderId": "myOrder1",
        //             "price": "0.1",
        //             "origQty": "1.0",
        //             "executedQty": "0.0",
        //             "cummulativeQuoteQty": "0.0",
        //             "status": "NEW",
        //             "timeInForce": "GTC",
        //             "type": "LIMIT",
        //             "side": "BUY",
        //             "stopPrice": "0.0",
        //             "icebergQty": "0.0",
        //             "time": 1499827319559,
        //             "updateTime": 1499827319559,
        //             "isWorking": true
        //         }
        //     ]
        //
        return this.parseOrders(response, market, since, limit);
    }

    /**
     * @method
     * @name bitrue#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://www.bitrue.com/api_docs_includes_file/spot/index.html#current-open-orders-user_data
     * @see https://www.bitrue.com/api_docs_includes_file/futures/index.html#cancel-all-open-orders-trade-hmac-sha256
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of open order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOpenOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object response = null;
        object data = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(getValue(market, "swap")))
        {
            ((IDictionary<string,object>)request)["contractName"] = getValue(market, "id");
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV2PrivateGetOpenOrders(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV2PrivateGetOpenOrders(this.extend(request, parameters));
            }
            data = this.safeList(response, "data", new List<object>() {});
        } else if (isTrue(getValue(market, "spot")))
        {
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
            response = await this.spotV1PrivateGetOpenOrders(this.extend(request, parameters));
            data = response;
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchOpenOrders only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //     [
        //         {
        //             "symbol":"USDCUSDT",
        //             "orderId":"2878854881",
        //             "clientOrderId":"",
        //             "price":"1.1000000000000000",
        //             "origQty":"100.0000000000000000",
        //             "executedQty":"0.0000000000000000",
        //             "cummulativeQuoteQty":"0.0000000000000000",
        //             "status":"NEW",
        //             "timeInForce":"",
        //             "type":"LIMIT",
        //             "side":"SELL",
        //             "stopPrice":"",
        //             "icebergQty":"",
        //             "time":1635551031000,
        //             "updateTime":1635551031000,
        //             "isWorking":false
        //         }
        //     ]
        //
        // swap
        //
        //      {
        //          "code": "0",
        //          "msg": "Success",
        //          "data": [{
        //                  "orderId": 1917641,
        //                  "clientOrderId": "2488514315",
        //                  "price": 100,
        //                  "origQty": 10,
        //                  "origAmount": 10,
        //                  "executedQty": 1,
        //                  "avgPrice": 12451,
        //                  "status": "INIT",
        //                  "type": "LIMIT",
        //                  "side": "BUY",
        //                  "action": "OPEN",
        //                  "transactTime": 1686717303975
        //              }
        //          ]
        //      }
        //
        return this.parseOrders(data, market, since, limit);
    }

    /**
     * @method
     * @name bitrue#cancelOrder
     * @description cancels an open order
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#cancel-order-trade
     * @see https://www.bitrue.com/api-docs#cancel-order-trade-hmac-sha256
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#cancel-order-trade-hmac-sha256
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object origClientOrderId = this.safeValue2(parameters, "origClientOrderId", "clientOrderId");
        parameters = this.omit(parameters, new List<object>() {"origClientOrderId", "clientOrderId"});
        object response = null;
        object data = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(isEqual(origClientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        } else
        {
            if (isTrue(getValue(market, "swap")))
            {
                ((IDictionary<string,object>)request)["clientOrderId"] = origClientOrderId;
            } else
            {
                ((IDictionary<string,object>)request)["origClientOrderId"] = origClientOrderId;
            }
        }
        if (isTrue(getValue(market, "swap")))
        {
            ((IDictionary<string,object>)request)["contractName"] = getValue(market, "id");
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV2PrivatePostCancel(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV2PrivatePostCancel(this.extend(request, parameters));
            }
            data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        } else if (isTrue(getValue(market, "spot")))
        {
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
            response = await this.spotV1PrivateDeleteOrder(this.extend(request, parameters));
            data = response;
        } else
        {
            throw new NotSupported ((string)add(this.id, " cancelOrder only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //     {
        //         "symbol": "LTCBTC",
        //         "origClientOrderId": "myOrder1",
        //         "orderId": 1,
        //         "clientOrderId": "cancelMyOrder1"
        //     }
        //
        // swap
        //
        //     {
        //         "code": "0",
        //         "msg": "Success",
        //         "data": {
        //             "orderId": 1690615847831143159
        //         }
        //     }
        //
        return this.parseOrder(data, market);
    }

    /**
     * @method
     * @name bitrue#cancelAllOrders
     * @description cancel all open orders in a market
     * @see https://www.bitrue.com/api-docs#cancel-all-open-orders-trade-hmac-sha256
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#cancel-all-open-orders-trade-hmac-sha256
     * @param {string} symbol unified market symbol of the market to cancel orders in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.marginMode] 'cross' or 'isolated', for spot margin trading
     * @returns {object[]} a list of [order structures]{@link https://github.com/ccxt/ccxt/wiki/Manual#order-structure}
     */
    public async override Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object response = null;
        object data = null;
        if (isTrue(getValue(market, "swap")))
        {
            object request = new Dictionary<string, object>() {
                { "contractName", getValue(market, "id") },
            };
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV2PrivatePostAllOpenOrders(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV2PrivatePostAllOpenOrders(this.extend(request, parameters));
            }
            data = this.safeList(response, "data", new List<object>() {});
        } else
        {
            throw new NotSupported ((string)add(this.id, " cancelAllOrders only support future markets")) ;
        }
        //
        // swap
        //
        //      {
        //          'code': '0',
        //          'msg': 'Success',
        //          'data': null
        //      }
        //
        return this.parseOrders(data, market);
    }

    /**
     * @method
     * @name bitrue#fetchMyTrades
     * @description fetch all trades made by the user
     * @see https://www.bitrue.com/api_docs_includes_file/spot/index.html#account-trade-list-user_data
     * @see https://www.bitrue.com/api_docs_includes_file/futures/index.html#account-trade-list-user_data-hmac-sha256
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchMyTrades() requires a symbol argument")) ;
        }
        object market = this.market(symbol);
        object response = null;
        object data = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            if (isTrue(isGreaterThan(limit, 1000)))
            {
                limit = 1000;
            }
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        if (isTrue(getValue(market, "swap")))
        {
            ((IDictionary<string,object>)request)["contractName"] = getValue(market, "id");
            if (isTrue(getValue(market, "linear")))
            {
                response = await this.fapiV2PrivateGetMyTrades(this.extend(request, parameters));
            } else if (isTrue(getValue(market, "inverse")))
            {
                response = await this.dapiV2PrivateGetMyTrades(this.extend(request, parameters));
            }
            data = this.safeList(response, "data", new List<object>() {});
        } else if (isTrue(getValue(market, "spot")))
        {
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
            response = await this.spotV2PrivateGetMyTrades(this.extend(request, parameters));
            data = response;
        } else
        {
            throw new NotSupported ((string)add(this.id, " fetchMyTrades only support spot & swap markets")) ;
        }
        //
        // spot
        //
        //     [
        //         {
        //             "symbol":"USDCUSDT",
        //             "id":20725156,
        //             "orderId":2880918576,
        //             "origClientOrderId":null,
        //             "price":"0.9996000000000000",
        //             "qty":"100.0000000000000000",
        //             "commission":null,
        //             "commissionAssert":null,
        //             "time":1635558511000,
        //             "isBuyer":false,
        //             "isMaker":false,
        //             "isBestMatch":true
        //         }
        //     ]
        //
        // swap
        //
        //     {
        //         "code":"0",
        //         "msg":"Success",
        //         "data":[
        //             {
        //                 "tradeId":12,
        //                 "price":0.9,
        //                 "qty":1,
        //                 "amount":9,
        //                 "contractName":"E-SAND-USDT",
        //                 "side":"BUY",
        //                 "fee":"0.0018",
        //                 "bidId":1558124009467904992,
        //                 "askId":1558124043827644908,
        //                 "bidUserId":10294,
        //                 "askUserId":10467,
        //                 "isBuyer":true,
        //                 "isMaker":true,
        //                 "ctime":*************
        //             }
        //         ]
        //     }
        //
        return this.parseTrades(data, market, since, limit);
    }

    /**
     * @method
     * @name bitrue#fetchDeposits
     * @description fetch all deposits made to an account
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#deposit-history--withdraw_data
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for
     * @param {int} [limit] the maximum number of deposits structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDeposits(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(code, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchDeposits() requires a code argument")) ;
        }
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
            { "status", 1 },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.spotV1PrivateGetDepositHistory(this.extend(request, parameters));
        //
        //     {
        //         "code":200,
        //         "msg":"succ",
        //         "data":[
        //             {
        //                 "id":2659137,
        //                 "symbol":"USDC",
        //                 "amount":"200.0000000000000000",
        //                 "fee":"0.0E-15",
        //                 "createdAt":1635503169000,
        //                 "updatedAt":1635503202000,
        //                 "addressFrom":"0x2faf487a4414fe77e2327f0bf4ae2a264a776ad2",
        //                 "addressTo":"0x190ceccb1f8bfbec1749180f0ba8922b488d865b",
        //                 "txid":"0x9970aec41099ac385568859517308707bc7d716df8dabae7b52f5b17351c3ed0",
        //                 "confirmations":5,
        //                 "status":0,
        //                 "tagType":null,
        //             },
        //             {
        //                 "id":2659137,
        //                 "symbol": "XRP",
        //                 "amount": "20.0000000000000000",
        //                 "fee": "0.0E-15",
        //                 "createdAt": 1544669393000,
        //                 "updatedAt": 1544669413000,
        //                 "addressFrom": "",
        //                 "addressTo": "raLPjTYeGezfdb6crXZzcC8RkLBEwbBHJ5_18113641",
        //                 "txid": "515B23E1F9864D3AF7F5B4C4FCBED784BAE861854FAB95F4031922B6AAEFC7AC",
        //                 "confirmations": 7,
        //                 "status": 1,
        //                 "tagType": "Tag"
        //             }
        //         ]
        //     }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseTransactions(data, currency, since, limit);
    }

    /**
     * @method
     * @name bitrue#fetchWithdrawals
     * @description fetch all withdrawals made from an account
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#withdraw-history--withdraw_data
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch withdrawals for
     * @param {int} [limit] the maximum number of withdrawals structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(code, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchWithdrawals() requires a code argument")) ;
        }
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
            { "status", 5 },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.spotV1PrivateGetWithdrawHistory(this.extend(request, parameters));
        //
        //    {
        //        "code": 200,
        //        "msg": "succ",
        //        "data": [
        //            {
        //                "id": 183745,
        //                "symbol": "usdt_erc20",
        //                "amount": "8.4000000000000000",
        //                "fee": "1.6000000000000000",
        //                "payAmount": "0.0000000000000000",
        //                "createdAt": 1595336441000,
        //                "updatedAt": 1595336576000,
        //                "addressFrom": "",
        //                "addressTo": "0x2edfae3878d7b6db70ce4abed177ab2636f60c83",
        //                "txid": "",
        //                "confirmations": 0,
        //                "status": 6,
        //                "tagType": null
        //            }
        //        ]
        //    }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseTransactions(data, currency);
    }

    public virtual object parseTransactionStatusByType(object status, object type = null)
    {
        object statusesByType = new Dictionary<string, object>() {
            { "deposit", new Dictionary<string, object>() {
                { "0", "pending" },
                { "1", "ok" },
            } },
            { "withdrawal", new Dictionary<string, object>() {
                { "0", "pending" },
                { "5", "ok" },
                { "6", "canceled" },
            } },
        };
        object statuses = this.safeDict(statusesByType, type, new Dictionary<string, object>() {});
        return this.safeString(statuses, status, status);
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        // fetchDeposits
        //
        //     {
        //         "symbol": "XRP",
        //         "amount": "261.3361000000000000",
        //         "fee": "0.0E-15",
        //         "createdAt": 1548816979000,
        //         "updatedAt": 1548816999000,
        //         "addressFrom": "",
        //         "addressTo": "raLPjTYeGezfdb6crXZzcC8RkLBEwbBHJ5_18113641",
        //         "txid": "86D6EB68A7A28938BCE06BD348F8C07DEF500C5F7FE92069EF8C0551CE0F2C7D",
        //         "confirmations": 8,
        //         "status": 1,
        //         "tagType": "Tag"
        //     },
        //     {
        //         "symbol": "XRP",
        //         "amount": "20.0000000000000000",
        //         "fee": "0.0E-15",
        //         "createdAt": 1544669393000,
        //         "updatedAt": 1544669413000,
        //         "addressFrom": "",
        //         "addressTo": "raLPjTYeGezfdb6crXZzcC8RkLBEwbBHJ5_18113641",
        //         "txid": "515B23E1F9864D3AF7F5B4C4FCBED784BAE861854FAB95F4031922B6AAEFC7AC",
        //         "confirmations": 7,
        //         "status": 1,
        //         "tagType": "Tag"
        //     }
        //
        // fetchWithdrawals
        //
        //     {
        //         "id": 183745,
        //         "symbol": "usdt_erc20",
        //         "amount": "8.4000000000000000",
        //         "fee": "1.6000000000000000",
        //         "payAmount": "0.0000000000000000",
        //         "createdAt": 1595336441000,
        //         "updatedAt": 1595336576000,
        //         "addressFrom": "",
        //         "addressTo": "0x2edfae3878d7b6db70ce4abed177ab2636f60c83",
        //         "txid": "",
        //         "confirmations": 0,
        //         "status": 6,
        //         "tagType": null
        //     }
        //
        // withdraw
        //
        //     {
        //         "msg": null,
        //         "amount": 1000,
        //         "fee": 1,
        //         "ctime": null,
        //         "coin": "usdt_erc20",
        //         "withdrawId": 1156423,
        //         "addressTo": "0x2edfae3878d7b6db70ce4abed177ab2636f60c83"
        //     }
        //
        object id = this.safeString2(transaction, "id", "withdrawId");
        object tagType = this.safeString(transaction, "tagType");
        object addressTo = this.safeString(transaction, "addressTo");
        object addressFrom = this.safeString(transaction, "addressFrom");
        object tagTo = null;
        object tagFrom = null;
        if (isTrue(!isEqual(tagType, null)))
        {
            if (isTrue(!isEqual(addressTo, null)))
            {
                object parts = ((string)addressTo).Split(new [] {((string)"_")}, StringSplitOptions.None).ToList<object>();
                addressTo = this.safeString(parts, 0);
                tagTo = this.safeString(parts, 1);
            }
            if (isTrue(!isEqual(addressFrom, null)))
            {
                object parts = ((string)addressFrom).Split(new [] {((string)"_")}, StringSplitOptions.None).ToList<object>();
                addressFrom = this.safeString(parts, 0);
                tagFrom = this.safeString(parts, 1);
            }
        }
        object txid = this.safeString(transaction, "txid");
        object timestamp = this.safeInteger(transaction, "createdAt");
        object updated = this.safeInteger(transaction, "updatedAt");
        object payAmount = (inOp(transaction, "payAmount"));
        object ctime = (inOp(transaction, "ctime"));
        object type = ((bool) isTrue((isTrue(payAmount) || isTrue(ctime)))) ? "withdrawal" : "deposit";
        object status = this.parseTransactionStatusByType(this.safeString(transaction, "status"), type);
        object amount = this.safeNumber(transaction, "amount");
        object network = null;
        object currencyId = this.safeString2(transaction, "symbol", "coin");
        if (isTrue(!isEqual(currencyId, null)))
        {
            object parts = ((string)currencyId).Split(new [] {((string)"_")}, StringSplitOptions.None).ToList<object>();
            currencyId = this.safeString(parts, 0);
            object networkId = this.safeString(parts, 1);
            if (isTrue(!isEqual(networkId, null)))
            {
                network = ((string)networkId).ToUpper();
            }
        }
        object code = this.safeCurrencyCode(currencyId, currency);
        object feeCost = this.safeNumber(transaction, "fee");
        object fee = null;
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "currency", code },
                { "cost", feeCost },
            };
        }
        return new Dictionary<string, object>() {
            { "info", transaction },
            { "id", id },
            { "txid", txid },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "network", network },
            { "address", addressTo },
            { "addressTo", addressTo },
            { "addressFrom", addressFrom },
            { "tag", tagTo },
            { "tagTo", tagTo },
            { "tagFrom", tagFrom },
            { "type", type },
            { "amount", amount },
            { "currency", code },
            { "status", status },
            { "updated", updated },
            { "internal", false },
            { "comment", null },
            { "fee", fee },
        };
    }

    /**
     * @method
     * @name bitrue#withdraw
     * @description make a withdrawal
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#withdraw-commit--withdraw_data
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address the address to withdraw to
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        var tagparametersVariable = this.handleWithdrawTagAndParams(tag, parameters);
        tag = ((IList<object>)tagparametersVariable)[0];
        parameters = ((IList<object>)tagparametersVariable)[1];
        this.checkAddress(address);
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
            { "amount", amount },
            { "addressTo", address },
        };
        object networkCode = null;
        var networkCodeparametersVariable = this.handleNetworkCodeAndParams(parameters);
        networkCode = ((IList<object>)networkCodeparametersVariable)[0];
        parameters = ((IList<object>)networkCodeparametersVariable)[1];
        if (isTrue(!isEqual(networkCode, null)))
        {
            ((IDictionary<string,object>)request)["chainName"] = this.networkCodeToId(networkCode);
        }
        if (isTrue(!isEqual(tag, null)))
        {
            ((IDictionary<string,object>)request)["tag"] = tag;
        }
        object response = await this.spotV1PrivatePostWithdrawCommit(this.extend(request, parameters));
        //
        //     {
        //         "code": 200,
        //         "msg": "succ",
        //         "data": {
        //             "msg": null,
        //             "amount": 1000,
        //             "fee": 1,
        //             "ctime": null,
        //             "coin": "usdt_erc20",
        //             "withdrawId": 1156423,
        //             "addressTo": "0x2edfae3878d7b6db70ce4abed177ab2636f60c83"
        //         }
        //     }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        return this.parseTransaction(data, currency);
    }

    public override object parseDepositWithdrawFee(object fee, object currency = null)
    {
        //
        //   {
        //       "coin": "adx",
        //       "coinFulName": "Ambire AdEx",
        //       "chains": [ "BSC" ],
        //       "chainDetail": [ [Object] ]
        //   }
        //
        object chainDetails = this.safeList(fee, "chainDetail", new List<object>() {});
        object chainDetailLength = getArrayLength(chainDetails);
        object result = new Dictionary<string, object>() {
            { "info", fee },
            { "withdraw", new Dictionary<string, object>() {
                { "fee", null },
                { "percentage", null },
            } },
            { "deposit", new Dictionary<string, object>() {
                { "fee", null },
                { "percentage", null },
            } },
            { "networks", new Dictionary<string, object>() {} },
        };
        if (isTrue(!isEqual(chainDetailLength, 0)))
        {
            for (object i = 0; isLessThan(i, chainDetailLength); postFixIncrement(ref i))
            {
                object chainDetail = getValue(chainDetails, i);
                object networkId = this.safeString(chainDetail, "chain");
                object currencyCode = this.safeString(currency, "code");
                object networkCode = this.networkIdToCode(networkId, currencyCode);
                ((IDictionary<string,object>)getValue(result, "networks"))[(string)networkCode] = new Dictionary<string, object>() {
                    { "deposit", new Dictionary<string, object>() {
                        { "fee", null },
                        { "percentage", null },
                    } },
                    { "withdraw", new Dictionary<string, object>() {
                        { "fee", this.safeNumber(chainDetail, "withdrawFee") },
                        { "percentage", false },
                    } },
                };
                if (isTrue(isEqual(chainDetailLength, 1)))
                {
                    ((IDictionary<string,object>)getValue(result, "withdraw"))["fee"] = this.safeNumber(chainDetail, "withdrawFee");
                    ((IDictionary<string,object>)getValue(result, "withdraw"))["percentage"] = false;
                }
            }
        }
        return result;
    }

    /**
     * @method
     * @name bitrue#fetchDepositWithdrawFees
     * @description fetch deposit and withdraw fees
     * @see https://github.com/Bitrue-exchange/Spot-official-api-docs#exchangeInfo_endpoint
     * @param {string[]|undefined} codes list of unified currency codes
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchDepositWithdrawFees(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.spotV1PublicGetExchangeInfo(parameters);
        object coins = this.safeList(response, "coins");
        return this.parseDepositWithdrawFees(coins, codes, "coin");
    }

    public override object parseTransfer(object transfer, object currency = null)
    {
        //
        //     fetchTransfers
        //
        //     {
        //         'transferType': 'wallet_to_contract',
        //         'symbol': 'USDT',
        //         'amount': 1.0,
        //         'status': 1,
        //         'ctime': *************
        //     }
        //
        //     transfer
        //
        //     {}
        //
        object transferType = this.safeString(transfer, "transferType");
        object fromAccount = null;
        object toAccount = null;
        if (isTrue(!isEqual(transferType, null)))
        {
            object accountSplit = ((string)transferType).Split(new [] {((string)"_to_")}, StringSplitOptions.None).ToList<object>();
            fromAccount = this.safeString(accountSplit, 0);
            toAccount = this.safeString(accountSplit, 1);
        }
        object timestamp = this.safeInteger(transfer, "ctime");
        return new Dictionary<string, object>() {
            { "info", transfer },
            { "id", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "currency", this.safeString(currency, "code") },
            { "amount", this.safeNumber(transfer, "amount") },
            { "fromAccount", fromAccount },
            { "toAccount", toAccount },
            { "status", "ok" },
        };
    }

    /**
     * @method
     * @name bitrue#fetchTransfers
     * @description fetch a history of internal transfers made on an account
     * @see https://www.bitrue.com/api-docs#get-future-account-transfer-history-list-user_data-hmac-sha256
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#get-future-account-transfer-history-list-user_data-hmac-sha256
     * @param {string} code unified currency code of the currency transferred
     * @param {int} [since] the earliest time in ms to fetch transfers for
     * @param {int} [limit] the maximum number of transfers structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] the latest time in ms to fetch transfers for
     * @param {string} [params.type] transfer type wallet_to_contract or contract_to_wallet
     * @returns {object[]} a list of [transfer structures]{@link https://github.com/ccxt/ccxt/wiki/Manual#transfer-structure}
     */
    public async override Task<object> fetchTransfers(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object type = this.safeString2(parameters, "type", "transferType");
        object request = new Dictionary<string, object>() {
            { "transferType", type },
        };
        object currency = null;
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["coinSymbol"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["beginTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            if (isTrue(isGreaterThan(limit, 200)))
            {
                limit = 200;
            }
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object until = this.safeInteger(parameters, "until");
        if (isTrue(!isEqual(until, null)))
        {
            parameters = this.omit(parameters, "until");
            ((IDictionary<string,object>)request)["endTime"] = until;
        }
        object response = await this.fapiV2PrivateGetFuturesTransferHistory(this.extend(request, parameters));
        //
        //     {
        //         'code': '0',
        //         'msg': 'Success',
        //         'data': [{
        //             'transferType': 'wallet_to_contract',
        //             'symbol': 'USDT',
        //             'amount': 1.0,
        //             'status': 1,
        //             'ctime': *************
        //         }]
        //     }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseTransfers(data, currency, since, limit);
    }

    /**
     * @method
     * @name bitrue#transfer
     * @description transfer currency internally between wallets on the same account
     * @see https://www.bitrue.com/api-docs#new-future-account-transfer-user_data-hmac-sha256
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#user-commission-rate-user_data-hmac-sha256
     * @param {string} code unified currency code
     * @param {float} amount amount to transfer
     * @param {string} fromAccount account to transfer from
     * @param {string} toAccount account to transfer to
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transfer structure]{@link https://github.com/ccxt/ccxt/wiki/Manual#transfer-structure}
     */
    public async override Task<object> transfer(object code, object amount, object fromAccount, object toAccount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object accountTypes = this.safeDict(this.options, "accountsByType", new Dictionary<string, object>() {});
        object fromId = this.safeString(accountTypes, fromAccount, fromAccount);
        object toId = this.safeString(accountTypes, toAccount, toAccount);
        object request = new Dictionary<string, object>() {
            { "coinSymbol", getValue(currency, "id") },
            { "amount", this.currencyToPrecision(code, amount) },
            { "transferType", add(add(fromId, "_to_"), toId) },
        };
        object response = await this.fapiV2PrivatePostFuturesTransfer(this.extend(request, parameters));
        //
        //     {
        //         'code': '0',
        //         'msg': 'Success',
        //         'data': null
        //     }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        return this.parseTransfer(data, currency);
    }

    /**
     * @method
     * @name bitrue#setLeverage
     * @description set the level of leverage for a market
     * @see https://www.bitrue.com/api-docs#change-initial-leverage-trade-hmac-sha256
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#change-initial-leverage-trade-hmac-sha256
     * @param {float} leverage the rate of leverage
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} response from the exchange
     */
    public async override Task<object> setLeverage(object leverage, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " setLeverage() requires a symbol argument")) ;
        }
        if (isTrue(isTrue((isLessThan(leverage, 1))) || isTrue((isGreaterThan(leverage, 125)))))
        {
            throw new BadRequest ((string)add(this.id, " leverage should be between 1 and 125")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object response = null;
        object request = new Dictionary<string, object>() {
            { "contractName", getValue(market, "id") },
            { "leverage", leverage },
        };
        if (!isTrue(getValue(market, "swap")))
        {
            throw new NotSupported ((string)add(this.id, " setLeverage only support swap markets")) ;
        }
        if (isTrue(getValue(market, "linear")))
        {
            response = await this.fapiV2PrivatePostLevelEdit(this.extend(request, parameters));
        } else if (isTrue(getValue(market, "inverse")))
        {
            response = await this.dapiV2PrivatePostLevelEdit(this.extend(request, parameters));
        }
        return response;
    }

    public override object parseMarginModification(object data, object market = null)
    {
        //
        // setMargin
        //
        //     {
        //         "code": 0,
        //         "msg": "success"
        //         "data": null
        //     }
        //
        return new Dictionary<string, object>() {
            { "info", data },
            { "symbol", getValue(market, "symbol") },
            { "type", null },
            { "marginMode", "isolated" },
            { "amount", null },
            { "total", null },
            { "code", null },
            { "status", null },
            { "timestamp", null },
            { "datetime", null },
        };
    }

    /**
     * @method
     * @name bitrue#setMargin
     * @description Either adds or reduces margin in an isolated position in order to set the margin to a specific value
     * @see https://www.bitrue.com/api-docs#modify-isolated-position-margin-trade-hmac-sha256
     * @see https://www.bitrue.com/api_docs_includes_file/delivery.html#modify-isolated-position-margin-trade-hmac-sha256
     * @param {string} symbol unified market symbol of the market to set margin in
     * @param {float} amount the amount to set the margin to
     * @param {object} [params] parameters specific to the exchange API endpoint
     * @returns {object} A [margin structure]{@link https://github.com/ccxt/ccxt/wiki/Manual#add-margin-structure}
     */
    public async override Task<object> setMargin(object symbol, object amount, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        if (!isTrue(getValue(market, "swap")))
        {
            throw new NotSupported ((string)add(this.id, " setMargin only support swap markets")) ;
        }
        object response = null;
        object request = new Dictionary<string, object>() {
            { "contractName", getValue(market, "id") },
            { "amount", this.parseToNumeric(amount) },
        };
        if (isTrue(getValue(market, "linear")))
        {
            response = await this.fapiV2PrivatePostPositionMargin(this.extend(request, parameters));
        } else if (isTrue(getValue(market, "inverse")))
        {
            response = await this.dapiV2PrivatePostPositionMargin(this.extend(request, parameters));
        }
        //
        //     {
        //         "code": 0,
        //         "msg": "success"
        //         "data": null
        //     }
        //
        return this.parseMarginModification(response, market);
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object type = this.safeString(api, 0);
        object version = this.safeString(api, 1);
        object access = this.safeString(api, 2);
        object url = null;
        if (isTrue(isTrue((isTrue(isEqual(type, "api")) && isTrue(isEqual(version, "kline")))) || isTrue((isTrue(isEqual(type, "open")) && isTrue(isGreaterThanOrEqual(getIndexOf(path, "listenKey"), 0))))))
        {
            url = getValue(getValue(this.urls, "api"), type);
        } else
        {
            url = add(add(getValue(getValue(this.urls, "api"), type), "/"), version);
        }
        url = add(add(url, "/"), this.implodeParams(path, parameters));
        parameters = this.omit(parameters, this.extractParams(path));
        if (isTrue(isEqual(access, "private")))
        {
            this.checkRequiredCredentials();
            object recvWindow = this.safeInteger(this.options, "recvWindow", 5000);
            if (isTrue(isTrue(isEqual(type, "spot")) || isTrue(isEqual(type, "open"))))
            {
                object query = this.urlencode(this.extend(new Dictionary<string, object>() {
                    { "timestamp", this.nonce() },
                    { "recvWindow", recvWindow },
                }, parameters));
                object signature = this.hmac(this.encode(query), this.encode(this.secret), sha256);
                query = add(query, add(add("&", "signature="), signature));
                headers = new Dictionary<string, object>() {
                    { "X-MBX-APIKEY", this.apiKey },
                };
                if (isTrue(isTrue((isEqual(method, "GET"))) || isTrue((isEqual(method, "DELETE")))))
                {
                    url = add(url, add("?", query));
                } else
                {
                    body = query;
                    ((IDictionary<string,object>)headers)["Content-Type"] = "application/x-www-form-urlencoded";
                }
            } else
            {
                object timestamp = ((object)this.nonce()).ToString();
                object signPath = null;
                if (isTrue(isEqual(type, "fapi")))
                {
                    signPath = "/fapi";
                } else if (isTrue(isEqual(type, "dapi")))
                {
                    signPath = "/dapi";
                }
                signPath = add(add(add(add(signPath, "/"), version), "/"), path);
                object signMessage = add(add(timestamp, method), signPath);
                if (isTrue(isEqual(method, "GET")))
                {
                    object keys = new List<object>(((IDictionary<string,object>)parameters).Keys);
                    object keysLength = getArrayLength(keys);
                    if (isTrue(isGreaterThan(keysLength, 0)))
                    {
                        signMessage = add(signMessage, add("?", this.urlencode(parameters)));
                    }
                    object signature = this.hmac(this.encode(signMessage), this.encode(this.secret), sha256);
                    headers = new Dictionary<string, object>() {
                        { "X-CH-APIKEY", this.apiKey },
                        { "X-CH-SIGN", signature },
                        { "X-CH-TS", timestamp },
                    };
                    url = add(url, add("?", this.urlencode(parameters)));
                } else
                {
                    object query = this.extend(new Dictionary<string, object>() {
                        { "recvWindow", recvWindow },
                    }, parameters);
                    body = this.json(query);
                    signMessage = add(signMessage, body);
                    object signature = this.hmac(this.encode(signMessage), this.encode(this.secret), sha256);
                    headers = new Dictionary<string, object>() {
                        { "Content-Type", "application/json" },
                        { "X-CH-APIKEY", this.apiKey },
                        { "X-CH-SIGN", signature },
                        { "X-CH-TS", timestamp },
                    };
                }
            }
        } else
        {
            if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)parameters).Keys))))
            {
                url = add(url, add("?", this.urlencode(parameters)));
            }
        }
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object code, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        if (isTrue(isTrue((isEqual(code, 418))) || isTrue((isEqual(code, 429)))))
        {
            throw new DDoSProtection ((string)add(add(add(add(add(add(this.id, " "), ((object)code).ToString()), " "), reason), " "), body)) ;
        }
        // error response in a form: { "code": -1013, "msg": "Invalid quantity." }
        // following block cointains legacy checks against message patterns in "msg" property
        // will switch "code" checks eventually, when we know all of them
        if (isTrue(isGreaterThanOrEqual(code, 400)))
        {
            if (isTrue(isGreaterThanOrEqual(getIndexOf(body, "Price * QTY is zero or less"), 0)))
            {
                throw new InvalidOrder ((string)add(add(this.id, " order cost = amount * price is zero or less "), body)) ;
            }
            if (isTrue(isGreaterThanOrEqual(getIndexOf(body, "LOT_SIZE"), 0)))
            {
                throw new InvalidOrder ((string)add(add(this.id, " order amount should be evenly divisible by lot size "), body)) ;
            }
            if (isTrue(isGreaterThanOrEqual(getIndexOf(body, "PRICE_FILTER"), 0)))
            {
                throw new InvalidOrder ((string)add(add(this.id, " order price is invalid, i.e. exceeds allowed price precision, exceeds min price or max price limits or is invalid float value in general, use this.priceToPrecision (symbol, amount) "), body)) ;
            }
        }
        if (isTrue(isEqual(response, null)))
        {
            return null;  // fallback to default error handler
        }
        // check success value for wapi endpoints
        // response in format {'msg': 'The coin does not exist.', 'success': true/false}
        object success = this.safeBool(response, "success", true);
        if (!isTrue(success))
        {
            object messageInner = this.safeString(response, "msg");
            object parsedMessage = null;
            if (isTrue(!isEqual(messageInner, null)))
            {
                try
                {
                    parsedMessage = parseJson(messageInner);
                } catch(Exception e)
                {
                    // do nothing
                    parsedMessage = null;
                }
                if (isTrue(!isEqual(parsedMessage, null)))
                {
                    response = parsedMessage;
                }
            }
        }
        object message = this.safeString(response, "msg");
        if (isTrue(!isEqual(message, null)))
        {
            this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), message, add(add(this.id, " "), message));
            this.throwBroadlyMatchedException(getValue(this.exceptions, "broad"), message, add(add(this.id, " "), message));
        }
        // checks against error codes
        object error = this.safeString(response, "code");
        if (isTrue(!isEqual(error, null)))
        {
            // https://github.com/ccxt/ccxt/issues/6501
            // https://github.com/ccxt/ccxt/issues/7742
            if (isTrue(isTrue((isEqual(error, "200"))) || isTrue(Precise.stringEquals(error, "0"))))
            {
                return null;
            }
            // a workaround for {"code":-2015,"msg":"Invalid API-key, IP, or permissions for action."}
            // despite that their message is very confusing, it is raised by Binance
            // on a temporary ban, the API key is valid, but disabled for a while
            if (isTrue(isTrue((isEqual(error, "-2015"))) && isTrue(getValue(this.options, "hasAlreadyAuthenticatedSuccessfully"))))
            {
                throw new DDoSProtection ((string)add(add(this.id, " temporary banned: "), body)) ;
            }
            object feedback = add(add(this.id, " "), body);
            this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), error, feedback);
            throw new ExchangeError ((string)feedback) ;
        }
        if (!isTrue(success))
        {
            throw new ExchangeError ((string)add(add(this.id, " "), body)) ;
        }
        return null;
    }

    public override object calculateRateLimiterCost(object api, object method, object path, object parameters, object config = null)
    {
        config ??= new Dictionary<string, object>();
        if (isTrue(isTrue((inOp(config, "noSymbol"))) && !isTrue((inOp(parameters, "symbol")))))
        {
            return getValue(config, "noSymbol");
        } else if (isTrue(isTrue((inOp(config, "byLimit"))) && isTrue((inOp(parameters, "limit")))))
        {
            object limit = getValue(parameters, "limit");
            object byLimit = ((object)getValue(config, "byLimit"));
            for (object i = 0; isLessThan(i, getArrayLength(byLimit)); postFixIncrement(ref i))
            {
                object entry = getValue(byLimit, i);
                if (isTrue(isLessThanOrEqual(limit, getValue(entry, 0))))
                {
                    return getValue(entry, 1);
                }
            }
        }
        return this.safeValue(config, "cost", 1);
    }
}
