namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class indodax : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "indodax" },
            { "name", "INDODAX" },
            { "countries", new List<object>() {"ID"} },
            { "rateLimit", 50 },
            { "has", new Dictionary<string, object>() {
                { "CORS", null },
                { "spot", true },
                { "margin", false },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "addMargin", false },
                { "borrowCrossMargin", false },
                { "borrowIsolatedMargin", false },
                { "borrowMargin", false },
                { "cancelAllOrders", false },
                { "cancelOrder", true },
                { "cancelOrders", false },
                { "closeAllPositions", false },
                { "closePosition", false },
                { "createDepositAddress", false },
                { "createOrder", true },
                { "createReduceOnlyOrder", false },
                { "createStopLimitOrder", false },
                { "createStopMarketOrder", false },
                { "createStopOrder", false },
                { "fetchAllGreeks", false },
                { "fetchBalance", true },
                { "fetchBorrowInterest", false },
                { "fetchBorrowRate", false },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchBorrowRates", false },
                { "fetchBorrowRatesPerSymbol", false },
                { "fetchClosedOrders", true },
                { "fetchCrossBorrowRate", false },
                { "fetchCrossBorrowRates", false },
                { "fetchDeposit", false },
                { "fetchDepositAddress", "emulated" },
                { "fetchDepositAddresses", true },
                { "fetchDepositAddressesByNetwork", false },
                { "fetchDeposits", false },
                { "fetchDepositsWithdrawals", true },
                { "fetchFundingHistory", false },
                { "fetchFundingInterval", false },
                { "fetchFundingIntervals", false },
                { "fetchFundingRate", false },
                { "fetchFundingRateHistory", false },
                { "fetchFundingRates", false },
                { "fetchGreeks", false },
                { "fetchIndexOHLCV", false },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchIsolatedPositions", false },
                { "fetchLeverage", false },
                { "fetchLeverages", false },
                { "fetchLeverageTiers", false },
                { "fetchLiquidations", false },
                { "fetchLongShortRatio", false },
                { "fetchLongShortRatioHistory", false },
                { "fetchMarginAdjustmentHistory", false },
                { "fetchMarginMode", false },
                { "fetchMarginModes", false },
                { "fetchMarketLeverageTiers", false },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMarkPrice", false },
                { "fetchMarkPrices", false },
                { "fetchMyLiquidations", false },
                { "fetchMySettlementHistory", false },
                { "fetchOpenInterest", false },
                { "fetchOpenInterestHistory", false },
                { "fetchOpenInterests", false },
                { "fetchOpenOrders", true },
                { "fetchOption", false },
                { "fetchOptionChain", false },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchOrders", false },
                { "fetchPosition", false },
                { "fetchPositionForSymbolWs", false },
                { "fetchPositionHistory", false },
                { "fetchPositionMode", false },
                { "fetchPositions", false },
                { "fetchPositionsForSymbol", false },
                { "fetchPositionsForSymbolWs", false },
                { "fetchPositionsHistory", false },
                { "fetchPositionsRisk", false },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchSettlementHistory", false },
                { "fetchTicker", true },
                { "fetchTime", true },
                { "fetchTrades", true },
                { "fetchTradingFee", false },
                { "fetchTradingFees", false },
                { "fetchTransactionFee", true },
                { "fetchTransactionFees", false },
                { "fetchTransactions", "emulated" },
                { "fetchTransfer", false },
                { "fetchTransfers", false },
                { "fetchUnderlyingAssets", false },
                { "fetchVolatilityHistory", false },
                { "fetchWithdrawal", false },
                { "fetchWithdrawals", false },
                { "reduceMargin", false },
                { "repayCrossMargin", false },
                { "repayIsolatedMargin", false },
                { "setLeverage", false },
                { "setMargin", false },
                { "setMarginMode", false },
                { "setPositionMode", false },
                { "transfer", false },
                { "withdraw", true },
            } },
            { "version", "2.0" },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://user-images.githubusercontent.com/51840849/87070508-9358c880-c221-11ea-8dc5-5391afbbb422.jpg" },
                { "api", new Dictionary<string, object>() {
                    { "public", "https://indodax.com" },
                    { "private", "https://indodax.com/tapi" },
                } },
                { "www", "https://www.indodax.com" },
                { "doc", "https://github.com/btcid/indodax-official-api-docs" },
                { "referral", "https://indodax.com/ref/testbitcoincoid/1" },
            } },
            { "api", new Dictionary<string, object>() {
                { "public", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "api/server_time", 5 },
                        { "api/pairs", 5 },
                        { "api/price_increments", 5 },
                        { "api/summaries", 5 },
                        { "api/ticker/{pair}", 5 },
                        { "api/ticker_all", 5 },
                        { "api/trades/{pair}", 5 },
                        { "api/depth/{pair}", 5 },
                        { "tradingview/history_v2", 5 },
                    } },
                } },
                { "private", new Dictionary<string, object>() {
                    { "post", new Dictionary<string, object>() {
                        { "getInfo", 4 },
                        { "transHistory", 4 },
                        { "trade", 1 },
                        { "tradeHistory", 4 },
                        { "openOrders", 4 },
                        { "orderHistory", 4 },
                        { "getOrder", 4 },
                        { "cancelOrder", 4 },
                        { "withdrawFee", 4 },
                        { "withdrawCoin", 4 },
                        { "listDownline", 4 },
                        { "checkDownline", 4 },
                        { "createVoucher", 4 },
                    } },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "tierBased", false },
                    { "percentage", true },
                    { "maker", 0 },
                    { "taker", 0.003 },
                } },
            } },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "invalid_pair", typeof(BadSymbol) },
                    { "Insufficient balance.", typeof(InsufficientFunds) },
                    { "invalid order.", typeof(OrderNotFound) },
                    { "Invalid credentials. API not found or session has expired.", typeof(AuthenticationError) },
                    { "Invalid credentials. Bad sign.", typeof(AuthenticationError) },
                } },
                { "broad", new Dictionary<string, object>() {
                    { "Minimum price", typeof(InvalidOrder) },
                    { "Minimum order", typeof(InvalidOrder) },
                } },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", "1" },
                { "15m", "15" },
                { "30m", "30" },
                { "1h", "60" },
                { "4h", "240" },
                { "1d", "1D" },
                { "3d", "3D" },
                { "1w", "1W" },
            } },
            { "options", new Dictionary<string, object>() {
                { "recvWindow", multiply(5, 1000) },
                { "timeDifference", 0 },
                { "adjustForTimeDifference", false },
                { "networks", new Dictionary<string, object>() {
                    { "XLM", "Stellar Token" },
                    { "BSC", "bep20" },
                    { "TRC20", "trc20" },
                    { "MATIC", "polygon" },
                } },
            } },
            { "features", new Dictionary<string, object>() {
                { "spot", new Dictionary<string, object>() {
                    { "sandbox", false },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "triggerPrice", false },
                        { "triggerPriceType", null },
                        { "triggerDirection", false },
                        { "stopLossPrice", false },
                        { "takeProfitPrice", false },
                        { "attachedStopLossTakeProfit", null },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", false },
                            { "PO", false },
                            { "GTD", false },
                        } },
                        { "hedged", false },
                        { "selfTradePrevention", false },
                        { "trailing", false },
                        { "leverage", false },
                        { "marketBuyByCost", false },
                        { "marketBuyRequiresPrice", false },
                        { "iceberg", false },
                    } },
                    { "createOrders", null },
                    { "fetchMyTrades", null },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", null },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrders", null },
                    { "fetchClosedOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", 100000 },
                        { "daysBackCanceled", 1 },
                        { "untilDays", null },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 2000 },
                    } },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
            } },
            { "commonCurrencies", new Dictionary<string, object>() {
                { "STR", "XLM" },
                { "BCHABC", "BCH" },
                { "BCHSV", "BSV" },
                { "DRK", "DASH" },
                { "NEM", "XEM" },
            } },
            { "precisionMode", TICK_SIZE },
        });
    }

    public override object nonce()
    {
        return subtract(this.milliseconds(), getValue(this.options, "timeDifference"));
    }

    /**
     * @method
     * @name indodax#fetchTime
     * @description fetches the current integer timestamp in milliseconds from the exchange server
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Public-RestAPI.md#server-time
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int} the current integer timestamp in milliseconds from the exchange server
     */
    public async override Task<object> fetchTime(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetApiServerTime(parameters);
        //
        //     {
        //         "timezone": "UTC",
        //         "server_time": 1571205969552
        //     }
        //
        return this.safeInteger(response, "server_time");
    }

    /**
     * @method
     * @name indodax#fetchMarkets
     * @description retrieves data on all markets for indodax
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Public-RestAPI.md#pairs
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetApiPairs(parameters);
        //
        //     [
        //         {
        //             "id": "btcidr",
        //             "symbol": "BTCIDR",
        //             "base_currency": "idr",
        //             "traded_currency": "btc",
        //             "traded_currency_unit": "BTC",
        //             "description": "BTC/IDR",
        //             "ticker_id": "btc_idr",
        //             "volume_precision": 0,
        //             "price_precision": 1000,
        //             "price_round": 8,
        //             "pricescale": 1000,
        //             "trade_min_base_currency": 10000,
        //             "trade_min_traded_currency": 0.00007457,
        //             "has_memo": false,
        //             "memo_name": false,
        //             "has_payment_id": false,
        //             "trade_fee_percent": 0.3,
        //             "url_logo": "https://indodax.com/v2/logo/svg/color/btc.svg",
        //             "url_logo_png": "https://indodax.com/v2/logo/png/color/btc.png",
        //             "is_maintenance": 0
        //         }
        //     ]
        //
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object market = getValue(response, i);
            object id = this.safeString(market, "ticker_id");
            object baseId = this.safeString(market, "traded_currency");
            object quoteId = this.safeString(market, "base_currency");
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object isMaintenance = this.safeInteger(market, "is_maintenance");
            ((IList<object>)result).Add(new Dictionary<string, object>() {
                { "id", id },
                { "symbol", add(add(bs, "/"), quote) },
                { "base", bs },
                { "quote", quote },
                { "settle", null },
                { "baseId", baseId },
                { "quoteId", quoteId },
                { "settleId", null },
                { "type", "spot" },
                { "spot", true },
                { "margin", false },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "active", ((bool) isTrue(isMaintenance)) ? false : true },
                { "contract", false },
                { "linear", null },
                { "inverse", null },
                { "taker", this.safeNumber(market, "trade_fee_percent") },
                { "contractSize", null },
                { "expiry", null },
                { "expiryDatetime", null },
                { "strike", null },
                { "optionType", null },
                { "percentage", true },
                { "precision", new Dictionary<string, object>() {
                    { "amount", this.parseNumber("1e-8") },
                    { "price", this.parseNumber(this.parsePrecision(this.safeString(market, "price_round"))) },
                    { "cost", this.parseNumber(this.parsePrecision(this.safeString(market, "volume_precision"))) },
                } },
                { "limits", new Dictionary<string, object>() {
                    { "leverage", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "amount", new Dictionary<string, object>() {
                        { "min", this.safeNumber(market, "trade_min_traded_currency") },
                        { "max", null },
                    } },
                    { "price", new Dictionary<string, object>() {
                        { "min", this.safeNumber(market, "trade_min_base_currency") },
                        { "max", null },
                    } },
                    { "cost", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                } },
                { "created", null },
                { "info", market },
            });
        }
        return result;
    }

    public override object parseBalance(object response)
    {
        object balances = this.safeValue(response, "return", new Dictionary<string, object>() {});
        object free = this.safeValue(balances, "balance", new Dictionary<string, object>() {});
        object used = this.safeValue(balances, "balance_hold", new Dictionary<string, object>() {});
        object timestamp = this.safeTimestamp(balances, "server_time");
        object result = new Dictionary<string, object>() {
            { "info", response },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
        };
        object currencyIds = new List<object>(((IDictionary<string,object>)free).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(currencyIds)); postFixIncrement(ref i))
        {
            object currencyId = getValue(currencyIds, i);
            object code = this.safeCurrencyCode(currencyId);
            object account = this.account();
            ((IDictionary<string,object>)account)["free"] = this.safeString(free, currencyId);
            ((IDictionary<string,object>)account)["used"] = this.safeString(used, currencyId);
            ((IDictionary<string,object>)result)[(string)code] = account;
        }
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name indodax#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#get-info-endpoint
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.privatePostGetInfo(parameters);
        //
        //     {
        //         "success":1,
        //         "return":{
        //             "server_time":**********,
        //             "balance":{
        //                 "idr":167,
        //                 "btc":"0.00000000",
        //                 "1inch":"0.00000000",
        //             },
        //             "balance_hold":{
        //                 "idr":0,
        //                 "btc":"0.00000000",
        //                 "1inch":"0.00000000",
        //             },
        //             "address":{
        //                 "btc":"**********************************",
        //                 "1inch":"******************************************",
        //                 "xrp":"rwWr7KUZ3ZFwzgaDGjKBysADByzxvohQ3C",
        //                 "zrx":"******************************************"
        //             },
        //             "user_id":"276011",
        //             "name":"",
        //             "email":"<EMAIL>",
        //             "profile_picture":null,
        //             "verification_status":"unverified",
        //             "gauth_enable":true
        //         }
        //     }
        //
        return this.parseBalance(response);
    }

    /**
     * @method
     * @name indodax#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Public-RestAPI.md#depth
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "pair", add(getValue(market, "base"), getValue(market, "quote")) },
        };
        object orderbook = await this.publicGetApiDepthPair(this.extend(request, parameters));
        return this.parseOrderBook(orderbook, getValue(market, "symbol"), null, "buy", "sell");
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        //     {
        //         "high":"0.01951",
        //         "low":"0.01877",
        //         "vol_eth":"39.38839319",
        //         "vol_btc":"0.75320886",
        //         "last":"0.01896",
        //         "buy":"0.01896",
        //         "sell":"0.019",
        //         "server_time":1565248908
        //     }
        //
        object symbol = this.safeSymbol(null, market);
        object timestamp = this.safeTimestamp(ticker, "server_time");
        object baseVolume = add("vol_", ((string)getValue(market, "baseId")).ToLower());
        object quoteVolume = add("vol_", ((string)getValue(market, "quoteId")).ToLower());
        object last = this.safeString(ticker, "last");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", symbol },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "high", this.safeString(ticker, "high") },
            { "low", this.safeString(ticker, "low") },
            { "bid", this.safeString(ticker, "buy") },
            { "bidVolume", null },
            { "ask", this.safeString(ticker, "sell") },
            { "askVolume", null },
            { "vwap", null },
            { "open", null },
            { "close", last },
            { "last", last },
            { "previousClose", null },
            { "change", null },
            { "percentage", null },
            { "average", null },
            { "baseVolume", this.safeString(ticker, baseVolume) },
            { "quoteVolume", this.safeString(ticker, quoteVolume) },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name indodax#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Public-RestAPI.md#ticker
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "pair", add(getValue(market, "base"), getValue(market, "quote")) },
        };
        object response = await this.publicGetApiTickerPair(this.extend(request, parameters));
        //
        //     {
        //         "ticker": {
        //             "high":"0.01951",
        //             "low":"0.01877",
        //             "vol_eth":"39.38839319",
        //             "vol_btc":"0.75320886",
        //             "last":"0.01896",
        //             "buy":"0.01896",
        //             "sell":"0.019",
        //             "server_time":1565248908
        //         }
        //     }
        //
        object ticker = this.safeDict(response, "ticker", new Dictionary<string, object>() {});
        return this.parseTicker(ticker, market);
    }

    /**
     * @method
     * @name indodax#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Public-RestAPI.md#ticker-all
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        //
        // {
        //     "tickers": {
        //         "btc_idr": {
        //             "high": "120009000",
        //             "low": "116735000",
        //             "vol_btc": "218.13777777",
        //             "vol_idr": "25800033297",
        //             "last": "117088000",
        //             "buy": "117002000",
        //             "sell": "117078000",
        //             "server_time": 1571207881
        //         }
        //     }
        // }
        //
        object response = await this.publicGetApiTickerAll(parameters);
        object tickers = this.safeDict(response, "tickers", new Dictionary<string, object>() {});
        return this.parseTickers(tickers, symbols);
    }

    public override object parseTrade(object trade, object market = null)
    {
        object timestamp = this.safeTimestamp(trade, "date");
        return this.safeTrade(new Dictionary<string, object>() {
            { "id", this.safeString(trade, "tid") },
            { "info", trade },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", this.safeSymbol(null, market) },
            { "type", null },
            { "side", this.safeString(trade, "type") },
            { "order", null },
            { "takerOrMaker", null },
            { "price", this.safeString(trade, "price") },
            { "amount", this.safeString(trade, "amount") },
            { "cost", null },
            { "fee", null },
        }, market);
    }

    /**
     * @method
     * @name indodax#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Public-RestAPI.md#trades
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "pair", add(getValue(market, "base"), getValue(market, "quote")) },
        };
        object response = await this.publicGetApiTradesPair(this.extend(request, parameters));
        return this.parseTrades(response, market, since, limit);
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        //
        //     {
        //         "Time": 1708416900,
        //         "Open": 51707.52,
        //         "High": 51707.52,
        //         "Low": 51707.52,
        //         "Close": 51707.52,
        //         "Volume": "0"
        //     }
        //
        return new List<object> {this.safeTimestamp(ohlcv, "Time"), this.safeNumber(ohlcv, "Open"), this.safeNumber(ohlcv, "High"), this.safeNumber(ohlcv, "Low"), this.safeNumber(ohlcv, "Close"), this.safeNumber(ohlcv, "Volume")};
    }

    /**
     * @method
     * @name indodax#fetchOHLCV
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms of the latest candle to fetch
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object selectedTimeframe = this.safeString(this.timeframes, timeframe, timeframe);
        object now = this.seconds();
        object until = this.safeInteger(parameters, "until", now);
        parameters = this.omit(parameters, new List<object>() {"until"});
        object request = new Dictionary<string, object>() {
            { "to", until },
            { "tf", selectedTimeframe },
            { "symbol", add(getValue(market, "base"), getValue(market, "quote")) },
        };
        if (isTrue(isEqual(limit, null)))
        {
            limit = 1000;
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["from"] = (Math.Floor(Double.Parse((divide(since, 1000)).ToString())));
        } else
        {
            object duration = this.parseTimeframe(timeframe);
            ((IDictionary<string,object>)request)["from"] = subtract(subtract(now, multiply(limit, duration)), 1);
        }
        object response = await this.publicGetTradingviewHistoryV2(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "Time": 1708416900,
        //             "Open": 51707.52,
        //             "High": 51707.52,
        //             "Low": 51707.52,
        //             "Close": 51707.52,
        //             "Volume": "0"
        //         }
        //     ]
        //
        return this.parseOHLCVs(response, market, timeframe, since, limit);
    }

    public virtual object parseOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "open", "open" },
            { "filled", "closed" },
            { "cancelled", "canceled" },
        };
        return this.safeString(statuses, status, status);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        //     {
        //         "order_id": "12345",
        //         "submit_time": "1392228122",
        //         "price": "8000000",
        //         "type": "sell",
        //         "order_ltc": "100000000",
        //         "remain_ltc": "100000000"
        //     }
        //
        // market closed orders - note that the price is very high
        // and does not reflect actual price the order executed at
        //
        //     {
        //       "order_id": "49326856",
        //       "type": "sell",
        //       "price": "1000000000",
        //       "submit_time": "1618314671",
        //       "finish_time": "1618314671",
        //       "status": "filled",
        //       "order_xrp": "30.45000000",
        //       "remain_xrp": "0.00000000"
        //     }
        //
        // cancelOrder
        //
        //    {
        //        "order_id": 666883,
        //        "client_order_id": "clientx-sj82ks82j",
        //        "type": "sell",
        //        "pair": "btc_idr",
        //        "balance": {
        //            "idr": "33605800",
        //            "btc": "0.00000000",
        //            ...
        //            "frozen_idr": "0",
        //            "frozen_btc": "0.00000000",
        //            ...
        //        }
        //    }
        //
        object side = null;
        if (isTrue(inOp(order, "type")))
        {
            side = getValue(order, "type");
        }
        object status = this.parseOrderStatus(this.safeString(order, "status", "open"));
        object symbol = null;
        object cost = null;
        object price = this.safeString(order, "price");
        object amount = null;
        object remaining = null;
        object marketId = this.safeString(order, "pair");
        market = this.safeMarket(marketId, market);
        if (isTrue(!isEqual(market, null)))
        {
            symbol = getValue(market, "symbol");
            object quoteId = getValue(market, "quoteId");
            object baseId = getValue(market, "baseId");
            if (isTrue(isTrue((isEqual(getValue(market, "quoteId"), "idr"))) && isTrue((inOp(order, "order_rp")))))
            {
                quoteId = "rp";
            }
            if (isTrue(isTrue((isEqual(getValue(market, "baseId"), "idr"))) && isTrue((inOp(order, "remain_rp")))))
            {
                baseId = "rp";
            }
            cost = this.safeString(order, add("order_", quoteId));
            if (!isTrue(cost))
            {
                amount = this.safeString(order, add("order_", baseId));
                remaining = this.safeString(order, add("remain_", baseId));
            }
        }
        object timestamp = this.safeInteger(order, "submit_time");
        object fee = null;
        object id = this.safeString(order, "order_id");
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", order },
            { "id", id },
            { "clientOrderId", this.safeString(order, "client_order_id") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastTradeTimestamp", null },
            { "symbol", symbol },
            { "type", "limit" },
            { "timeInForce", null },
            { "postOnly", null },
            { "side", side },
            { "price", price },
            { "triggerPrice", null },
            { "cost", cost },
            { "average", null },
            { "amount", amount },
            { "filled", null },
            { "remaining", remaining },
            { "status", status },
            { "fee", fee },
            { "trades", null },
        });
    }

    /**
     * @method
     * @name indodax#fetchOrder
     * @description fetches information on an order made by the user
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#get-order-endpoints
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "pair", getValue(market, "id") },
            { "order_id", id },
        };
        object response = await this.privatePostGetOrder(this.extend(request, parameters));
        object orders = getValue(response, "return");
        object order = this.parseOrder(this.extend(new Dictionary<string, object>() {
            { "id", id },
        }, getValue(orders, "order")), market);
        ((IDictionary<string,object>)order)["info"] = response;
        return order;
    }

    /**
     * @method
     * @name indodax#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#open-orders-endpoints
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of  open orders structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["pair"] = getValue(market, "id");
        }
        object response = await this.privatePostOpenOrders(this.extend(request, parameters));
        object rawOrders = getValue(getValue(response, "return"), "orders");
        // { success: 1, return: { orders: null }} if no orders
        if (!isTrue(rawOrders))
        {
            return new List<object>() {};
        }
        // { success: 1, return: { orders: [ ... objects ] }} for orders fetched by symbol
        if (isTrue(!isEqual(symbol, null)))
        {
            return this.parseOrders(rawOrders, market, since, limit);
        }
        // { success: 1, return: { orders: { marketid: [ ... objects ] }}} if all orders are fetched
        object marketIds = new List<object>(((IDictionary<string,object>)rawOrders).Keys);
        object exchangeOrders = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(marketIds)); postFixIncrement(ref i))
        {
            object marketId = getValue(marketIds, i);
            object marketOrders = getValue(rawOrders, marketId);
            market = this.safeMarket(marketId);
            object parsedOrders = this.parseOrders(marketOrders, market, since, limit);
            exchangeOrders = this.arrayConcat(exchangeOrders, parsedOrders);
        }
        return exchangeOrders;
    }

    /**
     * @method
     * @name indodax#fetchClosedOrders
     * @description fetches information on multiple closed orders made by the user
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#order-history
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchClosedOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "pair", getValue(market, "id") },
        };
        object response = await this.privatePostOrderHistory(this.extend(request, parameters));
        object orders = this.parseOrders(getValue(getValue(response, "return"), "orders"), market);
        orders = this.filterBy(orders, "status", "closed");
        return this.filterBySymbolSinceLimit(orders, symbol, since, limit);
    }

    /**
     * @method
     * @name indodax#createOrder
     * @description create a trade order
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#trade-endpoints
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "pair", getValue(market, "id") },
            { "type", side },
            { "price", price },
        };
        object priceIsRequired = false;
        object quantityIsRequired = false;
        if (isTrue(isEqual(type, "market")))
        {
            if (isTrue(isEqual(side, "buy")))
            {
                object quoteAmount = null;
                object cost = this.safeNumber(parameters, "cost");
                parameters = this.omit(parameters, "cost");
                if (isTrue(!isEqual(cost, null)))
                {
                    quoteAmount = this.costToPrecision(symbol, cost);
                } else
                {
                    if (isTrue(isEqual(price, null)))
                    {
                        throw new InvalidOrder ((string)add(this.id, " createOrder() requires the price argument for market buy orders to calculate the total cost to spend (amount * price).")) ;
                    }
                    object amountString = this.numberToString(amount);
                    object priceString = this.numberToString(price);
                    object costRequest = Precise.stringMul(amountString, priceString);
                    quoteAmount = this.costToPrecision(symbol, costRequest);
                }
                ((IDictionary<string,object>)request)[(string)getValue(market, "quoteId")] = quoteAmount;
            } else
            {
                quantityIsRequired = true;
            }
        } else if (isTrue(isEqual(type, "limit")))
        {
            priceIsRequired = true;
            quantityIsRequired = true;
            if (isTrue(isEqual(side, "buy")))
            {
                ((IDictionary<string,object>)request)[(string)getValue(market, "quoteId")] = this.parseToNumeric(Precise.stringMul(this.numberToString(amount), this.numberToString(price)));
            }
        }
        if (isTrue(priceIsRequired))
        {
            if (isTrue(isEqual(price, null)))
            {
                throw new InvalidOrder ((string)add(add(add(this.id, " createOrder() requires a price argument for a "), type), " order")) ;
            }
            ((IDictionary<string,object>)request)["price"] = price;
        }
        if (isTrue(quantityIsRequired))
        {
            ((IDictionary<string,object>)request)[(string)getValue(market, "baseId")] = this.amountToPrecision(symbol, amount);
        }
        object result = await this.privatePostTrade(this.extend(request, parameters));
        object data = this.safeValue(result, "return", new Dictionary<string, object>() {});
        object id = this.safeString(data, "order_id");
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", result },
            { "id", id },
        }, market);
    }

    /**
     * @method
     * @name indodax#cancelOrder
     * @description cancels an open order
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#cancel-order-endpoints
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires a symbol argument")) ;
        }
        object side = this.safeValue(parameters, "side");
        if (isTrue(isEqual(side, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires an extra \"side\" param")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "order_id", id },
            { "pair", getValue(market, "id") },
            { "type", side },
        };
        object response = await this.privatePostCancelOrder(this.extend(request, parameters));
        //
        //    {
        //        "success": 1,
        //        "return": {
        //            "order_id": 666883,
        //            "client_order_id": "clientx-sj82ks82j",
        //            "type": "sell",
        //            "pair": "btc_idr",
        //            "balance": {
        //                "idr": "33605800",
        //                "btc": "0.00000000",
        //                ...
        //                "frozen_idr": "0",
        //                "frozen_btc": "0.00000000",
        //                ...
        //            }
        //        }
        //    }
        //
        object data = this.safeDict(response, "return");
        return this.parseOrder(data);
    }

    /**
     * @method
     * @name indodax#fetchTransactionFee
     * @description fetch the fee for a transaction
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#withdraw-fee-endpoints
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [fee structure]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchTransactionFee(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "currency", getValue(currency, "id") },
        };
        object response = await this.privatePostWithdrawFee(this.extend(request, parameters));
        //
        //     {
        //         "success": 1,
        //         "return": {
        //             "server_time": 1607923272,
        //             "withdraw_fee": 0.005,
        //             "currency": "eth"
        //         }
        //     }
        //
        object data = this.safeValue(response, "return", new Dictionary<string, object>() {});
        object currencyId = this.safeString(data, "currency");
        return new Dictionary<string, object>() {
            { "info", response },
            { "rate", this.safeNumber(data, "withdraw_fee") },
            { "currency", this.safeCurrencyCode(currencyId, currency) },
        };
    }

    /**
     * @method
     * @name indodax#fetchDepositsWithdrawals
     * @description fetch history of deposits and withdrawals
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#transaction-history-endpoints
     * @param {string} [code] unified currency code for the currency of the deposit/withdrawals, default is undefined
     * @param {int} [since] timestamp in ms of the earliest deposit/withdrawal, default is undefined
     * @param {int} [limit] max number of deposit/withdrawals to return, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDepositsWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(since, null)))
        {
            object startTime = slice(this.iso8601(since), 0, 10);
            ((IDictionary<string,object>)request)["start"] = startTime;
            ((IDictionary<string,object>)request)["end"] = slice(this.iso8601(this.milliseconds()), 0, 10);
        }
        object response = await this.privatePostTransHistory(this.extend(request, parameters));
        //
        //     {
        //         "success": 1,
        //         "return": {
        //             "withdraw": {
        //                 "idr": [
        //                     {
        //                         "status": "success",
        //                         "type": "coupon",
        //                         "rp": "115205",
        //                         "fee": "500",
        //                         "amount": "114705",
        //                         "submit_time": "1539844166",
        //                         "success_time": "1539844189",
        //                         "withdraw_id": "1783717",
        //                         "tx": "BTC-IDR-RDTVVO2P-ETD0EVAW-VTNZGMIR-HTNTUAPI-84ULM9OI",
        //                         "sender": "boris",
        //                         "used_by": "viginia88"
        //                     },
        //                     ...
        //                 ],
        //                 "btc": [],
        //                 "abyss": [],
        //                 ...
        //             },
        //             "deposit": {
        //                 "idr": [
        //                     {
        //                         "status": "success",
        //                         "type": "duitku",
        //                         "rp": "393000",
        //                         "fee": "5895",
        //                         "amount": "387105",
        //                         "submit_time": "1576555012",
        //                         "success_time": "1576555012",
        //                         "deposit_id": "3395438",
        //                         "tx": "Duitku OVO Settlement"
        //                     },
        //                     ...
        //                 ],
        //                 "btc": [
        //                     {
        //                         "status": "success",
        //                         "btc": "0.00118769",
        //                         "amount": "0.00118769",
        //                         "success_time": "1539529208",
        //                         "deposit_id": "3602369",
        //                         "tx": "c816aeb35a5b42f389970325a32aff69bb6b2126784dcda8f23b9dd9570d6573"
        //                     },
        //                     ...
        //                 ],
        //                 "abyss": [],
        //                 ...
        //             }
        //         }
        //     }
        //
        object data = this.safeValue(response, "return", new Dictionary<string, object>() {});
        object withdraw = this.safeValue(data, "withdraw", new Dictionary<string, object>() {});
        object deposit = this.safeValue(data, "deposit", new Dictionary<string, object>() {});
        object transactions = new List<object>() {};
        object currency = null;
        if (isTrue(isEqual(code, null)))
        {
            object keys = new List<object>(((IDictionary<string,object>)withdraw).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
            {
                object key = getValue(keys, i);
                transactions = this.arrayConcat(transactions, getValue(withdraw, key));
            }
            keys = new List<object>(((IDictionary<string,object>)deposit).Keys);
            for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
            {
                object key = getValue(keys, i);
                transactions = this.arrayConcat(transactions, getValue(deposit, key));
            }
        } else
        {
            currency = this.currency(code);
            object withdraws = this.safeValue(withdraw, getValue(currency, "id"), new List<object>() {});
            object deposits = this.safeValue(deposit, getValue(currency, "id"), new List<object>() {});
            transactions = this.arrayConcat(withdraws, deposits);
        }
        return this.parseTransactions(transactions, currency, since, limit);
    }

    /**
     * @method
     * @name indodax#withdraw
     * @description make a withdrawal
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#withdraw-coin-endpoints
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address the address to withdraw to
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        var tagparametersVariable = this.handleWithdrawTagAndParams(tag, parameters);
        tag = ((IList<object>)tagparametersVariable)[0];
        parameters = ((IList<object>)tagparametersVariable)[1];
        this.checkAddress(address);
        await this.loadMarkets();
        object currency = this.currency(code);
        // Custom string you need to provide to identify each withdrawal.
        // Will be passed to callback URL (assigned via website to the API key)
        // so your system can identify the request and confirm it.
        // Alphanumeric, max length 255.
        object requestId = this.milliseconds();
        // Alternatively:
        // let requestId = this.uuid ();
        object request = new Dictionary<string, object>() {
            { "currency", getValue(currency, "id") },
            { "withdraw_amount", amount },
            { "withdraw_address", address },
            { "request_id", ((object)requestId).ToString() },
        };
        if (isTrue(tag))
        {
            ((IDictionary<string,object>)request)["withdraw_memo"] = tag;
        }
        object response = await this.privatePostWithdrawCoin(this.extend(request, parameters));
        //
        //     {
        //         "success": 1,
        //         "status": "approved",
        //         "withdraw_currency": "xrp",
        //         "withdraw_address": "rwWr7KUZ3ZFwzgaDGjKBysADByzxvohQ3C",
        //         "withdraw_amount": "10000.00000000",
        //         "fee": "2.00000000",
        //         "amount_after_fee": "9998.00000000",
        //         "submit_time": "1509469200",
        //         "withdraw_id": "xrp-12345",
        //         "txid": "",
        //         "withdraw_memo": "123123"
        //     }
        //
        return this.parseTransaction(response, currency);
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        // withdraw
        //
        //     {
        //         "success": 1,
        //         "status": "approved",
        //         "withdraw_currency": "xrp",
        //         "withdraw_address": "rwWr7KUZ3ZFwzgaDGjKBysADByzxvohQ3C",
        //         "withdraw_amount": "10000.00000000",
        //         "fee": "2.00000000",
        //         "amount_after_fee": "9998.00000000",
        //         "submit_time": "1509469200",
        //         "withdraw_id": "xrp-12345",
        //         "txid": "",
        //         "withdraw_memo": "123123"
        //     }
        //
        // transHistory
        //
        //     {
        //         "status": "success",
        //         "type": "coupon",
        //         "rp": "115205",
        //         "fee": "500",
        //         "amount": "114705",
        //         "submit_time": "1539844166",
        //         "success_time": "1539844189",
        //         "withdraw_id": "1783717",
        //         "tx": "BTC-IDR-RDTVVO2P-ETD0EVAW-VTNZGMIR-HTNTUAPI-84ULM9OI",
        //         "sender": "boris",
        //         "used_by": "viginia88"
        //     }
        //
        //     {
        //         "status": "success",
        //         "btc": "0.00118769",
        //         "amount": "0.00118769",
        //         "success_time": "1539529208",
        //         "deposit_id": "3602369",
        //         "tx": "c816aeb35a5b42f389970325a32aff69bb6b2126784dcda8f23b9dd9570d6573"
        //     },
        object status = this.safeString(transaction, "status");
        object timestamp = this.safeTimestamp2(transaction, "success_time", "submit_time");
        object depositId = this.safeString(transaction, "deposit_id");
        object feeCost = this.safeNumber(transaction, "fee");
        object fee = null;
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "currency", this.safeCurrencyCode(null, currency) },
                { "cost", feeCost },
                { "rate", null },
            };
        }
        return new Dictionary<string, object>() {
            { "id", this.safeString2(transaction, "withdraw_id", "deposit_id") },
            { "txid", this.safeString2(transaction, "txid", "tx") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "network", null },
            { "addressFrom", null },
            { "address", this.safeString(transaction, "withdraw_address") },
            { "addressTo", null },
            { "amount", this.safeNumberN(transaction, new List<object>() {"amount", "withdraw_amount", "deposit_amount"}) },
            { "type", ((bool) isTrue((isEqual(depositId, null)))) ? "withdraw" : "deposit" },
            { "currency", this.safeCurrencyCode(null, currency) },
            { "status", this.parseTransactionStatus(status) },
            { "updated", null },
            { "tagFrom", null },
            { "tag", null },
            { "tagTo", null },
            { "comment", this.safeString(transaction, "withdraw_memo") },
            { "internal", null },
            { "fee", fee },
            { "info", transaction },
        };
    }

    public virtual object parseTransactionStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "success", "ok" },
        };
        return this.safeString(statuses, status, status);
    }

    /**
     * @method
     * @name indodax#fetchDepositAddresses
     * @description fetch deposit addresses for multiple currencies and chain types
     * @see https://github.com/btcid/indodax-official-api-docs/blob/master/Private-RestAPI.md#general-information-on-endpoints
     * @param {string[]} [codes] list of unified currency codes, default is undefined
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a list of [address structures]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> fetchDepositAddresses(object codes = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.privatePostGetInfo(parameters);
        //
        //    {
        //        success: '1',
        //        return: {
        //            server_time: '1708031570',
        //            balance: {
        //                idr: '29952',
        //                ...
        //            },
        //            balance_hold: {
        //                idr: '0',
        //                ...
        //            },
        //            address: {
        //                btc: '**********************************',
        //                ...
        //            },
        //            memo_is_required: {
        //                btc: { mainnet: false },
        //                ...
        //            },
        //            network: {
        //                btc: 'mainnet',
        //                ...
        //            },
        //            user_id: '276011',
        //            name: '',
        //            email: '<EMAIL>',
        //            profile_picture: null,
        //            verification_status: 'unverified',
        //            gauth_enable: true,
        //            withdraw_status: '0'
        //        }
        //    }
        //
        object data = this.safeDict(response, "return");
        object addresses = this.safeDict(data, "address", new Dictionary<string, object>() {});
        object networks = this.safeDict(data, "network", new Dictionary<string, object>() {});
        object addressKeys = new List<object>(((IDictionary<string,object>)addresses).Keys);
        object result = new Dictionary<string, object>() {
            { "info", data },
        };
        for (object i = 0; isLessThan(i, getArrayLength(addressKeys)); postFixIncrement(ref i))
        {
            object marketId = getValue(addressKeys, i);
            object code = this.safeCurrencyCode(marketId);
            object address = this.safeString(addresses, marketId);
            if (isTrue(isTrue((!isEqual(address, null))) && isTrue((isTrue((isEqual(codes, null))) || isTrue((this.inArray(code, codes)))))))
            {
                this.checkAddress(address);
                object network = null;
                if (isTrue(inOp(networks, marketId)))
                {
                    object networkId = this.safeString(networks, marketId);
                    if (isTrue(isGreaterThanOrEqual(getIndexOf(networkId, ","), 0)))
                    {
                        network = new List<object>() {};
                        object networkIds = ((string)networkId).Split(new [] {((string)",")}, StringSplitOptions.None).ToList<object>();
                        for (object j = 0; isLessThan(j, getArrayLength(networkIds)); postFixIncrement(ref j))
                        {
                            ((IList<object>)network).Add(((string)this.networkIdToCode(getValue(networkIds, j))).ToUpper());
                        }
                    } else
                    {
                        network = ((string)this.networkIdToCode(networkId)).ToUpper();
                    }
                }
                ((IDictionary<string,object>)result)[(string)code] = new Dictionary<string, object>() {
                    { "info", new Dictionary<string, object>() {} },
                    { "currency", code },
                    { "network", network },
                    { "address", address },
                    { "tag", null },
                };
            }
        }
        return result;
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object url = getValue(getValue(this.urls, "api"), api);
        if (isTrue(isEqual(api, "public")))
        {
            object query = this.omit(parameters, this.extractParams(path));
            object requestPath = add("/", this.implodeParams(path, parameters));
            url = add(url, requestPath);
            if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)query).Keys))))
            {
                url = add(url, add("?", this.urlencodeWithArrayRepeat(query)));
            }
        } else
        {
            this.checkRequiredCredentials();
            body = this.urlencode(this.extend(new Dictionary<string, object>() {
                { "method", path },
                { "timestamp", this.nonce() },
                { "recvWindow", getValue(this.options, "recvWindow") },
            }, parameters));
            headers = new Dictionary<string, object>() {
                { "Content-Type", "application/x-www-form-urlencoded" },
                { "Key", this.apiKey },
                { "Sign", this.hmac(this.encode(body), this.encode(this.secret), sha512) },
            };
        }
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object code, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        if (isTrue(isEqual(response, null)))
        {
            return null;
        }
        // { success: 0, error: "invalid order." }
        // or
        // [{ data, ... }, { ... }, ... ]
        // {"success":"1","status":"approved","withdraw_currency":"strm","withdraw_address":"******************************************","withdraw_amount":"2165.05767839","fee":"21.11000000","amount_after_fee":"2143.94767839","submit_time":"1730759489","withdraw_id":"strm-3423","txid":""}
        if (isTrue(((response is IList<object>) || (response.GetType().IsGenericType && response.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
        {
            return null;  // public endpoints may return []-arrays
        }
        object error = this.safeValue(response, "error", "");
        if (isTrue(!isTrue((inOp(response, "success"))) && isTrue(isEqual(error, ""))))
        {
            return null;  // no 'success' property on public responses
        }
        object status = this.safeString(response, "success");
        if (isTrue(isEqual(status, "approved")))
        {
            return null;
        }
        if (isTrue(isEqual(this.safeInteger(response, "success", 0), 1)))
        {
            // { success: 1, return: { orders: [] }}
            if (!isTrue((inOp(response, "return"))))
            {
                throw new ExchangeError ((string)add(add(this.id, ": malformed response: "), this.json(response))) ;
            } else
            {
                return null;
            }
        }
        object feedback = add(add(this.id, " "), body);
        this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), error, feedback);
        this.throwBroadlyMatchedException(getValue(this.exceptions, "broad"), error, feedback);
        throw new ExchangeError ((string)feedback) ;
    }
}
