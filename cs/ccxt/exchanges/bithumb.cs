namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class bithumb : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "bithumb" },
            { "name", "Bithumb" },
            { "countries", new List<object>() {"KR"} },
            { "rateLimit", 500 },
            { "pro", true },
            { "has", new Dictionary<string, object>() {
                { "CORS", true },
                { "spot", true },
                { "margin", false },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "addMargin", false },
                { "borrowCrossMargin", false },
                { "borrowIsolatedMargin", false },
                { "borrowMargin", false },
                { "cancelOrder", true },
                { "closeAllPositions", false },
                { "closePosition", false },
                { "createMarketOrder", true },
                { "createOrder", true },
                { "createOrderWithTakeProfitAndStopLoss", false },
                { "createOrderWithTakeProfitAndStopLossWs", false },
                { "createReduceOnlyOrder", false },
                { "fetchBalance", true },
                { "fetchBorrowInterest", false },
                { "fetchBorrowRate", false },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchBorrowRates", false },
                { "fetchBorrowRatesPerSymbol", false },
                { "fetchCrossBorrowRate", false },
                { "fetchCrossBorrowRates", false },
                { "fetchFundingHistory", false },
                { "fetchFundingInterval", false },
                { "fetchFundingIntervals", false },
                { "fetchFundingRate", false },
                { "fetchFundingRateHistory", false },
                { "fetchFundingRates", false },
                { "fetchGreeks", false },
                { "fetchIndexOHLCV", false },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchIsolatedPositions", false },
                { "fetchLeverage", false },
                { "fetchLeverages", false },
                { "fetchLeverageTiers", false },
                { "fetchLiquidations", false },
                { "fetchLongShortRatio", false },
                { "fetchLongShortRatioHistory", false },
                { "fetchMarginAdjustmentHistory", false },
                { "fetchMarginMode", false },
                { "fetchMarginModes", false },
                { "fetchMarketLeverageTiers", false },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMarkPrices", false },
                { "fetchMyLiquidations", false },
                { "fetchMySettlementHistory", false },
                { "fetchOHLCV", true },
                { "fetchOpenInterest", false },
                { "fetchOpenInterestHistory", false },
                { "fetchOpenInterests", false },
                { "fetchOpenOrders", true },
                { "fetchOption", false },
                { "fetchOptionChain", false },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchPosition", false },
                { "fetchPositionHistory", false },
                { "fetchPositionMode", false },
                { "fetchPositions", false },
                { "fetchPositionsForSymbol", false },
                { "fetchPositionsHistory", false },
                { "fetchPositionsRisk", false },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchSettlementHistory", false },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTrades", true },
                { "fetchTransfer", false },
                { "fetchTransfers", false },
                { "fetchVolatilityHistory", false },
                { "reduceMargin", false },
                { "repayCrossMargin", false },
                { "repayIsolatedMargin", false },
                { "setLeverage", false },
                { "setMargin", false },
                { "setMarginMode", false },
                { "setPositionMode", false },
                { "transfer", false },
                { "withdraw", true },
            } },
            { "hostname", "bithumb.com" },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://github.com/user-attachments/assets/c9e0eefb-4777-46b9-8f09-9d7f7c4af82d" },
                { "api", new Dictionary<string, object>() {
                    { "public", "https://api.{hostname}/public" },
                    { "private", "https://api.{hostname}" },
                } },
                { "www", "https://www.bithumb.com" },
                { "doc", "https://apidocs.bithumb.com" },
                { "fees", "https://en.bithumb.com/customer_support/info_fee" },
            } },
            { "api", new Dictionary<string, object>() {
                { "public", new Dictionary<string, object>() {
                    { "get", new List<object>() {"ticker/ALL_{quoteId}", "ticker/{baseId}_{quoteId}", "orderbook/ALL_{quoteId}", "orderbook/{baseId}_{quoteId}", "transaction_history/{baseId}_{quoteId}", "network-info", "assetsstatus/multichain/ALL", "assetsstatus/multichain/{currency}", "withdraw/minimum/ALL", "withdraw/minimum/{currency}", "assetsstatus/ALL", "assetsstatus/{baseId}", "candlestick/{baseId}_{quoteId}/{interval}"} },
                } },
                { "private", new Dictionary<string, object>() {
                    { "post", new List<object>() {"info/account", "info/balance", "info/wallet_address", "info/ticker", "info/orders", "info/user_transactions", "info/order_detail", "trade/place", "trade/cancel", "trade/btc_withdrawal", "trade/krw_deposit", "trade/krw_withdrawal", "trade/market_buy", "trade/market_sell", "trade/stop_limit"} },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "maker", this.parseNumber("0.0025") },
                    { "taker", this.parseNumber("0.0025") },
                } },
            } },
            { "precisionMode", SIGNIFICANT_DIGITS },
            { "features", new Dictionary<string, object>() {
                { "spot", new Dictionary<string, object>() {
                    { "sandbox", false },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "triggerPrice", false },
                        { "triggerPriceType", null },
                        { "triggerDirection", false },
                        { "stopLossPrice", false },
                        { "takeProfitPrice", false },
                        { "attachedStopLossTakeProfit", null },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", false },
                            { "FOK", false },
                            { "PO", false },
                            { "GTD", false },
                        } },
                        { "hedged", false },
                        { "trailing", false },
                        { "leverage", false },
                        { "marketBuyRequiresPrice", false },
                        { "marketBuyByCost", false },
                        { "selfTradePrevention", false },
                        { "iceberg", false },
                    } },
                    { "createOrders", null },
                    { "fetchMyTrades", null },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOrders", null },
                    { "fetchClosedOrders", null },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 1000 },
                    } },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
            } },
            { "exceptions", new Dictionary<string, object>() {
                { "Bad Request(SSL)", typeof(BadRequest) },
                { "Bad Request(Bad Method)", typeof(BadRequest) },
                { "Bad Request.(Auth Data)", typeof(AuthenticationError) },
                { "Not Member", typeof(AuthenticationError) },
                { "Invalid Apikey", typeof(AuthenticationError) },
                { "Method Not Allowed.(Access IP)", typeof(PermissionDenied) },
                { "Method Not Allowed.(BTC Adress)", typeof(InvalidAddress) },
                { "Method Not Allowed.(Access)", typeof(PermissionDenied) },
                { "Database Fail", typeof(ExchangeNotAvailable) },
                { "Invalid Parameter", typeof(BadRequest) },
                { "5600", typeof(ExchangeError) },
                { "Unknown Error", typeof(ExchangeError) },
                { "After May 23th, recent_transactions is no longer, hence users will not be able to connect to recent_transactions", typeof(ExchangeError) },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", "1m" },
                { "3m", "3m" },
                { "5m", "5m" },
                { "10m", "10m" },
                { "30m", "30m" },
                { "1h", "1h" },
                { "6h", "6h" },
                { "12h", "12h" },
                { "1d", "24h" },
            } },
            { "options", new Dictionary<string, object>() {
                { "quoteCurrencies", new Dictionary<string, object>() {
                    { "BTC", new Dictionary<string, object>() {
                        { "limits", new Dictionary<string, object>() {
                            { "cost", new Dictionary<string, object>() {
                                { "min", 0.0002 },
                                { "max", 100 },
                            } },
                        } },
                    } },
                    { "KRW", new Dictionary<string, object>() {
                        { "limits", new Dictionary<string, object>() {
                            { "cost", new Dictionary<string, object>() {
                                { "min", 500 },
                                { "max", 5000000000 },
                            } },
                        } },
                    } },
                } },
            } },
            { "commonCurrencies", new Dictionary<string, object>() {
                { "ALT", "ArchLoot" },
                { "FTC", "FTC2" },
                { "SOC", "Soda Coin" },
            } },
        });
    }

    public override object safeMarket(object marketId = null, object market = null, object delimiter = null, object marketType = null)
    {
        // bithumb has a different type of conflict in markets, because
        // their ids are the base currency (BTC for instance), so we can have
        // multiple "BTC" ids representing the different markets (BTC/ETH, "BTC/DOGE", etc)
        // since they're the same we just need to return one
        return base.safeMarket(marketId, market, delimiter, "spot");
    }

    public override object amountToPrecision(object symbol, object amount)
    {
        return this.decimalToPrecision(amount, TRUNCATE, getValue(getValue(getValue(this.markets, symbol), "precision"), "amount"), DECIMAL_PLACES);
    }

    /**
     * @method
     * @name bithumb#fetchMarkets
     * @description retrieves data on all markets for bithumb
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%ED%98%84%EC%9E%AC%EA%B0%80-%EC%A0%95%EB%B3%B4-%EC%A1%B0%ED%9A%8C-all
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object result = new List<object>() {};
        object quoteCurrencies = this.safeDict(this.options, "quoteCurrencies", new Dictionary<string, object>() {});
        object quotes = new List<object>(((IDictionary<string,object>)quoteCurrencies).Keys);
        object promises = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(quotes)); postFixIncrement(ref i))
        {
            object request = new Dictionary<string, object>() {
                { "quoteId", getValue(quotes, i) },
            };
            ((IList<object>)promises).Add(this.publicGetTickerALLQuoteId(this.extend(request, parameters)));
        }
        object results = await promiseAll(promises);
        for (object i = 0; isLessThan(i, getArrayLength(quotes)); postFixIncrement(ref i))
        {
            object quote = getValue(quotes, i);
            object quoteId = quote;
            object response = getValue(results, i);
            object data = this.safeDict(response, "data");
            object extension = this.safeDict(quoteCurrencies, quote, new Dictionary<string, object>() {});
            object currencyIds = new List<object>(((IDictionary<string,object>)data).Keys);
            for (object j = 0; isLessThan(j, getArrayLength(currencyIds)); postFixIncrement(ref j))
            {
                object currencyId = getValue(currencyIds, j);
                if (isTrue(isEqual(currencyId, "date")))
                {
                    continue;
                }
                object market = getValue(data, currencyId);
                object bs = this.safeCurrencyCode(currencyId);
                object active = true;
                if (isTrue(((market is IList<object>) || (market.GetType().IsGenericType && market.GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
                {
                    object numElements = getArrayLength(market);
                    if (isTrue(isEqual(numElements, 0)))
                    {
                        active = false;
                    }
                }
                object entry = this.deepExtend(new Dictionary<string, object>() {
                    { "id", currencyId },
                    { "symbol", add(add(bs, "/"), quote) },
                    { "base", bs },
                    { "quote", quote },
                    { "settle", null },
                    { "baseId", currencyId },
                    { "quoteId", quoteId },
                    { "settleId", null },
                    { "type", "spot" },
                    { "spot", true },
                    { "margin", false },
                    { "swap", false },
                    { "future", false },
                    { "option", false },
                    { "active", active },
                    { "contract", false },
                    { "linear", null },
                    { "inverse", null },
                    { "contractSize", null },
                    { "expiry", null },
                    { "expiryDateTime", null },
                    { "strike", null },
                    { "optionType", null },
                    { "precision", new Dictionary<string, object>() {
                        { "amount", parseInt("4") },
                        { "price", parseInt("4") },
                    } },
                    { "limits", new Dictionary<string, object>() {
                        { "leverage", new Dictionary<string, object>() {
                            { "min", null },
                            { "max", null },
                        } },
                        { "amount", new Dictionary<string, object>() {
                            { "min", null },
                            { "max", null },
                        } },
                        { "price", new Dictionary<string, object>() {
                            { "min", null },
                            { "max", null },
                        } },
                        { "cost", new Dictionary<string, object>() {} },
                    } },
                    { "created", null },
                    { "info", market },
                }, extension);
                ((IList<object>)result).Add(entry);
            }
        }
        return result;
    }

    public override object parseBalance(object response)
    {
        object result = new Dictionary<string, object>() {
            { "info", response },
        };
        object balances = this.safeDict(response, "data");
        object codes = new List<object>(((IDictionary<string,object>)this.currencies).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(codes)); postFixIncrement(ref i))
        {
            object code = getValue(codes, i);
            object account = this.account();
            object currency = this.currency(code);
            object lowerCurrencyId = this.safeStringLower(currency, "id");
            ((IDictionary<string,object>)account)["total"] = this.safeString(balances, add("total_", lowerCurrencyId));
            ((IDictionary<string,object>)account)["used"] = this.safeString(balances, add("in_use_", lowerCurrencyId));
            ((IDictionary<string,object>)account)["free"] = this.safeString(balances, add("available_", lowerCurrencyId));
            ((IDictionary<string,object>)result)[(string)code] = account;
        }
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name bithumb#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EB%B3%B4%EC%9C%A0%EC%9E%90%EC%82%B0-%EC%A1%B0%ED%9A%8C
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {
            { "currency", "ALL" },
        };
        object response = await this.privatePostInfoBalance(this.extend(request, parameters));
        return this.parseBalance(response);
    }

    /**
     * @method
     * @name bithumb#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%ED%98%B8%EA%B0%80-%EC%A0%95%EB%B3%B4-%EC%A1%B0%ED%9A%8C
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "baseId", getValue(market, "baseId") },
            { "quoteId", getValue(market, "quoteId") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["count"] = limit; // default 30, max 30
        }
        object response = await this.publicGetOrderbookBaseIdQuoteId(this.extend(request, parameters));
        //
        //     {
        //         "status":"0000",
        //         "data":{
        //             "timestamp":"1587621553942",
        //             "payment_currency":"KRW",
        //             "order_currency":"BTC",
        //             "bids":[
        //                 {"price":"8652000","quantity":"0.0043"},
        //                 {"price":"8651000","quantity":"0.0049"},
        //                 {"price":"8650000","quantity":"8.4791"},
        //             ],
        //             "asks":[
        //                 {"price":"8654000","quantity":"0.119"},
        //                 {"price":"8655000","quantity":"0.254"},
        //                 {"price":"8658000","quantity":"0.119"},
        //             ]
        //         }
        //     }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        object timestamp = this.safeInteger(data, "timestamp");
        return this.parseOrderBook(data, symbol, timestamp, "bids", "asks", "price", "quantity");
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        // fetchTicker, fetchTickers
        //
        //     {
        //         "opening_price":"227100",
        //         "closing_price":"228400",
        //         "min_price":"222300",
        //         "max_price":"230000",
        //         "units_traded":"82618.56075337",
        //         "acc_trade_value":"18767376138.6031",
        //         "prev_closing_price":"227100",
        //         "units_traded_24H":"151871.13484676",
        //         "acc_trade_value_24H":"34247610416.8974",
        //         "fluctate_24H":"8700",
        //         "fluctate_rate_24H":"3.96",
        //         "date":"1587710327264", // fetchTickers inject this
        //     }
        //
        object timestamp = this.safeInteger(ticker, "date");
        object symbol = this.safeSymbol(null, market);
        object open = this.safeString(ticker, "opening_price");
        object close = this.safeString(ticker, "closing_price");
        object baseVolume = this.safeString(ticker, "units_traded_24H");
        object quoteVolume = this.safeString(ticker, "acc_trade_value_24H");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", symbol },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "high", this.safeString(ticker, "max_price") },
            { "low", this.safeString(ticker, "min_price") },
            { "bid", this.safeString(ticker, "buy_price") },
            { "bidVolume", null },
            { "ask", this.safeString(ticker, "sell_price") },
            { "askVolume", null },
            { "vwap", null },
            { "open", open },
            { "close", close },
            { "last", close },
            { "previousClose", null },
            { "change", null },
            { "percentage", null },
            { "average", null },
            { "baseVolume", baseVolume },
            { "quoteVolume", quoteVolume },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name bithumb#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%ED%98%84%EC%9E%AC%EA%B0%80-%EC%A0%95%EB%B3%B4-%EC%A1%B0%ED%9A%8C-all
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object result = new Dictionary<string, object>() {};
        object quoteCurrencies = this.safeDict(this.options, "quoteCurrencies", new Dictionary<string, object>() {});
        object quotes = new List<object>(((IDictionary<string,object>)quoteCurrencies).Keys);
        object promises = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(quotes)); postFixIncrement(ref i))
        {
            object request = new Dictionary<string, object>() {
                { "quoteId", getValue(quotes, i) },
            };
            ((IList<object>)promises).Add(this.publicGetTickerALLQuoteId(this.extend(request, parameters)));
        }
        object responses = await promiseAll(promises);
        for (object i = 0; isLessThan(i, getArrayLength(quotes)); postFixIncrement(ref i))
        {
            object quote = getValue(quotes, i);
            object response = getValue(responses, i);
            //
            //     {
            //         "status":"0000",
            //         "data":{
            //             "BTC":{
            //                 "opening_price":"9045000",
            //                 "closing_price":"9132000",
            //                 "min_price":"8938000",
            //                 "max_price":"9168000",
            //                 "units_traded":"4619.79967497",
            //                 "acc_trade_value":"42021363832.5187",
            //                 "prev_closing_price":"9041000",
            //                 "units_traded_24H":"8793.5045804",
            //                 "acc_trade_value_24H":"78933458515.4962",
            //                 "fluctate_24H":"530000",
            //                 "fluctate_rate_24H":"6.16"
            //             },
            //             "date":"1587710878669"
            //         }
            //     }
            //
            object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
            object timestamp = this.safeInteger(data, "date");
            object tickers = this.omit(data, "date");
            object currencyIds = new List<object>(((IDictionary<string,object>)tickers).Keys);
            for (object j = 0; isLessThan(j, getArrayLength(currencyIds)); postFixIncrement(ref j))
            {
                object currencyId = getValue(currencyIds, j);
                object ticker = getValue(data, currencyId);
                object bs = this.safeCurrencyCode(currencyId);
                object symbol = add(add(bs, "/"), quote);
                object market = this.safeMarket(symbol);
                ((IDictionary<string,object>)ticker)["date"] = timestamp;
                ((IDictionary<string,object>)result)[(string)symbol] = this.parseTicker(ticker, market);
            }
        }
        return this.filterByArrayTickers(result, "symbol", symbols);
    }

    /**
     * @method
     * @name bithumb#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%ED%98%84%EC%9E%AC%EA%B0%80-%EC%A0%95%EB%B3%B4-%EC%A1%B0%ED%9A%8C
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "baseId", getValue(market, "baseId") },
            { "quoteId", getValue(market, "quoteId") },
        };
        object response = await this.publicGetTickerBaseIdQuoteId(this.extend(request, parameters));
        //
        //     {
        //         "status":"0000",
        //         "data":{
        //             "opening_price":"227100",
        //             "closing_price":"228400",
        //             "min_price":"222300",
        //             "max_price":"230000",
        //             "units_traded":"82618.56075337",
        //             "acc_trade_value":"18767376138.6031",
        //             "prev_closing_price":"227100",
        //             "units_traded_24H":"151871.13484676",
        //             "acc_trade_value_24H":"34247610416.8974",
        //             "fluctate_24H":"8700",
        //             "fluctate_rate_24H":"3.96",
        //             "date":"1587710327264"
        //         }
        //     }
        //
        object data = this.safeDict(response, "data", new Dictionary<string, object>() {});
        return this.parseTicker(data, market);
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        //
        //     [
        //         1576823400000, // 기준 시간
        //         "8284000", // 시가
        //         "8286000", // 종가
        //         "8289000", // 고가
        //         "8276000", // 저가
        //         "15.41503692" // 거래량
        //     ]
        //
        return new List<object> {this.safeInteger(ohlcv, 0), this.safeNumber(ohlcv, 1), this.safeNumber(ohlcv, 3), this.safeNumber(ohlcv, 4), this.safeNumber(ohlcv, 2), this.safeNumber(ohlcv, 5)};
    }

    /**
     * @method
     * @name bithumb#fetchOHLCV
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @see https://apidocs.bithumb.com/v1.2.0/reference/candlestick-rest-api
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "baseId", getValue(market, "baseId") },
            { "quoteId", getValue(market, "quoteId") },
            { "interval", this.safeString(this.timeframes, timeframe, timeframe) },
        };
        object response = await this.publicGetCandlestickBaseIdQuoteIdInterval(this.extend(request, parameters));
        //
        //     {
        //         "status": "0000",
        //         "data": {
        //             [
        //                 1576823400000, // 기준 시간
        //                 "8284000", // 시가
        //                 "8286000", // 종가
        //                 "8289000", // 고가
        //                 "8276000", // 저가
        //                 "15.41503692" // 거래량
        //             ],
        //             [
        //                 1576824000000, // 기준 시간
        //                 "8284000", // 시가
        //                 "8281000", // 종가
        //                 "8289000", // 고가
        //                 "8275000", // 저가
        //                 "6.19584467" // 거래량
        //             ],
        //         }
        //     }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseOHLCVs(data, market, timeframe, since, limit);
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        // fetchTrades (public)
        //
        //     {
        //         "transaction_date":"2020-04-23 22:21:46",
        //         "type":"ask",
        //         "units_traded":"0.0125",
        //         "price":"8667000",
        //         "total":"108337"
        //     }
        //
        // fetchOrder (private)
        //
        //     {
        //         "transaction_date": "1572497603902030",
        //         "price": "8601000",
        //         "units": "0.005",
        //         "fee_currency": "KRW",
        //         "fee": "107.51",
        //         "total": "43005"
        //     }
        //
        // a workaround for their bug in date format, hours are not 0-padded
        object timestamp = null;
        object transactionDatetime = this.safeString(trade, "transaction_date");
        if (isTrue(!isEqual(transactionDatetime, null)))
        {
            object parts = ((string)transactionDatetime).Split(new [] {((string)" ")}, StringSplitOptions.None).ToList<object>();
            object numParts = getArrayLength(parts);
            if (isTrue(isGreaterThan(numParts, 1)))
            {
                object transactionDate = getValue(parts, 0);
                object transactionTime = getValue(parts, 1);
                if (isTrue(isLessThan(((string)transactionTime).Length, 8)))
                {
                    transactionTime = add("0", transactionTime);
                }
                timestamp = this.parse8601(add(add(transactionDate, " "), transactionTime));
            } else
            {
                timestamp = this.safeIntegerProduct(trade, "transaction_date", 0.001);
            }
        }
        if (isTrue(!isEqual(timestamp, null)))
        {
            timestamp = subtract(timestamp, multiply(9, 3600000)); // they report UTC + 9 hours, server in Korean timezone
        }
        object type = null;
        object side = this.safeString(trade, "type");
        side = ((bool) isTrue((isEqual(side, "ask")))) ? "sell" : "buy";
        object id = this.safeString(trade, "cont_no");
        market = this.safeMarket(null, market);
        object priceString = this.safeString(trade, "price");
        object amountString = this.fixCommaNumber(this.safeString2(trade, "units_traded", "units"));
        object costString = this.safeString(trade, "total");
        object fee = null;
        object feeCostString = this.safeString(trade, "fee");
        if (isTrue(!isEqual(feeCostString, null)))
        {
            object feeCurrencyId = this.safeString(trade, "fee_currency");
            object feeCurrencyCode = this.commonCurrencyCode(feeCurrencyId);
            fee = new Dictionary<string, object>() {
                { "cost", feeCostString },
                { "currency", feeCurrencyCode },
            };
        }
        return this.safeTrade(new Dictionary<string, object>() {
            { "id", id },
            { "info", trade },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", getValue(market, "symbol") },
            { "order", null },
            { "type", type },
            { "side", side },
            { "takerOrMaker", null },
            { "price", priceString },
            { "amount", amountString },
            { "cost", costString },
            { "fee", fee },
        }, market);
    }

    /**
     * @method
     * @name bithumb#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EC%B5%9C%EA%B7%BC-%EC%B2%B4%EA%B2%B0-%EB%82%B4%EC%97%AD
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "baseId", getValue(market, "baseId") },
            { "quoteId", getValue(market, "quoteId") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["count"] = limit; // default 20, max 100
        }
        object response = await this.publicGetTransactionHistoryBaseIdQuoteId(this.extend(request, parameters));
        //
        //     {
        //         "status":"0000",
        //         "data":[
        //             {
        //                 "transaction_date":"2020-04-23 22:21:46",
        //                 "type":"ask",
        //                 "units_traded":"0.0125",
        //                 "price":"8667000",
        //                 "total":"108337"
        //             },
        //         ]
        //     }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseTrades(data, market, since, limit);
    }

    /**
     * @method
     * @name bithumb#createOrder
     * @description create a trade order
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EC%A7%80%EC%A0%95%EA%B0%80-%EC%A3%BC%EB%AC%B8%ED%95%98%EA%B8%B0
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EC%8B%9C%EC%9E%A5%EA%B0%80-%EB%A7%A4%EC%88%98%ED%95%98%EA%B8%B0
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EC%8B%9C%EC%9E%A5%EA%B0%80-%EB%A7%A4%EB%8F%84%ED%95%98%EA%B8%B0
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market' or 'limit'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "order_currency", getValue(market, "id") },
            { "payment_currency", getValue(market, "quote") },
            { "units", amount },
        };
        object method = "privatePostTradePlace";
        if (isTrue(isEqual(type, "limit")))
        {
            ((IDictionary<string,object>)request)["price"] = price;
            ((IDictionary<string,object>)request)["type"] = ((bool) isTrue((isEqual(side, "buy")))) ? "bid" : "ask";
        } else
        {
            method = add("privatePostTradeMarket", this.capitalize(side));
        }
        object response = await ((Task<object>)callDynamically(this, method, new object[] { this.extend(request, parameters) }));
        object id = this.safeString(response, "order_id");
        if (isTrue(isEqual(id, null)))
        {
            throw new InvalidOrder ((string)add(this.id, " createOrder() did not return an order id")) ;
        }
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", response },
            { "symbol", symbol },
            { "type", type },
            { "side", side },
            { "id", id },
        }, market);
    }

    /**
     * @method
     * @name bithumb#fetchOrder
     * @description fetches information on an order made by the user
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EA%B1%B0%EB%9E%98-%EC%A3%BC%EB%AC%B8%EB%82%B4%EC%97%AD-%EC%83%81%EC%84%B8-%EC%A1%B0%ED%9A%8C
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrder() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "order_id", id },
            { "count", 1 },
            { "order_currency", getValue(market, "base") },
            { "payment_currency", getValue(market, "quote") },
        };
        object response = await this.privatePostInfoOrderDetail(this.extend(request, parameters));
        //
        //     {
        //         "status": "0000",
        //         "data": {
        //             "order_date": "1603161798539254",
        //             "type": "ask",
        //             "order_status": "Cancel",
        //             "order_currency": "BTC",
        //             "payment_currency": "KRW",
        //             "watch_price": "0",
        //             "order_price": "13344000",
        //             "order_qty": "0.0125",
        //             "cancel_date": "1603161803809993",
        //             "cancel_type": "사용자취소",
        //             "contract": [
        //                 {
        //                     "transaction_date": "1603161799976383",
        //                     "price": "13344000",
        //                     "units": "0.0015",
        //                     "fee_currency": "KRW",
        //                     "fee": "0",
        //                     "total": "20016"
        //                 }
        //             ],
        //         }
        //     }
        //
        object data = this.safeDict(response, "data");
        return this.parseOrder(this.extend(data, new Dictionary<string, object>() {
            { "order_id", id },
        }), market);
    }

    public virtual object parseOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "Pending", "open" },
            { "Completed", "closed" },
            { "Cancel", "canceled" },
        };
        return this.safeString(statuses, status, status);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        //
        // fetchOrder
        //
        //     {
        //         "transaction_date": "1572497603668315",
        //         "type": "bid",
        //         "order_status": "Completed", // Completed, Cancel ...
        //         "order_currency": "BTC",
        //         "payment_currency": "KRW",
        //         "watch_price": "0", // present in Cancel order
        //         "order_price": "8601000",
        //         "order_qty": "0.007",
        //         "cancel_date": "", // filled in Cancel order
        //         "cancel_type": "", // filled in Cancel order, i.e. 사용자취소
        //         "contract": [
        //             {
        //                 "transaction_date": "1572497603902030",
        //                 "price": "8601000",
        //                 "units": "0.005",
        //                 "fee_currency": "KRW",
        //                 "fee": "107.51",
        //                 "total": "43005"
        //             },
        //         ]
        //     }
        //
        // fetchOpenOrders
        //
        //     {
        //         "order_currency": "BTC",
        //         "payment_currency": "KRW",
        //         "order_id": "C0101000007408440032",
        //         "order_date": "1571728739360570",
        //         "type": "bid",
        //         "units": "5.0",
        //         "units_remaining": "5.0",
        //         "price": "501000",
        //     }
        //
        object timestamp = this.safeIntegerProduct(order, "order_date", 0.001);
        object sideProperty = this.safeString2(order, "type", "side");
        object side = ((bool) isTrue((isEqual(sideProperty, "bid")))) ? "buy" : "sell";
        object status = this.parseOrderStatus(this.safeString(order, "order_status"));
        object price = this.safeString2(order, "order_price", "price");
        object type = "limit";
        if (isTrue(Precise.stringEquals(price, "0")))
        {
            type = "market";
        }
        object amount = this.fixCommaNumber(this.safeString2(order, "order_qty", "units"));
        object remaining = this.fixCommaNumber(this.safeString(order, "units_remaining"));
        if (isTrue(isEqual(remaining, null)))
        {
            if (isTrue(isEqual(status, "closed")))
            {
                remaining = "0";
            } else if (isTrue(!isEqual(status, "canceled")))
            {
                remaining = amount;
            }
        }
        object symbol = null;
        object baseId = this.safeString(order, "order_currency");
        object quoteId = this.safeString(order, "payment_currency");
        object bs = this.safeCurrencyCode(baseId);
        object quote = this.safeCurrencyCode(quoteId);
        if (isTrue(isTrue((!isEqual(bs, null))) && isTrue((!isEqual(quote, null)))))
        {
            symbol = add(add(bs, "/"), quote);
        }
        if (isTrue(isEqual(symbol, null)))
        {
            market = this.safeMarket(null, market);
            symbol = getValue(market, "symbol");
        }
        object id = this.safeString(order, "order_id");
        object rawTrades = this.safeList(order, "contract", new List<object>() {});
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", order },
            { "id", id },
            { "clientOrderId", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastTradeTimestamp", null },
            { "symbol", symbol },
            { "type", type },
            { "timeInForce", null },
            { "postOnly", null },
            { "side", side },
            { "price", price },
            { "triggerPrice", null },
            { "amount", amount },
            { "cost", null },
            { "average", null },
            { "filled", null },
            { "remaining", remaining },
            { "status", status },
            { "fee", null },
            { "trades", rawTrades },
        }, market);
    }

    /**
     * @method
     * @name bithumb#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EA%B1%B0%EB%9E%98-%EC%A3%BC%EB%AC%B8%EB%82%B4%EC%97%AD-%EC%A1%B0%ED%9A%8C
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of open order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOpenOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        if (isTrue(isEqual(limit, null)))
        {
            limit = 100;
        }
        object request = new Dictionary<string, object>() {
            { "count", limit },
            { "order_currency", getValue(market, "base") },
            { "payment_currency", getValue(market, "quote") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["after"] = since;
        }
        object response = await this.privatePostInfoOrders(this.extend(request, parameters));
        //
        //     {
        //         "status": "0000",
        //         "data": [
        //             {
        //                 "order_currency": "BTC",
        //                 "payment_currency": "KRW",
        //                 "order_id": "C0101000007408440032",
        //                 "order_date": "1571728739360570",
        //                 "type": "bid",
        //                 "units": "5.0",
        //                 "units_remaining": "5.0",
        //                 "price": "501000",
        //             }
        //         ]
        //     }
        //
        object data = this.safeList(response, "data", new List<object>() {});
        return this.parseOrders(data, market, since, limit);
    }

    /**
     * @method
     * @name bithumb#cancelOrder
     * @description cancels an open order
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EC%A3%BC%EB%AC%B8-%EC%B7%A8%EC%86%8C%ED%95%98%EA%B8%B0
     * @param {string} id order id
     * @param {string} symbol unified symbol of the market the order was made in
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires a symbol argument")) ;
        }
        object side_in_params = (inOp(parameters, "side"));
        if (!isTrue(side_in_params))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelOrder() requires a `side` parameter (sell or buy)")) ;
        }
        object market = this.market(symbol);
        object side = ((bool) isTrue((isEqual(getValue(parameters, "side"), "buy")))) ? "bid" : "ask";
        parameters = this.omit(parameters, new List<object>() {"side", "currency"});
        // https://github.com/ccxt/ccxt/issues/6771
        object request = new Dictionary<string, object>() {
            { "order_id", id },
            { "type", side },
            { "order_currency", getValue(market, "base") },
            { "payment_currency", getValue(market, "quote") },
        };
        object response = await this.privatePostTradeCancel(this.extend(request, parameters));
        //
        //    {
        //       'status': 'string',
        //    }
        //
        return this.safeOrder(new Dictionary<string, object>() {
            { "info", response },
        });
    }

    public async override Task<object> cancelUnifiedOrder(object order, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object request = new Dictionary<string, object>() {
            { "side", getValue(order, "side") },
        };
        return await this.cancelOrder(getValue(order, "id"), getValue(order, "symbol"), this.extend(request, parameters));
    }

    /**
     * @method
     * @name bithumb#withdraw
     * @description make a withdrawal
     * @see https://apidocs.bithumb.com/v1.2.0/reference/%EC%BD%94%EC%9D%B8-%EC%B6%9C%EA%B8%88%ED%95%98%EA%B8%B0-%EA%B0%9C%EC%9D%B8
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address the address to withdraw to
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        var tagparametersVariable = this.handleWithdrawTagAndParams(tag, parameters);
        tag = ((IList<object>)tagparametersVariable)[0];
        parameters = ((IList<object>)tagparametersVariable)[1];
        this.checkAddress(address);
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "units", amount },
            { "address", address },
            { "currency", getValue(currency, "id") },
        };
        if (isTrue(isTrue(isTrue(isTrue(isTrue(isEqual(code, "XRP")) || isTrue(isEqual(code, "XMR"))) || isTrue(isEqual(code, "EOS"))) || isTrue(isEqual(code, "STEEM"))) || isTrue(isEqual(code, "TON"))))
        {
            object destination = this.safeString(parameters, "destination");
            if (isTrue(isTrue((isEqual(tag, null))) && isTrue((isEqual(destination, null)))))
            {
                throw new ArgumentsRequired ((string)add(add(add(this.id, " "), code), " withdraw() requires a tag argument or an extra destination param")) ;
            } else if (isTrue(!isEqual(tag, null)))
            {
                ((IDictionary<string,object>)request)["destination"] = tag;
            }
        }
        object response = await this.privatePostTradeBtcWithdrawal(this.extend(request, parameters));
        //
        // { "status" : "0000"}
        //
        return this.parseTransaction(response, currency);
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        // withdraw
        //
        //     { "status" : "0000"}
        //
        currency = this.safeCurrency(null, currency);
        return new Dictionary<string, object>() {
            { "id", null },
            { "txid", null },
            { "timestamp", null },
            { "datetime", null },
            { "network", null },
            { "addressFrom", null },
            { "address", null },
            { "addressTo", null },
            { "amount", null },
            { "type", null },
            { "currency", getValue(currency, "code") },
            { "status", null },
            { "updated", null },
            { "tagFrom", null },
            { "tag", null },
            { "tagTo", null },
            { "comment", null },
            { "internal", null },
            { "fee", null },
            { "info", transaction },
        };
    }

    public virtual object fixCommaNumber(object numberStr)
    {
        // some endpoints need this https://github.com/ccxt/ccxt/issues/11031
        if (isTrue(isEqual(numberStr, null)))
        {
            return null;
        }
        object finalNumberStr = numberStr;
        while (isGreaterThan(getIndexOf(finalNumberStr, ","), -1))
        {
            finalNumberStr = ((string)finalNumberStr).Replace((string)",", (string)"");
        }
        return finalNumberStr;
    }

    public override object nonce()
    {
        return this.milliseconds();
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object endpoint = add("/", this.implodeParams(path, parameters));
        object url = add(this.implodeHostname(getValue(getValue(this.urls, "api"), api)), endpoint);
        object query = this.omit(parameters, this.extractParams(path));
        if (isTrue(isEqual(api, "public")))
        {
            if (isTrue(getArrayLength(new List<object>(((IDictionary<string,object>)query).Keys))))
            {
                url = add(url, add("?", this.urlencode(query)));
            }
        } else
        {
            this.checkRequiredCredentials();
            body = this.urlencode(this.extend(new Dictionary<string, object>() {
                { "endpoint", endpoint },
            }, query));
            object nonce = ((object)this.nonce()).ToString();
            object auth = add(add(add(add(endpoint, " "), body), " "), nonce); // eslint-disable-line quotes
            object signature = this.hmac(this.encode(auth), this.encode(this.secret), sha512);
            object signature64 = this.stringToBase64(signature);
            headers = new Dictionary<string, object>() {
                { "Accept", "application/json" },
                { "Content-Type", "application/x-www-form-urlencoded" },
                { "Api-Key", this.apiKey },
                { "Api-Sign", signature64 },
                { "Api-Nonce", nonce },
            };
        }
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object httpCode, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        if (isTrue(isEqual(response, null)))
        {
            return null;  // fallback to default error handler
        }
        if (isTrue(inOp(response, "status")))
        {
            //
            //     {"status":"5100","message":"After May 23th, recent_transactions is no longer, hence users will not be able to connect to recent_transactions"}
            //
            object status = this.safeString(response, "status");
            object message = this.safeString(response, "message");
            if (isTrue(!isEqual(status, null)))
            {
                if (isTrue(isEqual(status, "0000")))
                {
                    return null;  // no error
                } else if (isTrue(isEqual(message, "거래 진행중인 내역이 존재하지 않습니다.")))
                {
                    // https://github.com/ccxt/ccxt/issues/9017
                    return null;  // no error
                }
                object feedback = add(add(this.id, " "), message);
                this.throwExactlyMatchedException(this.exceptions, status, feedback);
                this.throwExactlyMatchedException(this.exceptions, message, feedback);
                throw new ExchangeError ((string)feedback) ;
            }
        }
        return null;
    }
}
