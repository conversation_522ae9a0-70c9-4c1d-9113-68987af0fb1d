namespace ccxt;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

public partial class coinsph : Exchange
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "coinsph" },
            { "name", "Coins.ph" },
            { "countries", new List<object>() {"PH"} },
            { "version", "v1" },
            { "rateLimit", 50 },
            { "certified", false },
            { "pro", false },
            { "has", new Dictionary<string, object>() {
                { "CORS", null },
                { "spot", true },
                { "margin", false },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "addMargin", false },
                { "borrowCrossMargin", false },
                { "borrowIsolatedMargin", false },
                { "borrowMargin", false },
                { "cancelAllOrders", true },
                { "cancelOrder", true },
                { "cancelOrders", false },
                { "closeAllPositions", false },
                { "closePosition", false },
                { "createDepositAddress", false },
                { "createMarketBuyOrderWithCost", true },
                { "createMarketOrderWithCost", false },
                { "createMarketSellOrderWithCost", false },
                { "createOrder", true },
                { "createOrderWithTakeProfitAndStopLoss", false },
                { "createOrderWithTakeProfitAndStopLossWs", false },
                { "createPostOnlyOrder", false },
                { "createReduceOnlyOrder", false },
                { "createStopLimitOrder", true },
                { "createStopMarketOrder", true },
                { "createStopOrder", true },
                { "deposit", true },
                { "editOrder", false },
                { "fetchAccounts", false },
                { "fetchBalance", true },
                { "fetchBidsAsks", false },
                { "fetchBorrowInterest", false },
                { "fetchBorrowRate", false },
                { "fetchBorrowRateHistories", false },
                { "fetchBorrowRateHistory", false },
                { "fetchBorrowRates", false },
                { "fetchBorrowRatesPerSymbol", false },
                { "fetchCanceledOrders", false },
                { "fetchClosedOrder", false },
                { "fetchClosedOrders", true },
                { "fetchCrossBorrowRate", false },
                { "fetchCrossBorrowRates", false },
                { "fetchCurrencies", true },
                { "fetchDeposit", null },
                { "fetchDepositAddress", true },
                { "fetchDepositAddresses", false },
                { "fetchDepositAddressesByNetwork", false },
                { "fetchDeposits", true },
                { "fetchDepositWithdrawFee", false },
                { "fetchDepositWithdrawFees", false },
                { "fetchFundingHistory", false },
                { "fetchFundingInterval", false },
                { "fetchFundingIntervals", false },
                { "fetchFundingRate", false },
                { "fetchFundingRateHistory", false },
                { "fetchFundingRates", false },
                { "fetchGreeks", false },
                { "fetchIndexOHLCV", false },
                { "fetchIsolatedBorrowRate", false },
                { "fetchIsolatedBorrowRates", false },
                { "fetchIsolatedPositions", false },
                { "fetchL3OrderBook", false },
                { "fetchLedger", false },
                { "fetchLeverage", false },
                { "fetchLeverages", false },
                { "fetchLeverageTiers", false },
                { "fetchLiquidations", false },
                { "fetchLongShortRatio", false },
                { "fetchLongShortRatioHistory", false },
                { "fetchMarginAdjustmentHistory", false },
                { "fetchMarginMode", false },
                { "fetchMarginModes", false },
                { "fetchMarketLeverageTiers", false },
                { "fetchMarkets", true },
                { "fetchMarkOHLCV", false },
                { "fetchMarkPrices", false },
                { "fetchMyLiquidations", false },
                { "fetchMySettlementHistory", false },
                { "fetchMyTrades", true },
                { "fetchOHLCV", true },
                { "fetchOpenInterest", false },
                { "fetchOpenInterestHistory", false },
                { "fetchOpenInterests", false },
                { "fetchOpenOrder", null },
                { "fetchOpenOrders", true },
                { "fetchOption", false },
                { "fetchOptionChain", false },
                { "fetchOrder", true },
                { "fetchOrderBook", true },
                { "fetchOrderBooks", false },
                { "fetchOrders", false },
                { "fetchOrderTrades", true },
                { "fetchPosition", false },
                { "fetchPositionHistory", false },
                { "fetchPositionMode", false },
                { "fetchPositions", false },
                { "fetchPositionsForSymbol", false },
                { "fetchPositionsHistory", false },
                { "fetchPositionsRisk", false },
                { "fetchPremiumIndexOHLCV", false },
                { "fetchSettlementHistory", false },
                { "fetchStatus", true },
                { "fetchTicker", true },
                { "fetchTickers", true },
                { "fetchTime", true },
                { "fetchTrades", true },
                { "fetchTradingFee", true },
                { "fetchTradingFees", true },
                { "fetchTradingLimits", false },
                { "fetchTransactionFee", false },
                { "fetchTransactionFees", false },
                { "fetchTransactions", false },
                { "fetchTransfers", false },
                { "fetchVolatilityHistory", false },
                { "fetchWithdrawal", null },
                { "fetchWithdrawals", true },
                { "fetchWithdrawalWhitelist", false },
                { "reduceMargin", false },
                { "repayCrossMargin", false },
                { "repayIsolatedMargin", false },
                { "repayMargin", false },
                { "setLeverage", false },
                { "setMargin", false },
                { "setMarginMode", false },
                { "setPositionMode", false },
                { "signIn", false },
                { "transfer", false },
                { "withdraw", true },
                { "ws", false },
            } },
            { "timeframes", new Dictionary<string, object>() {
                { "1m", "1m" },
                { "3m", "3m" },
                { "5m", "5m" },
                { "15m", "15m" },
                { "30m", "30m" },
                { "1h", "1h" },
                { "2h", "2h" },
                { "4h", "4h" },
                { "6h", "6h" },
                { "8h", "8h" },
                { "12h", "12h" },
                { "1d", "1d" },
                { "3d", "3d" },
                { "1w", "1w" },
                { "1M", "1M" },
            } },
            { "urls", new Dictionary<string, object>() {
                { "logo", "https://user-images.githubusercontent.com/1294454/225719995-48ab2026-4ddb-496c-9da7-0d7566617c9b.jpg" },
                { "api", new Dictionary<string, object>() {
                    { "public", "https://api.pro.coins.ph" },
                    { "private", "https://api.pro.coins.ph" },
                } },
                { "www", "https://coins.ph/" },
                { "doc", new List<object>() {"https://coins-docs.github.io/rest-api"} },
                { "fees", "https://support.coins.ph/hc/en-us/sections/4407198694681-Limits-Fees" },
            } },
            { "api", new Dictionary<string, object>() {
                { "public", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "openapi/v1/ping", 1 },
                        { "openapi/v1/time", 1 },
                        { "openapi/quote/v1/ticker/24hr", new Dictionary<string, object>() {
                            { "cost", 1 },
                            { "noSymbolAndNoSymbols", 40 },
                            { "byNumberOfSymbols", new List<object>() {new List<object>() {101, 40}, new List<object>() {21, 20}, new List<object>() {0, 1}} },
                        } },
                        { "openapi/quote/v1/ticker/price", new Dictionary<string, object>() {
                            { "cost", 1 },
                            { "noSymbol", 2 },
                        } },
                        { "openapi/quote/v1/ticker/bookTicker", new Dictionary<string, object>() {
                            { "cost", 1 },
                            { "noSymbol", 2 },
                        } },
                        { "openapi/v1/exchangeInfo", 10 },
                        { "openapi/quote/v1/depth", new Dictionary<string, object>() {
                            { "cost", 1 },
                            { "byLimit", new List<object>() {new List<object>() {101, 5}, new List<object>() {0, 1}} },
                        } },
                        { "openapi/quote/v1/klines", 1 },
                        { "openapi/quote/v1/trades", 1 },
                        { "openapi/v1/pairs", 1 },
                        { "openapi/quote/v1/avgPrice", 1 },
                    } },
                } },
                { "private", new Dictionary<string, object>() {
                    { "get", new Dictionary<string, object>() {
                        { "openapi/wallet/v1/config/getall", 10 },
                        { "openapi/wallet/v1/deposit/address", 10 },
                        { "openapi/wallet/v1/deposit/history", 1 },
                        { "openapi/wallet/v1/withdraw/history", 1 },
                        { "openapi/v1/account", 10 },
                        { "openapi/v1/openOrders", new Dictionary<string, object>() {
                            { "cost", 3 },
                            { "noSymbol", 40 },
                        } },
                        { "openapi/v1/asset/tradeFee", 1 },
                        { "openapi/v1/order", 2 },
                        { "openapi/v1/historyOrders", new Dictionary<string, object>() {
                            { "cost", 10 },
                            { "noSymbol", 40 },
                        } },
                        { "openapi/v1/myTrades", 10 },
                        { "openapi/v1/capital/deposit/history", 1 },
                        { "openapi/v1/capital/withdraw/history", 1 },
                        { "openapi/v3/payment-request/get-payment-request", 1 },
                        { "merchant-api/v1/get-invoices", 1 },
                        { "openapi/account/v3/crypto-accounts", 1 },
                        { "openapi/transfer/v3/transfers/{id}", 1 },
                    } },
                    { "post", new Dictionary<string, object>() {
                        { "openapi/wallet/v1/withdraw/apply", 600 },
                        { "openapi/v1/order/test", 1 },
                        { "openapi/v1/order", 1 },
                        { "openapi/v1/capital/withdraw/apply", 1 },
                        { "openapi/v1/capital/deposit/apply", 1 },
                        { "openapi/v3/payment-request/payment-requests", 1 },
                        { "openapi/v3/payment-request/delete-payment-request", 1 },
                        { "openapi/v3/payment-request/payment-request-reminder", 1 },
                        { "openapi/v1/userDataStream", 1 },
                        { "merchant-api/v1/invoices", 1 },
                        { "merchant-api/v1/invoices-cancel", 1 },
                        { "openapi/convert/v1/get-supported-trading-pairs", 1 },
                        { "openapi/convert/v1/get-quote", 1 },
                        { "openapi/convert/v1/accpet-quote", 1 },
                        { "openapi/fiat/v1/support-channel", 1 },
                        { "openapi/fiat/v1/cash-out", 1 },
                        { "openapi/fiat/v1/history", 1 },
                        { "openapi/migration/v4/sellorder", 1 },
                        { "openapi/migration/v4/validate-field", 1 },
                        { "openapi/transfer/v3/transfers", 1 },
                    } },
                    { "delete", new Dictionary<string, object>() {
                        { "openapi/v1/order", 1 },
                        { "openapi/v1/openOrders", 1 },
                        { "openapi/v1/userDataStream", 1 },
                    } },
                } },
            } },
            { "fees", new Dictionary<string, object>() {
                { "trading", new Dictionary<string, object>() {
                    { "feeSide", "get" },
                    { "tierBased", true },
                    { "percentage", true },
                    { "maker", this.parseNumber("0.0025") },
                    { "taker", this.parseNumber("0.003") },
                    { "tiers", new Dictionary<string, object>() {
                        { "taker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.003")}, new List<object> {this.parseNumber("500000"), this.parseNumber("0.0027")}, new List<object> {this.parseNumber("1000000"), this.parseNumber("0.0024")}, new List<object> {this.parseNumber("2500000"), this.parseNumber("0.002")}, new List<object> {this.parseNumber("5000000"), this.parseNumber("0.0018")}, new List<object> {this.parseNumber("10000000"), this.parseNumber("0.0015")}, new List<object> {this.parseNumber("1********"), this.parseNumber("0.0012")}, new List<object> {this.parseNumber("5********"), this.parseNumber("0.0009")}, new List<object> {this.parseNumber("1********0"), this.parseNumber("0.0007")}, new List<object> {this.parseNumber("25********"), this.parseNumber("0.0005")}} },
                        { "maker", new List<object>() {new List<object> {this.parseNumber("0"), this.parseNumber("0.0025")}, new List<object> {this.parseNumber("500000"), this.parseNumber("0.0022")}, new List<object> {this.parseNumber("1000000"), this.parseNumber("0.0018")}, new List<object> {this.parseNumber("2500000"), this.parseNumber("0.0015")}, new List<object> {this.parseNumber("5000000"), this.parseNumber("0.0012")}, new List<object> {this.parseNumber("10000000"), this.parseNumber("0.001")}, new List<object> {this.parseNumber("1********"), this.parseNumber("0.0008")}, new List<object> {this.parseNumber("5********"), this.parseNumber("0.0007")}, new List<object> {this.parseNumber("1********0"), this.parseNumber("0.0006")}, new List<object> {this.parseNumber("25********"), this.parseNumber("0.0005")}} },
                    } },
                } },
            } },
            { "precisionMode", TICK_SIZE },
            { "options", new Dictionary<string, object>() {
                { "createMarketBuyOrderRequiresPrice", true },
                { "withdraw", new Dictionary<string, object>() {
                    { "warning", false },
                } },
                { "deposit", new Dictionary<string, object>() {
                    { "warning", false },
                } },
                { "createOrder", new Dictionary<string, object>() {
                    { "timeInForce", "GTC" },
                    { "newOrderRespType", new Dictionary<string, object>() {
                        { "market", "FULL" },
                        { "limit", "FULL" },
                    } },
                } },
                { "fetchTicker", new Dictionary<string, object>() {
                    { "method", "publicGetOpenapiQuoteV1Ticker24hr" },
                } },
                { "fetchTickers", new Dictionary<string, object>() {
                    { "method", "publicGetOpenapiQuoteV1Ticker24hr" },
                } },
                { "networks", new Dictionary<string, object>() {
                    { "TRC20", "TRX" },
                    { "ERC20", "ETH" },
                    { "BEP20", "BSC" },
                    { "ARB", "ARBITRUM" },
                } },
            } },
            { "features", new Dictionary<string, object>() {
                { "spot", new Dictionary<string, object>() {
                    { "sandbox", false },
                    { "createOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "triggerPrice", true },
                        { "triggerPriceType", null },
                        { "triggerDirection", false },
                        { "stopLossPrice", false },
                        { "takeProfitPrice", false },
                        { "attachedStopLossTakeProfit", null },
                        { "timeInForce", new Dictionary<string, object>() {
                            { "IOC", true },
                            { "FOK", true },
                            { "PO", false },
                            { "GTD", false },
                        } },
                        { "hedged", false },
                        { "trailing", false },
                        { "leverage", false },
                        { "marketBuyByCost", true },
                        { "marketBuyRequiresPrice", false },
                        { "selfTradePrevention", true },
                        { "iceberg", false },
                    } },
                    { "createOrders", null },
                    { "fetchMyTrades", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", 100000 },
                        { "untilDays", 100000 },
                        { "symbolRequired", true },
                    } },
                    { "fetchOrder", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOpenOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", null },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", false },
                    } },
                    { "fetchOrders", null },
                    { "fetchClosedOrders", new Dictionary<string, object>() {
                        { "marginMode", false },
                        { "limit", 1000 },
                        { "daysBack", 100000 },
                        { "daysBackCanceled", 1 },
                        { "untilDays", 100000 },
                        { "trigger", false },
                        { "trailing", false },
                        { "symbolRequired", true },
                    } },
                    { "fetchOHLCV", new Dictionary<string, object>() {
                        { "limit", 1000 },
                    } },
                } },
                { "swap", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
                { "future", new Dictionary<string, object>() {
                    { "linear", null },
                    { "inverse", null },
                } },
            } },
            { "exceptions", new Dictionary<string, object>() {
                { "exact", new Dictionary<string, object>() {
                    { "-1000", typeof(BadRequest) },
                    { "-1001", typeof(BadRequest) },
                    { "-1002", typeof(AuthenticationError) },
                    { "-1003", typeof(RateLimitExceeded) },
                    { "-1004", typeof(InvalidOrder) },
                    { "-1006", typeof(BadResponse) },
                    { "-1007", typeof(BadResponse) },
                    { "-1014", typeof(InvalidOrder) },
                    { "-1015", typeof(RateLimitExceeded) },
                    { "-1016", typeof(NotSupported) },
                    { "-1020", typeof(NotSupported) },
                    { "-1021", typeof(BadRequest) },
                    { "-1022", typeof(BadRequest) },
                    { "-1023", typeof(AuthenticationError) },
                    { "-1024", typeof(BadRequest) },
                    { "-1025", typeof(BadRequest) },
                    { "-1030", typeof(ExchangeError) },
                    { "-1100", typeof(BadRequest) },
                    { "-1101", typeof(BadRequest) },
                    { "-1102", typeof(BadRequest) },
                    { "-1103", typeof(BadRequest) },
                    { "-1104", typeof(BadRequest) },
                    { "-1105", typeof(BadRequest) },
                    { "-1106", typeof(BadRequest) },
                    { "-1111", typeof(BadRequest) },
                    { "-1112", typeof(BadResponse) },
                    { "-1114", typeof(BadRequest) },
                    { "-1115", typeof(InvalidOrder) },
                    { "-1116", typeof(InvalidOrder) },
                    { "-1117", typeof(InvalidOrder) },
                    { "-1118", typeof(InvalidOrder) },
                    { "-1119", typeof(InvalidOrder) },
                    { "-1120", typeof(BadRequest) },
                    { "-1121", typeof(BadSymbol) },
                    { "-1122", typeof(InvalidOrder) },
                    { "-1125", typeof(BadRequest) },
                    { "-1127", typeof(BadRequest) },
                    { "-1128", typeof(BadRequest) },
                    { "-1130", typeof(BadRequest) },
                    { "-1131", typeof(InsufficientFunds) },
                    { "-1132", typeof(InvalidOrder) },
                    { "-1133", typeof(InvalidOrder) },
                    { "-1134", typeof(InvalidOrder) },
                    { "-1135", typeof(InvalidOrder) },
                    { "-1136", typeof(InvalidOrder) },
                    { "-1137", typeof(InvalidOrder) },
                    { "-1138", typeof(InvalidOrder) },
                    { "-1139", typeof(InvalidOrder) },
                    { "-1140", typeof(InvalidOrder) },
                    { "-1141", typeof(DuplicateOrderId) },
                    { "-1142", typeof(InvalidOrder) },
                    { "-1143", typeof(OrderNotFound) },
                    { "-1144", typeof(InvalidOrder) },
                    { "-1145", typeof(InvalidOrder) },
                    { "-1146", typeof(InvalidOrder) },
                    { "-1147", typeof(InvalidOrder) },
                    { "-1148", typeof(InvalidOrder) },
                    { "-1149", typeof(InvalidOrder) },
                    { "-1150", typeof(InvalidOrder) },
                    { "-1151", typeof(BadSymbol) },
                    { "-1152", typeof(NotSupported) },
                    { "-1153", typeof(AuthenticationError) },
                    { "-1154", typeof(BadRequest) },
                    { "-1155", typeof(BadRequest) },
                    { "-1156", typeof(InvalidOrder) },
                    { "-1157", typeof(BadSymbol) },
                    { "-1158", typeof(InvalidOrder) },
                    { "-1159", typeof(InvalidOrder) },
                    { "-1160", typeof(BadRequest) },
                    { "-1161", typeof(BadRequest) },
                    { "-2010", typeof(InvalidOrder) },
                    { "-2013", typeof(OrderNotFound) },
                    { "-2011", typeof(BadRequest) },
                    { "-2014", typeof(BadRequest) },
                    { "-2015", typeof(AuthenticationError) },
                    { "-2016", typeof(BadResponse) },
                    { "-3126", typeof(InvalidOrder) },
                    { "-3127", typeof(InvalidOrder) },
                    { "-4001", typeof(BadRequest) },
                    { "-100011", typeof(BadSymbol) },
                    { "-100012", typeof(BadSymbol) },
                    { "-30008", typeof(InsufficientFunds) },
                    { "-30036", typeof(InsufficientFunds) },
                    { "403", typeof(ExchangeNotAvailable) },
                } },
                { "broad", new Dictionary<string, object>() {
                    { "Unknown order sent", typeof(OrderNotFound) },
                    { "Duplicate order sent", typeof(DuplicateOrderId) },
                    { "Market is closed", typeof(BadSymbol) },
                    { "Account has insufficient balance for requested action", typeof(InsufficientFunds) },
                    { "Market orders are not supported for this symbol", typeof(BadSymbol) },
                    { "Iceberg orders are not supported for this symbol", typeof(BadSymbol) },
                    { "Stop loss orders are not supported for this symbol", typeof(BadSymbol) },
                    { "Stop loss limit orders are not supported for this symbol", typeof(BadSymbol) },
                    { "Take profit orders are not supported for this symbol", typeof(BadSymbol) },
                    { "Take profit limit orders are not supported for this symbol", typeof(BadSymbol) },
                    { "Price* QTY is zero or less", typeof(BadRequest) },
                    { "IcebergQty exceeds QTY", typeof(BadRequest) },
                    { "This action disabled is on this account", typeof(PermissionDenied) },
                    { "Unsupported order combination", typeof(InvalidOrder) },
                    { "Order would trigger immediately", typeof(InvalidOrder) },
                    { "Cancel order is invalid. Check origClOrdId and orderId", typeof(InvalidOrder) },
                    { "Order would immediately match and take", typeof(OrderImmediatelyFillable) },
                    { "PRICE_FILTER", typeof(InvalidOrder) },
                    { "LOT_SIZE", typeof(InvalidOrder) },
                    { "MIN_NOTIONAL", typeof(InvalidOrder) },
                    { "MAX_NUM_ORDERS", typeof(InvalidOrder) },
                    { "MAX_ALGO_ORDERS", typeof(InvalidOrder) },
                    { "BROKER_MAX_NUM_ORDERS", typeof(InvalidOrder) },
                    { "BROKER_MAX_ALGO_ORDERS", typeof(InvalidOrder) },
                    { "ICEBERG_PARTS", typeof(BadRequest) },
                } },
            } },
        });
    }

    /**
     * @method
     * @name coinsph#fetchCurrencies
     * @description fetches all available currencies on an exchange
     * @see https://docs.coins.ph/rest-api/#all-coins-information-user_data
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} an associative dictionary of currencies
     */
    public async override Task<object> fetchCurrencies(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (!isTrue(this.checkRequiredCredentials(false)))
        {
            return null;
        }
        object response = await this.privateGetOpenapiWalletV1ConfigGetall(parameters);
        //
        //    [
        //        {
        //            "coin": "PHP",
        //            "name": "PHP",
        //            "depositAllEnable": false,
        //            "withdrawAllEnable": false,
        //            "free": "0",
        //            "locked": "0",
        //            "transferPrecision": "2",
        //            "transferMinQuantity": "0",
        //            "networkList": [],
        //            "legalMoney": true
        //        },
        //        {
        //            "coin": "USDT",
        //            "name": "USDT",
        //            "depositAllEnable": true,
        //            "withdrawAllEnable": true,
        //            "free": "0",
        //            "locked": "0",
        //            "transferPrecision": "8",
        //            "transferMinQuantity": "0",
        //            "networkList": [
        //                {
        //                    "addressRegex": "^0x[0-9a-fA-F]{40}$",
        //                    "memoRegex": " ",
        //                    "network": "ETH",
        //                    "name": "Ethereum (ERC20)",
        //                    "depositEnable": true,
        //                    "minConfirm": "12",
        //                    "unLockConfirm": "-1",
        //                    "withdrawDesc": "",
        //                    "withdrawEnable": true,
        //                    "withdrawFee": "6",
        //                    "withdrawIntegerMultiple": "0.000001",
        //                    "withdrawMax": "500000",
        //                    "withdrawMin": "10",
        //                    "sameAddress": false
        //                },
        //                {
        //                    "addressRegex": "^T[0-9a-zA-Z]{33}$",
        //                    "memoRegex": "",
        //                    "network": "TRX",
        //                    "name": "TRON",
        //                    "depositEnable": true,
        //                    "minConfirm": "19",
        //                    "unLockConfirm": "-1",
        //                    "withdrawDesc": "",
        //                    "withdrawEnable": true,
        //                    "withdrawFee": "3",
        //                    "withdrawIntegerMultiple": "0.000001",
        //                    "withdrawMax": "1000000",
        //                    "withdrawMin": "20",
        //                    "sameAddress": false
        //                }
        //            ],
        //            "legalMoney": false
        //        }
        //    ]
        //
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object entry = getValue(response, i);
            object id = this.safeString(entry, "coin");
            object code = this.safeCurrencyCode(id);
            object isFiat = this.safeBool(entry, "isLegalMoney");
            object networkList = this.safeList(entry, "networkList", new List<object>() {});
            object networks = new Dictionary<string, object>() {};
            for (object j = 0; isLessThan(j, getArrayLength(networkList)); postFixIncrement(ref j))
            {
                object networkItem = getValue(networkList, j);
                object network = this.safeString(networkItem, "network");
                object networkCode = this.networkIdToCode(network);
                ((IDictionary<string,object>)networks)[(string)networkCode] = new Dictionary<string, object>() {
                    { "info", networkItem },
                    { "id", network },
                    { "network", networkCode },
                    { "active", null },
                    { "deposit", this.safeBool(networkItem, "depositEnable") },
                    { "withdraw", this.safeBool(networkItem, "withdrawEnable") },
                    { "fee", this.safeNumber(networkItem, "withdrawFee") },
                    { "precision", this.safeNumber(networkItem, "withdrawIntegerMultiple") },
                    { "limits", new Dictionary<string, object>() {
                        { "withdraw", new Dictionary<string, object>() {
                            { "min", this.safeNumber(networkItem, "withdrawMin") },
                            { "max", this.safeNumber(networkItem, "withdrawMax") },
                        } },
                        { "deposit", new Dictionary<string, object>() {
                            { "min", null },
                            { "max", null },
                        } },
                    } },
                };
            }
            ((IDictionary<string,object>)result)[(string)code] = this.safeCurrencyStructure(new Dictionary<string, object>() {
                { "id", id },
                { "name", this.safeString(entry, "name") },
                { "code", code },
                { "type", ((bool) isTrue(isFiat)) ? "fiat" : "crypto" },
                { "precision", this.parseNumber(this.parsePrecision(this.safeString(entry, "transferPrecision"))) },
                { "info", entry },
                { "active", null },
                { "deposit", this.safeBool(entry, "depositAllEnable") },
                { "withdraw", this.safeBool(entry, "withdrawAllEnable") },
                { "networks", networks },
                { "fee", null },
                { "fees", null },
                { "limits", new Dictionary<string, object>() {} },
            });
        }
        return result;
    }

    public override object calculateRateLimiterCost(object api, object method, object path, object parameters, object config = null)
    {
        config ??= new Dictionary<string, object>();
        if (isTrue(isTrue((inOp(config, "noSymbol"))) && !isTrue((inOp(parameters, "symbol")))))
        {
            return getValue(config, "noSymbol");
        } else if (isTrue(isTrue(isTrue((inOp(config, "noSymbolAndNoSymbols"))) && !isTrue((inOp(parameters, "symbol")))) && !isTrue((inOp(parameters, "symbols")))))
        {
            return getValue(config, "noSymbolAndNoSymbols");
        } else if (isTrue(isTrue((inOp(config, "byNumberOfSymbols"))) && isTrue((inOp(parameters, "symbols")))))
        {
            object symbols = getValue(parameters, "symbols");
            object symbolsAmount = getArrayLength(symbols);
            object byNumberOfSymbols = ((object)getValue(config, "byNumberOfSymbols"));
            for (object i = 0; isLessThan(i, getArrayLength(byNumberOfSymbols)); postFixIncrement(ref i))
            {
                object entry = getValue(byNumberOfSymbols, i);
                if (isTrue(isGreaterThanOrEqual(symbolsAmount, getValue(entry, 0))))
                {
                    return getValue(entry, 1);
                }
            }
        } else if (isTrue(isTrue((inOp(config, "byLimit"))) && isTrue((inOp(parameters, "limit")))))
        {
            object limit = getValue(parameters, "limit");
            object byLimit = ((object)getValue(config, "byLimit"));
            for (object i = 0; isLessThan(i, getArrayLength(byLimit)); postFixIncrement(ref i))
            {
                object entry = getValue(byLimit, i);
                if (isTrue(isGreaterThanOrEqual(limit, getValue(entry, 0))))
                {
                    return getValue(entry, 1);
                }
            }
        }
        return this.safeValue(config, "cost", 1);
    }

    /**
     * @method
     * @name coinsph#fetchStatus
     * @description the latest known information on the availability of the exchange API
     * @see https://coins-docs.github.io/rest-api/#test-connectivity
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [status structure]{@link https://docs.ccxt.com/#/?id=exchange-status-structure}
     */
    public async override Task<object> fetchStatus(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetOpenapiV1Ping(parameters);
        return new Dictionary<string, object>() {
            { "status", "ok" },
            { "updated", null },
            { "eta", null },
            { "url", null },
            { "info", response },
        };
    }

    /**
     * @method
     * @name coinsph#fetchTime
     * @description fetches the current integer timestamp in milliseconds from the exchange server
     * @see https://coins-docs.github.io/rest-api/#check-server-time
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {int} the current integer timestamp in milliseconds from the exchange server
     */
    public async override Task<object> fetchTime(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetOpenapiV1Time(parameters);
        //
        //     {"serverTime":1677705408268}
        //
        return this.safeInteger(response, "serverTime");
    }

    /**
     * @method
     * @name coinsph#fetchMarkets
     * @description retrieves data on all markets for coinsph
     * @see https://coins-docs.github.io/rest-api/#exchange-information
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} an array of objects representing market data
     */
    public async override Task<object> fetchMarkets(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object response = await this.publicGetOpenapiV1ExchangeInfo(parameters);
        //
        //     {
        //         "timezone": "UTC",
        //         "serverTime": "1677449496897",
        //         "exchangeFilters": [],
        //         "symbols": [
        //             {
        //                 "symbol": "XRPPHP",
        //                 "status": "TRADING",
        //                 "baseAsset": "XRP",
        //                 "baseAssetPrecision": "2",
        //                 "quoteAsset": "PHP",
        //                 "quoteAssetPrecision": "4",
        //                 "orderTypes": [
        //                     "LIMIT",
        //                     "MARKET",
        //                     "LIMIT_MAKER",
        //                     "STOP_LOSS_LIMIT",
        //                     "STOP_LOSS",
        //                     "TAKE_PROFIT_LIMIT",
        //                     "TAKE_PROFIT"
        //                 ],
        //                 "filters": [
        //                     {
        //                         "minPrice": "0.01",
        //                         "maxPrice": "99999999.********",
        //                         "tickSize": "0.01",
        //                         "filterType": "PRICE_FILTER"
        //                     },
        //                     {
        //                         "minQty": "0.01",
        //                         "maxQty": "99999999999.********",
        //                         "stepSize": "0.01",
        //                         "filterType": "LOT_SIZE"
        //                     },
        //                     { minNotional: "50", filterType: "NOTIONAL" },
        //                     { minNotional: "50", filterType: "MIN_NOTIONAL" },
        //                     {
        //                         "priceUp": "99999999",
        //                         "priceDown": "0.01",
        //                         "filterType": "STATIC_PRICE_RANGE"
        //                     },
        //                     {
        //                         "multiplierUp": "1.1",
        //                         "multiplierDown": "0.9",
        //                         "filterType": "PERCENT_PRICE_INDEX"
        //                     },
        //                     {
        //                         "multiplierUp": "1.1",
        //                         "multiplierDown": "0.9",
        //                         "filterType": "PERCENT_PRICE_ORDER_SIZE"
        //                     },
        //                     { maxNumOrders: "200", filterType: "MAX_NUM_ORDERS" },
        //                     { maxNumAlgoOrders: "5", filterType: "MAX_NUM_ALGO_ORDERS" }
        //                 ]
        //             },
        //         ]
        //     }
        //
        object markets = this.safeList(response, "symbols", new List<object>() {});
        object result = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(markets)); postFixIncrement(ref i))
        {
            object market = getValue(markets, i);
            object id = this.safeString(market, "symbol");
            object baseId = this.safeString(market, "baseAsset");
            object quoteId = this.safeString(market, "quoteAsset");
            object bs = this.safeCurrencyCode(baseId);
            object quote = this.safeCurrencyCode(quoteId);
            object limits = this.indexBy(this.safeList(market, "filters", new List<object>() {}), "filterType");
            object amountLimits = this.safeValue(limits, "LOT_SIZE", new Dictionary<string, object>() {});
            object priceLimits = this.safeValue(limits, "PRICE_FILTER", new Dictionary<string, object>() {});
            object costLimits = this.safeValue(limits, "NOTIONAL", new Dictionary<string, object>() {});
            ((IList<object>)result).Add(new Dictionary<string, object>() {
                { "id", id },
                { "symbol", add(add(bs, "/"), quote) },
                { "base", bs },
                { "quote", quote },
                { "settle", null },
                { "baseId", baseId },
                { "quoteId", quoteId },
                { "settleId", null },
                { "type", "spot" },
                { "spot", true },
                { "margin", false },
                { "swap", false },
                { "future", false },
                { "option", false },
                { "active", isEqual(this.safeStringLower(market, "status"), "trading") },
                { "contract", false },
                { "linear", null },
                { "inverse", null },
                { "taker", null },
                { "maker", null },
                { "contractSize", null },
                { "expiry", null },
                { "expiryDatetime", null },
                { "strike", null },
                { "optionType", null },
                { "precision", new Dictionary<string, object>() {
                    { "amount", this.parseNumber(this.safeString(amountLimits, "stepSize")) },
                    { "price", this.parseNumber(this.safeString(priceLimits, "tickSize")) },
                } },
                { "limits", new Dictionary<string, object>() {
                    { "leverage", new Dictionary<string, object>() {
                        { "min", null },
                        { "max", null },
                    } },
                    { "amount", new Dictionary<string, object>() {
                        { "min", this.parseNumber(this.safeString(amountLimits, "minQty")) },
                        { "max", this.parseNumber(this.safeString(amountLimits, "maxQty")) },
                    } },
                    { "price", new Dictionary<string, object>() {
                        { "min", this.parseNumber(this.safeString(priceLimits, "minPrice")) },
                        { "max", this.parseNumber(this.safeString(priceLimits, "maxPrice")) },
                    } },
                    { "cost", new Dictionary<string, object>() {
                        { "min", this.parseNumber(this.safeString(costLimits, "minNotional")) },
                        { "max", null },
                    } },
                } },
                { "created", null },
                { "info", market },
            });
        }
        this.setMarkets(result);
        return result;
    }

    /**
     * @method
     * @name coinsph#fetchTickers
     * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
     * @see https://coins-docs.github.io/rest-api/#24hr-ticker-price-change-statistics
     * @see https://coins-docs.github.io/rest-api/#symbol-price-ticker
     * @see https://coins-docs.github.io/rest-api/#symbol-order-book-ticker
     * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbols, null)))
        {
            object ids = new List<object>() {};
            for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
            {
                object market = this.market(getValue(symbols, i));
                object id = getValue(market, "id");
                ((IList<object>)ids).Add(id);
            }
            ((IDictionary<string,object>)request)["symbols"] = ids;
        }
        object defaultMethod = "publicGetOpenapiQuoteV1Ticker24hr";
        object options = this.safeDict(this.options, "fetchTickers", new Dictionary<string, object>() {});
        object method = this.safeString(options, "method", defaultMethod);
        object tickers = null;
        if (isTrue(isEqual(method, "publicGetOpenapiQuoteV1TickerPrice")))
        {
            tickers = await this.publicGetOpenapiQuoteV1TickerPrice(this.extend(request, parameters));
        } else if (isTrue(isEqual(method, "publicGetOpenapiQuoteV1TickerBookTicker")))
        {
            tickers = await this.publicGetOpenapiQuoteV1TickerBookTicker(this.extend(request, parameters));
        } else
        {
            tickers = await this.publicGetOpenapiQuoteV1Ticker24hr(this.extend(request, parameters));
        }
        return this.parseTickers(tickers, symbols, parameters);
    }

    /**
     * @method
     * @name coinsph#fetchTicker
     * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://coins-docs.github.io/rest-api/#24hr-ticker-price-change-statistics
     * @see https://coins-docs.github.io/rest-api/#symbol-price-ticker
     * @see https://coins-docs.github.io/rest-api/#symbol-order-book-ticker
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> fetchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object defaultMethod = "publicGetOpenapiQuoteV1Ticker24hr";
        object options = this.safeDict(this.options, "fetchTicker", new Dictionary<string, object>() {});
        object method = this.safeString(options, "method", defaultMethod);
        object ticker = null;
        if (isTrue(isEqual(method, "publicGetOpenapiQuoteV1TickerPrice")))
        {
            ticker = await this.publicGetOpenapiQuoteV1TickerPrice(this.extend(request, parameters));
        } else if (isTrue(isEqual(method, "publicGetOpenapiQuoteV1TickerBookTicker")))
        {
            ticker = await this.publicGetOpenapiQuoteV1TickerBookTicker(this.extend(request, parameters));
        } else
        {
            ticker = await this.publicGetOpenapiQuoteV1Ticker24hr(this.extend(request, parameters));
        }
        return this.parseTicker(ticker, market);
    }

    public override object parseTicker(object ticker, object market = null)
    {
        //
        // publicGetOpenapiQuoteV1Ticker24hr
        //     {
        //         "symbol": "ETHUSDT",
        //         "priceChange": "41.44****************",
        //         "priceChangePercent": "0.0259",
        //         "weightedAvgPrice": "1631.169825783972125436",
        //         "prevClosePrice": "1601.52****************",
        //         "lastPrice": "1642.96",
        //         "lastQty": "0.000001********0000",
        //         "bidPrice": "1638.79****************",
        //         "bidQty": "0.280075********0000",
        //         "askPrice": "1647.34****************",
        //         "askQty": "0.165183********0000",
        //         "openPrice": "1601.52",
        //         "highPrice": "1648.28",
        //         "lowPrice": "1601.52",
        //         "volume": "0.000287",
        //         "quoteVolume": "0.46814574",
        //         "openTime": "1677417000000",
        //         "closeTime": "1677503415200",
        //         "firstId": "1364680572697591809",
        //         "lastId": "1365389809203560449",
        //         "count": "100"
        //     }
        //
        // publicGetOpenapiQuoteV1TickerPrice
        //     { "symbol": "ETHUSDT", "price": "1599.68" }
        //
        // publicGetOpenapiQuoteV1TickerBookTicker
        //     {
        //         "symbol": "ETHUSDT",
        //         "bidPrice": "1596.57",
        //         "bidQty": "0.246405",
        //         "askPrice": "1605.12",
        //         "askQty": "0.242681"
        //     }
        //
        object marketId = this.safeString(ticker, "symbol");
        market = this.safeMarket(marketId, market);
        object timestamp = this.safeInteger(ticker, "closeTime");
        object bid = this.safeString(ticker, "bidPrice");
        object ask = this.safeString(ticker, "askPrice");
        object bidVolume = this.safeString(ticker, "bidQty");
        object askVolume = this.safeString(ticker, "askQty");
        object baseVolume = this.safeString(ticker, "volume");
        object quoteVolume = this.safeString(ticker, "quoteVolume");
        object open = this.safeString(ticker, "openPrice");
        object high = this.safeString(ticker, "highPrice");
        object low = this.safeString(ticker, "lowPrice");
        object prevClose = this.safeString(ticker, "prevClosePrice");
        object vwap = this.safeString(ticker, "weightedAvgPrice");
        object changeValue = this.safeString(ticker, "priceChange");
        object changePcnt = this.safeString(ticker, "priceChangePercent");
        changePcnt = Precise.stringMul(changePcnt, "100");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", getValue(market, "symbol") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "open", open },
            { "high", high },
            { "low", low },
            { "close", this.safeString2(ticker, "lastPrice", "price") },
            { "bid", bid },
            { "bidVolume", bidVolume },
            { "ask", ask },
            { "askVolume", askVolume },
            { "vwap", vwap },
            { "previousClose", prevClose },
            { "change", changeValue },
            { "percentage", changePcnt },
            { "average", null },
            { "baseVolume", baseVolume },
            { "quoteVolume", quoteVolume },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name coinsph#fetchOrderBook
     * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://coins-docs.github.io/rest-api/#order-book
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return (default 100, max 200)
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> fetchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.publicGetOpenapiQuoteV1Depth(this.extend(request, parameters));
        //
        //     {
        //         "lastUpdateId": "1667022157000699400",
        //         "bids": [
        //             [ '1651.81****************', '0.214556********0000' ],
        //             [ '1651.73****************', '0.257343********0000' ],
        //         ],
        //         "asks": [
        //             [ '1660.51****************', '0.299092********0000' ],
        //             [ '1660.6****************0', '0.253667********0000' ],
        //         ]
        //     }
        //
        object orderbook = this.parseOrderBook(response, symbol);
        ((IDictionary<string,object>)orderbook)["nonce"] = this.safeInteger(response, "lastUpdateId");
        return orderbook;
    }

    /**
     * @method
     * @name coinsph#fetchOHLCV
     * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @see https://coins-docs.github.io/rest-api/#klinecandlestick-data
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch
     * @param {int} [limit] the maximum amount of candles to fetch (default 500, max 1000)
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.until] timestamp in ms of the latest candle to fetch
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> fetchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object interval = this.safeString(this.timeframes, timeframe);
        object until = this.safeInteger(parameters, "until");
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "interval", interval },
        };
        if (isTrue(isEqual(limit, null)))
        {
            limit = 1000;
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
            // since work properly only when it is "younger" than last "limit" candle
            if (isTrue(!isEqual(until, null)))
            {
                ((IDictionary<string,object>)request)["endTime"] = until;
            } else
            {
                object duration = multiply(this.parseTimeframe(timeframe), 1000);
                object endTimeByLimit = this.sum(since, multiply(duration, (subtract(limit, 1))));
                object now = this.milliseconds();
                ((IDictionary<string,object>)request)["endTime"] = mathMin(endTimeByLimit, now);
            }
        } else if (isTrue(!isEqual(until, null)))
        {
            ((IDictionary<string,object>)request)["endTime"] = until;
            // since work properly only when it is "younger" than last "limit" candle
            object duration = multiply(this.parseTimeframe(timeframe), 1000);
            ((IDictionary<string,object>)request)["startTime"] = subtract(until, (multiply(duration, (subtract(limit, 1)))));
        }
        ((IDictionary<string,object>)request)["limit"] = limit;
        parameters = this.omit(parameters, "until");
        object response = await this.publicGetOpenapiQuoteV1Klines(this.extend(request, parameters));
        //
        //     [
        //         [
        //             1499040000000,      // Open time
        //             "0.01634790",       // Open
        //             "0.80000000",       // High
        //             "0.01575800",       // Low
        //             "0.01577100",       // Close
        //             "148976.11427815",  // Volume
        //             1499644799999,      // Close time
        //             "2434.19055334",    // Quote asset volume
        //             308,                // Number of trades
        //             "1756.87402397",    // Taker buy base asset volume
        //             "28.46694368"       // Taker buy quote asset volume
        //         ]
        //     ]
        //
        return this.parseOHLCVs(response, market, timeframe, since, limit);
    }

    public override object parseOHLCV(object ohlcv, object market = null)
    {
        return new List<object> {this.safeInteger(ohlcv, 0), this.safeNumber(ohlcv, 1), this.safeNumber(ohlcv, 2), this.safeNumber(ohlcv, 3), this.safeNumber(ohlcv, 4), this.safeNumber(ohlcv, 5)};
    }

    /**
     * @method
     * @name coinsph#fetchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://coins-docs.github.io/rest-api/#recent-trades-list
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch (default 500, max 1000)
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> fetchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            // since work properly only when it is "younger" than last 'limit' trade
            ((IDictionary<string,object>)request)["limit"] = 1000;
        } else
        {
            if (isTrue(!isEqual(limit, null)))
            {
                ((IDictionary<string,object>)request)["limit"] = limit;
            }
        }
        object response = await this.publicGetOpenapiQuoteV1Trades(this.extend(request, parameters));
        //
        //     [
        //         {
        //             "price": "89685.8",
        //             "id": "1365561108437680129",
        //             "qty": "0.000004",
        //             "quoteQty": "0.000004********0000",
        //             "time": "*************",
        //             "isBuyerMaker": false,
        //             "isBestMatch": true
        //         },
        //     ]
        //
        return this.parseTrades(response, market, since, limit);
    }

    /**
     * @method
     * @name coinsph#fetchMyTrades
     * @description fetch all trades made by the user
     * @see https://coins-docs.github.io/rest-api/#account-trade-list-user_data
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades structures to retrieve (default 500, max 1000)
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchMyTrades(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchMyTrades() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
            // since work properly only when it is "younger" than last 'limit' trade
            ((IDictionary<string,object>)request)["limit"] = 1000;
        } else if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.privateGetOpenapiV1MyTrades(this.extend(request, parameters));
        return this.parseTrades(response, market, since, limit);
    }

    /**
     * @method
     * @name coinsph#fetchOrderTrades
     * @description fetch all the trades made from a single order
     * @see https://coins-docs.github.io/rest-api/#account-trade-list-user_data
     * @param {string} id order id
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch trades for
     * @param {int} [limit] the maximum number of trades to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> fetchOrderTrades(object id, object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchOrderTrades() requires a symbol argument")) ;
        }
        object request = new Dictionary<string, object>() {
            { "orderId", id },
        };
        return await this.fetchMyTrades(symbol, since, limit, this.extend(request, parameters));
    }

    public override object parseTrade(object trade, object market = null)
    {
        //
        // fetchTrades
        //     {
        //         "price": "89685.8",
        //         "id": "1365561108437680129",
        //         "qty": "0.000004",
        //         "quoteQty": "0.000004********0000", // warning: report to exchange - this is not quote quantity, this is base quantity
        //         "time": "*************",
        //         "isBuyerMaker": false,
        //         "isBestMatch": true
        //     },
        //
        // fetchMyTrades
        //     {
        //         "symbol": "ETHUSDT",
        //         "id": 1375426310524125185,
        //         "orderId": 1375426310415879614,
        //         "price": "1580.91",
        //         "qty": "0.01",
        //         "quoteQty": "15.8091",
        //         "commission": "0",
        //         "commissionAsset": "USDT",
        //         "time": 1678699593307,
        //         "isBuyer": false,
        //         "isMaker":false,
        //         "isBestMatch":false
        //     }
        //
        // createOrder
        //     {
        //         "price": "1579.51",
        //         "qty": "0.001899",
        //         "commission": "0",
        //         "commissionAsset": "ETH",
        //         "tradeId":1375445992035598337
        //     }
        //
        object marketId = this.safeString(trade, "symbol");
        market = this.safeMarket(marketId, market);
        object symbol = getValue(market, "symbol");
        object id = this.safeString2(trade, "id", "tradeId");
        object orderId = this.safeString(trade, "orderId");
        object timestamp = this.safeInteger(trade, "time");
        object priceString = this.safeString(trade, "price");
        object amountString = this.safeString(trade, "qty");
        object type = null;
        object fee = null;
        object feeCost = this.safeString(trade, "commission");
        if (isTrue(!isEqual(feeCost, null)))
        {
            object feeCurrencyId = this.safeString(trade, "commissionAsset");
            fee = new Dictionary<string, object>() {
                { "cost", feeCost },
                { "currency", this.safeCurrencyCode(feeCurrencyId) },
            };
        }
        object isBuyer = this.safeBool2(trade, "isBuyer", "isBuyerMaker", null);
        object side = null;
        if (isTrue(!isEqual(isBuyer, null)))
        {
            side = ((bool) isTrue((isEqual(isBuyer, true)))) ? "buy" : "sell";
        }
        object isMaker = this.safeString2(trade, "isMaker", null);
        object takerOrMaker = null;
        if (isTrue(!isEqual(isMaker, null)))
        {
            takerOrMaker = ((bool) isTrue((isEqual(isMaker, "true")))) ? "maker" : "taker";
        }
        object costString = null;
        if (isTrue(!isEqual(orderId, null)))
        {
            costString = this.safeString(trade, "quoteQty");
        }
        return this.safeTrade(new Dictionary<string, object>() {
            { "id", id },
            { "order", orderId },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", symbol },
            { "type", type },
            { "side", side },
            { "takerOrMaker", takerOrMaker },
            { "price", priceString },
            { "amount", amountString },
            { "cost", costString },
            { "fee", fee },
            { "info", trade },
        }, market);
    }

    /**
     * @method
     * @name coinsph#fetchBalance
     * @description query for balance and get the amount of funds available for trading or funds locked in orders
     * @see https://coins-docs.github.io/rest-api/#accept-the-quote
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> fetchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.privateGetOpenapiV1Account(parameters);
        //
        //     {
        //         "accountType": "SPOT",
        //         "balances": [
        //             {
        //                 "asset": "BTC",
        //                 "free": "4723846.********",
        //                 "locked": "0.********"
        //             },
        //             {
        //                 "asset": "LTC",
        //                 "free": "4763368.********",
        //                 "locked": "0.********"
        //             }
        //         ],
        //         "canDeposit": true,
        //         "canTrade": true,
        //         "canWithdraw": true,
        //         "updateTime": "*************"
        //     }
        //
        return this.parseBalance(response);
    }

    public override object parseBalance(object response)
    {
        object balances = this.safeList(response, "balances", new List<object>() {});
        object result = new Dictionary<string, object>() {
            { "info", response },
            { "timestamp", null },
            { "datetime", null },
        };
        for (object i = 0; isLessThan(i, getArrayLength(balances)); postFixIncrement(ref i))
        {
            object balance = getValue(balances, i);
            object currencyId = this.safeString(balance, "asset");
            object code = this.safeCurrencyCode(currencyId);
            object account = this.account();
            ((IDictionary<string,object>)account)["free"] = this.safeString(balance, "free");
            ((IDictionary<string,object>)account)["used"] = this.safeString(balance, "locked");
            ((IDictionary<string,object>)result)[(string)code] = account;
        }
        return this.safeBalance(result);
    }

    /**
     * @method
     * @name coinsph#createOrder
     * @description create a trade order
     * @see https://coins-docs.github.io/rest-api/#new-order--trade
     * @param {string} symbol unified symbol of the market to create an order in
     * @param {string} type 'market', 'limit', 'stop_loss', 'take_profit', 'stop_loss_limit', 'take_profit_limit' or 'limit_maker'
     * @param {string} side 'buy' or 'sell'
     * @param {float} amount how much of currency you want to trade in units of base currency
     * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {float} [params.cost] the quote quantity that can be used as an alternative for the amount for market buy orders
     * @param {bool} [params.test] set to true to test an order, no order will be created but the request will be validated
     * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> createOrder(object symbol, object type, object side, object amount, object price = null, object parameters = null)
    {
        // todo: add test order low priority
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object testOrder = this.safeBool(parameters, "test", false);
        parameters = this.omit(parameters, "test");
        object orderType = this.safeString(parameters, "type", type);
        orderType = this.encodeOrderType(orderType);
        parameters = this.omit(parameters, "type");
        object orderSide = this.encodeOrderSide(side);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
            { "type", orderType },
            { "side", orderSide },
        };
        object options = this.safeValue(this.options, "createOrder", new Dictionary<string, object>() {});
        object newOrderRespType = this.safeValue(options, "newOrderRespType", new Dictionary<string, object>() {});
        // if limit order
        if (isTrue(isTrue(isTrue(isTrue(isEqual(orderType, "LIMIT")) || isTrue(isEqual(orderType, "STOP_LOSS_LIMIT"))) || isTrue(isEqual(orderType, "TAKE_PROFIT_LIMIT"))) || isTrue(isEqual(orderType, "LIMIT_MAKER"))))
        {
            if (isTrue(isEqual(price, null)))
            {
                throw new ArgumentsRequired ((string)add(add(add(this.id, " createOrder() requires a price argument for a "), type), " order")) ;
            }
            newOrderRespType = this.safeString(newOrderRespType, "limit", "FULL");
            ((IDictionary<string,object>)request)["price"] = this.priceToPrecision(symbol, price);
            ((IDictionary<string,object>)request)["quantity"] = this.amountToPrecision(symbol, amount);
            if (isTrue(!isEqual(orderType, "LIMIT_MAKER")))
            {
                ((IDictionary<string,object>)request)["timeInForce"] = this.safeString(options, "timeInForce", "GTC");
            }
        } else if (isTrue(isTrue(isTrue(isEqual(orderType, "MARKET")) || isTrue(isEqual(orderType, "STOP_LOSS"))) || isTrue(isEqual(orderType, "TAKE_PROFIT"))))
        {
            newOrderRespType = this.safeString(newOrderRespType, "market", "FULL");
            if (isTrue(isEqual(orderSide, "SELL")))
            {
                ((IDictionary<string,object>)request)["quantity"] = this.amountToPrecision(symbol, amount);
            } else if (isTrue(isEqual(orderSide, "BUY")))
            {
                object quoteAmount = null;
                object createMarketBuyOrderRequiresPrice = true;
                var createMarketBuyOrderRequiresPriceparametersVariable = this.handleOptionAndParams(parameters, "createOrder", "createMarketBuyOrderRequiresPrice", true);
                createMarketBuyOrderRequiresPrice = ((IList<object>)createMarketBuyOrderRequiresPriceparametersVariable)[0];
                parameters = ((IList<object>)createMarketBuyOrderRequiresPriceparametersVariable)[1];
                object cost = this.safeNumber2(parameters, "cost", "quoteOrderQty");
                parameters = this.omit(parameters, "cost");
                if (isTrue(!isEqual(cost, null)))
                {
                    quoteAmount = this.costToPrecision(symbol, cost);
                } else if (isTrue(createMarketBuyOrderRequiresPrice))
                {
                    if (isTrue(isEqual(price, null)))
                    {
                        throw new InvalidOrder ((string)add(this.id, " createOrder() requires the price argument for market buy orders to calculate the total cost to spend (amount * price), alternatively set the createMarketBuyOrderRequiresPrice option or param to false and pass the cost to spend in the amount argument")) ;
                    } else
                    {
                        object amountString = this.numberToString(amount);
                        object priceString = this.numberToString(price);
                        object costRequest = Precise.stringMul(amountString, priceString);
                        quoteAmount = this.costToPrecision(symbol, costRequest);
                    }
                } else
                {
                    quoteAmount = this.costToPrecision(symbol, amount);
                }
                ((IDictionary<string,object>)request)["quoteOrderQty"] = quoteAmount;
            }
        }
        if (isTrue(isTrue(isTrue(isTrue(isEqual(orderType, "STOP_LOSS")) || isTrue(isEqual(orderType, "STOP_LOSS_LIMIT"))) || isTrue(isEqual(orderType, "TAKE_PROFIT"))) || isTrue(isEqual(orderType, "TAKE_PROFIT_LIMIT"))))
        {
            object triggerPrice = this.safeString2(parameters, "triggerPrice", "stopPrice");
            if (isTrue(isEqual(triggerPrice, null)))
            {
                throw new InvalidOrder ((string)add(this.id, " createOrder () requires a triggerPrice or stopPrice param for stop_loss, take_profit, stop_loss_limit, and take_profit_limit orders")) ;
            }
            ((IDictionary<string,object>)request)["stopPrice"] = this.priceToPrecision(symbol, triggerPrice);
        }
        ((IDictionary<string,object>)request)["newOrderRespType"] = newOrderRespType;
        parameters = this.omit(parameters, "price", "stopPrice", "triggerPrice", "quantity", "quoteOrderQty");
        object response = null;
        if (isTrue(testOrder))
        {
            response = await this.privatePostOpenapiV1OrderTest(this.extend(request, parameters));
        } else
        {
            response = await this.privatePostOpenapiV1Order(this.extend(request, parameters));
        }
        //
        //     {
        //         "symbol": "ETHUSDT",
        //         "orderId": "1375407140139731486",
        //         "clientOrderId": "1375407140139733169",
        //         "transactTime": "1678697308023",
        //         "price": "1600",
        //         "origQty": "0.02",
        //         "executedQty": "0.02",
        //         "cummulativeQuoteQty": "31.9284",
        //         "status": "FILLED",
        //         "timeInForce": "GTC",
        //         "type": "LIMIT",
        //         "side": "BUY",
        //         "stopPrice": "0",
        //         "origQuoteOrderQty": "0",
        //         "fills": [
        //             {
        //                 "price": "1596.42",
        //                 "qty": "0.02",
        //                 "commission": "0",
        //                 "commissionAsset": "ETH",
        //                 "tradeId": "1375407140281532417"
        //             }
        //         ]
        //     },
        //
        return this.parseOrder(response, market);
    }

    /**
     * @method
     * @name coinsph#fetchOrder
     * @description fetches information on an order made by the user
     * @see https://coins-docs.github.io/rest-api/#query-order-user_data
     * @param {int|string} id order id
     * @param {string} symbol not used by coinsph fetchOrder ()
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object clientOrderId = this.safeValue2(parameters, "origClientOrderId", "clientOrderId");
        if (isTrue(!isEqual(clientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["origClientOrderId"] = clientOrderId;
        } else
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        }
        parameters = this.omit(parameters, new List<object>() {"clientOrderId", "origClientOrderId"});
        object response = await this.privateGetOpenapiV1Order(this.extend(request, parameters));
        return this.parseOrder(response);
    }

    /**
     * @method
     * @name coinsph#fetchOpenOrders
     * @description fetch all unfilled currently open orders
     * @see https://coins-docs.github.io/rest-api/#current-open-orders-user_data
     * @param {string} symbol unified market symbol
     * @param {int} [since] the earliest time in ms to fetch open orders for
     * @param {int} [limit] the maximum number of  open orders structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchOpenOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object response = await this.privateGetOpenapiV1OpenOrders(this.extend(request, parameters));
        return this.parseOrders(response, market, since, limit);
    }

    /**
     * @method
     * @name coinsph#fetchClosedOrders
     * @description fetches information on multiple closed orders made by the user
     * @see https://coins-docs.github.io/rest-api/#history-orders-user_data
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve (default 500, max 1000)
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> fetchClosedOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " fetchClosedOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
            // since work properly only when it is "younger" than last 'limit' order
            ((IDictionary<string,object>)request)["limit"] = 1000;
        } else if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.privateGetOpenapiV1HistoryOrders(this.extend(request, parameters));
        return this.parseOrders(response, market, since, limit);
    }

    /**
     * @method
     * @name coinsph#cancelOrder
     * @description cancels an open order
     * @see https://coins-docs.github.io/rest-api/#cancel-order-trade
     * @param {string} id order id
     * @param {string} symbol not used by coinsph cancelOrder ()
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelOrder(object id, object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object request = new Dictionary<string, object>() {};
        object clientOrderId = this.safeValue2(parameters, "origClientOrderId", "clientOrderId");
        if (isTrue(!isEqual(clientOrderId, null)))
        {
            ((IDictionary<string,object>)request)["origClientOrderId"] = clientOrderId;
        } else
        {
            ((IDictionary<string,object>)request)["orderId"] = id;
        }
        parameters = this.omit(parameters, new List<object>() {"clientOrderId", "origClientOrderId"});
        object response = await this.privateDeleteOpenapiV1Order(this.extend(request, parameters));
        return this.parseOrder(response);
    }

    /**
     * @method
     * @name coinsph#cancelAllOrders
     * @description cancel open orders of market
     * @see https://coins-docs.github.io/rest-api/#cancel-all-open-orders-on-a-symbol-trade
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> cancelAllOrders(object symbol = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        if (isTrue(isEqual(symbol, null)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " cancelAllOrders() requires a symbol argument")) ;
        }
        await this.loadMarkets();
        object market = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            ((IDictionary<string,object>)request)["symbol"] = getValue(market, "id");
        }
        object response = await this.privateDeleteOpenapiV1OpenOrders(this.extend(request, parameters));
        return this.parseOrders(response, market);
    }

    public override object parseOrder(object order, object market = null)
    {
        //
        // createOrder POST /openapi/v1/order
        //     {
        //         "symbol": "ETHUSDT",
        //         "orderId": 1375445991893797391,
        //         "clientOrderId": "1375445991893799115",
        //         "transactTime": 1678701939513,
        //         "price": "0",
        //         "origQty": "0",
        //         "executedQty": "0.001899",
        //         "cummulativeQuoteQty": "2.99948949",
        //         "status": "FILLED",
        //         "timeInForce": "GTC",
        //         "type": "MARKET",
        //         "side": "BUY",
        //         "stopPrice": "0",
        //         "origQuoteOrderQty": "3",
        //         "fills": [
        //             {
        //                 "price": "1579.51",
        //                 "qty": "0.001899",
        //                 "commission": "0",
        //                 "commissionAsset": "ETH",
        //                 "tradeId":1375445992035598337
        //             }
        //         ]
        //     }
        //
        // fetchOrder GET /openapi/v1/order
        // fetchOpenOrders GET /openapi/v1/openOrders
        // fetchClosedOrders GET /openapi/v1/historyOrders
        // cancelAllOrders DELETE /openapi/v1/openOrders
        //     {
        //         "symbol": "DOGEPHP",
        //         "orderId":1375465375097982423,
        //         "clientOrderId": "1375465375098001241",
        //         "price": "0",
        //         "origQty": "0",
        //         "executedQty": "13",
        //         "cummulativeQuoteQty": "49.621",
        //         "status": "FILLED",
        //         "timeInForce": "GTC",
        //         "type": "MARKET",
        //         "side": "BUY",
        //         "stopPrice": "0",
        //         "time":1678704250171,
        //         "updateTime":1678704250256,
        //         "isWorking":false,
        //         "origQuoteOrderQty": "50"
        //     }
        //
        // cancelOrder DELETE /openapi/v1/order
        //     {
        //         "symbol": "ETHPHP",
        //         "orderId":1375609441915774332,
        //         "clientOrderId": "1375609441915899557",
        //         "price": "96000",
        //         "origQty": "0.001",
        //         "executedQty": "0",
        //         "cummulativeQuoteQty": "0",
        //         "status": "CANCELED",
        //         "timeInForce": "GTC",
        //         "type": "LIMIT",
        //         "side": "SELL",
        //         "stopPrice": "0",
        //         "origQuoteOrderQty": "0"
        //     }
        //
        object id = this.safeString(order, "orderId");
        object marketId = this.safeString(order, "symbol");
        market = this.safeMarket(marketId, market);
        object timestamp = this.safeInteger2(order, "time", "transactTime");
        object trades = this.safeValue(order, "fills", null);
        object triggerPrice = this.safeString(order, "stopPrice");
        if (isTrue(Precise.stringEq(triggerPrice, "0")))
        {
            triggerPrice = null;
        }
        return this.safeOrder(new Dictionary<string, object>() {
            { "id", id },
            { "clientOrderId", this.safeString(order, "clientOrderId") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastTradeTimestamp", null },
            { "status", this.parseOrderStatus(this.safeString(order, "status")) },
            { "symbol", getValue(market, "symbol") },
            { "type", this.parseOrderType(this.safeString(order, "type")) },
            { "timeInForce", this.parseOrderTimeInForce(this.safeString(order, "timeInForce")) },
            { "side", this.parseOrderSide(this.safeString(order, "side")) },
            { "price", this.safeString(order, "price") },
            { "triggerPrice", triggerPrice },
            { "average", null },
            { "amount", this.safeString(order, "origQty") },
            { "cost", this.safeString(order, "cummulativeQuoteQty") },
            { "filled", this.safeString(order, "executedQty") },
            { "remaining", null },
            { "fee", null },
            { "fees", null },
            { "trades", trades },
            { "info", order },
        }, market);
    }

    public virtual object parseOrderSide(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "BUY", "buy" },
            { "SELL", "sell" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object encodeOrderSide(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "buy", "BUY" },
            { "sell", "SELL" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object parseOrderType(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "MARKET", "market" },
            { "LIMIT", "limit" },
            { "LIMIT_MAKER", "limit" },
            { "STOP_LOSS", "market" },
            { "STOP_LOSS_LIMIT", "limit" },
            { "TAKE_PROFIT", "market" },
            { "TAKE_PROFIT_LIMIT", "limit" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object encodeOrderType(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "market", "MARKET" },
            { "limit", "LIMIT" },
            { "limit_maker", "LIMIT_MAKER" },
            { "stop_loss", "STOP_LOSS" },
            { "stop_loss_limit", "STOP_LOSS_LIMIT" },
            { "take_profit", "TAKE_PROFIT" },
            { "take_profit_limit", "TAKE_PROFIT_LIMIT" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object parseOrderStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "NEW", "open" },
            { "FILLED", "closed" },
            { "CANCELED", "canceled" },
            { "PARTIALLY_FILLED", "open" },
            { "PARTIALLY_CANCELED", "canceled" },
            { "REJECTED", "rejected" },
        };
        return this.safeString(statuses, status, status);
    }

    public virtual object parseOrderTimeInForce(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "GTC", "GTC" },
            { "FOK", "FOK" },
            { "IOC", "IOC" },
        };
        return this.safeString(statuses, status, status);
    }

    /**
     * @method
     * @name coinsph#fetchTradingFee
     * @description fetch the trading fees for a market
     * @see https://coins-docs.github.io/rest-api/#trade-fee-user_data
     * @param {string} symbol unified market symbol
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [fee structure]{@link https://docs.ccxt.com/#/?id=fee-structure}
     */
    public async override Task<object> fetchTradingFee(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object request = new Dictionary<string, object>() {
            { "symbol", getValue(market, "id") },
        };
        object response = await this.privateGetOpenapiV1AssetTradeFee(this.extend(request, parameters));
        //
        //     [
        //       {
        //         "symbol": "ETHUSDT",
        //         "makerCommission": "0.0025",
        //         "takerCommission": "0.003"
        //       }
        //     ]
        //
        object tradingFee = this.safeDict(response, 0, new Dictionary<string, object>() {});
        return this.parseTradingFee(tradingFee, market);
    }

    /**
     * @method
     * @name coinsph#fetchTradingFees
     * @description fetch the trading fees for multiple markets
     * @see https://coins-docs.github.io/rest-api/#trade-fee-user_data
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
     */
    public async override Task<object> fetchTradingFees(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object response = await this.privateGetOpenapiV1AssetTradeFee(parameters);
        //
        //     [
        //         {
        //             "symbol": "ETHPHP",
        //             "makerCommission": "0.0025",
        //             "takerCommission": "0.003"
        //         },
        //         {
        //             "symbol": "UNIPHP",
        //             "makerCommission": "0.0025",
        //             "takerCommission": "0.003"
        //         },
        //     ]
        //
        object result = new Dictionary<string, object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(response)); postFixIncrement(ref i))
        {
            object fee = this.parseTradingFee(getValue(response, i));
            object symbol = getValue(fee, "symbol");
            ((IDictionary<string,object>)result)[(string)symbol] = fee;
        }
        return result;
    }

    public virtual object parseTradingFee(object fee, object market = null)
    {
        //
        //     {
        //         "symbol": "ETHUSDT",
        //         "makerCommission": "0.0025",
        //         "takerCommission": "0.003"
        //     }
        //
        object marketId = this.safeString(fee, "symbol");
        market = this.safeMarket(marketId, market);
        object symbol = getValue(market, "symbol");
        return new Dictionary<string, object>() {
            { "info", fee },
            { "symbol", symbol },
            { "maker", this.safeNumber(fee, "makerCommission") },
            { "taker", this.safeNumber(fee, "takerCommission") },
            { "percentage", null },
            { "tierBased", null },
        };
    }

    /**
     * @method
     * @name coinsph#withdraw
     * @description make a withdrawal to coins_ph account
     * @see https://coins-docs.github.io/rest-api/#withdrawuser_data
     * @param {string} code unified currency code
     * @param {float} amount the amount to withdraw
     * @param {string} address not used by coinsph withdraw ()
     * @param {string} tag
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> withdraw(object code, object amount, object address, object tag = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object options = this.safeValue(this.options, "withdraw");
        object warning = this.safeBool(options, "warning", true);
        if (isTrue(warning))
        {
            throw new InvalidAddress ((string)add(this.id, " withdraw() makes a withdrawals only to coins_ph account, add .options['withdraw']['warning'] = false to make a withdrawal to your coins_ph account")) ;
        }
        object networkCode = this.safeString(parameters, "network");
        object networkId = this.networkCodeToId(networkCode, code);
        if (isTrue(isEqual(networkId, null)))
        {
            throw new BadRequest ((string)add(this.id, " withdraw() require network parameter")) ;
        }
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
            { "amount", this.numberToString(amount) },
            { "network", networkId },
            { "address", address },
        };
        if (isTrue(!isEqual(tag, null)))
        {
            ((IDictionary<string,object>)request)["withdrawOrderId"] = tag;
        }
        parameters = this.omit(parameters, "network");
        object response = await this.privatePostOpenapiWalletV1WithdrawApply(this.extend(request, parameters));
        return this.parseTransaction(response, currency);
    }

    /**
     * @method
     * @name coinsph#fetchDeposits
     * @description fetch all deposits made to an account
     * @see https://coins-docs.github.io/rest-api/#deposit-history-user_data
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch deposits for
     * @param {int} [limit] the maximum number of deposits structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchDeposits(object code = null, object since = null, object limit = null, object parameters = null)
    {
        // todo: returns an empty array - find out why
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["coin"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.privateGetOpenapiWalletV1DepositHistory(this.extend(request, parameters));
        //
        // [
        //     {
        //         "id": "d_769800519366885376",
        //         "amount": "0.001",
        //         "coin": "BNB",
        //         "network": "BNB",
        //         "status": 0,
        //         "address": "bnb136ns6lfw4zs5hg4n85vdthaad7hq5m4gtkgf23",
        //         "addressTag": "101764890",
        //         "txId": "98A3EA560C6B3336D348B6C83F0F95ECE4F1F5919E94BD006E5BF3BF264FACFC",
        //         "insertTime": 1661493146000,
        //         "confirmNo": 10,
        //     },
        //     {
        //         "id": "d_769754833590042625",
        //         "amount":"0.5",
        //         "coin":"IOTA",
        //         "network":"IOTA",
        //         "status":1,
        //         "address":"SIZ9VLMHWATXKV99LH99CIGFJFUMLEHGWVZVNNZXRJJVWBPHYWPPBOSDORZ9EQSHCZAMPVAPGFYQAUUV9DROOXJLNW",
        //         "addressTag":"",
        //         "txId":"ESBFVQUTPIWQNJSPXFNHNYHSQNTGKRVKPRABQWTAXCDWOAKDKYWPTVG9BGXNVNKTLEJGESAVXIKIZ9999",
        //         "insertTime":*************,
        //         "confirmNo": 20,
        //     }
        // ]
        //
        return this.parseTransactions(response, currency, since, limit);
    }

    /**
     * @method
     * @name coinsph#fetchWithdrawals
     * @description fetch all withdrawals made from an account
     * @see https://coins-docs.github.io/rest-api/#withdraw-history-user_data
     * @param {string} code unified currency code
     * @param {int} [since] the earliest time in ms to fetch withdrawals for
     * @param {int} [limit] the maximum number of withdrawals structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
     */
    public async override Task<object> fetchWithdrawals(object code = null, object since = null, object limit = null, object parameters = null)
    {
        // todo: returns an empty array - find out why
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object currency = null;
        object request = new Dictionary<string, object>() {};
        if (isTrue(!isEqual(code, null)))
        {
            currency = this.currency(code);
            ((IDictionary<string,object>)request)["coin"] = getValue(currency, "id");
        }
        if (isTrue(!isEqual(since, null)))
        {
            ((IDictionary<string,object>)request)["startTime"] = since;
        }
        if (isTrue(!isEqual(limit, null)))
        {
            ((IDictionary<string,object>)request)["limit"] = limit;
        }
        object response = await this.privateGetOpenapiWalletV1WithdrawHistory(this.extend(request, parameters));
        //
        // [
        //     {
        //         "id": "459890698271244288",
        //         "amount": "0.01",
        //         "transactionFee": "0",
        //         "coin": "ETH",
        //         "status": 1,
        //         "address": "******************************************",
        //         "addressTag": "",
        //         "txId": "0x4ae2fed36a90aada978fc31c38488e8b60d7435cfe0b4daed842456b4771fcf7",
        //         "applyTime": 1673601139000,
        //         "network": "ETH",
        //         "withdrawOrderId": "thomas123",
        //         "info": "",
        //         "confirmNo": 100
        //     },
        //     {
        //         "id": "451899190746456064",
        //         "amount": "0.00063",
        //         "transactionFee": "0.00037",
        //         "coin": "ETH",
        //         "status": 1,
        //         "address": "******************************************",
        //         "addressTag": "",
        //         "txId": "0x62690ca4f9d6a8868c258e2ce613805af614d9354dda7b39779c57b2e4da0260",
        //         "applyTime": 1671695815000,
        //         "network": "ETH",
        //         "withdrawOrderId": "",
        //         "info": "",
        //         "confirmNo": 100
        //     }
        // ]
        //
        return this.parseTransactions(response, currency, since, limit);
    }

    public override object parseTransaction(object transaction, object currency = null)
    {
        //
        // fetchDeposits
        //     {
        //         "coin": "PHP",
        //         "address": "Internal Transfer",
        //         "addressTag": "Internal Transfer",
        //         "amount": "0.02",
        //         "id": "31312321312312312312322",
        //         "network": "Internal",
        //         "transferType": "0",
        //         "status": 3,
        //         "confirmTimes": "",
        //         "unlockConfirm": "",
        //         "txId": "Internal Transfer",
        //         "insertTime": 1657623798000,
        //         "depositOrderId": "the deposit id which created by client"
        //     }
        //
        // fetchWithdrawals
        //     {
        //         "coin": "BTC",
        //         "address": "Internal Transfer",
        //         "amount": "0.1",
        //         "id": "1201515362324421632",
        //         "withdrawOrderId": null,
        //         "network": "Internal",
        //         "transferType": "0",
        //         "status": 0,
        //         "transactionFee": "0",
        //         "confirmNo": 0,
        //         "info": "{}",
        //         "txId": "Internal Transfer",
        //         "applyTime": 1657967792000
        //     }
        //
        // todo: this is in progress
        object id = this.safeString(transaction, "id");
        object address = this.safeString(transaction, "address");
        object tag = this.safeString(transaction, "addressTag");
        if (isTrue(!isEqual(tag, null)))
        {
            if (isTrue(isLessThan(((string)tag).Length, 1)))
            {
                tag = null;
            }
        }
        object txid = this.safeString(transaction, "txId");
        object currencyId = this.safeString(transaction, "coin");
        object code = this.safeCurrencyCode(currencyId, currency);
        object timestamp = null;
        timestamp = this.safeInteger2(transaction, "insertTime", "applyTime");
        object updated = null;
        object type = null;
        object withdrawOrderId = this.safeString(transaction, "withdrawOrderId");
        object depositOrderId = this.safeString(transaction, "depositOrderId");
        if (isTrue(!isEqual(withdrawOrderId, null)))
        {
            type = "withdrawal";
        } else if (isTrue(!isEqual(depositOrderId, null)))
        {
            type = "deposit";
        }
        object status = this.parseTransactionStatus(this.safeString(transaction, "status"));
        object amount = this.safeNumber(transaction, "amount");
        object feeCost = this.safeNumber(transaction, "transactionFee");
        object fee = null;
        if (isTrue(!isEqual(feeCost, null)))
        {
            fee = new Dictionary<string, object>() {
                { "currency", code },
                { "cost", feeCost },
            };
        }
        object network = this.safeString(transaction, "network");
        object intern = isEqual(network, "Internal");
        return new Dictionary<string, object>() {
            { "info", transaction },
            { "id", id },
            { "txid", txid },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "network", network },
            { "address", address },
            { "addressTo", address },
            { "addressFrom", null },
            { "tag", tag },
            { "tagTo", tag },
            { "tagFrom", null },
            { "type", type },
            { "amount", amount },
            { "currency", code },
            { "status", status },
            { "updated", updated },
            { "internal", intern },
            { "comment", null },
            { "fee", fee },
        };
    }

    public virtual object parseTransactionStatus(object status)
    {
        object statuses = new Dictionary<string, object>() {
            { "0", "pending" },
            { "1", "ok" },
            { "2", "failed" },
            { "3", "pending" },
        };
        return this.safeString(statuses, status, status);
    }

    /**
     * @method
     * @name coinsph#fetchDepositAddress
     * @description fetch the deposit address for a currency associated with this account
     * @see https://coins-docs.github.io/rest-api/#deposit-address-user_data
     * @param {string} code unified currency code
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.network] network for fetch deposit address
     * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
     */
    public async override Task<object> fetchDepositAddress(object code, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object networkCode = this.safeString(parameters, "network");
        object networkId = this.networkCodeToId(networkCode, code);
        if (isTrue(isEqual(networkId, null)))
        {
            throw new BadRequest ((string)add(this.id, " fetchDepositAddress() require network parameter")) ;
        }
        await this.loadMarkets();
        object currency = this.currency(code);
        object request = new Dictionary<string, object>() {
            { "coin", getValue(currency, "id") },
            { "network", networkId },
        };
        parameters = this.omit(parameters, "network");
        object response = await this.privateGetOpenapiWalletV1DepositAddress(this.extend(request, parameters));
        //
        //     {
        //         "coin": "ETH",
        //         "address": "******************************************",
        //         "addressTag": ""
        //     }
        //
        return this.parseDepositAddress(response, currency);
    }

    public override object parseDepositAddress(object depositAddress, object currency = null)
    {
        //
        //     {
        //         "coin": "ETH",
        //         "address": "******************************************",
        //         "addressTag": ""
        //     }
        //
        object currencyId = this.safeString(depositAddress, "coin");
        object parsedCurrency = this.safeCurrencyCode(currencyId, currency);
        return new Dictionary<string, object>() {
            { "info", depositAddress },
            { "currency", parsedCurrency },
            { "network", null },
            { "address", this.safeString(depositAddress, "address") },
            { "tag", this.safeString(depositAddress, "addressTag") },
        };
    }

    public virtual object urlEncodeQuery(object query = null)
    {
        query ??= new Dictionary<string, object>();
        object encodedArrayParams = "";
        object keys = new List<object>(((IDictionary<string,object>)query).Keys);
        for (object i = 0; isLessThan(i, getArrayLength(keys)); postFixIncrement(ref i))
        {
            object key = getValue(keys, i);
            if (isTrue(((getValue(query, key) is IList<object>) || (getValue(query, key).GetType().IsGenericType && getValue(query, key).GetType().GetGenericTypeDefinition().IsAssignableFrom(typeof(List<>))))))
            {
                if (isTrue(!isEqual(i, 0)))
                {
                    encodedArrayParams = add(encodedArrayParams, "&");
                }
                object innerArray = getValue(query, key);
                query = this.omit(query, key);
                object encodedArrayParam = this.parseArrayParam(innerArray, key);
                encodedArrayParams = add(encodedArrayParams, encodedArrayParam);
            }
        }
        object encodedQuery = this.urlencode(query);
        if (isTrue(!isEqual(getArrayLength(encodedQuery), 0)))
        {
            return add(add(encodedQuery, "&"), encodedArrayParams);
        } else
        {
            return encodedArrayParams;
        }
    }

    public virtual object parseArrayParam(object array, object key)
    {
        object stringifiedArray = this.json(array);
        stringifiedArray = ((string)stringifiedArray).Replace((string)"[", (string)"%5B");
        stringifiedArray = ((string)stringifiedArray).Replace((string)"]", (string)"%5D");
        object urlEncodedParam = add(add(key, "="), stringifiedArray);
        return urlEncodedParam;
    }

    public override object sign(object path, object api = null, object method = null, object parameters = null, object headers = null, object body = null)
    {
        api ??= "public";
        method ??= "GET";
        parameters ??= new Dictionary<string, object>();
        object url = getValue(getValue(this.urls, "api"), api);
        object query = this.omit(parameters, this.extractParams(path));
        object endpoint = this.implodeParams(path, parameters);
        url = add(add(url, "/"), endpoint);
        if (isTrue(isEqual(api, "private")))
        {
            this.checkRequiredCredentials();
            ((IDictionary<string,object>)query)["timestamp"] = this.milliseconds();
            object recvWindow = this.safeInteger(query, "recvWindow");
            if (isTrue(isEqual(recvWindow, null)))
            {
                object defaultRecvWindow = this.safeInteger(this.options, "recvWindow");
                if (isTrue(!isEqual(defaultRecvWindow, null)))
                {
                    ((IDictionary<string,object>)query)["recvWindow"] = defaultRecvWindow;
                }
            }
            query = this.urlEncodeQuery(query);
            object signature = this.hmac(this.encode(query), this.encode(this.secret), sha256);
            url = add(add(add(add(url, "?"), query), "&signature="), signature);
            headers = new Dictionary<string, object>() {
                { "X-COINS-APIKEY", this.apiKey },
            };
        } else
        {
            query = this.urlEncodeQuery(query);
            if (isTrue(!isEqual(getArrayLength(query), 0)))
            {
                url = add(url, add("?", query));
            }
        }
        return new Dictionary<string, object>() {
            { "url", url },
            { "method", method },
            { "body", body },
            { "headers", headers },
        };
    }

    public override object handleErrors(object code, object reason, object url, object method, object headers, object body, object response, object requestHeaders, object requestBody)
    {
        if (isTrue(isEqual(response, null)))
        {
            return null;
        }
        object responseCode = this.safeString(response, "code", null);
        if (isTrue(isTrue(isTrue((!isEqual(responseCode, null))) && isTrue((!isEqual(responseCode, "200")))) && isTrue((!isEqual(responseCode, "0")))))
        {
            object feedback = add(add(this.id, " "), body);
            this.throwBroadlyMatchedException(getValue(this.exceptions, "broad"), body, feedback);
            this.throwExactlyMatchedException(getValue(this.exceptions, "exact"), responseCode, feedback);
            throw new ExchangeError ((string)feedback) ;
        }
        return null;
    }
}
