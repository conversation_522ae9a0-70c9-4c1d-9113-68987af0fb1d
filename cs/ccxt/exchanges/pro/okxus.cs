namespace ccxt.pro;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code


public partial class okxus { public okxus(object args = null) : base(args) { } }
public partial class okxus : okx
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "id", "okxus" },
            { "name", "OKX (US)" },
            { "hostname", "us.okx.com" },
            { "urls", new Dictionary<string, object>() {
                { "api", new Dictionary<string, object>() {
                    { "rest", "https://{hostname}" },
                    { "ws", "wss://wsus.okx.com:8443/ws/v5" },
                } },
                { "www", "https://app.okx.com" },
                { "doc", "https://app.okx.com/docs-v5/en/#overview" },
                { "fees", "https://app.okx.com/pages/products/fees.html" },
                { "referral", new Dictionary<string, object>() {
                    { "url", "https://www.app.okx.com/join/CCXT2023" },
                    { "discount", 0.2 },
                } },
                { "test", new Dictionary<string, object>() {
                    { "ws", "wss://wsuspap.okx.com:8443/ws/v5" },
                } },
            } },
            { "has", new Dictionary<string, object>() {
                { "swap", false },
                { "future", false },
                { "option", false },
            } },
        });
    }
}
