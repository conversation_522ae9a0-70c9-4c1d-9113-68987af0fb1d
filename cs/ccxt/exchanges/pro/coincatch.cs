namespace ccxt.pro;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code


public partial class coincatch { public coincatch(object args = null) : base(args) { } }
public partial class coincatch : ccxt.coincatch
{
    public override object describe()
    {
        return this.deepExtend(base.describe(), new Dictionary<string, object>() {
            { "has", new Dictionary<string, object>() {
                { "ws", true },
                { "watchTrades", true },
                { "watchTradesForSymbols", true },
                { "watchOrderBook", true },
                { "watchOrderBookForSymbols", true },
                { "watchOHLCV", true },
                { "watchOHLCVForSymbols", false },
                { "watchOrders", true },
                { "watchMyTrades", false },
                { "watchTicker", true },
                { "watchTickers", true },
                { "watchBalance", true },
                { "watchPositions", true },
            } },
            { "urls", new Dictionary<string, object>() {
                { "api", new Dictionary<string, object>() {
                    { "ws", new Dictionary<string, object>() {
                        { "public", "wss://ws.coincatch.com/public/v1/stream" },
                        { "private", "wss://ws.coincatch.com/private/v1/stream" },
                    } },
                } },
            } },
            { "options", new Dictionary<string, object>() {
                { "tradesLimit", 1000 },
                { "OHLCVLimit", 200 },
                { "timeframesForWs", new Dictionary<string, object>() {
                    { "1m", "1m" },
                    { "5m", "5m" },
                    { "15m", "15m" },
                    { "30m", "30m" },
                    { "1h", "1H" },
                    { "4h", "4H" },
                    { "12h", "12H" },
                    { "1d", "1D" },
                    { "1w", "1W" },
                } },
                { "watchOrderBook", new Dictionary<string, object>() {
                    { "checksum", true },
                } },
            } },
            { "streaming", new Dictionary<string, object>() {
                { "ping", this.ping },
            } },
            { "exceptions", new Dictionary<string, object>() {
                { "ws", new Dictionary<string, object>() {
                    { "exact", new Dictionary<string, object>() {
                        { "30001", typeof(BadRequest) },
                        { "30002", typeof(AuthenticationError) },
                        { "30003", typeof(BadRequest) },
                        { "30004", typeof(AuthenticationError) },
                        { "30005", typeof(AuthenticationError) },
                        { "30006", typeof(RateLimitExceeded) },
                        { "30007", typeof(RateLimitExceeded) },
                        { "30011", typeof(AuthenticationError) },
                        { "30012", typeof(AuthenticationError) },
                        { "30013", typeof(AuthenticationError) },
                        { "30014", typeof(BadRequest) },
                        { "30015", typeof(AuthenticationError) },
                    } },
                    { "broad", new Dictionary<string, object>() {} },
                } },
            } },
        });
    }

    public virtual object getMarketFromArg(object entry)
    {
        object instId = this.safeString(entry, "instId");
        object instType = this.safeString(entry, "instType");
        object baseAndQuote = this.parseSpotMarketId(instId);
        object baseId = getValue(baseAndQuote, "baseId");
        object quoteId = getValue(baseAndQuote, "quoteId");
        object suffix = "_SPBL"; // spot suffix
        if (isTrue(isEqual(instType, "mc")))
        {
            if (isTrue(isEqual(quoteId, "USD")))
            {
                suffix = "_DMCBL";
            } else
            {
                suffix = "_UMCBL";
            }
        }
        object marketId = add(add(this.safeCurrencyCode(baseId), this.safeCurrencyCode(quoteId)), suffix);
        return this.safeMarketCustom(marketId);
    }

    public async virtual Task<object> authenticate(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        this.checkRequiredCredentials();
        object url = getValue(getValue(getValue(this.urls, "api"), "ws"), "private");
        var client = this.client(url);
        object messageHash = "authenticated";
        var future = client.future(messageHash);
        object authenticated = this.safeValue(((WebSocketClient)client).subscriptions, messageHash);
        if (isTrue(isEqual(authenticated, null)))
        {
            object timestamp = ((object)this.seconds()).ToString();
            object auth = add(add(timestamp, "GET"), "/user/verify");
            object signature = this.hmac(this.encode(auth), this.encode(this.secret), sha256, "base64");
            object operation = "login";
            object request = new Dictionary<string, object>() {
                { "op", operation },
                { "args", new List<object>() {new Dictionary<string, object>() {
    { "apiKey", this.apiKey },
    { "passphrase", this.password },
    { "timestamp", timestamp },
    { "sign", signature },
}} },
            };
            object message = this.extend(request, parameters);
            this.watch(url, messageHash, message, messageHash);
        }
        return await (future as Exchange.Future);
    }

    public async virtual Task<object> watchPublic(object messageHash, object subscribeHash, object args, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object url = getValue(getValue(getValue(this.urls, "api"), "ws"), "public");
        object request = new Dictionary<string, object>() {
            { "op", "subscribe" },
            { "args", new List<object>() {args} },
        };
        object message = this.extend(request, parameters);
        return await this.watch(url, messageHash, message, subscribeHash);
    }

    public async virtual Task<object> unWatchPublic(object messageHash, object args, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object url = getValue(getValue(getValue(this.urls, "api"), "ws"), "public");
        object request = new Dictionary<string, object>() {
            { "op", "unsubscribe" },
            { "args", new List<object>() {args} },
        };
        object message = this.extend(request, parameters);
        return await this.watch(url, messageHash, message, messageHash);
    }

    public async virtual Task<object> watchPrivate(object messageHash, object subscribeHash, object args, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.authenticate();
        object url = getValue(getValue(getValue(this.urls, "api"), "ws"), "private");
        object request = new Dictionary<string, object>() {
            { "op", "subscribe" },
            { "args", new List<object>() {args} },
        };
        object message = this.extend(request, parameters);
        return await this.watch(url, messageHash, message, subscribeHash);
    }

    public async virtual Task<object> watchPrivateMultiple(object messageHashes, object subscribeHashes, object args, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.authenticate();
        object url = getValue(getValue(getValue(this.urls, "api"), "ws"), "private");
        object request = new Dictionary<string, object>() {
            { "op", "subscribe" },
            { "args", args },
        };
        object message = this.extend(request, parameters);
        return await this.watchMultiple(url, messageHashes, message, subscribeHashes);
    }

    public virtual void handleAuthenticate(WebSocketClient client, object message)
    {
        //
        //  { event: "login", code: 0 }
        //
        object messageHash = "authenticated";
        var future = this.safeValue((client as WebSocketClient).futures, messageHash);
        (future as Future).resolve(true);
    }

    public async virtual Task<object> watchPublicMultiple(object messageHashes, object subscribeHashes, object argsArray, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object url = getValue(getValue(getValue(this.urls, "api"), "ws"), "public");
        object request = new Dictionary<string, object>() {
            { "op", "subscribe" },
            { "args", argsArray },
        };
        object message = this.extend(request, parameters);
        return await this.watchMultiple(url, messageHashes, message, subscribeHashes);
    }

    public async virtual Task<object> unWatchChannel(object symbol, object channel, object messageHashTopic, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        var instTypeinstIdVariable = this.getPublicInstTypeAndId(market);
        var instType = ((IList<object>) instTypeinstIdVariable)[0];
        var instId = ((IList<object>) instTypeinstIdVariable)[1];
        object messageHash = add(add(add("unsubscribe:", messageHashTopic), ":"), symbol);
        object args = new Dictionary<string, object>() {
            { "instType", instType },
            { "channel", channel },
            { "instId", instId },
        };
        return await this.unWatchPublic(messageHash, args, parameters);
    }

    public virtual object getPublicInstTypeAndId(object market)
    {
        object instId = add(getValue(market, "baseId"), getValue(market, "quoteId"));
        object instType = null;
        if (isTrue(getValue(market, "spot")))
        {
            instType = "SP";
        } else if (isTrue(getValue(market, "swap")))
        {
            instType = "MC";
        } else
        {
            throw new NotSupported ((string)add(this.id, " supports only spot and swap markets")) ;
        }
        return new List<object>() {instType, instId};
    }

    public virtual object handleDMCBLMarketByMessageHashes(object market, object hash, object client, object timeframe = null)
    {
        object marketId = getValue(market, "id");
        object messageHashes = this.findMessageHashes(client as WebSocketClient, hash);
        // the exchange counts DMCBL markets as the same market with different quote currencies
        // for example symbols ETHUSD:ETH and ETH/USD:BTC both have the same marketId ETHUSD_DMCBL
        // we need to check all markets with the same marketId to find the correct market that is in messageHashes
        object marketsWithCurrentId = this.safeList(this.markets_by_id, marketId, new List<object>() {});
        object suffix = "";
        if (isTrue(!isEqual(timeframe, null)))
        {
            suffix = add(":", timeframe);
        }
        for (object i = 0; isLessThan(i, getArrayLength(marketsWithCurrentId)); postFixIncrement(ref i))
        {
            market = getValue(marketsWithCurrentId, i);
            object symbol = getValue(market, "symbol");
            object messageHash = add(add(hash, symbol), suffix);
            if (isTrue(this.inArray(messageHash, messageHashes)))
            {
                return market;
            }
        }
        return market;
    }

    /**
     * @method
     * @name coincatch#watchTicker
     * @description watches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
     * @see https://coincatch.github.io/github.io/en/spot/#tickers-channel
     * @param {string} symbol unified symbol of the market to fetch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.instType] the type of the instrument to fetch the ticker for, 'SP' for spot markets, 'MC' for futures markets (default is 'SP')
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> watchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        var instTypeinstIdVariable = this.getPublicInstTypeAndId(market);
        var instType = ((IList<object>) instTypeinstIdVariable)[0];
        var instId = ((IList<object>) instTypeinstIdVariable)[1];
        object channel = "ticker";
        object messageHash = add(add(channel, ":"), symbol);
        object args = new Dictionary<string, object>() {
            { "instType", instType },
            { "channel", channel },
            { "instId", instId },
        };
        return await this.watchPublic(messageHash, messageHash, args, parameters);
    }

    /**
     * @method
     * @name coincatch#unWatchTicker
     * @description unsubscribe from the ticker channel
     * @see https://coincatch.github.io/github.io/en/mix/#tickers-channel
     * @param {string} symbol unified symbol of the market to unwatch the ticker for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {any} status of the unwatch request
     */
    public async override Task<object> unWatchTicker(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        return await this.unWatchChannel(symbol, "ticker", "ticker", parameters);
    }

    /**
     * @method
     * @name coincatch#watchTickers
     * @description watches a price ticker, a statistical calculation with the information calculated over the past 24 hours for all markets of a specific list
     * @see https://coincatch.github.io/github.io/en/mix/#tickers-channel
     * @param {string[]} symbols unified symbol of the market to watch the tickers for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
     */
    public async override Task<object> watchTickers(object symbols = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        if (isTrue(isEqual(symbols, null)))
        {
            symbols = this.symbols;
        }
        object topics = new List<object>() {};
        object messageHashes = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
        {
            object symbol = getValue(symbols, i);
            object market = this.market(symbol);
            var instTypeinstIdVariable = this.getPublicInstTypeAndId(market);
            var instType = ((IList<object>) instTypeinstIdVariable)[0];
            var instId = ((IList<object>) instTypeinstIdVariable)[1];
            object args = new Dictionary<string, object>() {
                { "instType", instType },
                { "channel", "ticker" },
                { "instId", instId },
            };
            ((IList<object>)topics).Add(args);
            ((IList<object>)messageHashes).Add(add("ticker:", symbol));
        }
        object tickers = await this.watchPublicMultiple(messageHashes, messageHashes, topics, parameters);
        if (isTrue(this.newUpdates))
        {
            object result = new Dictionary<string, object>() {};
            ((IDictionary<string,object>)result)[(string)getValue(tickers, "symbol")] = tickers;
            return result;
        }
        return this.filterByArray(this.tickers, "symbol", symbols);
    }

    public virtual void handleTicker(WebSocketClient client, object message)
    {
        //
        //     action: 'snapshot',
        //     arg: { instType: 'sp', channel: 'ticker', instId: 'ETHUSDT' },
        //     data: [
        //         {
        //             instId: 'ETHUSDT',
        //             last: '2421.06',
        //             open24h: '2416.93',
        //             high24h: '2441.47',
        //             low24h: '2352.99',
        //             bestBid: '2421.03',
        //             bestAsk: '2421.06',
        //             baseVolume: '9445.2043',
        //             quoteVolume: '22807159.1148',
        //             ts: 1728131730687,
        //             labeId: 0,
        //             openUtc: '2414.50',
        //             chgUTC: '0.00272',
        //             bidSz: '3.866',
        //             askSz: '0.124'
        //         }
        //     ],
        //     ts: 1728131730688
        //
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object market = this.getMarketFromArg(arg);
        object marketId = getValue(market, "id");
        object hash = "ticker:";
        if (isTrue(isGreaterThanOrEqual(getIndexOf(marketId, "_DMCBL"), 0)))
        {
            market = this.handleDMCBLMarketByMessageHashes(market, hash, client);
        }
        object data = this.safeList(message, "data", new List<object>() {});
        object ticker = this.parseWsTicker(this.safeDict(data, 0, new Dictionary<string, object>() {}), market);
        object symbol = getValue(market, "symbol");
        ((IDictionary<string,object>)this.tickers)[(string)symbol] = ticker;
        object messageHash = add(hash, symbol);
        callDynamically(client as WebSocketClient, "resolve", new object[] {getValue(this.tickers, symbol), messageHash});
    }

    public virtual object parseWsTicker(object ticker, object market = null)
    {
        //
        // spot
        //     {
        //         instId: 'ETHUSDT',
        //         last: '2421.06',
        //         open24h: '2416.93',
        //         high24h: '2441.47',
        //         low24h: '2352.99',
        //         bestBid: '2421.03',
        //         bestAsk: '2421.06',
        //         baseVolume: '9445.2043',
        //         quoteVolume: '22807159.1148',
        //         ts: 1728131730687,
        //         labeId: 0,
        //         openUtc: '2414.50',
        //         chgUTC: '0.00272',
        //         bidSz: '3.866',
        //         askSz: '0.124'
        //     }
        //
        // swap
        //     {
        //         instId: 'ETHUSDT',
        //         last: '2434.47',
        //         bestAsk: '2434.48',
        //         bestBid: '2434.47',
        //         high24h: '2471.68',
        //         low24h: '2400.01',
        //         priceChangePercent: '0.00674',
        //         capitalRate: '0.000082',
        //         nextSettleTime: 1728489600000,
        //         systemTime: 1728471993602,
        //         markPrice: '2434.46',
        //         indexPrice: '2435.44',
        //         holding: '171450.25',
        //         baseVolume: '1699298.91',
        //         quoteVolume: '4144522832.32',
        //         openUtc: '2439.67',
        //         chgUTC: '-0.00213',
        //         symbolType: 1,
        //         symbolId: 'ETHUSDT_UMCBL',
        //         deliveryPrice: '0',
        //         bidSz: '26.12',
        //         askSz: '49.6'
        //     }
        //
        object last = this.safeString(ticker, "last");
        object timestamp = this.safeInteger2(ticker, "ts", "systemTime");
        return this.safeTicker(new Dictionary<string, object>() {
            { "symbol", getValue(market, "symbol") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "high", this.safeString(ticker, "high24h") },
            { "low", this.safeString(ticker, "low24h") },
            { "bid", this.safeString(ticker, "bestBid") },
            { "bidVolume", this.safeString(ticker, "bidSz") },
            { "ask", this.safeString(ticker, "bestAsk") },
            { "askVolume", this.safeString(ticker, "askSz") },
            { "vwap", null },
            { "open", this.safeString2(ticker, "open24h", "openUtc") },
            { "close", last },
            { "last", last },
            { "previousClose", null },
            { "change", null },
            { "percentage", Precise.stringMul(this.safeString(ticker, "chgUTC"), "100") },
            { "average", null },
            { "baseVolume", this.safeNumber(ticker, "baseVolume") },
            { "quoteVolume", this.safeNumber(ticker, "quoteVolume") },
            { "indexPrice", this.safeString(ticker, "indexPrice") },
            { "markPrice", this.safeString(ticker, "markPrice") },
            { "info", ticker },
        }, market);
    }

    /**
     * @method
     * @name coincatch#watchOHLCV
     * @description watches historical candlestick data containing the open, high, low, and close price, and the volume of a market
     * @see https://coincatch.github.io/github.io/en/spot/#candlesticks-channel
     * @param {string} symbol unified symbol of the market to fetch OHLCV data for
     * @param {string} timeframe the length of time each candle represents
     * @param {int} [since] timestamp in ms of the earliest candle to fetch (not including)
     * @param {int} [limit] the maximum amount of candles to fetch (not including)
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {bool} [params.instType] the type of the instrument to fetch the OHLCV data for, 'SP' for spot markets, 'MC' for futures markets (default is 'SP')
     * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
     */
    public async override Task<object> watchOHLCV(object symbol, object timeframe = null, object since = null, object limit = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object market = this.market(symbol);
        object timeframes = getValue(this.options, "timeframesForWs");
        object channel = add("candle", this.safeString(timeframes, timeframe));
        var instTypeinstIdVariable = this.getPublicInstTypeAndId(market);
        var instType = ((IList<object>) instTypeinstIdVariable)[0];
        var instId = ((IList<object>) instTypeinstIdVariable)[1];
        object args = new Dictionary<string, object>() {
            { "instType", instType },
            { "channel", channel },
            { "instId", instId },
        };
        object messageHash = add(add(add("ohlcv:", symbol), ":"), timeframe);
        object ohlcv = await this.watchPublic(messageHash, messageHash, args, parameters);
        if (isTrue(this.newUpdates))
        {
            limit = callDynamically(ohlcv, "getLimit", new object[] {symbol, limit});
        }
        return this.filterBySinceLimit(ohlcv, since, limit, 0, true);
    }

    /**
     * @method
     * @name coincatch#unWatchOHLCV
     * @description unsubscribe from the ohlcv channel
     * @see https://www.bitget.com/api-doc/spot/websocket/public/Candlesticks-Channel
     * @param {string} symbol unified symbol of the market to unwatch the ohlcv for
     * @param timeframe
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async virtual Task<object> unWatchOHLCV(object symbol, object timeframe = null, object parameters = null)
    {
        timeframe ??= "1m";
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object timeframes = getValue(this.options, "timeframesForWs");
        object interval = this.safeString(timeframes, timeframe);
        object channel = add("candle", interval);
        return await this.unWatchChannel(symbol, channel, add("ohlcv:", interval), parameters);
    }

    public virtual void handleOHLCV(WebSocketClient client, object message)
    {
        //
        //     {
        //         action: 'update',
        //         arg: { instType: 'sp', channel: 'candle1D', instId: 'ETHUSDT' },
        //         data: [
        //             [
        //                 '1728316800000',
        //                 '2474.5',
        //                 '2478.21',
        //                 '2459.8',
        //                 '2463.51',
        //                 '86.0551'
        //             ]
        //         ],
        //         ts: 1728317607657
        //     }
        //
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object market = this.getMarketFromArg(arg);
        object marketId = getValue(market, "id");
        object hash = "ohlcv:";
        object data = this.safeList(message, "data", new List<object>() {});
        object channel = this.safeString(arg, "channel");
        object klineType = slice(channel, 6, null);
        object timeframe = this.findTimeframe(klineType);
        if (isTrue(isGreaterThanOrEqual(getIndexOf(marketId, "_DMCBL"), 0)))
        {
            market = this.handleDMCBLMarketByMessageHashes(market, hash, client, timeframe);
        }
        object symbol = getValue(market, "symbol");
        if (!isTrue((inOp(this.ohlcvs, symbol))))
        {
            ((IDictionary<string,object>)this.ohlcvs)[(string)symbol] = new Dictionary<string, object>() {};
        }
        if (!isTrue((inOp(getValue(this.ohlcvs, symbol), timeframe))))
        {
            object limit = this.safeInteger(this.options, "OHLCVLimit", 1000);
            ((IDictionary<string,object>)getValue(this.ohlcvs, symbol))[(string)timeframe] = new ArrayCacheByTimestamp(limit);
        }
        object stored = getValue(getValue(this.ohlcvs, symbol), timeframe);
        for (object i = 0; isLessThan(i, getArrayLength(data)); postFixIncrement(ref i))
        {
            object candle = this.safeList(data, i, new List<object>() {});
            object parsed = this.parseWsOHLCV(candle, market);
            callDynamically(stored, "append", new object[] {parsed});
        }
        object messageHash = add(add(add(hash, symbol), ":"), timeframe);
        callDynamically(client as WebSocketClient, "resolve", new object[] {stored, messageHash});
    }

    public override object parseWsOHLCV(object ohlcv, object market = null)
    {
        //
        //     [
        //         '1728316800000',
        //         '2474.5',
        //         '2478.21',
        //         '2459.8',
        //         '2463.51',
        //         '86.0551'
        //     ]
        //
        return new List<object> {this.safeInteger(ohlcv, 0), this.safeNumber(ohlcv, 1), this.safeNumber(ohlcv, 2), this.safeNumber(ohlcv, 3), this.safeNumber(ohlcv, 4), this.safeNumber(ohlcv, 5)};
    }

    /**
     * @method
     * @name coincatch#watchOrderBook
     * @description watches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://coincatch.github.io/github.io/en/spot/#depth-channel
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> watchOrderBook(object symbol, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.watchOrderBookForSymbols(new List<object>() {symbol}, limit, parameters);
    }

    /**
     * @method
     * @name coincatch#unWatchOrderBook
     * @description unsubscribe from the orderbook channel
     * @see https://coincatch.github.io/github.io/en/spot/#depth-channel
     * @param {string} symbol unified symbol of the market to fetch the order book for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {int} [params.limit] orderbook limit, default is undefined
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> unWatchOrderBook(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        object channel = "books";
        object limit = this.safeInteger(parameters, "limit");
        if (isTrue(isTrue((isEqual(limit, 5))) || isTrue((isEqual(limit, 15)))))
        {
            parameters = this.omit(parameters, "limit");
            channel = add(channel, ((object)limit).ToString());
        }
        return await this.unWatchChannel(symbol, channel, channel, parameters);
    }

    /**
     * @method
     * @name coincatch#watchOrderBookForSymbols
     * @description watches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
     * @see https://coincatch.github.io/github.io/en/spot/#depth-channel
     * @param symbols
     * @param {int} [limit] the maximum amount of order book entries to return
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
     */
    public async override Task<object> watchOrderBookForSymbols(object symbols, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        object channel = "books";
        object topics = new List<object>() {};
        object messageHashes = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
        {
            object symbol = getValue(symbols, i);
            object market = this.market(symbol);
            var instTypeinstIdVariable = this.getPublicInstTypeAndId(market);
            var instType = ((IList<object>) instTypeinstIdVariable)[0];
            var instId = ((IList<object>) instTypeinstIdVariable)[1];
            object args = new Dictionary<string, object>() {
                { "instType", instType },
                { "channel", channel },
                { "instId", instId },
            };
            ((IList<object>)topics).Add(args);
            ((IList<object>)messageHashes).Add(add(add(channel, ":"), symbol));
        }
        object orderbook = await this.watchPublicMultiple(messageHashes, messageHashes, topics, parameters);
        return (orderbook as IOrderBook).limit();
    }

    public virtual void handleOrderBook(WebSocketClient client, object message)
    {
        //
        //     {
        //         action: 'update',
        //         arg: { instType: 'sp', channel: 'books', instId: 'ETHUSDT' },
        //         data: [
        //             {
        //                 asks: [ [ 2507.07, 0.4248 ] ],
        //                 bids: [ [ 2507.05, 0.1198 ] ],
        //                 checksum: -1400923312,
        //                 ts: '1728339446908'
        //             }
        //         ],
        //         ts: 1728339446908
        //     }
        //
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object market = this.getMarketFromArg(arg);
        object marketId = getValue(market, "id");
        object hash = "books:";
        if (isTrue(isGreaterThanOrEqual(getIndexOf(marketId, "_DMCBL"), 0)))
        {
            market = this.handleDMCBLMarketByMessageHashes(market, hash, client);
        }
        object symbol = getValue(market, "symbol");
        object channel = this.safeString(arg, "channel");
        object messageHash = add(hash, symbol);
        object data = this.safeList(message, "data", new List<object>() {});
        object rawOrderBook = this.safeDict(data, 0);
        object timestamp = this.safeInteger(rawOrderBook, "ts");
        object incrementalBook = channel;
        if (isTrue(incrementalBook))
        {
            if (!isTrue((inOp(this.orderbooks, symbol))))
            {
                object ob = this.countedOrderBook(new Dictionary<string, object>() {});
                ((IDictionary<string,object>)ob)["symbol"] = symbol;
                ((IDictionary<string,object>)this.orderbooks)[(string)symbol] = ob;
            }
            object storedOrderBook = getValue(this.orderbooks, symbol);
            object asks = this.safeList(rawOrderBook, "asks", new List<object>() {});
            object bids = this.safeList(rawOrderBook, "bids", new List<object>() {});
            this.handleDeltas(getValue(storedOrderBook, "asks"), asks);
            this.handleDeltas(getValue(storedOrderBook, "bids"), bids);
            ((IDictionary<string,object>)storedOrderBook)["timestamp"] = timestamp;
            ((IDictionary<string,object>)storedOrderBook)["datetime"] = this.iso8601(timestamp);
            object checksum = this.safeBool(this.options, "checksum", true);
            object isSnapshot = isEqual(this.safeString(message, "action"), "snapshot");
            if (isTrue(!isTrue(isSnapshot) && isTrue(checksum)))
            {
                object storedAsks = getValue(storedOrderBook, "asks");
                object storedBids = getValue(storedOrderBook, "bids");
                object asksLength = getArrayLength(storedAsks);
                object bidsLength = getArrayLength(storedBids);
                object payloadArray = new List<object>() {};
                for (object i = 0; isLessThan(i, 25); postFixIncrement(ref i))
                {
                    if (isTrue(isLessThan(i, bidsLength)))
                    {
                        ((IList<object>)payloadArray).Add(getValue(getValue(getValue(storedBids, i), 2), 0));
                        ((IList<object>)payloadArray).Add(getValue(getValue(getValue(storedBids, i), 2), 1));
                    }
                    if (isTrue(isLessThan(i, asksLength)))
                    {
                        ((IList<object>)payloadArray).Add(getValue(getValue(getValue(storedAsks, i), 2), 0));
                        ((IList<object>)payloadArray).Add(getValue(getValue(getValue(storedAsks, i), 2), 1));
                    }
                }
                object payload = String.Join(":", ((IList<object>)payloadArray).ToArray());
                object calculatedChecksum = this.crc32(payload, true);
                object responseChecksum = this.safeInteger(rawOrderBook, "checksum");
                if (isTrue(!isEqual(calculatedChecksum, responseChecksum)))
                {
                    this.spawn(this.handleCheckSumError, new object[] { client, symbol, messageHash});
                    return;
                }
            }
        } else
        {
            object orderbook = this.orderBook(new Dictionary<string, object>() {});
            object parsedOrderbook = this.parseOrderBook(rawOrderBook, symbol, timestamp);
            (orderbook as IOrderBook).reset(parsedOrderbook);
            ((IDictionary<string,object>)this.orderbooks)[(string)symbol] = orderbook;
        }
        callDynamically(client as WebSocketClient, "resolve", new object[] {getValue(this.orderbooks, symbol), messageHash});
    }

    public async virtual Task handleCheckSumError(WebSocketClient client, object symbol, object messageHash)
    {
        await this.unWatchOrderBook(symbol);
        var error = new ChecksumError(add(add(this.id, " "), this.orderbookChecksumMessage(symbol)));
        ((WebSocketClient)client).reject(error, messageHash);
    }

    public override void handleDelta(object bookside, object delta)
    {
        object bidAsk = this.parseBidAsk(delta, 0, 1);
        ((IList<object>)bidAsk).Add(delta);
        (bookside as IOrderBookSide).storeArray(bidAsk);
    }

    public override void handleDeltas(object bookside, object deltas)
    {
        for (object i = 0; isLessThan(i, getArrayLength(deltas)); postFixIncrement(ref i))
        {
            this.handleDelta(bookside, getValue(deltas, i));
        }
    }

    /**
     * @method
     * @name coincatch#watchTrades
     * @description get the list of most recent trades for a particular symbol
     * @see https://coincatch.github.io/github.io/en/spot/#trades-channel
     * @param {string} symbol unified symbol of the market to fetch trades for
     * @param {int} [since] timestamp in ms of the earliest trade to fetch
     * @param {int} [limit] the maximum amount of trades to fetch
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
     */
    public async override Task<object> watchTrades(object symbol, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        return await this.watchTradesForSymbols(new List<object>() {symbol}, since, limit, parameters);
    }

    /**
     * @method
     * @name coincatch#watchTradesForSymbols
     * @description watches information on multiple trades made in a market
     * @see https://coincatch.github.io/github.io/en/spot/#trades-channel
     * @param symbols
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of trade structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
     */
    public async override Task<object> watchTradesForSymbols(object symbols, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object symbolsLength = getArrayLength(symbols);
        if (isTrue(isEqual(symbolsLength, 0)))
        {
            throw new ArgumentsRequired ((string)add(this.id, " watchTradesForSymbols() requires a non-empty array of symbols")) ;
        }
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols);
        object topics = new List<object>() {};
        object messageHashes = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
        {
            object symbol = getValue(symbols, i);
            object market = this.market(symbol);
            var instTypeinstIdVariable = this.getPublicInstTypeAndId(market);
            var instType = ((IList<object>) instTypeinstIdVariable)[0];
            var instId = ((IList<object>) instTypeinstIdVariable)[1];
            object args = new Dictionary<string, object>() {
                { "instType", instType },
                { "channel", "trade" },
                { "instId", instId },
            };
            ((IList<object>)topics).Add(args);
            ((IList<object>)messageHashes).Add(add("trade:", symbol));
        }
        object trades = await this.watchPublicMultiple(messageHashes, messageHashes, topics, parameters);
        if (isTrue(this.newUpdates))
        {
            object first = this.safeDict(trades, 0);
            object tradeSymbol = this.safeString(first, "symbol");
            limit = callDynamically(trades, "getLimit", new object[] {tradeSymbol, limit});
        }
        return this.filterBySinceLimit(trades, since, limit, "timestamp", true);
    }

    /**
     * @method
     * @name coincatch#unWatchTrades
     * @description unsubscribe from the trades channel
     * @see https://coincatch.github.io/github.io/en/spot/#trades-channel
     * @param {string} symbol unified symbol of the market to unwatch the trades for
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @returns {any} status of the unwatch request
     */
    public async override Task<object> unWatchTrades(object symbol, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        return await this.unWatchChannel(symbol, "trade", "trade", parameters);
    }

    public virtual void handleTrades(WebSocketClient client, object message)
    {
        //
        //     {
        //         action: 'update',
        //         arg: { instType: 'sp', channel: 'trade', instId: 'ETHUSDT' },
        //         data: [ [ '1728341807469', '2421.41', '0.478', 'sell' ] ],
        //         ts: 1728341807482
        //     }
        //
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object market = this.getMarketFromArg(arg);
        object marketId = getValue(market, "id");
        object hash = "trade:";
        if (isTrue(isGreaterThanOrEqual(getIndexOf(marketId, "_DMCBL"), 0)))
        {
            market = this.handleDMCBLMarketByMessageHashes(market, hash, client);
        }
        object symbol = getValue(market, "symbol");
        if (!isTrue((inOp(this.trades, symbol))))
        {
            object limit = this.safeInteger(this.options, "tradesLimit", 1000);
            ((IDictionary<string,object>)this.trades)[(string)symbol] = new ArrayCache(limit);
        }
        object stored = getValue(this.trades, symbol);
        object data = this.safeList(message, "data", new List<object>() {});
        if (isTrue(!isEqual(data, null)))
        {
            data = this.sortBy(data, 0);
            for (object i = 0; isLessThan(i, getArrayLength(data)); postFixIncrement(ref i))
            {
                object trade = this.safeList(data, i);
                object parsed = this.parseWsTrade(trade, market);
                callDynamically(stored, "append", new object[] {parsed});
            }
        }
        object messageHash = add(hash, symbol);
        callDynamically(client as WebSocketClient, "resolve", new object[] {stored, messageHash});
    }

    public override object parseWsTrade(object trade, object market = null)
    {
        //
        //     [
        //         '1728341807469',
        //         '2421.41',
        //         '0.478',
        //         'sell'
        //     ]
        //
        object timestamp = this.safeInteger(trade, 0);
        return this.safeTrade(new Dictionary<string, object>() {
            { "id", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "symbol", getValue(market, "symbol") },
            { "side", this.safeStringLower(trade, 3) },
            { "price", this.safeString(trade, 1) },
            { "amount", this.safeString(trade, 2) },
            { "cost", null },
            { "takerOrMaker", null },
            { "type", null },
            { "order", null },
            { "fee", null },
            { "info", trade },
        }, market);
    }

    /**
     * @method
     * @name coincatch#watchBalance
     * @description watch balance and get the amount of funds available for trading or funds locked in orders
     * @see https://coincatch.github.io/github.io/en/spot/#account-channel
     * @see https://coincatch.github.io/github.io/en/mix/#account-channel
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {str} [params.type] 'spot' or 'swap' (default is 'spot')
     * @param {string} [params.instType] *swap only* 'umcbl' or 'dmcbl' (default is 'umcbl')
     * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
     */
    public async override Task<object> watchBalance(object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object type = null;
        var typeparametersVariable = this.handleMarketTypeAndParams("watchBalance", null, parameters);
        type = ((IList<object>)typeparametersVariable)[0];
        parameters = ((IList<object>)typeparametersVariable)[1];
        object instType = "spbl"; // must be lower case for spot
        if (isTrue(isEqual(type, "swap")))
        {
            instType = "umcbl";
        }
        object channel = "account";
        var instTypeparametersVariable = this.handleOptionAndParams(parameters, "watchBalance", "instType", instType);
        instType = ((IList<object>)instTypeparametersVariable)[0];
        parameters = ((IList<object>)instTypeparametersVariable)[1];
        object args = new Dictionary<string, object>() {
            { "instType", instType },
            { "channel", channel },
            { "instId", "default" },
        };
        object messageHash = add("balance:", ((string)instType).ToLower());
        return await this.watchPrivate(messageHash, messageHash, args, parameters);
    }

    public virtual void handleBalance(WebSocketClient client, object message)
    {
        //
        //  spot
        //     {
        //         action: 'snapshot',
        //         arg: { instType: 'spbl', channel: 'account', instId: 'default' },
        //         data: [
        //             {
        //                 coinId: '3',
        //                 coinName: 'ETH',
        //                 available: '0.0000832',
        //                 frozen: '0',
        //                 lock: '0'
        //             }
        //         ],
        //         ts: *************
        //     }
        //
        // //  swap
        //     {
        //         action: 'snapshot',
        //         arg: { instType: 'dmcbl', channel: 'account', instId: 'default' },
        //         data: [
        //             {
        //                 marginCoin: 'ETH',
        //                 locked: '0.********',
        //                 available: '0.********',
        //                 maxOpenPosAvailable: '0.********',
        //                 maxTransferOut: '0.********',
        //                 equity: '0.********',
        //                 usdtEquity: '0.************',
        //                 coinDisplayName: 'ETH'
        //             }
        //         ],
        //         ts: *************
        //     }
        //
        object data = this.safeList(message, "data", new List<object>() {});
        for (object i = 0; isLessThan(i, getArrayLength(data)); postFixIncrement(ref i))
        {
            object rawBalance = getValue(data, i);
            object currencyId = this.safeString2(rawBalance, "coinName", "marginCoin");
            object code = this.safeCurrencyCode(currencyId);
            object account = ((bool) isTrue((inOp(this.balance, code)))) ? getValue(this.balance, code) : this.account();
            object freeQuery = ((bool) isTrue((inOp(rawBalance, "maxTransferOut")))) ? "maxTransferOut" : "available";
            ((IDictionary<string,object>)account)["free"] = this.safeString(rawBalance, freeQuery);
            ((IDictionary<string,object>)account)["total"] = this.safeString(rawBalance, "equity");
            ((IDictionary<string,object>)account)["used"] = this.safeString(rawBalance, "frozen");
            ((IDictionary<string,object>)this.balance)[(string)code] = account;
        }
        this.balance = this.safeBalance(this.balance);
        object arg = this.safeDict(message, "arg");
        object instType = this.safeStringLower(arg, "instType");
        object messageHash = add("balance:", instType);
        callDynamically(client as WebSocketClient, "resolve", new object[] {this.balance, messageHash});
    }

    /**
     * @method
     * @name coincatch#watchOrders
     * @description watches information on multiple orders made by the user
     * @see https://coincatch.github.io/github.io/en/spot/#order-channel
     * @see https://coincatch.github.io/github.io/en/mix/#order-channel
     * @see https://coincatch.github.io/github.io/en/mix/#plan-order-channel
     * @param {string} symbol unified market symbol of the market orders were made in
     * @param {int} [since] the earliest time in ms to fetch orders for
     * @param {int} [limit] the maximum number of order structures to retrieve
     * @param {object} [params] extra parameters specific to the exchange API endpoint
     * @param {string} [params.type] 'spot' or 'swap'
     * @param {string} [params.instType] *swap only* 'umcbl' or 'dmcbl' (default is 'umcbl')
     * @param {bool} [params.trigger] *swap only* whether to watch trigger orders (default is false)
     * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
     */
    public async override Task<object> watchOrders(object symbol = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        object methodName = "watchOrders";
        await this.loadMarkets();
        object market = null;
        object marketId = null;
        if (isTrue(!isEqual(symbol, null)))
        {
            market = this.market(symbol);
            symbol = getValue(market, "symbol");
            marketId = getValue(market, "id");
        }
        object marketType = null;
        var marketTypeparametersVariable = this.handleMarketTypeAndParams(methodName, market, parameters);
        marketType = ((IList<object>)marketTypeparametersVariable)[0];
        parameters = ((IList<object>)marketTypeparametersVariable)[1];
        object instType = "spbl";
        object instId = marketId;
        if (isTrue(isEqual(marketType, "spot")))
        {
            if (isTrue(isEqual(symbol, null)))
            {
                throw new ArgumentsRequired ((string)add(add(add(add(add(this.id, " "), methodName), "() requires a symbol argument for "), marketType), " markets.")) ;
            }
        } else
        {
            instId = "default";
            instType = "umcbl";
            if (isTrue(isEqual(symbol, null)))
            {
                var instTypeparametersVariable = this.handleOptionAndParams(parameters, methodName, "instType", instType);
                instType = ((IList<object>)instTypeparametersVariable)[0];
                parameters = ((IList<object>)instTypeparametersVariable)[1];
            } else
            {
                if (isTrue(isGreaterThanOrEqual(getIndexOf(marketId, "_DMCBL"), 0)))
                {
                    instType = "dmcbl";
                }
            }
        }
        object channel = "orders";
        object isTrigger = this.safeBool(parameters, "trigger");
        if (isTrue(isTrigger))
        {
            channel = "ordersAlgo"; // channel does not return any data
            parameters = this.omit(parameters, "trigger");
        }
        object args = new Dictionary<string, object>() {
            { "instType", instType },
            { "channel", channel },
            { "instId", instId },
        };
        object messageHash = "orders";
        if (isTrue(!isEqual(symbol, null)))
        {
            messageHash = add(messageHash, add(":", symbol));
        }
        object orders = await this.watchPrivate(messageHash, messageHash, args, parameters);
        if (isTrue(this.newUpdates))
        {
            limit = callDynamically(orders, "getLimit", new object[] {symbol, limit});
        }
        return this.filterBySymbolSinceLimit(orders, symbol, since, limit, true);
    }

    public virtual void handleOrder(WebSocketClient client, object message)
    {
        //
        // spot
        //
        //     {
        //         action: 'snapshot',
        //         arg: { instType: 'spbl', channel: 'orders', instId: 'ETHUSDT_SPBL' },
        //         data: [
        //             {
        //                 instId: 'ETHUSDT_SPBL',
        //                 ordId: '1228627925964996608',
        //                 clOrdId: 'f0cccf74-c535-4523-a53d-dbe3b9958559',
        //                 px: '2000',
        //                 sz: '0.001',
        //                 notional: '2',
        //                 ordType: 'limit',
        //                 force: 'normal',
        //                 side: 'buy',
        //                 accFillSz: '0',
        //                 avgPx: '0',
        //                 status: 'new',
        //                 cTime: 1728653645030,
        //                 uTime: 1728653645030,
        //                 orderFee: [],
        //                 eps: 'API'
        //             }
        //         ],
        //         ts: 1728653645046
        //     }
        //
        // swap
        //
        //     {
        //         action: 'snapshot',
        //         arg: { instType: 'umcbl', channel: 'orders', instId: 'default' },
        //         data: [
        //             {
        //                 accFillSz: '0',
        //                 cTime: 1728653796976,
        //                 clOrdId: '1228628563272753152',
        //                 eps: 'API',
        //                 force: 'normal',
        //                 hM: 'single_hold',
        //                 instId: 'ETHUSDT_UMCBL',
        //                 lever: '5',
        //                 low: false,
        //                 notionalUsd: '20',
        //                 ordId: '1228628563188867072',
        //                 ordType: 'limit',
        //                 orderFee: [],
        //                 posSide: 'net',
        //                 px: '2000',
        //                 side: 'buy',
        //                 status: 'new',
        //                 sz: '0.01',
        //                 tS: 'buy_single',
        //                 tdMode: 'cross',
        //                 tgtCcy: 'USDT',
        //                 uTime: 1728653796976
        //             }
        //         ],
        //         ts: 1728653797002
        //     }
        //
        //
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object instType = this.safeString(arg, "instType");
        object argInstId = this.safeString(arg, "instId");
        object marketType = null;
        if (isTrue(isEqual(instType, "spbl")))
        {
            marketType = "spot";
        } else
        {
            marketType = "swap";
        }
        object data = this.safeList(message, "data", new List<object>() {});
        if (isTrue(isEqual(this.orders, null)))
        {
            object limit = this.safeInteger(this.options, "ordersLimit", 1000);
            this.orders = new ArrayCacheBySymbolById(limit);
        }
        object hash = "orders";
        object stored = this.orders;
        object symbol = null;
        for (object i = 0; isLessThan(i, getArrayLength(data)); postFixIncrement(ref i))
        {
            object order = getValue(data, i);
            object marketId = this.safeString(order, "instId", argInstId);
            object market = this.safeMarket(marketId, null, null, marketType);
            object parsed = this.parseWsOrder(order, market);
            callDynamically(stored, "append", new object[] {parsed});
            symbol = getValue(parsed, "symbol");
            object messageHash = add("orders:", symbol);
            callDynamically(client as WebSocketClient, "resolve", new object[] {stored, messageHash});
        }
        callDynamically(client as WebSocketClient, "resolve", new object[] {stored, hash});
    }

    public override object parseWsOrder(object order, object market = null)
    {
        //
        // spot
        //     {
        //         instId: 'ETHUSDT_SPBL',
        //         ordId: '1228627925964996608',
        //         clOrdId: 'f0cccf74-c535-4523-a53d-dbe3b9958559',
        //         px: '2000',
        //         sz: '0.001',
        //         notional: '2',
        //         ordType: 'limit',
        //         force: 'normal',
        //         side: 'buy',
        //         accFillSz: '0',
        //         avgPx: '0',
        //         status: 'new',
        //         cTime: 1728653645030,
        //         uTime: 1728653645030,
        //         orderFee: orderFee: [ { fee: '0', feeCcy: 'USDT' } ],
        //         eps: 'API'
        //     }
        //
        // swap
        //     {
        //         accFillSz: '0',
        //         cTime: 1728653796976,
        //         clOrdId: '1228628563272753152',
        //         eps: 'API',
        //         force: 'normal',
        //         hM: 'single_hold',
        //         instId: 'ETHUSDT_UMCBL',
        //         lever: '5',
        //         low: false,
        //         notionalUsd: '20',
        //         ordId: '1228628563188867072',
        //         ordType: 'limit',
        //         orderFee: [ { fee: '0', feeCcy: 'USDT' } ],
        //         posSide: 'net',
        //         px: '2000',
        //         side: 'buy',
        //         status: 'new',
        //         sz: '0.01',
        //         tS: 'buy_single',
        //         tdMode: 'cross',
        //         tgtCcy: 'USDT',
        //         uTime: 1728653796976
        //     }
        //
        object marketId = this.safeString(order, "instId");
        object settleId = this.safeString(order, "tgtCcy");
        market = this.safeMarketCustom(marketId, market, settleId);
        object timestamp = this.safeInteger(order, "cTime");
        object symbol = getValue(market, "symbol");
        object rawStatus = this.safeString(order, "status");
        object orderFee = this.safeList(order, "orderFee", new List<object>() {});
        object fee = this.safeDict(orderFee, 0);
        object feeCost = Precise.stringMul(this.safeString(fee, "fee"), "-1");
        object feeCurrency = this.safeString(fee, "feeCcy");
        object price = this.omitZero(this.safeString(order, "px"));
        object priceAvg = this.omitZero(this.safeString(order, "avgPx"));
        if (isTrue(isEqual(price, null)))
        {
            price = priceAvg;
        }
        object type = this.safeStringLower(order, "ordType");
        return this.safeOrder(new Dictionary<string, object>() {
            { "id", this.safeString(order, "ordId") },
            { "clientOrderId", this.safeString(order, "clOrdId") },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "lastTradeTimestamp", null },
            { "lastUpdateTimestamp", this.safeInteger(order, "uTime") },
            { "status", this.parseOrderStatus(rawStatus) },
            { "symbol", symbol },
            { "type", type },
            { "timeInForce", this.parseOrderTimeInForce(this.safeStringLower(order, "force")) },
            { "side", this.safeStringLower(order, "side") },
            { "price", price },
            { "average", this.safeString(order, "avgPx") },
            { "amount", this.safeString(order, "sz") },
            { "filled", this.safeString(order, "accFillSz") },
            { "remaining", null },
            { "triggerPrice", null },
            { "takeProfitPrice", null },
            { "stopLossPrice", null },
            { "cost", this.safeString(order, "notional") },
            { "trades", null },
            { "fee", new Dictionary<string, object>() {
                { "currency", feeCurrency },
                { "cost", feeCost },
            } },
            { "reduceOnly", this.safeBool(order, "low") },
            { "postOnly", null },
            { "info", order },
        }, market);
    }

    /**
     * @method
     * @name coincatch#watchPositions
     * @description watch all open positions
     * @see https://coincatch.github.io/github.io/en/mix/#positions-channel
     * @param {string[]|undefined} symbols list of unified market symbols
     * @param since
     * @param limit
     * @param {object} params extra parameters specific to the exchange API endpoint
     * @returns {object[]} a list of [position structure]{@link https://docs.ccxt.com/en/latest/manual.html#position-structure}
     */
    public async override Task<object> watchPositions(object symbols = null, object since = null, object limit = null, object parameters = null)
    {
        parameters ??= new Dictionary<string, object>();
        await this.loadMarkets();
        symbols = this.marketSymbols(symbols, "swap");
        object messageHashes = new List<object>() {};
        object hash = "positions";
        object instTypes = new List<object>() {};
        if (isTrue(!isEqual(symbols, null)))
        {
            for (object i = 0; isLessThan(i, getArrayLength(symbols)); postFixIncrement(ref i))
            {
                object symbol = getValue(symbols, i);
                object market = this.market(symbol);
                object instType = this.getPrivateInstType(market);
                if (!isTrue(this.inArray(instType, instTypes)))
                {
                    ((IList<object>)instTypes).Add(instType);
                }
                ((IList<object>)messageHashes).Add(add(add(hash, "::"), symbol));
            }
        } else
        {
            instTypes = new List<object>() {"umcbl", "dmcbl"};
            ((IList<object>)messageHashes).Add(hash);
        }
        object args = new List<object>() {};
        object subscribeHashes = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(instTypes)); postFixIncrement(ref i))
        {
            object instType = getValue(instTypes, i);
            object arg = new Dictionary<string, object>() {
                { "instType", instType },
                { "channel", hash },
                { "instId", "default" },
            };
            ((IList<object>)subscribeHashes).Add(add(add(hash, "::"), instType));
            ((IList<object>)args).Add(arg);
        }
        object newPositions = await this.watchPrivateMultiple(messageHashes, subscribeHashes, args, parameters);
        if (isTrue(this.newUpdates))
        {
            return newPositions;
        }
        return this.filterBySymbolsSinceLimit(newPositions, symbols, since, limit, true);
    }

    public virtual object getPrivateInstType(object market)
    {
        object marketId = getValue(market, "id");
        if (isTrue(isGreaterThanOrEqual(getIndexOf(marketId, "_DMCBL"), 0)))
        {
            return "dmcbl";
        }
        return "umcbl";
    }

    public virtual void handlePositions(WebSocketClient client, object message)
    {
        //
        //     {
        //         action: 'snapshot',
        //         arg: { instType: 'umcbl', channel: 'positions', instId: 'default' },
        //         data: [
        //             {
        //                 posId: '1221355728745619456',
        //                 instId: 'ETHUSDT_UMCBL',
        //                 instName: 'ETHUSDT',
        //                 marginCoin: 'USDT',
        //                 margin: '5.27182',
        //                 marginMode: 'crossed',
        //                 holdSide: 'long',
        //                 holdMode: 'single_hold',
        //                 total: '0.01',
        //                 available: '0.01',
        //                 locked: '0',
        //                 averageOpenPrice: '2635.91',
        //                 leverage: 5,
        //                 achievedProfits: '0',
        //                 upl: '-0.0267',
        //                 uplRate: '-0.005064664576',
        //                 liqPx: '-3110.66866033',
        //                 keepMarginRate: '0.0033',
        //                 marginRate: '0.002460827254',
        //                 cTime: '1726919818102',
        //                 uTime: '1728919604312',
        //                 markPrice: '2633.24',
        //                 autoMargin: 'off'
        //             }
        //         ],
        //         ts: 1728919604329
        //     }
        //
        if (isTrue(isEqual(this.positions, null)))
        {
            this.positions = new ArrayCacheBySymbolBySide();
        }
        object cache = this.positions;
        object rawPositions = this.safeValue(message, "data", new List<object>() {});
        object dataLength = getArrayLength(rawPositions);
        if (isTrue(isEqual(dataLength, 0)))
        {
            return;
        }
        object newPositions = new List<object>() {};
        object symbols = new List<object>() {};
        for (object i = 0; isLessThan(i, getArrayLength(rawPositions)); postFixIncrement(ref i))
        {
            object rawPosition = getValue(rawPositions, i);
            object position = this.parseWsPosition(rawPosition);
            ((IList<object>)symbols).Add(getValue(position, "symbol"));
            ((IList<object>)newPositions).Add(position);
            callDynamically(cache, "append", new object[] {position});
        }
        object hash = "positions";
        object messageHashes = this.findMessageHashes(client as WebSocketClient, hash);
        for (object i = 0; isLessThan(i, getArrayLength(messageHashes)); postFixIncrement(ref i))
        {
            object messageHash = getValue(messageHashes, i);
            object parts = ((string)messageHash).Split(new [] {((string)"::")}, StringSplitOptions.None).ToList<object>();
            object symbol = getValue(parts, 1);
            if (isTrue(this.inArray(symbol, symbols)))
            {
                object positionsForSymbol = new List<object>() {};
                for (object j = 0; isLessThan(j, getArrayLength(newPositions)); postFixIncrement(ref j))
                {
                    object position = getValue(newPositions, j);
                    if (isTrue(isEqual(getValue(position, "symbol"), symbol)))
                    {
                        ((IList<object>)positionsForSymbol).Add(position);
                    }
                }
                callDynamically(client as WebSocketClient, "resolve", new object[] {positionsForSymbol, messageHash});
            }
        }
        callDynamically(client as WebSocketClient, "resolve", new object[] {newPositions, hash});
    }

    public virtual object parseWsPosition(object position, object market = null)
    {
        //
        //     {
        //         posId: '1221355728745619456',
        //         instId: 'ETHUSDT_UMCBL',
        //         instName: 'ETHUSDT',
        //         marginCoin: 'USDT',
        //         margin: '5.27182',
        //         marginMode: 'crossed',
        //         holdSide: 'long',
        //         holdMode: 'single_hold',
        //         total: '0.01',
        //         available: '0.01',
        //         locked: '0',
        //         averageOpenPrice: '2635.91',
        //         leverage: 5,
        //         achievedProfits: '0',
        //         upl: '-0.0267',
        //         uplRate: '-0.005064664576',
        //         liqPx: '-3110.66866033',
        //         keepMarginRate: '0.0033',
        //         marginRate: '0.002460827254',
        //         cTime: '1726919818102',
        //         uTime: '1728919604312',
        //         markPrice: '2633.24',
        //         autoMargin: 'off'
        //     }
        //
        object marketId = this.safeString(position, "symbol");
        object settleId = this.safeString(position, "marginCoin");
        market = this.safeMarketCustom(marketId, market, settleId);
        object timestamp = this.safeInteger(position, "cTime");
        object marginModeId = this.safeString(position, "marginMode");
        object marginMode = this.getSupportedMapping(marginModeId, new Dictionary<string, object>() {
            { "crossed", "cross" },
            { "isolated", "isolated" },
        });
        object isHedged = null;
        object holdMode = this.safeString(position, "holdMode");
        if (isTrue(isEqual(holdMode, "double_hold")))
        {
            isHedged = true;
        } else if (isTrue(isEqual(holdMode, "single_hold")))
        {
            isHedged = false;
        }
        object percentageDecimal = this.safeString(position, "uplRate");
        object percentage = Precise.stringMul(percentageDecimal, "100");
        object margin = this.safeNumber(position, "margin");
        return this.safePosition(new Dictionary<string, object>() {
            { "symbol", getValue(market, "symbol") },
            { "id", null },
            { "timestamp", timestamp },
            { "datetime", this.iso8601(timestamp) },
            { "contracts", this.safeNumber(position, "total") },
            { "contractSize", null },
            { "side", this.safeStringLower(position, "holdSide") },
            { "notional", margin },
            { "leverage", this.safeInteger(position, "leverage") },
            { "unrealizedPnl", this.safeNumber(position, "upl") },
            { "realizedPnl", this.safeNumber(position, "achievedProfits") },
            { "collateral", null },
            { "entryPrice", this.safeNumber(position, "averageOpenPrice") },
            { "markPrice", this.safeNumber(position, "markPrice") },
            { "liquidationPrice", this.safeNumber(position, "liqPx") },
            { "marginMode", marginMode },
            { "hedged", isHedged },
            { "maintenanceMargin", null },
            { "maintenanceMarginPercentage", this.safeNumber(position, "keepMarginRate") },
            { "initialMargin", margin },
            { "initialMarginPercentage", null },
            { "marginRatio", this.safeNumber(position, "marginRate") },
            { "lastUpdateTimestamp", this.safeInteger(position, "uTime") },
            { "lastPrice", null },
            { "stopLossPrice", null },
            { "takeProfitPrice", null },
            { "percentage", percentage },
            { "info", position },
        });
    }

    public virtual object handleErrorMessage(WebSocketClient client, object message)
    {
        //
        //    { event: "error", code: 30001, msg: "Channel does not exist" }
        //
        object eventVar = this.safeString(message, "event");
        try
        {
            if (isTrue(isEqual(eventVar, "error")))
            {
                object code = this.safeString(message, "code");
                object feedback = add(add(this.id, " "), this.json(message));
                this.throwExactlyMatchedException(getValue(getValue(this.exceptions, "ws"), "exact"), code, feedback);
                object msg = this.safeString(message, "msg", "");
                this.throwBroadlyMatchedException(getValue(getValue(this.exceptions, "ws"), "broad"), msg, feedback);
                throw new ExchangeError ((string)feedback) ;
            }
            return false;
        } catch(Exception e)
        {
            if (isTrue(e is AuthenticationError))
            {
                object messageHash = "authenticated";
                ((WebSocketClient)client).reject(e, messageHash);
                if (isTrue(inOp(((WebSocketClient)client).subscriptions, messageHash)))
                {
                    ((IDictionary<string,object>)((WebSocketClient)client).subscriptions).Remove((string)messageHash);
                }
            } else
            {
                ((WebSocketClient)client).reject(e);
            }
            return true;
        }
    }

    public override void handleMessage(WebSocketClient client, object message)
    {
        // todo handle with subscribe and unsubscribe
        if (isTrue(this.handleErrorMessage(client as WebSocketClient, message)))
        {
            return;
        }
        object content = this.safeString(message, "message");
        if (isTrue(isEqual(content, "pong")))
        {
            this.handlePong(client as WebSocketClient, message);
            return;
        }
        if (isTrue(isEqual(message, "pong")))
        {
            this.handlePong(client as WebSocketClient, message);
            return;
        }
        object eventVar = this.safeString(message, "event");
        if (isTrue(isEqual(eventVar, "login")))
        {
            this.handleAuthenticate(client as WebSocketClient, message);
            return;
        }
        if (isTrue(isEqual(eventVar, "subscribe")))
        {
            this.handleSubscriptionStatus(client as WebSocketClient, message);
            return;
        }
        if (isTrue(isEqual(eventVar, "unsubscribe")))
        {
            this.handleUnSubscriptionStatus(client as WebSocketClient, message);
            return;
        }
        object data = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object channel = this.safeString(data, "channel");
        if (isTrue(isEqual(channel, "ticker")))
        {
            this.handleTicker(client as WebSocketClient, message);
        }
        if (isTrue(isGreaterThanOrEqual(getIndexOf(channel, "candle"), 0)))
        {
            this.handleOHLCV(client as WebSocketClient, message);
        }
        if (isTrue(isGreaterThanOrEqual(getIndexOf(channel, "books"), 0)))
        {
            this.handleOrderBook(client as WebSocketClient, message);
        }
        if (isTrue(isEqual(channel, "trade")))
        {
            this.handleTrades(client as WebSocketClient, message);
        }
        if (isTrue(isEqual(channel, "account")))
        {
            this.handleBalance(client as WebSocketClient, message);
        }
        if (isTrue(isTrue((isEqual(channel, "orders"))) || isTrue((isEqual(channel, "ordersAlgo")))))
        {
            this.handleOrder(client as WebSocketClient, message);
        }
        if (isTrue(isEqual(channel, "positions")))
        {
            this.handlePositions(client as WebSocketClient, message);
        }
    }

    public override object ping(WebSocketClient client)
    {
        return "ping";
    }

    public virtual object handlePong(WebSocketClient client, object message)
    {
        client.lastPong = this.milliseconds();
        return message;
    }

    public virtual object handleSubscriptionStatus(WebSocketClient client, object message)
    {
        return message;
    }

    public virtual object handleUnSubscriptionStatus(WebSocketClient client, object message)
    {
        object argsList = this.safeList(message, "args");
        if (isTrue(isEqual(argsList, null)))
        {
            argsList = new List<object> {this.safeDict(message, "arg", new Dictionary<string, object>() {})};
        }
        for (object i = 0; isLessThan(i, getArrayLength(argsList)); postFixIncrement(ref i))
        {
            object arg = getValue(argsList, i);
            object channel = this.safeString(arg, "channel");
            if (isTrue(isEqual(channel, "books")))
            {
                this.handleOrderBookUnSubscription(client as WebSocketClient, message);
            } else if (isTrue(isEqual(channel, "trade")))
            {
                this.handleTradesUnSubscription(client as WebSocketClient, message);
            } else if (isTrue(isEqual(channel, "ticker")))
            {
                this.handleTickerUnSubscription(client as WebSocketClient, message);
            } else if (isTrue(((string)channel).StartsWith(((string)"candle"))))
            {
                this.handleOHLCVUnSubscription(client as WebSocketClient, message);
            }
        }
        return message;
    }

    public virtual void handleOrderBookUnSubscription(WebSocketClient client, object message)
    {
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object instType = this.safeStringLower(arg, "instType");
        object type = ((bool) isTrue((isEqual(instType, "sp")))) ? "spot" : "swap";
        object instId = this.safeString(arg, "instId");
        object market = this.safeMarket(instId, null, null, type);
        object symbol = getValue(market, "symbol");
        object messageHash = add("unsubscribe:orderbook:", getValue(market, "symbol"));
        object subMessageHash = add("orderbook:", symbol);
        if (isTrue(inOp(this.orderbooks, symbol)))
        {
            ((IDictionary<string,object>)this.orderbooks).Remove((string)symbol);
        }
        if (isTrue(inOp(((WebSocketClient)client).subscriptions, subMessageHash)))
        {
            ((IDictionary<string,object>)((WebSocketClient)client).subscriptions).Remove((string)subMessageHash);
        }
        if (isTrue(inOp(((WebSocketClient)client).subscriptions, messageHash)))
        {
            ((IDictionary<string,object>)((WebSocketClient)client).subscriptions).Remove((string)messageHash);
        }
        var error = new UnsubscribeError(add(add(this.id, " orderbook "), symbol));
        ((WebSocketClient)client).reject(error, subMessageHash);
        callDynamically(client as WebSocketClient, "resolve", new object[] {true, messageHash});
    }

    public virtual void handleTradesUnSubscription(WebSocketClient client, object message)
    {
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object instType = this.safeStringLower(arg, "instType");
        object type = ((bool) isTrue((isEqual(instType, "sp")))) ? "spot" : "swap";
        object instId = this.safeString(arg, "instId");
        object market = this.safeMarket(instId, null, null, type);
        object symbol = getValue(market, "symbol");
        object messageHash = add("unsubscribe:trade:", getValue(market, "symbol"));
        object subMessageHash = add("trade:", symbol);
        if (isTrue(inOp(this.trades, symbol)))
        {
            ((IDictionary<string,object>)this.trades).Remove((string)symbol);
        }
        if (isTrue(inOp(((WebSocketClient)client).subscriptions, subMessageHash)))
        {
            ((IDictionary<string,object>)((WebSocketClient)client).subscriptions).Remove((string)subMessageHash);
        }
        if (isTrue(inOp(((WebSocketClient)client).subscriptions, messageHash)))
        {
            ((IDictionary<string,object>)((WebSocketClient)client).subscriptions).Remove((string)messageHash);
        }
        var error = new UnsubscribeError(add(add(this.id, " trades "), symbol));
        ((WebSocketClient)client).reject(error, subMessageHash);
        callDynamically(client as WebSocketClient, "resolve", new object[] {true, messageHash});
    }

    public virtual void handleTickerUnSubscription(WebSocketClient client, object message)
    {
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object instType = this.safeStringLower(arg, "instType");
        object type = ((bool) isTrue((isEqual(instType, "sp")))) ? "spot" : "swap";
        object instId = this.safeString(arg, "instId");
        object market = this.safeMarket(instId, null, null, type);
        object symbol = getValue(market, "symbol");
        object messageHash = add("unsubscribe:ticker:", getValue(market, "symbol"));
        object subMessageHash = add("ticker:", symbol);
        if (isTrue(inOp(this.tickers, symbol)))
        {
            ((IDictionary<string,object>)this.tickers).Remove((string)symbol);
        }
        if (isTrue(inOp(((WebSocketClient)client).subscriptions, subMessageHash)))
        {
            ((IDictionary<string,object>)((WebSocketClient)client).subscriptions).Remove((string)subMessageHash);
        }
        if (isTrue(inOp(((WebSocketClient)client).subscriptions, messageHash)))
        {
            ((IDictionary<string,object>)((WebSocketClient)client).subscriptions).Remove((string)messageHash);
        }
        var error = new UnsubscribeError(add(add(this.id, " ticker "), symbol));
        ((WebSocketClient)client).reject(error, subMessageHash);
        callDynamically(client as WebSocketClient, "resolve", new object[] {true, messageHash});
    }

    public virtual void handleOHLCVUnSubscription(WebSocketClient client, object message)
    {
        object arg = this.safeDict(message, "arg", new Dictionary<string, object>() {});
        object instType = this.safeStringLower(arg, "instType");
        object type = ((bool) isTrue((isEqual(instType, "sp")))) ? "spot" : "swap";
        object instId = this.safeString(arg, "instId");
        object channel = this.safeString(arg, "channel");
        object interval = ((string)channel).Replace((string)"candle", (string)"");
        object timeframes = this.safeDict(this.options, "timeframesForWs");
        object timeframe = this.findTimeframe(interval, timeframes);
        object market = this.safeMarket(instId, null, null, type);
        object symbol = getValue(market, "symbol");
        object messageHash = add(add(add("unsubscribe:ohlcv:", timeframe), ":"), getValue(market, "symbol"));
        object subMessageHash = add(add(add("ohlcv:", symbol), ":"), timeframe);
        if (isTrue(inOp(this.ohlcvs, symbol)))
        {
            if (isTrue(inOp(getValue(this.ohlcvs, symbol), timeframe)))
            {
                ((IDictionary<string,object>)getValue(this.ohlcvs, symbol)).Remove((string)timeframe);
            }
        }
        this.cleanUnsubscription(client as WebSocketClient, subMessageHash, messageHash);
    }
}
