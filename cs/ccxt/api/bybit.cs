// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

namespace ccxt;

public partial class bybit : Exchange
{
    public bybit (object args = null): base(args) {}

    public async Task<object> publicGetSpotV3PublicSymbols (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicSymbols",parameters);
    }

    public async Task<object> publicGetSpotV3PublicQuoteDepth (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicQuoteDepth",parameters);
    }

    public async Task<object> publicGetSpotV3PublicQuoteDepthMerged (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicQuoteDepthMerged",parameters);
    }

    public async Task<object> publicGetSpotV3PublicQuoteTrades (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicQuoteTrades",parameters);
    }

    public async Task<object> publicGetSpotV3PublicQuoteKline (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicQuoteKline",parameters);
    }

    public async Task<object> publicGetSpotV3PublicQuoteTicker24hr (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicQuoteTicker24hr",parameters);
    }

    public async Task<object> publicGetSpotV3PublicQuoteTickerPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicQuoteTickerPrice",parameters);
    }

    public async Task<object> publicGetSpotV3PublicQuoteTickerBookTicker (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicQuoteTickerBookTicker",parameters);
    }

    public async Task<object> publicGetSpotV3PublicServerTime (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicServerTime",parameters);
    }

    public async Task<object> publicGetSpotV3PublicInfos (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicInfos",parameters);
    }

    public async Task<object> publicGetSpotV3PublicMarginProductInfos (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicMarginProductInfos",parameters);
    }

    public async Task<object> publicGetSpotV3PublicMarginEnsureTokens (object parameters = null)
    {
        return await this.callAsync ("publicGetSpotV3PublicMarginEnsureTokens",parameters);
    }

    public async Task<object> publicGetV3PublicTime (object parameters = null)
    {
        return await this.callAsync ("publicGetV3PublicTime",parameters);
    }

    public async Task<object> publicGetContractV3PublicCopytradingSymbolList (object parameters = null)
    {
        return await this.callAsync ("publicGetContractV3PublicCopytradingSymbolList",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicOrderBookL2 (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicOrderBookL2",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicKline (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicKline",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicTickers",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicInstrumentsInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicInstrumentsInfo",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicMarkPriceKline (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicMarkPriceKline",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicIndexPriceKline (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicIndexPriceKline",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicFundingHistoryFundingRate (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicFundingHistoryFundingRate",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicRiskLimitList (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicRiskLimitList",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicDeliveryPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicDeliveryPrice",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicRecentTrade (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicRecentTrade",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicOpenInterest (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicOpenInterest",parameters);
    }

    public async Task<object> publicGetDerivativesV3PublicInsurance (object parameters = null)
    {
        return await this.callAsync ("publicGetDerivativesV3PublicInsurance",parameters);
    }

    public async Task<object> publicGetV5AnnouncementsIndex (object parameters = null)
    {
        return await this.callAsync ("publicGetV5AnnouncementsIndex",parameters);
    }

    public async Task<object> publicGetV5MarketTime (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketTime",parameters);
    }

    public async Task<object> publicGetV5MarketKline (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketKline",parameters);
    }

    public async Task<object> publicGetV5MarketMarkPriceKline (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketMarkPriceKline",parameters);
    }

    public async Task<object> publicGetV5MarketIndexPriceKline (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketIndexPriceKline",parameters);
    }

    public async Task<object> publicGetV5MarketPremiumIndexPriceKline (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketPremiumIndexPriceKline",parameters);
    }

    public async Task<object> publicGetV5MarketInstrumentsInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketInstrumentsInfo",parameters);
    }

    public async Task<object> publicGetV5MarketOrderbook (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketOrderbook",parameters);
    }

    public async Task<object> publicGetV5MarketTickers (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketTickers",parameters);
    }

    public async Task<object> publicGetV5MarketFundingHistory (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketFundingHistory",parameters);
    }

    public async Task<object> publicGetV5MarketRecentTrade (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketRecentTrade",parameters);
    }

    public async Task<object> publicGetV5MarketOpenInterest (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketOpenInterest",parameters);
    }

    public async Task<object> publicGetV5MarketHistoricalVolatility (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketHistoricalVolatility",parameters);
    }

    public async Task<object> publicGetV5MarketInsurance (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketInsurance",parameters);
    }

    public async Task<object> publicGetV5MarketRiskLimit (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketRiskLimit",parameters);
    }

    public async Task<object> publicGetV5MarketDeliveryPrice (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketDeliveryPrice",parameters);
    }

    public async Task<object> publicGetV5MarketAccountRatio (object parameters = null)
    {
        return await this.callAsync ("publicGetV5MarketAccountRatio",parameters);
    }

    public async Task<object> publicGetV5SpotLeverTokenInfo (object parameters = null)
    {
        return await this.callAsync ("publicGetV5SpotLeverTokenInfo",parameters);
    }

    public async Task<object> publicGetV5SpotLeverTokenReference (object parameters = null)
    {
        return await this.callAsync ("publicGetV5SpotLeverTokenReference",parameters);
    }

    public async Task<object> publicGetV5SpotMarginTradeData (object parameters = null)
    {
        return await this.callAsync ("publicGetV5SpotMarginTradeData",parameters);
    }

    public async Task<object> publicGetV5SpotMarginTradeCollateral (object parameters = null)
    {
        return await this.callAsync ("publicGetV5SpotMarginTradeCollateral",parameters);
    }

    public async Task<object> publicGetV5SpotCrossMarginTradeData (object parameters = null)
    {
        return await this.callAsync ("publicGetV5SpotCrossMarginTradeData",parameters);
    }

    public async Task<object> publicGetV5SpotCrossMarginTradePledgeToken (object parameters = null)
    {
        return await this.callAsync ("publicGetV5SpotCrossMarginTradePledgeToken",parameters);
    }

    public async Task<object> publicGetV5SpotCrossMarginTradeBorrowToken (object parameters = null)
    {
        return await this.callAsync ("publicGetV5SpotCrossMarginTradeBorrowToken",parameters);
    }

    public async Task<object> publicGetV5CryptoLoanCollateralData (object parameters = null)
    {
        return await this.callAsync ("publicGetV5CryptoLoanCollateralData",parameters);
    }

    public async Task<object> publicGetV5CryptoLoanLoanableData (object parameters = null)
    {
        return await this.callAsync ("publicGetV5CryptoLoanLoanableData",parameters);
    }

    public async Task<object> publicGetV5InsLoanProductInfos (object parameters = null)
    {
        return await this.callAsync ("publicGetV5InsLoanProductInfos",parameters);
    }

    public async Task<object> publicGetV5InsLoanEnsureTokensConvert (object parameters = null)
    {
        return await this.callAsync ("publicGetV5InsLoanEnsureTokensConvert",parameters);
    }

    public async Task<object> publicGetV5EarnProduct (object parameters = null)
    {
        return await this.callAsync ("publicGetV5EarnProduct",parameters);
    }

    public async Task<object> privateGetV5MarketInstrumentsInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5MarketInstrumentsInfo",parameters);
    }

    public async Task<object> privateGetV2PrivateWalletFundRecords (object parameters = null)
    {
        return await this.callAsync ("privateGetV2PrivateWalletFundRecords",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateOrder",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateOpenOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateOpenOrders",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateHistoryOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateHistoryOrders",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateMyTrades (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateMyTrades",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateAccount (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateAccount",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateReference (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateReference",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateRecord",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateCrossMarginOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateCrossMarginOrders",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateCrossMarginAccount (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateCrossMarginAccount",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateCrossMarginLoanInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateCrossMarginLoanInfo",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateCrossMarginRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateCrossMarginRepayHistory",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateMarginLoanInfos (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateMarginLoanInfos",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateMarginRepaidInfos (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateMarginRepaidInfos",parameters);
    }

    public async Task<object> privateGetSpotV3PrivateMarginLtv (object parameters = null)
    {
        return await this.callAsync ("privateGetSpotV3PrivateMarginLtv",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateTransferInterTransferListQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateTransferInterTransferListQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateTransferSubMemberListQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateTransferSubMemberListQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateTransferSubMemberTransferListQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateTransferSubMemberTransferListQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateTransferUniversalTransferListQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateTransferUniversalTransferListQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateCoinInfoQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateCoinInfoQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateDepositAddressQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateDepositAddressQuery",parameters);
    }

    public async Task<object> privateGetContractV3PrivateCopytradingOrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateCopytradingOrderList",parameters);
    }

    public async Task<object> privateGetContractV3PrivateCopytradingPositionList (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateCopytradingPositionList",parameters);
    }

    public async Task<object> privateGetContractV3PrivateCopytradingWalletBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateCopytradingWalletBalance",parameters);
    }

    public async Task<object> privateGetContractV3PrivatePositionLimitInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivatePositionLimitInfo",parameters);
    }

    public async Task<object> privateGetContractV3PrivateOrderUnfilledOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateOrderUnfilledOrders",parameters);
    }

    public async Task<object> privateGetContractV3PrivateOrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateOrderList",parameters);
    }

    public async Task<object> privateGetContractV3PrivatePositionList (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivatePositionList",parameters);
    }

    public async Task<object> privateGetContractV3PrivateExecutionList (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateExecutionList",parameters);
    }

    public async Task<object> privateGetContractV3PrivatePositionClosedPnl (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivatePositionClosedPnl",parameters);
    }

    public async Task<object> privateGetContractV3PrivateAccountWalletBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateAccountWalletBalance",parameters);
    }

    public async Task<object> privateGetContractV3PrivateAccountFeeRate (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateAccountFeeRate",parameters);
    }

    public async Task<object> privateGetContractV3PrivateAccountWalletFundRecords (object parameters = null)
    {
        return await this.callAsync ("privateGetContractV3PrivateAccountWalletFundRecords",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateOrderUnfilledOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateOrderUnfilledOrders",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateOrderList (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateOrderList",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivatePositionList (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivatePositionList",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateExecutionList (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateExecutionList",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateDeliveryRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateDeliveryRecord",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateSettlementRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateSettlementRecord",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateAccountWalletBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateAccountWalletBalance",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateAccountTransactionLog (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateAccountTransactionLog",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateAccountBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateAccountBorrowHistory",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateAccountBorrowRate (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateAccountBorrowRate",parameters);
    }

    public async Task<object> privateGetUnifiedV3PrivateAccountInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetUnifiedV3PrivateAccountInfo",parameters);
    }

    public async Task<object> privateGetUserV3PrivateFrozenSubMember (object parameters = null)
    {
        return await this.callAsync ("privateGetUserV3PrivateFrozenSubMember",parameters);
    }

    public async Task<object> privateGetUserV3PrivateQuerySubMembers (object parameters = null)
    {
        return await this.callAsync ("privateGetUserV3PrivateQuerySubMembers",parameters);
    }

    public async Task<object> privateGetUserV3PrivateQueryApi (object parameters = null)
    {
        return await this.callAsync ("privateGetUserV3PrivateQueryApi",parameters);
    }

    public async Task<object> privateGetUserV3PrivateGetMemberType (object parameters = null)
    {
        return await this.callAsync ("privateGetUserV3PrivateGetMemberType",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateTransferTransferCoinListQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateTransferTransferCoinListQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateTransferAccountCoinBalanceQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateTransferAccountCoinBalanceQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateTransferAccountCoinsBalanceQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateTransferAccountCoinsBalanceQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateTransferAssetInfoQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateTransferAssetInfoQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PublicDepositAllowedDepositListQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PublicDepositAllowedDepositListQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateDepositRecordQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateDepositRecordQuery",parameters);
    }

    public async Task<object> privateGetAssetV3PrivateWithdrawRecordQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetAssetV3PrivateWithdrawRecordQuery",parameters);
    }

    public async Task<object> privateGetV5OrderRealtime (object parameters = null)
    {
        return await this.callAsync ("privateGetV5OrderRealtime",parameters);
    }

    public async Task<object> privateGetV5OrderHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5OrderHistory",parameters);
    }

    public async Task<object> privateGetV5OrderSpotBorrowCheck (object parameters = null)
    {
        return await this.callAsync ("privateGetV5OrderSpotBorrowCheck",parameters);
    }

    public async Task<object> privateGetV5PositionList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PositionList",parameters);
    }

    public async Task<object> privateGetV5ExecutionList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5ExecutionList",parameters);
    }

    public async Task<object> privateGetV5PositionClosedPnl (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PositionClosedPnl",parameters);
    }

    public async Task<object> privateGetV5PositionMoveHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PositionMoveHistory",parameters);
    }

    public async Task<object> privateGetV5PreUpgradeOrderHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PreUpgradeOrderHistory",parameters);
    }

    public async Task<object> privateGetV5PreUpgradeExecutionList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PreUpgradeExecutionList",parameters);
    }

    public async Task<object> privateGetV5PreUpgradePositionClosedPnl (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PreUpgradePositionClosedPnl",parameters);
    }

    public async Task<object> privateGetV5PreUpgradeAccountTransactionLog (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PreUpgradeAccountTransactionLog",parameters);
    }

    public async Task<object> privateGetV5PreUpgradeAssetDeliveryRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PreUpgradeAssetDeliveryRecord",parameters);
    }

    public async Task<object> privateGetV5PreUpgradeAssetSettlementRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5PreUpgradeAssetSettlementRecord",parameters);
    }

    public async Task<object> privateGetV5AccountWalletBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountWalletBalance",parameters);
    }

    public async Task<object> privateGetV5AccountBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountBorrowHistory",parameters);
    }

    public async Task<object> privateGetV5AccountCollateralInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountCollateralInfo",parameters);
    }

    public async Task<object> privateGetV5AssetCoinGreeks (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetCoinGreeks",parameters);
    }

    public async Task<object> privateGetV5AccountFeeRate (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountFeeRate",parameters);
    }

    public async Task<object> privateGetV5AccountInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountInfo",parameters);
    }

    public async Task<object> privateGetV5AccountTransactionLog (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountTransactionLog",parameters);
    }

    public async Task<object> privateGetV5AccountContractTransactionLog (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountContractTransactionLog",parameters);
    }

    public async Task<object> privateGetV5AccountSmpGroup (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountSmpGroup",parameters);
    }

    public async Task<object> privateGetV5AccountMmpState (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountMmpState",parameters);
    }

    public async Task<object> privateGetV5AccountWithdrawal (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AccountWithdrawal",parameters);
    }

    public async Task<object> privateGetV5AssetExchangeQueryCoinList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetExchangeQueryCoinList",parameters);
    }

    public async Task<object> privateGetV5AssetExchangeConvertResultQuery (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetExchangeConvertResultQuery",parameters);
    }

    public async Task<object> privateGetV5AssetExchangeQueryConvertHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetExchangeQueryConvertHistory",parameters);
    }

    public async Task<object> privateGetV5AssetExchangeOrderRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetExchangeOrderRecord",parameters);
    }

    public async Task<object> privateGetV5AssetDeliveryRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetDeliveryRecord",parameters);
    }

    public async Task<object> privateGetV5AssetSettlementRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetSettlementRecord",parameters);
    }

    public async Task<object> privateGetV5AssetTransferQueryAssetInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetTransferQueryAssetInfo",parameters);
    }

    public async Task<object> privateGetV5AssetTransferQueryAccountCoinsBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetTransferQueryAccountCoinsBalance",parameters);
    }

    public async Task<object> privateGetV5AssetTransferQueryAccountCoinBalance (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetTransferQueryAccountCoinBalance",parameters);
    }

    public async Task<object> privateGetV5AssetTransferQueryTransferCoinList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetTransferQueryTransferCoinList",parameters);
    }

    public async Task<object> privateGetV5AssetTransferQueryInterTransferList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetTransferQueryInterTransferList",parameters);
    }

    public async Task<object> privateGetV5AssetTransferQuerySubMemberList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetTransferQuerySubMemberList",parameters);
    }

    public async Task<object> privateGetV5AssetTransferQueryUniversalTransferList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetTransferQueryUniversalTransferList",parameters);
    }

    public async Task<object> privateGetV5AssetDepositQueryAllowedList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetDepositQueryAllowedList",parameters);
    }

    public async Task<object> privateGetV5AssetDepositQueryRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetDepositQueryRecord",parameters);
    }

    public async Task<object> privateGetV5AssetDepositQuerySubMemberRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetDepositQuerySubMemberRecord",parameters);
    }

    public async Task<object> privateGetV5AssetDepositQueryInternalRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetDepositQueryInternalRecord",parameters);
    }

    public async Task<object> privateGetV5AssetDepositQueryAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetDepositQueryAddress",parameters);
    }

    public async Task<object> privateGetV5AssetDepositQuerySubMemberAddress (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetDepositQuerySubMemberAddress",parameters);
    }

    public async Task<object> privateGetV5AssetCoinQueryInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetCoinQueryInfo",parameters);
    }

    public async Task<object> privateGetV5AssetWithdrawQueryRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetWithdrawQueryRecord",parameters);
    }

    public async Task<object> privateGetV5AssetWithdrawWithdrawableAmount (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetWithdrawWithdrawableAmount",parameters);
    }

    public async Task<object> privateGetV5AssetWithdrawVaspList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AssetWithdrawVaspList",parameters);
    }

    public async Task<object> privateGetV5UserQuerySubMembers (object parameters = null)
    {
        return await this.callAsync ("privateGetV5UserQuerySubMembers",parameters);
    }

    public async Task<object> privateGetV5UserQueryApi (object parameters = null)
    {
        return await this.callAsync ("privateGetV5UserQueryApi",parameters);
    }

    public async Task<object> privateGetV5UserSubApikeys (object parameters = null)
    {
        return await this.callAsync ("privateGetV5UserSubApikeys",parameters);
    }

    public async Task<object> privateGetV5UserGetMemberType (object parameters = null)
    {
        return await this.callAsync ("privateGetV5UserGetMemberType",parameters);
    }

    public async Task<object> privateGetV5UserAffCustomerInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5UserAffCustomerInfo",parameters);
    }

    public async Task<object> privateGetV5UserDelSubmember (object parameters = null)
    {
        return await this.callAsync ("privateGetV5UserDelSubmember",parameters);
    }

    public async Task<object> privateGetV5UserSubmembers (object parameters = null)
    {
        return await this.callAsync ("privateGetV5UserSubmembers",parameters);
    }

    public async Task<object> privateGetV5AffiliateAffUserList (object parameters = null)
    {
        return await this.callAsync ("privateGetV5AffiliateAffUserList",parameters);
    }

    public async Task<object> privateGetV5SpotLeverTokenOrderRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5SpotLeverTokenOrderRecord",parameters);
    }

    public async Task<object> privateGetV5SpotMarginTradeInterestRateHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5SpotMarginTradeInterestRateHistory",parameters);
    }

    public async Task<object> privateGetV5SpotMarginTradeState (object parameters = null)
    {
        return await this.callAsync ("privateGetV5SpotMarginTradeState",parameters);
    }

    public async Task<object> privateGetV5SpotCrossMarginTradeLoanInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5SpotCrossMarginTradeLoanInfo",parameters);
    }

    public async Task<object> privateGetV5SpotCrossMarginTradeAccount (object parameters = null)
    {
        return await this.callAsync ("privateGetV5SpotCrossMarginTradeAccount",parameters);
    }

    public async Task<object> privateGetV5SpotCrossMarginTradeOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetV5SpotCrossMarginTradeOrders",parameters);
    }

    public async Task<object> privateGetV5SpotCrossMarginTradeRepayHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5SpotCrossMarginTradeRepayHistory",parameters);
    }

    public async Task<object> privateGetV5CryptoLoanBorrowableCollateralisableNumber (object parameters = null)
    {
        return await this.callAsync ("privateGetV5CryptoLoanBorrowableCollateralisableNumber",parameters);
    }

    public async Task<object> privateGetV5CryptoLoanOngoingOrders (object parameters = null)
    {
        return await this.callAsync ("privateGetV5CryptoLoanOngoingOrders",parameters);
    }

    public async Task<object> privateGetV5CryptoLoanRepaymentHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5CryptoLoanRepaymentHistory",parameters);
    }

    public async Task<object> privateGetV5CryptoLoanBorrowHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5CryptoLoanBorrowHistory",parameters);
    }

    public async Task<object> privateGetV5CryptoLoanMaxCollateralAmount (object parameters = null)
    {
        return await this.callAsync ("privateGetV5CryptoLoanMaxCollateralAmount",parameters);
    }

    public async Task<object> privateGetV5CryptoLoanAdjustmentHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5CryptoLoanAdjustmentHistory",parameters);
    }

    public async Task<object> privateGetV5InsLoanProductInfos (object parameters = null)
    {
        return await this.callAsync ("privateGetV5InsLoanProductInfos",parameters);
    }

    public async Task<object> privateGetV5InsLoanEnsureTokensConvert (object parameters = null)
    {
        return await this.callAsync ("privateGetV5InsLoanEnsureTokensConvert",parameters);
    }

    public async Task<object> privateGetV5InsLoanLoanOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetV5InsLoanLoanOrder",parameters);
    }

    public async Task<object> privateGetV5InsLoanRepaidHistory (object parameters = null)
    {
        return await this.callAsync ("privateGetV5InsLoanRepaidHistory",parameters);
    }

    public async Task<object> privateGetV5InsLoanLtvConvert (object parameters = null)
    {
        return await this.callAsync ("privateGetV5InsLoanLtvConvert",parameters);
    }

    public async Task<object> privateGetV5LendingInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5LendingInfo",parameters);
    }

    public async Task<object> privateGetV5LendingHistoryOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetV5LendingHistoryOrder",parameters);
    }

    public async Task<object> privateGetV5LendingAccount (object parameters = null)
    {
        return await this.callAsync ("privateGetV5LendingAccount",parameters);
    }

    public async Task<object> privateGetV5BrokerEarningRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5BrokerEarningRecord",parameters);
    }

    public async Task<object> privateGetV5BrokerEarningsInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5BrokerEarningsInfo",parameters);
    }

    public async Task<object> privateGetV5BrokerAccountInfo (object parameters = null)
    {
        return await this.callAsync ("privateGetV5BrokerAccountInfo",parameters);
    }

    public async Task<object> privateGetV5BrokerAssetQuerySubMemberDepositRecord (object parameters = null)
    {
        return await this.callAsync ("privateGetV5BrokerAssetQuerySubMemberDepositRecord",parameters);
    }

    public async Task<object> privateGetV5EarnOrder (object parameters = null)
    {
        return await this.callAsync ("privateGetV5EarnOrder",parameters);
    }

    public async Task<object> privateGetV5EarnPosition (object parameters = null)
    {
        return await this.callAsync ("privateGetV5EarnPosition",parameters);
    }

    public async Task<object> privatePostSpotV3PrivateOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3PrivateOrder",parameters);
    }

    public async Task<object> privatePostSpotV3PrivateCancelOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3PrivateCancelOrder",parameters);
    }

    public async Task<object> privatePostSpotV3PrivateCancelOrders (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3PrivateCancelOrders",parameters);
    }

    public async Task<object> privatePostSpotV3PrivateCancelOrdersByIds (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3PrivateCancelOrdersByIds",parameters);
    }

    public async Task<object> privatePostSpotV3PrivatePurchase (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3PrivatePurchase",parameters);
    }

    public async Task<object> privatePostSpotV3PrivateRedeem (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3PrivateRedeem",parameters);
    }

    public async Task<object> privatePostSpotV3PrivateCrossMarginLoan (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3PrivateCrossMarginLoan",parameters);
    }

    public async Task<object> privatePostSpotV3PrivateCrossMarginRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostSpotV3PrivateCrossMarginRepay",parameters);
    }

    public async Task<object> privatePostAssetV3PrivateTransferInterTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetV3PrivateTransferInterTransfer",parameters);
    }

    public async Task<object> privatePostAssetV3PrivateWithdrawCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetV3PrivateWithdrawCreate",parameters);
    }

    public async Task<object> privatePostAssetV3PrivateWithdrawCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetV3PrivateWithdrawCancel",parameters);
    }

    public async Task<object> privatePostAssetV3PrivateTransferSubMemberTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetV3PrivateTransferSubMemberTransfer",parameters);
    }

    public async Task<object> privatePostAssetV3PrivateTransferTransferSubMemberSave (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetV3PrivateTransferTransferSubMemberSave",parameters);
    }

    public async Task<object> privatePostAssetV3PrivateTransferUniversalTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostAssetV3PrivateTransferUniversalTransfer",parameters);
    }

    public async Task<object> privatePostUserV3PrivateCreateSubMember (object parameters = null)
    {
        return await this.callAsync ("privatePostUserV3PrivateCreateSubMember",parameters);
    }

    public async Task<object> privatePostUserV3PrivateCreateSubApi (object parameters = null)
    {
        return await this.callAsync ("privatePostUserV3PrivateCreateSubApi",parameters);
    }

    public async Task<object> privatePostUserV3PrivateUpdateApi (object parameters = null)
    {
        return await this.callAsync ("privatePostUserV3PrivateUpdateApi",parameters);
    }

    public async Task<object> privatePostUserV3PrivateDeleteApi (object parameters = null)
    {
        return await this.callAsync ("privatePostUserV3PrivateDeleteApi",parameters);
    }

    public async Task<object> privatePostUserV3PrivateUpdateSubApi (object parameters = null)
    {
        return await this.callAsync ("privatePostUserV3PrivateUpdateSubApi",parameters);
    }

    public async Task<object> privatePostUserV3PrivateDeleteSubApi (object parameters = null)
    {
        return await this.callAsync ("privatePostUserV3PrivateDeleteSubApi",parameters);
    }

    public async Task<object> privatePostContractV3PrivateCopytradingOrderCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateCopytradingOrderCreate",parameters);
    }

    public async Task<object> privatePostContractV3PrivateCopytradingOrderCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateCopytradingOrderCancel",parameters);
    }

    public async Task<object> privatePostContractV3PrivateCopytradingOrderClose (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateCopytradingOrderClose",parameters);
    }

    public async Task<object> privatePostContractV3PrivateCopytradingPositionClose (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateCopytradingPositionClose",parameters);
    }

    public async Task<object> privatePostContractV3PrivateCopytradingPositionSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateCopytradingPositionSetLeverage",parameters);
    }

    public async Task<object> privatePostContractV3PrivateCopytradingWalletTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateCopytradingWalletTransfer",parameters);
    }

    public async Task<object> privatePostContractV3PrivateCopytradingOrderTradingStop (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateCopytradingOrderTradingStop",parameters);
    }

    public async Task<object> privatePostContractV3PrivateOrderCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateOrderCreate",parameters);
    }

    public async Task<object> privatePostContractV3PrivateOrderCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateOrderCancel",parameters);
    }

    public async Task<object> privatePostContractV3PrivateOrderCancelAll (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateOrderCancelAll",parameters);
    }

    public async Task<object> privatePostContractV3PrivateOrderReplace (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateOrderReplace",parameters);
    }

    public async Task<object> privatePostContractV3PrivatePositionSetAutoAddMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivatePositionSetAutoAddMargin",parameters);
    }

    public async Task<object> privatePostContractV3PrivatePositionSwitchIsolated (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivatePositionSwitchIsolated",parameters);
    }

    public async Task<object> privatePostContractV3PrivatePositionSwitchMode (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivatePositionSwitchMode",parameters);
    }

    public async Task<object> privatePostContractV3PrivatePositionSwitchTpslMode (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivatePositionSwitchTpslMode",parameters);
    }

    public async Task<object> privatePostContractV3PrivatePositionSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivatePositionSetLeverage",parameters);
    }

    public async Task<object> privatePostContractV3PrivatePositionTradingStop (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivatePositionTradingStop",parameters);
    }

    public async Task<object> privatePostContractV3PrivatePositionSetRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivatePositionSetRiskLimit",parameters);
    }

    public async Task<object> privatePostContractV3PrivateAccountSetMarginMode (object parameters = null)
    {
        return await this.callAsync ("privatePostContractV3PrivateAccountSetMarginMode",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateOrderCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateOrderCreate",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateOrderReplace (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateOrderReplace",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateOrderCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateOrderCancel",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateOrderCreateBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateOrderCreateBatch",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateOrderReplaceBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateOrderReplaceBatch",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateOrderCancelBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateOrderCancelBatch",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateOrderCancelAll (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateOrderCancelAll",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivatePositionSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivatePositionSetLeverage",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivatePositionTpslSwitchMode (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivatePositionTpslSwitchMode",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivatePositionSetRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivatePositionSetRiskLimit",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivatePositionTradingStop (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivatePositionTradingStop",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateAccountUpgradeUnifiedAccount (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateAccountUpgradeUnifiedAccount",parameters);
    }

    public async Task<object> privatePostUnifiedV3PrivateAccountSetMarginMode (object parameters = null)
    {
        return await this.callAsync ("privatePostUnifiedV3PrivateAccountSetMarginMode",parameters);
    }

    public async Task<object> privatePostFhtComplianceTaxV3PrivateRegistertime (object parameters = null)
    {
        return await this.callAsync ("privatePostFhtComplianceTaxV3PrivateRegistertime",parameters);
    }

    public async Task<object> privatePostFhtComplianceTaxV3PrivateCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostFhtComplianceTaxV3PrivateCreate",parameters);
    }

    public async Task<object> privatePostFhtComplianceTaxV3PrivateStatus (object parameters = null)
    {
        return await this.callAsync ("privatePostFhtComplianceTaxV3PrivateStatus",parameters);
    }

    public async Task<object> privatePostFhtComplianceTaxV3PrivateUrl (object parameters = null)
    {
        return await this.callAsync ("privatePostFhtComplianceTaxV3PrivateUrl",parameters);
    }

    public async Task<object> privatePostV5OrderCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostV5OrderCreate",parameters);
    }

    public async Task<object> privatePostV5OrderAmend (object parameters = null)
    {
        return await this.callAsync ("privatePostV5OrderAmend",parameters);
    }

    public async Task<object> privatePostV5OrderCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostV5OrderCancel",parameters);
    }

    public async Task<object> privatePostV5OrderCancelAll (object parameters = null)
    {
        return await this.callAsync ("privatePostV5OrderCancelAll",parameters);
    }

    public async Task<object> privatePostV5OrderCreateBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostV5OrderCreateBatch",parameters);
    }

    public async Task<object> privatePostV5OrderAmendBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostV5OrderAmendBatch",parameters);
    }

    public async Task<object> privatePostV5OrderCancelBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostV5OrderCancelBatch",parameters);
    }

    public async Task<object> privatePostV5OrderDisconnectedCancelAll (object parameters = null)
    {
        return await this.callAsync ("privatePostV5OrderDisconnectedCancelAll",parameters);
    }

    public async Task<object> privatePostV5PositionSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionSetLeverage",parameters);
    }

    public async Task<object> privatePostV5PositionSwitchIsolated (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionSwitchIsolated",parameters);
    }

    public async Task<object> privatePostV5PositionSetTpslMode (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionSetTpslMode",parameters);
    }

    public async Task<object> privatePostV5PositionSwitchMode (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionSwitchMode",parameters);
    }

    public async Task<object> privatePostV5PositionSetRiskLimit (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionSetRiskLimit",parameters);
    }

    public async Task<object> privatePostV5PositionTradingStop (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionTradingStop",parameters);
    }

    public async Task<object> privatePostV5PositionSetAutoAddMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionSetAutoAddMargin",parameters);
    }

    public async Task<object> privatePostV5PositionAddMargin (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionAddMargin",parameters);
    }

    public async Task<object> privatePostV5PositionMovePositions (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionMovePositions",parameters);
    }

    public async Task<object> privatePostV5PositionConfirmPendingMmr (object parameters = null)
    {
        return await this.callAsync ("privatePostV5PositionConfirmPendingMmr",parameters);
    }

    public async Task<object> privatePostV5AccountUpgradeToUta (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountUpgradeToUta",parameters);
    }

    public async Task<object> privatePostV5AccountQuickRepayment (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountQuickRepayment",parameters);
    }

    public async Task<object> privatePostV5AccountSetMarginMode (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountSetMarginMode",parameters);
    }

    public async Task<object> privatePostV5AccountSetHedgingMode (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountSetHedgingMode",parameters);
    }

    public async Task<object> privatePostV5AccountMmpModify (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountMmpModify",parameters);
    }

    public async Task<object> privatePostV5AccountMmpReset (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountMmpReset",parameters);
    }

    public async Task<object> privatePostV5AssetExchangeQuoteApply (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AssetExchangeQuoteApply",parameters);
    }

    public async Task<object> privatePostV5AssetExchangeConvertExecute (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AssetExchangeConvertExecute",parameters);
    }

    public async Task<object> privatePostV5AssetTransferInterTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AssetTransferInterTransfer",parameters);
    }

    public async Task<object> privatePostV5AssetTransferSaveTransferSubMember (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AssetTransferSaveTransferSubMember",parameters);
    }

    public async Task<object> privatePostV5AssetTransferUniversalTransfer (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AssetTransferUniversalTransfer",parameters);
    }

    public async Task<object> privatePostV5AssetDepositDepositToAccount (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AssetDepositDepositToAccount",parameters);
    }

    public async Task<object> privatePostV5AssetWithdrawCreate (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AssetWithdrawCreate",parameters);
    }

    public async Task<object> privatePostV5AssetWithdrawCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AssetWithdrawCancel",parameters);
    }

    public async Task<object> privatePostV5UserCreateSubMember (object parameters = null)
    {
        return await this.callAsync ("privatePostV5UserCreateSubMember",parameters);
    }

    public async Task<object> privatePostV5UserCreateSubApi (object parameters = null)
    {
        return await this.callAsync ("privatePostV5UserCreateSubApi",parameters);
    }

    public async Task<object> privatePostV5UserFrozenSubMember (object parameters = null)
    {
        return await this.callAsync ("privatePostV5UserFrozenSubMember",parameters);
    }

    public async Task<object> privatePostV5UserUpdateApi (object parameters = null)
    {
        return await this.callAsync ("privatePostV5UserUpdateApi",parameters);
    }

    public async Task<object> privatePostV5UserUpdateSubApi (object parameters = null)
    {
        return await this.callAsync ("privatePostV5UserUpdateSubApi",parameters);
    }

    public async Task<object> privatePostV5UserDeleteApi (object parameters = null)
    {
        return await this.callAsync ("privatePostV5UserDeleteApi",parameters);
    }

    public async Task<object> privatePostV5UserDeleteSubApi (object parameters = null)
    {
        return await this.callAsync ("privatePostV5UserDeleteSubApi",parameters);
    }

    public async Task<object> privatePostV5SpotLeverTokenPurchase (object parameters = null)
    {
        return await this.callAsync ("privatePostV5SpotLeverTokenPurchase",parameters);
    }

    public async Task<object> privatePostV5SpotLeverTokenRedeem (object parameters = null)
    {
        return await this.callAsync ("privatePostV5SpotLeverTokenRedeem",parameters);
    }

    public async Task<object> privatePostV5SpotMarginTradeSwitchMode (object parameters = null)
    {
        return await this.callAsync ("privatePostV5SpotMarginTradeSwitchMode",parameters);
    }

    public async Task<object> privatePostV5SpotMarginTradeSetLeverage (object parameters = null)
    {
        return await this.callAsync ("privatePostV5SpotMarginTradeSetLeverage",parameters);
    }

    public async Task<object> privatePostV5SpotCrossMarginTradeLoan (object parameters = null)
    {
        return await this.callAsync ("privatePostV5SpotCrossMarginTradeLoan",parameters);
    }

    public async Task<object> privatePostV5SpotCrossMarginTradeRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostV5SpotCrossMarginTradeRepay",parameters);
    }

    public async Task<object> privatePostV5SpotCrossMarginTradeSwitch (object parameters = null)
    {
        return await this.callAsync ("privatePostV5SpotCrossMarginTradeSwitch",parameters);
    }

    public async Task<object> privatePostV5CryptoLoanBorrow (object parameters = null)
    {
        return await this.callAsync ("privatePostV5CryptoLoanBorrow",parameters);
    }

    public async Task<object> privatePostV5CryptoLoanRepay (object parameters = null)
    {
        return await this.callAsync ("privatePostV5CryptoLoanRepay",parameters);
    }

    public async Task<object> privatePostV5CryptoLoanAdjustLtv (object parameters = null)
    {
        return await this.callAsync ("privatePostV5CryptoLoanAdjustLtv",parameters);
    }

    public async Task<object> privatePostV5InsLoanAssociationUid (object parameters = null)
    {
        return await this.callAsync ("privatePostV5InsLoanAssociationUid",parameters);
    }

    public async Task<object> privatePostV5LendingPurchase (object parameters = null)
    {
        return await this.callAsync ("privatePostV5LendingPurchase",parameters);
    }

    public async Task<object> privatePostV5LendingRedeem (object parameters = null)
    {
        return await this.callAsync ("privatePostV5LendingRedeem",parameters);
    }

    public async Task<object> privatePostV5LendingRedeemCancel (object parameters = null)
    {
        return await this.callAsync ("privatePostV5LendingRedeemCancel",parameters);
    }

    public async Task<object> privatePostV5AccountSetCollateralSwitch (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountSetCollateralSwitch",parameters);
    }

    public async Task<object> privatePostV5AccountSetCollateralSwitchBatch (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountSetCollateralSwitchBatch",parameters);
    }

    public async Task<object> privatePostV5AccountDemoApplyMoney (object parameters = null)
    {
        return await this.callAsync ("privatePostV5AccountDemoApplyMoney",parameters);
    }

    public async Task<object> privatePostV5BrokerAwardInfo (object parameters = null)
    {
        return await this.callAsync ("privatePostV5BrokerAwardInfo",parameters);
    }

    public async Task<object> privatePostV5BrokerAwardDistributeAward (object parameters = null)
    {
        return await this.callAsync ("privatePostV5BrokerAwardDistributeAward",parameters);
    }

    public async Task<object> privatePostV5BrokerAwardDistributionRecord (object parameters = null)
    {
        return await this.callAsync ("privatePostV5BrokerAwardDistributionRecord",parameters);
    }

    public async Task<object> privatePostV5EarnPlaceOrder (object parameters = null)
    {
        return await this.callAsync ("privatePostV5EarnPlaceOrder",parameters);
    }

}