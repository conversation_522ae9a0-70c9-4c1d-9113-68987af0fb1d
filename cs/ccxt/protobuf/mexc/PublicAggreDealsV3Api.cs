// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: PublicAggreDealsV3Api.proto
// </auto-generated>
#pragma warning disable 1591, 0612, 3021, 8981
#region Designer generated code

using pb = global::Google.Protobuf;
using pbc = global::Google.Protobuf.Collections;
using pbr = global::Google.Protobuf.Reflection;
using scg = global::System.Collections.Generic;
/// <summary>Holder for reflection information generated from PublicAggreDealsV3Api.proto</summary>
public static partial class PublicAggreDealsV3ApiReflection {

  #region Descriptor
  /// <summary>File descriptor for PublicAggreDealsV3Api.proto</summary>
  public static pbr::FileDescriptor Descriptor {
    get { return descriptor; }
  }
  private static pbr::FileDescriptor descriptor;

  static PublicAggreDealsV3ApiReflection() {
    byte[] descriptorData = global::System.Convert.FromBase64String(
        string.Concat(
          "ChtQdWJsaWNBZ2dyZURlYWxzVjNBcGkucHJvdG8iVQoVUHVibGljQWdncmVE",
          "ZWFsc1YzQXBpEikKBWRlYWxzGAEgAygLMhouUHVibGljQWdncmVEZWFsc1Yz",
          "QXBpSXRlbRIRCglldmVudFR5cGUYAiABKAkiXQoZUHVibGljQWdncmVEZWFs",
          "c1YzQXBpSXRlbRINCgVwcmljZRgBIAEoCRIQCghxdWFudGl0eRgCIAEoCRIR",
          "Cgl0cmFkZVR5cGUYAyABKAUSDAoEdGltZRgEIAEoA0I+Chxjb20ubXhjLnB1",
          "c2guY29tbW9uLnByb3RvYnVmQhpQdWJsaWNBZ2dyZURlYWxzVjNBcGlQcm90",
          "b0gBUAFiBnByb3RvMw=="));
    descriptor = pbr::FileDescriptor.FromGeneratedCode(descriptorData,
        new pbr::FileDescriptor[] { },
        new pbr::GeneratedClrTypeInfo(null, null, new pbr::GeneratedClrTypeInfo[] {
          new pbr::GeneratedClrTypeInfo(typeof(global::PublicAggreDealsV3Api), global::PublicAggreDealsV3Api.Parser, new[]{ "Deals", "EventType" }, null, null, null, null),
          new pbr::GeneratedClrTypeInfo(typeof(global::PublicAggreDealsV3ApiItem), global::PublicAggreDealsV3ApiItem.Parser, new[]{ "Price", "Quantity", "TradeType", "Time" }, null, null, null, null)
        }));
  }
  #endregion

}
#region Messages
[global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
public sealed partial class PublicAggreDealsV3Api : pb::IMessage<PublicAggreDealsV3Api>
#if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    , pb::IBufferMessage
#endif
{
  private static readonly pb::MessageParser<PublicAggreDealsV3Api> _parser = new pb::MessageParser<PublicAggreDealsV3Api>(() => new PublicAggreDealsV3Api());
  private pb::UnknownFieldSet _unknownFields;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public static pb::MessageParser<PublicAggreDealsV3Api> Parser { get { return _parser; } }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public static pbr::MessageDescriptor Descriptor {
    get { return global::PublicAggreDealsV3ApiReflection.Descriptor.MessageTypes[0]; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  pbr::MessageDescriptor pb::IMessage.Descriptor {
    get { return Descriptor; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public PublicAggreDealsV3Api() {
    OnConstruction();
  }

  partial void OnConstruction();

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public PublicAggreDealsV3Api(PublicAggreDealsV3Api other) : this() {
    deals_ = other.deals_.Clone();
    eventType_ = other.eventType_;
    _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public PublicAggreDealsV3Api Clone() {
    return new PublicAggreDealsV3Api(this);
  }

  /// <summary>Field number for the "deals" field.</summary>
  public const int DealsFieldNumber = 1;
  private static readonly pb::FieldCodec<global::PublicAggreDealsV3ApiItem> _repeated_deals_codec
      = pb::FieldCodec.ForMessage(10, global::PublicAggreDealsV3ApiItem.Parser);
  private readonly pbc::RepeatedField<global::PublicAggreDealsV3ApiItem> deals_ = new pbc::RepeatedField<global::PublicAggreDealsV3ApiItem>();
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public pbc::RepeatedField<global::PublicAggreDealsV3ApiItem> Deals {
    get { return deals_; }
  }

  /// <summary>Field number for the "eventType" field.</summary>
  public const int EventTypeFieldNumber = 2;
  private string eventType_ = "";
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public string EventType {
    get { return eventType_; }
    set {
      eventType_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
    }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public override bool Equals(object other) {
    return Equals(other as PublicAggreDealsV3Api);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public bool Equals(PublicAggreDealsV3Api other) {
    if (ReferenceEquals(other, null)) {
      return false;
    }
    if (ReferenceEquals(other, this)) {
      return true;
    }
    if(!deals_.Equals(other.deals_)) return false;
    if (EventType != other.EventType) return false;
    return Equals(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public override int GetHashCode() {
    int hash = 1;
    hash ^= deals_.GetHashCode();
    if (EventType.Length != 0) hash ^= EventType.GetHashCode();
    if (_unknownFields != null) {
      hash ^= _unknownFields.GetHashCode();
    }
    return hash;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public override string ToString() {
    return pb::JsonFormatter.ToDiagnosticString(this);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public void WriteTo(pb::CodedOutputStream output) {
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    output.WriteRawMessage(this);
  #else
    deals_.WriteTo(output, _repeated_deals_codec);
    if (EventType.Length != 0) {
      output.WriteRawTag(18);
      output.WriteString(EventType);
    }
    if (_unknownFields != null) {
      _unknownFields.WriteTo(output);
    }
  #endif
  }

  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
    deals_.WriteTo(ref output, _repeated_deals_codec);
    if (EventType.Length != 0) {
      output.WriteRawTag(18);
      output.WriteString(EventType);
    }
    if (_unknownFields != null) {
      _unknownFields.WriteTo(ref output);
    }
  }
  #endif

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public int CalculateSize() {
    int size = 0;
    size += deals_.CalculateSize(_repeated_deals_codec);
    if (EventType.Length != 0) {
      size += 1 + pb::CodedOutputStream.ComputeStringSize(EventType);
    }
    if (_unknownFields != null) {
      size += _unknownFields.CalculateSize();
    }
    return size;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public void MergeFrom(PublicAggreDealsV3Api other) {
    if (other == null) {
      return;
    }
    deals_.Add(other.deals_);
    if (other.EventType.Length != 0) {
      EventType = other.EventType;
    }
    _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public void MergeFrom(pb::CodedInputStream input) {
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    input.ReadRawMessage(this);
  #else
    uint tag;
    while ((tag = input.ReadTag()) != 0) {
    if ((tag & 7) == 4) {
      // Abort on any end group tag.
      return;
    }
    switch(tag) {
        default:
          _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
          break;
        case 10: {
          deals_.AddEntriesFrom(input, _repeated_deals_codec);
          break;
        }
        case 18: {
          EventType = input.ReadString();
          break;
        }
      }
    }
  #endif
  }

  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
    uint tag;
    while ((tag = input.ReadTag()) != 0) {
    if ((tag & 7) == 4) {
      // Abort on any end group tag.
      return;
    }
    switch(tag) {
        default:
          _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
          break;
        case 10: {
          deals_.AddEntriesFrom(ref input, _repeated_deals_codec);
          break;
        }
        case 18: {
          EventType = input.ReadString();
          break;
        }
      }
    }
  }
  #endif

}

[global::System.Diagnostics.DebuggerDisplayAttribute("{ToString(),nq}")]
public sealed partial class PublicAggreDealsV3ApiItem : pb::IMessage<PublicAggreDealsV3ApiItem>
#if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    , pb::IBufferMessage
#endif
{
  private static readonly pb::MessageParser<PublicAggreDealsV3ApiItem> _parser = new pb::MessageParser<PublicAggreDealsV3ApiItem>(() => new PublicAggreDealsV3ApiItem());
  private pb::UnknownFieldSet _unknownFields;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public static pb::MessageParser<PublicAggreDealsV3ApiItem> Parser { get { return _parser; } }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public static pbr::MessageDescriptor Descriptor {
    get { return global::PublicAggreDealsV3ApiReflection.Descriptor.MessageTypes[1]; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  pbr::MessageDescriptor pb::IMessage.Descriptor {
    get { return Descriptor; }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public PublicAggreDealsV3ApiItem() {
    OnConstruction();
  }

  partial void OnConstruction();

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public PublicAggreDealsV3ApiItem(PublicAggreDealsV3ApiItem other) : this() {
    price_ = other.price_;
    quantity_ = other.quantity_;
    tradeType_ = other.tradeType_;
    time_ = other.time_;
    _unknownFields = pb::UnknownFieldSet.Clone(other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public PublicAggreDealsV3ApiItem Clone() {
    return new PublicAggreDealsV3ApiItem(this);
  }

  /// <summary>Field number for the "price" field.</summary>
  public const int PriceFieldNumber = 1;
  private string price_ = "";
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public string Price {
    get { return price_; }
    set {
      price_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
    }
  }

  /// <summary>Field number for the "quantity" field.</summary>
  public const int QuantityFieldNumber = 2;
  private string quantity_ = "";
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public string Quantity {
    get { return quantity_; }
    set {
      quantity_ = pb::ProtoPreconditions.CheckNotNull(value, "value");
    }
  }

  /// <summary>Field number for the "tradeType" field.</summary>
  public const int TradeTypeFieldNumber = 3;
  private int tradeType_;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public int TradeType {
    get { return tradeType_; }
    set {
      tradeType_ = value;
    }
  }

  /// <summary>Field number for the "time" field.</summary>
  public const int TimeFieldNumber = 4;
  private long time_;
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public long Time {
    get { return time_; }
    set {
      time_ = value;
    }
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public override bool Equals(object other) {
    return Equals(other as PublicAggreDealsV3ApiItem);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public bool Equals(PublicAggreDealsV3ApiItem other) {
    if (ReferenceEquals(other, null)) {
      return false;
    }
    if (ReferenceEquals(other, this)) {
      return true;
    }
    if (Price != other.Price) return false;
    if (Quantity != other.Quantity) return false;
    if (TradeType != other.TradeType) return false;
    if (Time != other.Time) return false;
    return Equals(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public override int GetHashCode() {
    int hash = 1;
    if (Price.Length != 0) hash ^= Price.GetHashCode();
    if (Quantity.Length != 0) hash ^= Quantity.GetHashCode();
    if (TradeType != 0) hash ^= TradeType.GetHashCode();
    if (Time != 0L) hash ^= Time.GetHashCode();
    if (_unknownFields != null) {
      hash ^= _unknownFields.GetHashCode();
    }
    return hash;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public override string ToString() {
    return pb::JsonFormatter.ToDiagnosticString(this);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public void WriteTo(pb::CodedOutputStream output) {
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    output.WriteRawMessage(this);
  #else
    if (Price.Length != 0) {
      output.WriteRawTag(10);
      output.WriteString(Price);
    }
    if (Quantity.Length != 0) {
      output.WriteRawTag(18);
      output.WriteString(Quantity);
    }
    if (TradeType != 0) {
      output.WriteRawTag(24);
      output.WriteInt32(TradeType);
    }
    if (Time != 0L) {
      output.WriteRawTag(32);
      output.WriteInt64(Time);
    }
    if (_unknownFields != null) {
      _unknownFields.WriteTo(output);
    }
  #endif
  }

  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  void pb::IBufferMessage.InternalWriteTo(ref pb::WriteContext output) {
    if (Price.Length != 0) {
      output.WriteRawTag(10);
      output.WriteString(Price);
    }
    if (Quantity.Length != 0) {
      output.WriteRawTag(18);
      output.WriteString(Quantity);
    }
    if (TradeType != 0) {
      output.WriteRawTag(24);
      output.WriteInt32(TradeType);
    }
    if (Time != 0L) {
      output.WriteRawTag(32);
      output.WriteInt64(Time);
    }
    if (_unknownFields != null) {
      _unknownFields.WriteTo(ref output);
    }
  }
  #endif

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public int CalculateSize() {
    int size = 0;
    if (Price.Length != 0) {
      size += 1 + pb::CodedOutputStream.ComputeStringSize(Price);
    }
    if (Quantity.Length != 0) {
      size += 1 + pb::CodedOutputStream.ComputeStringSize(Quantity);
    }
    if (TradeType != 0) {
      size += 1 + pb::CodedOutputStream.ComputeInt32Size(TradeType);
    }
    if (Time != 0L) {
      size += 1 + pb::CodedOutputStream.ComputeInt64Size(Time);
    }
    if (_unknownFields != null) {
      size += _unknownFields.CalculateSize();
    }
    return size;
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public void MergeFrom(PublicAggreDealsV3ApiItem other) {
    if (other == null) {
      return;
    }
    if (other.Price.Length != 0) {
      Price = other.Price;
    }
    if (other.Quantity.Length != 0) {
      Quantity = other.Quantity;
    }
    if (other.TradeType != 0) {
      TradeType = other.TradeType;
    }
    if (other.Time != 0L) {
      Time = other.Time;
    }
    _unknownFields = pb::UnknownFieldSet.MergeFrom(_unknownFields, other._unknownFields);
  }

  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  public void MergeFrom(pb::CodedInputStream input) {
  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
    input.ReadRawMessage(this);
  #else
    uint tag;
    while ((tag = input.ReadTag()) != 0) {
    if ((tag & 7) == 4) {
      // Abort on any end group tag.
      return;
    }
    switch(tag) {
        default:
          _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, input);
          break;
        case 10: {
          Price = input.ReadString();
          break;
        }
        case 18: {
          Quantity = input.ReadString();
          break;
        }
        case 24: {
          TradeType = input.ReadInt32();
          break;
        }
        case 32: {
          Time = input.ReadInt64();
          break;
        }
      }
    }
  #endif
  }

  #if !GOOGLE_PROTOBUF_REFSTRUCT_COMPATIBILITY_MODE
  [global::System.Diagnostics.DebuggerNonUserCodeAttribute]
  [global::System.CodeDom.Compiler.GeneratedCode("protoc", null)]
  void pb::IBufferMessage.InternalMergeFrom(ref pb::ParseContext input) {
    uint tag;
    while ((tag = input.ReadTag()) != 0) {
    if ((tag & 7) == 4) {
      // Abort on any end group tag.
      return;
    }
    switch(tag) {
        default:
          _unknownFields = pb::UnknownFieldSet.MergeFieldFrom(_unknownFields, ref input);
          break;
        case 10: {
          Price = input.ReadString();
          break;
        }
        case 18: {
          Quantity = input.ReadString();
          break;
        }
        case 24: {
          TradeType = input.ReadInt32();
          break;
        }
        case 32: {
          Time = input.ReadInt64();
          break;
        }
      }
    }
  }
  #endif

}

#endregion


#endregion Designer generated code
