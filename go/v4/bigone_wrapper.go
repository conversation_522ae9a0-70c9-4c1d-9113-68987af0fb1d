package ccxt

type Bigone struct {
	*bigone
	Core          *bigone
	exchangeTyped *ExchangeTyped
}

func NewBigone(userConfig map[string]interface{}) *Bigone {
	p := &bigone{}
	p.Init(userConfig)
	return &Bigone{
		bigone:        p,
		Core:          p,
		exchangeTyped: NewExchangeTyped(&p.Exchange),
	}
}

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

/**
 * @method
 * @name bigone#fetchCurrencies
 * @description fetches all available currencies on an exchange
 * @param {dict} [params] extra parameters specific to the exchange API endpoint
 * @returns {dict} an associative dictionary of currencies
 */
func (this *Bigone) FetchCurrencies(params ...interface{}) (Currencies, error) {
	res := <-this.Core.FetchCurrencies(params...)
	if IsError(res) {
		return Currencies{}, CreateReturnError(res)
	}
	return NewCurrencies(res), nil
}

/**
 * @method
 * @name bigone#fetchMarkets
 * @description retrieves data on all markets for bigone
 * @see https://open.big.one/docs/spot_asset_pair.html
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} an array of objects representing market data
 */
func (this *Bigone) FetchMarkets(params ...interface{}) ([]MarketInterface, error) {
	res := <-this.Core.FetchMarkets(params...)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewMarketInterfaceArray(res), nil
}

/**
 * @method
 * @name bigone#fetchTicker
 * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
 * @see https://open.big.one/docs/spot_tickers.html
 * @param {string} symbol unified symbol of the market to fetch the ticker for
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *Bigone) FetchTicker(symbol string, options ...FetchTickerOptions) (Ticker, error) {

	opts := FetchTickerOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTicker(symbol, params)
	if IsError(res) {
		return Ticker{}, CreateReturnError(res)
	}
	return NewTicker(res), nil
}

/**
 * @method
 * @name bigone#fetchTickers
 * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
 * @see https://open.big.one/docs/spot_tickers.html
 * @param {string[]} [symbols] unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *Bigone) FetchTickers(options ...FetchTickersOptions) (Tickers, error) {

	opts := FetchTickersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTickers(symbols, params)
	if IsError(res) {
		return Tickers{}, CreateReturnError(res)
	}
	return NewTickers(res), nil
}

/**
 * @method
 * @name bigone#fetchTime
 * @description fetches the current integer timestamp in milliseconds from the exchange server
 * @see https://open.big.one/docs/spot_ping.html
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {int} the current integer timestamp in milliseconds from the exchange server
 */
func (this *Bigone) FetchTime(params ...interface{}) (int64, error) {
	res := <-this.Core.FetchTime(params...)
	if IsError(res) {
		return -1, CreateReturnError(res)
	}
	return (res).(int64), nil
}

/**
 * @method
 * @name bigone#fetchOrderBook
 * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
 * @see https://open.big.one/docs/contract_misc.html#get-orderbook-snapshot
 * @param {string} symbol unified symbol of the market to fetch the order book for
 * @param {int} [limit] the maximum amount of order book entries to return
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
 */
func (this *Bigone) FetchOrderBook(symbol string, options ...FetchOrderBookOptions) (OrderBook, error) {

	opts := FetchOrderBookOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrderBook(symbol, limit, params)
	if IsError(res) {
		return OrderBook{}, CreateReturnError(res)
	}
	return NewOrderBook(res), nil
}

/**
 * @method
 * @name bigone#fetchTrades
 * @description get the list of most recent trades for a particular symbol
 * @see https://open.big.one/docs/spot_asset_pair_trade.html
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch
 * @param {int} [limit] the maximum amount of trades to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *Bigone) FetchTrades(symbol string, options ...FetchTradesOptions) ([]Trade, error) {

	opts := FetchTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}

/**
 * @method
 * @name bigone#fetchOHLCV
 * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
 * @see https://open.big.one/docs/spot_asset_pair_candle.html
 * @param {string} symbol unified symbol of the market to fetch OHLCV data for
 * @param {string} timeframe the length of time each candle represents
 * @param {int} [since] timestamp in ms of the earliest candle to fetch
 * @param {int} [limit] the maximum amount of candles to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] timestamp in ms of the earliest candle to fetch
 * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
 */
func (this *Bigone) FetchOHLCV(symbol string, options ...FetchOHLCVOptions) ([]OHLCV, error) {

	opts := FetchOHLCVOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOHLCV(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOHLCVArray(res), nil
}

/**
 * @method
 * @name bigone#fetchBalance
 * @description query for balance and get the amount of funds available for trading or funds locked in orders
 * @see https://open.big.one/docs/fund_accounts.html
 * @see https://open.big.one/docs/spot_accounts.html
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
 */
func (this *Bigone) FetchBalance(params ...interface{}) (Balances, error) {
	res := <-this.Core.FetchBalance(params...)
	if IsError(res) {
		return Balances{}, CreateReturnError(res)
	}
	return NewBalances(res), nil
}

/**
 * @method
 * @name bigone#createMarketBuyOrderWithCost
 * @description create a market buy order by providing the symbol and cost
 * @see https://open.big.one/docs/spot_orders.html#create-order
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {float} cost how much you want to trade in units of the quote currency
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Bigone) CreateMarketBuyOrderWithCost(symbol string, cost float64, options ...CreateMarketBuyOrderWithCostOptions) (Order, error) {

	opts := CreateMarketBuyOrderWithCostOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CreateMarketBuyOrderWithCost(symbol, cost, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name bigone#createOrder
 * @description create a trade order
 * @see https://open.big.one/docs/spot_orders.html#create-order
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type 'market' or 'limit'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much of currency you want to trade in units of base currency
 * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {float} [params.triggerPrice] the price at which a trigger order is triggered at
 * @param {bool} [params.postOnly] if true, the order will only be posted to the order book and not executed immediately
 * @param {string} [params.timeInForce] "GTC", "IOC", or "PO"
 * @param {float} [params.cost] *spot market buy only* the quote quantity that can be used as an alternative for the amount
 *
 * EXCHANGE SPECIFIC PARAMETERS
 * @param {string} [params.operator] *stop order only* GTE or LTE (default)
 * @param {string} [params.client_order_id] must match ^[a-zA-Z0-9-_]{1,36}$ this regex. client_order_id is unique in 24 hours, If created 24 hours later and the order closed, it will be released and can be reused
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Bigone) CreateOrder(symbol string, typeVar string, side string, amount float64, options ...CreateOrderOptions) (Order, error) {

	opts := CreateOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CreateOrder(symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name bigone#cancelOrder
 * @description cancels an open order
 * @see https://open.big.one/docs/spot_orders.html#cancel-order
 * @param {string} id order id
 * @param {string} symbol Not used by bigone cancelOrder ()
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Bigone) CancelOrder(id string, options ...CancelOrderOptions) (Order, error) {

	opts := CancelOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CancelOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name bigone#cancelAllOrders
 * @description cancel all open orders
 * @see https://open.big.one/docs/spot_orders.html#cancel-all-orders
 * @param {string} symbol unified market symbol, only orders in the market of this symbol are cancelled when symbol is not undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Bigone) CancelAllOrders(options ...CancelAllOrdersOptions) ([]Order, error) {

	opts := CancelAllOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CancelAllOrders(symbol, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name bigone#fetchOrder
 * @description fetches information on an order made by the user
 * @see https://open.big.one/docs/spot_orders.html#get-one-order
 * @param {string} id the order id
 * @param {string} symbol not used by bigone fetchOrder
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Bigone) FetchOrder(id string, options ...FetchOrderOptions) (Order, error) {

	opts := FetchOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name bigone#fetchOrders
 * @description fetches information on multiple orders made by the user
 * @see https://open.big.one/docs/spot_orders.html#get-user-orders-in-one-asset-pair
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] the earliest time in ms to fetch orders for
 * @param {int} [limit] the maximum number of order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Bigone) FetchOrders(options ...FetchOrdersOptions) ([]Order, error) {

	opts := FetchOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name bigone#fetchMyTrades
 * @description fetch all trades made by the user
 * @see https://open.big.one/docs/spot_trade.html#trades-of-user
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch trades for
 * @param {int} [limit] the maximum number of trades structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
 */
func (this *Bigone) FetchMyTrades(options ...FetchMyTradesOptions) ([]Trade, error) {

	opts := FetchMyTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchMyTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}

/**
 * @method
 * @name bigone#fetchOpenOrders
 * @description fetch all unfilled currently open orders
 * @see https://open.big.one/docs/spot_orders.html#get-user-orders-in-one-asset-pair
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch open orders for
 * @param {int} [limit] the maximum number of  open orders structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Bigone) FetchOpenOrders(options ...FetchOpenOrdersOptions) ([]Order, error) {

	opts := FetchOpenOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOpenOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name bigone#fetchClosedOrders
 * @description fetches information on multiple closed orders made by the user
 * @see https://open.big.one/docs/spot_orders.html#get-user-orders-in-one-asset-pair
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] the earliest time in ms to fetch orders for
 * @param {int} [limit] the maximum number of order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Bigone) FetchClosedOrders(options ...FetchClosedOrdersOptions) ([]Order, error) {

	opts := FetchClosedOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchClosedOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name bigone#fetchDepositAddress
 * @description fetch the deposit address for a currency associated with this account
 * @see https://open.big.one/docs/spot_deposit.html#get-deposite-address-of-one-asset-of-user
 * @param {string} code unified currency code
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
 */
func (this *Bigone) FetchDepositAddress(code string, options ...FetchDepositAddressOptions) (DepositAddress, error) {

	opts := FetchDepositAddressOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchDepositAddress(code, params)
	if IsError(res) {
		return DepositAddress{}, CreateReturnError(res)
	}
	return NewDepositAddress(res), nil
}

/**
 * @method
 * @name bigone#fetchDeposits
 * @description fetch all deposits made to an account
 * @see https://open.big.one/docs/spot_deposit.html#deposit-of-user
 * @param {string} code unified currency code
 * @param {int} [since] the earliest time in ms to fetch deposits for
 * @param {int} [limit] the maximum number of deposits structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Bigone) FetchDeposits(options ...FetchDepositsOptions) ([]Transaction, error) {

	opts := FetchDepositsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchDeposits(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}

/**
 * @method
 * @name bigone#fetchWithdrawals
 * @description fetch all withdrawals made from an account
 * @see https://open.big.one/docs/spot_withdrawal.html#get-withdrawals-of-user
 * @param {string} code unified currency code
 * @param {int} [since] the earliest time in ms to fetch withdrawals for
 * @param {int} [limit] the maximum number of withdrawals structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Bigone) FetchWithdrawals(options ...FetchWithdrawalsOptions) ([]Transaction, error) {

	opts := FetchWithdrawalsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchWithdrawals(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}

/**
 * @method
 * @name bigone#transfer
 * @description transfer currency internally between wallets on the same account
 * @see https://open.big.one/docs/spot_transfer.html#transfer-of-user
 * @param {string} code unified currency code
 * @param {float} amount amount to transfer
 * @param {string} fromAccount 'SPOT', 'FUND', or 'CONTRACT'
 * @param {string} toAccount 'SPOT', 'FUND', or 'CONTRACT'
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transfer structure]{@link https://docs.ccxt.com/#/?id=transfer-structure}
 */
func (this *Bigone) Transfer(code string, amount float64, fromAccount string, toAccount string, options ...TransferOptions) (TransferEntry, error) {

	opts := TransferOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.Transfer(code, amount, fromAccount, toAccount, params)
	if IsError(res) {
		return TransferEntry{}, CreateReturnError(res)
	}
	return NewTransferEntry(res), nil
}

/**
 * @method
 * @name bigone#withdraw
 * @description make a withdrawal
 * @see https://open.big.one/docs/spot_withdrawal.html#create-withdrawal-of-user
 * @param {string} code unified currency code
 * @param {float} amount the amount to withdraw
 * @param {string} address the address to withdraw to
 * @param {string} tag
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Bigone) Withdraw(code string, amount float64, address string, options ...WithdrawOptions) (Transaction, error) {

	opts := WithdrawOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var tag interface{} = nil
	if opts.Tag != nil {
		tag = *opts.Tag
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.Withdraw(code, amount, address, tag, params)
	if IsError(res) {
		return Transaction{}, CreateReturnError(res)
	}
	return NewTransaction(res), nil
}

// missing typed methods from base
// nolint
func (this *Bigone) LoadMarkets(params ...interface{}) (map[string]MarketInterface, error) {
	return this.exchangeTyped.LoadMarkets(params...)
}
func (this *Bigone) CancelAllOrdersAfter(timeout int64, options ...CancelAllOrdersAfterOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.CancelAllOrdersAfter(timeout, options...)
}
func (this *Bigone) CancelOrdersForSymbols(orders []CancellationRequest, options ...CancelOrdersForSymbolsOptions) ([]Order, error) {
	return this.exchangeTyped.CancelOrdersForSymbols(orders, options...)
}
func (this *Bigone) CreateConvertTrade(id string, fromCode string, toCode string, options ...CreateConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.CreateConvertTrade(id, fromCode, toCode, options...)
}
func (this *Bigone) CreateDepositAddress(code string, options ...CreateDepositAddressOptions) (DepositAddress, error) {
	return this.exchangeTyped.CreateDepositAddress(code, options...)
}
func (this *Bigone) CreateLimitBuyOrder(symbol string, amount float64, price float64, options ...CreateLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitBuyOrder(symbol, amount, price, options...)
}
func (this *Bigone) CreateLimitOrder(symbol string, side string, amount float64, price float64, options ...CreateLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitOrder(symbol, side, amount, price, options...)
}
func (this *Bigone) CreateLimitSellOrder(symbol string, amount float64, price float64, options ...CreateLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitSellOrder(symbol, amount, price, options...)
}
func (this *Bigone) CreateMarketBuyOrder(symbol string, amount float64, options ...CreateMarketBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrder(symbol, amount, options...)
}
func (this *Bigone) CreateMarketOrder(symbol string, side string, amount float64, options ...CreateMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrder(symbol, side, amount, options...)
}
func (this *Bigone) CreateMarketOrderWithCost(symbol string, side string, cost float64, options ...CreateMarketOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrderWithCost(symbol, side, cost, options...)
}
func (this *Bigone) CreateMarketSellOrder(symbol string, amount float64, options ...CreateMarketSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrder(symbol, amount, options...)
}
func (this *Bigone) CreateMarketSellOrderWithCost(symbol string, cost float64, options ...CreateMarketSellOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrderWithCost(symbol, cost, options...)
}
func (this *Bigone) CreateOrders(orders []OrderRequest, options ...CreateOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.CreateOrders(orders, options...)
}
func (this *Bigone) CreateOrderWithTakeProfitAndStopLoss(symbol string, typeVar string, side string, amount float64, options ...CreateOrderWithTakeProfitAndStopLossOptions) (Order, error) {
	return this.exchangeTyped.CreateOrderWithTakeProfitAndStopLoss(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) CreatePostOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreatePostOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreatePostOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) CreateReduceOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreateReduceOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateReduceOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) CreateStopLimitOrder(symbol string, side string, amount float64, price float64, triggerPrice float64, options ...CreateStopLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLimitOrder(symbol, side, amount, price, triggerPrice, options...)
}
func (this *Bigone) CreateStopLossOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopLossOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLossOrder(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) CreateStopMarketOrder(symbol string, side string, amount float64, triggerPrice float64, options ...CreateStopMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopMarketOrder(symbol, side, amount, triggerPrice, options...)
}
func (this *Bigone) CreateStopOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopOrder(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) CreateTakeProfitOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTakeProfitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTakeProfitOrder(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) CreateTrailingAmountOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingAmountOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingAmountOrder(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) CreateTrailingPercentOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingPercentOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingPercentOrder(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) CreateTriggerOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTriggerOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTriggerOrder(symbol, typeVar, side, amount, options...)
}
func (this *Bigone) EditLimitBuyOrder(id string, symbol string, amount float64, options ...EditLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitBuyOrder(id, symbol, amount, options...)
}
func (this *Bigone) EditLimitOrder(id string, symbol string, side string, amount float64, options ...EditLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitOrder(id, symbol, side, amount, options...)
}
func (this *Bigone) EditLimitSellOrder(id string, symbol string, amount float64, options ...EditLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitSellOrder(id, symbol, amount, options...)
}
func (this *Bigone) EditOrder(id string, symbol string, typeVar string, side string, options ...EditOrderOptions) (Order, error) {
	return this.exchangeTyped.EditOrder(id, symbol, typeVar, side, options...)
}
func (this *Bigone) EditOrders(orders []OrderRequest, options ...EditOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.EditOrders(orders, options...)
}
func (this *Bigone) FetchAccounts(params ...interface{}) ([]Account, error) {
	return this.exchangeTyped.FetchAccounts(params...)
}
func (this *Bigone) FetchAllGreeks(options ...FetchAllGreeksOptions) ([]Greeks, error) {
	return this.exchangeTyped.FetchAllGreeks(options...)
}
func (this *Bigone) FetchBidsAsks(options ...FetchBidsAsksOptions) (Tickers, error) {
	return this.exchangeTyped.FetchBidsAsks(options...)
}
func (this *Bigone) FetchBorrowInterest(options ...FetchBorrowInterestOptions) ([]BorrowInterest, error) {
	return this.exchangeTyped.FetchBorrowInterest(options...)
}
func (this *Bigone) FetchBorrowRate(code string, amount float64, options ...FetchBorrowRateOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchBorrowRate(code, amount, options...)
}
func (this *Bigone) FetchCanceledAndClosedOrders(options ...FetchCanceledAndClosedOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchCanceledAndClosedOrders(options...)
}
func (this *Bigone) FetchConvertCurrencies(params ...interface{}) (Currencies, error) {
	return this.exchangeTyped.FetchConvertCurrencies(params...)
}
func (this *Bigone) FetchConvertQuote(fromCode string, toCode string, options ...FetchConvertQuoteOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertQuote(fromCode, toCode, options...)
}
func (this *Bigone) FetchConvertTrade(id string, options ...FetchConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertTrade(id, options...)
}
func (this *Bigone) FetchConvertTradeHistory(options ...FetchConvertTradeHistoryOptions) ([]Conversion, error) {
	return this.exchangeTyped.FetchConvertTradeHistory(options...)
}
func (this *Bigone) FetchCrossBorrowRate(code string, options ...FetchCrossBorrowRateOptions) (CrossBorrowRate, error) {
	return this.exchangeTyped.FetchCrossBorrowRate(code, options...)
}
func (this *Bigone) FetchCrossBorrowRates(params ...interface{}) (CrossBorrowRates, error) {
	return this.exchangeTyped.FetchCrossBorrowRates(params...)
}
func (this *Bigone) FetchDepositAddresses(options ...FetchDepositAddressesOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddresses(options...)
}
func (this *Bigone) FetchDepositAddressesByNetwork(code string, options ...FetchDepositAddressesByNetworkOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddressesByNetwork(code, options...)
}
func (this *Bigone) FetchDepositsWithdrawals(options ...FetchDepositsWithdrawalsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchDepositsWithdrawals(options...)
}
func (this *Bigone) FetchDepositWithdrawFee(code string, options ...FetchDepositWithdrawFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFee(code, options...)
}
func (this *Bigone) FetchDepositWithdrawFees(options ...FetchDepositWithdrawFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFees(options...)
}
func (this *Bigone) FetchFreeBalance(params ...interface{}) (Balance, error) {
	return this.exchangeTyped.FetchFreeBalance(params...)
}
func (this *Bigone) FetchFundingHistory(options ...FetchFundingHistoryOptions) ([]FundingHistory, error) {
	return this.exchangeTyped.FetchFundingHistory(options...)
}
func (this *Bigone) FetchFundingInterval(symbol string, options ...FetchFundingIntervalOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingInterval(symbol, options...)
}
func (this *Bigone) FetchFundingIntervals(options ...FetchFundingIntervalsOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingIntervals(options...)
}
func (this *Bigone) FetchFundingRate(symbol string, options ...FetchFundingRateOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingRate(symbol, options...)
}
func (this *Bigone) FetchFundingRateHistory(options ...FetchFundingRateHistoryOptions) ([]FundingRateHistory, error) {
	return this.exchangeTyped.FetchFundingRateHistory(options...)
}
func (this *Bigone) FetchFundingRates(options ...FetchFundingRatesOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingRates(options...)
}
func (this *Bigone) FetchGreeks(symbol string, options ...FetchGreeksOptions) (Greeks, error) {
	return this.exchangeTyped.FetchGreeks(symbol, options...)
}
func (this *Bigone) FetchIndexOHLCV(symbol string, options ...FetchIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchIndexOHLCV(symbol, options...)
}
func (this *Bigone) FetchIsolatedBorrowRate(symbol string, options ...FetchIsolatedBorrowRateOptions) (IsolatedBorrowRate, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRate(symbol, options...)
}
func (this *Bigone) FetchIsolatedBorrowRates(params ...interface{}) (IsolatedBorrowRates, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRates(params...)
}
func (this *Bigone) FetchLastPrices(options ...FetchLastPricesOptions) (LastPrices, error) {
	return this.exchangeTyped.FetchLastPrices(options...)
}
func (this *Bigone) FetchLedger(options ...FetchLedgerOptions) ([]LedgerEntry, error) {
	return this.exchangeTyped.FetchLedger(options...)
}
func (this *Bigone) FetchLedgerEntry(id string, options ...FetchLedgerEntryOptions) (LedgerEntry, error) {
	return this.exchangeTyped.FetchLedgerEntry(id, options...)
}
func (this *Bigone) FetchLeverage(symbol string, options ...FetchLeverageOptions) (Leverage, error) {
	return this.exchangeTyped.FetchLeverage(symbol, options...)
}
func (this *Bigone) FetchLeverages(options ...FetchLeveragesOptions) (Leverages, error) {
	return this.exchangeTyped.FetchLeverages(options...)
}
func (this *Bigone) FetchLeverageTiers(options ...FetchLeverageTiersOptions) (LeverageTiers, error) {
	return this.exchangeTyped.FetchLeverageTiers(options...)
}
func (this *Bigone) FetchLiquidations(symbol string, options ...FetchLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchLiquidations(symbol, options...)
}
func (this *Bigone) FetchLongShortRatio(symbol string, options ...FetchLongShortRatioOptions) (LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatio(symbol, options...)
}
func (this *Bigone) FetchLongShortRatioHistory(options ...FetchLongShortRatioHistoryOptions) ([]LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatioHistory(options...)
}
func (this *Bigone) FetchMarginAdjustmentHistory(options ...FetchMarginAdjustmentHistoryOptions) ([]MarginModification, error) {
	return this.exchangeTyped.FetchMarginAdjustmentHistory(options...)
}
func (this *Bigone) FetchMarginMode(symbol string, options ...FetchMarginModeOptions) (MarginMode, error) {
	return this.exchangeTyped.FetchMarginMode(symbol, options...)
}
func (this *Bigone) FetchMarginModes(options ...FetchMarginModesOptions) (MarginModes, error) {
	return this.exchangeTyped.FetchMarginModes(options...)
}
func (this *Bigone) FetchMarketLeverageTiers(symbol string, options ...FetchMarketLeverageTiersOptions) ([]LeverageTier, error) {
	return this.exchangeTyped.FetchMarketLeverageTiers(symbol, options...)
}
func (this *Bigone) FetchMarkOHLCV(symbol string, options ...FetchMarkOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchMarkOHLCV(symbol, options...)
}
func (this *Bigone) FetchMarkPrice(symbol string, options ...FetchMarkPriceOptions) (Ticker, error) {
	return this.exchangeTyped.FetchMarkPrice(symbol, options...)
}
func (this *Bigone) FetchMarkPrices(options ...FetchMarkPricesOptions) (Tickers, error) {
	return this.exchangeTyped.FetchMarkPrices(options...)
}
func (this *Bigone) FetchMyLiquidations(options ...FetchMyLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchMyLiquidations(options...)
}
func (this *Bigone) FetchOpenInterest(symbol string, options ...FetchOpenInterestOptions) (OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterest(symbol, options...)
}
func (this *Bigone) FetchOpenInterestHistory(symbol string, options ...FetchOpenInterestHistoryOptions) ([]OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterestHistory(symbol, options...)
}
func (this *Bigone) FetchOpenInterests(options ...FetchOpenInterestsOptions) (OpenInterests, error) {
	return this.exchangeTyped.FetchOpenInterests(options...)
}
func (this *Bigone) FetchOption(symbol string, options ...FetchOptionOptions) (Option, error) {
	return this.exchangeTyped.FetchOption(symbol, options...)
}
func (this *Bigone) FetchOptionChain(code string, options ...FetchOptionChainOptions) (OptionChain, error) {
	return this.exchangeTyped.FetchOptionChain(code, options...)
}
func (this *Bigone) FetchOrderBooks(options ...FetchOrderBooksOptions) (OrderBooks, error) {
	return this.exchangeTyped.FetchOrderBooks(options...)
}
func (this *Bigone) FetchOrderStatus(id string, options ...FetchOrderStatusOptions) (string, error) {
	return this.exchangeTyped.FetchOrderStatus(id, options...)
}
func (this *Bigone) FetchOrderTrades(id string, options ...FetchOrderTradesOptions) ([]Trade, error) {
	return this.exchangeTyped.FetchOrderTrades(id, options...)
}
func (this *Bigone) FetchPaymentMethods(params ...interface{}) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPaymentMethods(params...)
}
func (this *Bigone) FetchPosition(symbol string, options ...FetchPositionOptions) (Position, error) {
	return this.exchangeTyped.FetchPosition(symbol, options...)
}
func (this *Bigone) FetchPositionHistory(symbol string, options ...FetchPositionHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionHistory(symbol, options...)
}
func (this *Bigone) FetchPositionMode(options ...FetchPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPositionMode(options...)
}
func (this *Bigone) FetchPositions(options ...FetchPositionsOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositions(options...)
}
func (this *Bigone) FetchPositionsForSymbol(symbol string, options ...FetchPositionsForSymbolOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsForSymbol(symbol, options...)
}
func (this *Bigone) FetchPositionsHistory(options ...FetchPositionsHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsHistory(options...)
}
func (this *Bigone) FetchPositionsRisk(options ...FetchPositionsRiskOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsRisk(options...)
}
func (this *Bigone) FetchPremiumIndexOHLCV(symbol string, options ...FetchPremiumIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchPremiumIndexOHLCV(symbol, options...)
}
func (this *Bigone) FetchStatus(params ...interface{}) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchStatus(params...)
}
func (this *Bigone) FetchTradingFee(symbol string, options ...FetchTradingFeeOptions) (TradingFeeInterface, error) {
	return this.exchangeTyped.FetchTradingFee(symbol, options...)
}
func (this *Bigone) FetchTradingFees(params ...interface{}) (TradingFees, error) {
	return this.exchangeTyped.FetchTradingFees(params...)
}
func (this *Bigone) FetchTradingLimits(options ...FetchTradingLimitsOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTradingLimits(options...)
}
func (this *Bigone) FetchTransactionFee(code string, options ...FetchTransactionFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFee(code, options...)
}
func (this *Bigone) FetchTransactionFees(options ...FetchTransactionFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFees(options...)
}
func (this *Bigone) FetchTransactions(options ...FetchTransactionsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchTransactions(options...)
}
func (this *Bigone) FetchTransfer(id string, options ...FetchTransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.FetchTransfer(id, options...)
}
func (this *Bigone) FetchTransfers(options ...FetchTransfersOptions) ([]TransferEntry, error) {
	return this.exchangeTyped.FetchTransfers(options...)
}
func (this *Bigone) SetMargin(symbol string, amount float64, options ...SetMarginOptions) (MarginModification, error) {
	return this.exchangeTyped.SetMargin(symbol, amount, options...)
}
func (this *Bigone) SetMarginMode(marginMode string, options ...SetMarginModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetMarginMode(marginMode, options...)
}
func (this *Bigone) SetPositionMode(hedged bool, options ...SetPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetPositionMode(hedged, options...)
}
