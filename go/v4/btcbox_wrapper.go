package ccxt

type Btcbox struct {
	*btcbox
	Core          *btcbox
	exchangeTyped *ExchangeTyped
}

func NewBtcbox(userConfig map[string]interface{}) *Btcbox {
	p := &btcbox{}
	p.Init(userConfig)
	return &Btcbox{
		btcbox:        p,
		Core:          p,
		exchangeTyped: NewExchangeTyped(&p.Exchange),
	}
}

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

/**
 * @method
 * @name btcbox#fetchMarkets
 * @description retrieves data on all markets for ace
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} an array of objects representing market data
 */
func (this *Btcbox) FetchMarkets(params ...interface{}) ([]MarketInterface, error) {
	res := <-this.Core.FetchMarkets(params...)
	if Is<PERSON>rror(res) {
		return nil, CreateR<PERSON>urn<PERSON>rror(res)
	}
	return NewMarketInterfaceArray(res), nil
}

/**
 * @method
 * @name btcbox#fetchBalance
 * @description query for balance and get the amount of funds available for trading or funds locked in orders
 * @see https://blog.btcbox.jp/en/archives/8762#toc13
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
 */
func (this *Btcbox) FetchBalance(params ...interface{}) (Balances, error) {
	res := <-this.Core.FetchBalance(params...)
	if IsError(res) {
		return Balances{}, CreateReturnError(res)
	}
	return NewBalances(res), nil
}

/**
 * @method
 * @name btcbox#fetchOrderBook
 * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
 * @see https://blog.btcbox.jp/en/archives/8762#toc6
 * @param {string} symbol unified symbol of the market to fetch the order book for
 * @param {int} [limit] the maximum amount of order book entries to return
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
 */
func (this *Btcbox) FetchOrderBook(symbol string, options ...FetchOrderBookOptions) (OrderBook, error) {

	opts := FetchOrderBookOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrderBook(symbol, limit, params)
	if IsError(res) {
		return OrderBook{}, CreateReturnError(res)
	}
	return NewOrderBook(res), nil
}

/**
 * @method
 * @name btcbox#fetchTicker
 * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
 * @see https://blog.btcbox.jp/en/archives/8762#toc5
 * @param {string} symbol unified symbol of the market to fetch the ticker for
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *Btcbox) FetchTicker(symbol string, options ...FetchTickerOptions) (Ticker, error) {

	opts := FetchTickerOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTicker(symbol, params)
	if IsError(res) {
		return Ticker{}, CreateReturnError(res)
	}
	return NewTicker(res), nil
}

/**
 * @method
 * @name btcbox#fetchTickers
 * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
 * @param {string[]} [symbols] unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *Btcbox) FetchTickers(options ...FetchTickersOptions) (Tickers, error) {

	opts := FetchTickersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTickers(symbols, params)
	if IsError(res) {
		return Tickers{}, CreateReturnError(res)
	}
	return NewTickers(res), nil
}

/**
 * @method
 * @name btcbox#fetchTrades
 * @description get the list of most recent trades for a particular symbol
 * @see https://blog.btcbox.jp/en/archives/8762#toc7
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch
 * @param {int} [limit] the maximum amount of trades to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *Btcbox) FetchTrades(symbol string, options ...FetchTradesOptions) ([]Trade, error) {

	opts := FetchTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}

/**
 * @method
 * @name btcbox#createOrder
 * @description create a trade order
 * @see https://blog.btcbox.jp/en/archives/8762#toc18
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type 'market' or 'limit'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much of currency you want to trade in units of base currency
 * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Btcbox) CreateOrder(symbol string, typeVar string, side string, amount float64, options ...CreateOrderOptions) (Order, error) {

	opts := CreateOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CreateOrder(symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name btcbox#cancelOrder
 * @description cancels an open order
 * @see https://blog.btcbox.jp/en/archives/8762#toc17
 * @param {string} id order id
 * @param {string} symbol unified symbol of the market the order was made in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Btcbox) CancelOrder(id string, options ...CancelOrderOptions) (Order, error) {

	opts := CancelOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CancelOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name btcbox#fetchOrder
 * @description fetches information on an order made by the user
 * @see https://blog.btcbox.jp/en/archives/8762#toc16
 * @param {string} id the order id
 * @param {string} symbol unified symbol of the market the order was made in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Btcbox) FetchOrder(id string, options ...FetchOrderOptions) (Order, error) {

	opts := FetchOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *Btcbox) FetchOrdersByType(typeVar interface{}, options ...FetchOrdersByTypeOptions) ([]Order, error) {

	opts := FetchOrdersByTypeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrdersByType(typeVar, symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name btcbox#fetchOrders
 * @description fetches information on multiple orders made by the user
 * @see https://blog.btcbox.jp/en/archives/8762#toc15
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] the earliest time in ms to fetch orders for
 * @param {int} [limit] the maximum number of order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Btcbox) FetchOrders(options ...FetchOrdersOptions) ([]Order, error) {

	opts := FetchOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name btcbox#fetchOpenOrders
 * @description fetch all unfilled currently open orders
 * @see https://blog.btcbox.jp/en/archives/8762#toc15
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch open orders for
 * @param {int} [limit] the maximum number of  open orders structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Btcbox) FetchOpenOrders(options ...FetchOpenOrdersOptions) ([]Order, error) {

	opts := FetchOpenOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOpenOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

// missing typed methods from base
// nolint
func (this *Btcbox) LoadMarkets(params ...interface{}) (map[string]MarketInterface, error) {
	return this.exchangeTyped.LoadMarkets(params...)
}
func (this *Btcbox) CancelAllOrders(options ...CancelAllOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.CancelAllOrders(options...)
}
func (this *Btcbox) CancelAllOrdersAfter(timeout int64, options ...CancelAllOrdersAfterOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.CancelAllOrdersAfter(timeout, options...)
}
func (this *Btcbox) CancelOrdersForSymbols(orders []CancellationRequest, options ...CancelOrdersForSymbolsOptions) ([]Order, error) {
	return this.exchangeTyped.CancelOrdersForSymbols(orders, options...)
}
func (this *Btcbox) CreateConvertTrade(id string, fromCode string, toCode string, options ...CreateConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.CreateConvertTrade(id, fromCode, toCode, options...)
}
func (this *Btcbox) CreateDepositAddress(code string, options ...CreateDepositAddressOptions) (DepositAddress, error) {
	return this.exchangeTyped.CreateDepositAddress(code, options...)
}
func (this *Btcbox) CreateLimitBuyOrder(symbol string, amount float64, price float64, options ...CreateLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitBuyOrder(symbol, amount, price, options...)
}
func (this *Btcbox) CreateLimitOrder(symbol string, side string, amount float64, price float64, options ...CreateLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitOrder(symbol, side, amount, price, options...)
}
func (this *Btcbox) CreateLimitSellOrder(symbol string, amount float64, price float64, options ...CreateLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitSellOrder(symbol, amount, price, options...)
}
func (this *Btcbox) CreateMarketBuyOrder(symbol string, amount float64, options ...CreateMarketBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrder(symbol, amount, options...)
}
func (this *Btcbox) CreateMarketBuyOrderWithCost(symbol string, cost float64, options ...CreateMarketBuyOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrderWithCost(symbol, cost, options...)
}
func (this *Btcbox) CreateMarketOrder(symbol string, side string, amount float64, options ...CreateMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrder(symbol, side, amount, options...)
}
func (this *Btcbox) CreateMarketOrderWithCost(symbol string, side string, cost float64, options ...CreateMarketOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrderWithCost(symbol, side, cost, options...)
}
func (this *Btcbox) CreateMarketSellOrder(symbol string, amount float64, options ...CreateMarketSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrder(symbol, amount, options...)
}
func (this *Btcbox) CreateMarketSellOrderWithCost(symbol string, cost float64, options ...CreateMarketSellOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrderWithCost(symbol, cost, options...)
}
func (this *Btcbox) CreateOrders(orders []OrderRequest, options ...CreateOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.CreateOrders(orders, options...)
}
func (this *Btcbox) CreateOrderWithTakeProfitAndStopLoss(symbol string, typeVar string, side string, amount float64, options ...CreateOrderWithTakeProfitAndStopLossOptions) (Order, error) {
	return this.exchangeTyped.CreateOrderWithTakeProfitAndStopLoss(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) CreatePostOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreatePostOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreatePostOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) CreateReduceOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreateReduceOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateReduceOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) CreateStopLimitOrder(symbol string, side string, amount float64, price float64, triggerPrice float64, options ...CreateStopLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLimitOrder(symbol, side, amount, price, triggerPrice, options...)
}
func (this *Btcbox) CreateStopLossOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopLossOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLossOrder(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) CreateStopMarketOrder(symbol string, side string, amount float64, triggerPrice float64, options ...CreateStopMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopMarketOrder(symbol, side, amount, triggerPrice, options...)
}
func (this *Btcbox) CreateStopOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopOrder(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) CreateTakeProfitOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTakeProfitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTakeProfitOrder(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) CreateTrailingAmountOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingAmountOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingAmountOrder(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) CreateTrailingPercentOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingPercentOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingPercentOrder(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) CreateTriggerOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTriggerOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTriggerOrder(symbol, typeVar, side, amount, options...)
}
func (this *Btcbox) EditLimitBuyOrder(id string, symbol string, amount float64, options ...EditLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitBuyOrder(id, symbol, amount, options...)
}
func (this *Btcbox) EditLimitOrder(id string, symbol string, side string, amount float64, options ...EditLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitOrder(id, symbol, side, amount, options...)
}
func (this *Btcbox) EditLimitSellOrder(id string, symbol string, amount float64, options ...EditLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitSellOrder(id, symbol, amount, options...)
}
func (this *Btcbox) EditOrder(id string, symbol string, typeVar string, side string, options ...EditOrderOptions) (Order, error) {
	return this.exchangeTyped.EditOrder(id, symbol, typeVar, side, options...)
}
func (this *Btcbox) EditOrders(orders []OrderRequest, options ...EditOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.EditOrders(orders, options...)
}
func (this *Btcbox) FetchAccounts(params ...interface{}) ([]Account, error) {
	return this.exchangeTyped.FetchAccounts(params...)
}
func (this *Btcbox) FetchAllGreeks(options ...FetchAllGreeksOptions) ([]Greeks, error) {
	return this.exchangeTyped.FetchAllGreeks(options...)
}
func (this *Btcbox) FetchBidsAsks(options ...FetchBidsAsksOptions) (Tickers, error) {
	return this.exchangeTyped.FetchBidsAsks(options...)
}
func (this *Btcbox) FetchBorrowInterest(options ...FetchBorrowInterestOptions) ([]BorrowInterest, error) {
	return this.exchangeTyped.FetchBorrowInterest(options...)
}
func (this *Btcbox) FetchBorrowRate(code string, amount float64, options ...FetchBorrowRateOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchBorrowRate(code, amount, options...)
}
func (this *Btcbox) FetchCanceledAndClosedOrders(options ...FetchCanceledAndClosedOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchCanceledAndClosedOrders(options...)
}
func (this *Btcbox) FetchClosedOrders(options ...FetchClosedOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchClosedOrders(options...)
}
func (this *Btcbox) FetchConvertCurrencies(params ...interface{}) (Currencies, error) {
	return this.exchangeTyped.FetchConvertCurrencies(params...)
}
func (this *Btcbox) FetchConvertQuote(fromCode string, toCode string, options ...FetchConvertQuoteOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertQuote(fromCode, toCode, options...)
}
func (this *Btcbox) FetchConvertTrade(id string, options ...FetchConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertTrade(id, options...)
}
func (this *Btcbox) FetchConvertTradeHistory(options ...FetchConvertTradeHistoryOptions) ([]Conversion, error) {
	return this.exchangeTyped.FetchConvertTradeHistory(options...)
}
func (this *Btcbox) FetchCrossBorrowRate(code string, options ...FetchCrossBorrowRateOptions) (CrossBorrowRate, error) {
	return this.exchangeTyped.FetchCrossBorrowRate(code, options...)
}
func (this *Btcbox) FetchCrossBorrowRates(params ...interface{}) (CrossBorrowRates, error) {
	return this.exchangeTyped.FetchCrossBorrowRates(params...)
}
func (this *Btcbox) FetchCurrencies(params ...interface{}) (Currencies, error) {
	return this.exchangeTyped.FetchCurrencies(params...)
}
func (this *Btcbox) FetchDepositAddress(code string, options ...FetchDepositAddressOptions) (DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddress(code, options...)
}
func (this *Btcbox) FetchDepositAddresses(options ...FetchDepositAddressesOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddresses(options...)
}
func (this *Btcbox) FetchDepositAddressesByNetwork(code string, options ...FetchDepositAddressesByNetworkOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddressesByNetwork(code, options...)
}
func (this *Btcbox) FetchDeposits(options ...FetchDepositsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchDeposits(options...)
}
func (this *Btcbox) FetchDepositsWithdrawals(options ...FetchDepositsWithdrawalsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchDepositsWithdrawals(options...)
}
func (this *Btcbox) FetchDepositWithdrawFee(code string, options ...FetchDepositWithdrawFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFee(code, options...)
}
func (this *Btcbox) FetchDepositWithdrawFees(options ...FetchDepositWithdrawFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFees(options...)
}
func (this *Btcbox) FetchFreeBalance(params ...interface{}) (Balance, error) {
	return this.exchangeTyped.FetchFreeBalance(params...)
}
func (this *Btcbox) FetchFundingHistory(options ...FetchFundingHistoryOptions) ([]FundingHistory, error) {
	return this.exchangeTyped.FetchFundingHistory(options...)
}
func (this *Btcbox) FetchFundingInterval(symbol string, options ...FetchFundingIntervalOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingInterval(symbol, options...)
}
func (this *Btcbox) FetchFundingIntervals(options ...FetchFundingIntervalsOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingIntervals(options...)
}
func (this *Btcbox) FetchFundingRate(symbol string, options ...FetchFundingRateOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingRate(symbol, options...)
}
func (this *Btcbox) FetchFundingRateHistory(options ...FetchFundingRateHistoryOptions) ([]FundingRateHistory, error) {
	return this.exchangeTyped.FetchFundingRateHistory(options...)
}
func (this *Btcbox) FetchFundingRates(options ...FetchFundingRatesOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingRates(options...)
}
func (this *Btcbox) FetchGreeks(symbol string, options ...FetchGreeksOptions) (Greeks, error) {
	return this.exchangeTyped.FetchGreeks(symbol, options...)
}
func (this *Btcbox) FetchIndexOHLCV(symbol string, options ...FetchIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchIndexOHLCV(symbol, options...)
}
func (this *Btcbox) FetchIsolatedBorrowRate(symbol string, options ...FetchIsolatedBorrowRateOptions) (IsolatedBorrowRate, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRate(symbol, options...)
}
func (this *Btcbox) FetchIsolatedBorrowRates(params ...interface{}) (IsolatedBorrowRates, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRates(params...)
}
func (this *Btcbox) FetchLastPrices(options ...FetchLastPricesOptions) (LastPrices, error) {
	return this.exchangeTyped.FetchLastPrices(options...)
}
func (this *Btcbox) FetchLedger(options ...FetchLedgerOptions) ([]LedgerEntry, error) {
	return this.exchangeTyped.FetchLedger(options...)
}
func (this *Btcbox) FetchLedgerEntry(id string, options ...FetchLedgerEntryOptions) (LedgerEntry, error) {
	return this.exchangeTyped.FetchLedgerEntry(id, options...)
}
func (this *Btcbox) FetchLeverage(symbol string, options ...FetchLeverageOptions) (Leverage, error) {
	return this.exchangeTyped.FetchLeverage(symbol, options...)
}
func (this *Btcbox) FetchLeverages(options ...FetchLeveragesOptions) (Leverages, error) {
	return this.exchangeTyped.FetchLeverages(options...)
}
func (this *Btcbox) FetchLeverageTiers(options ...FetchLeverageTiersOptions) (LeverageTiers, error) {
	return this.exchangeTyped.FetchLeverageTiers(options...)
}
func (this *Btcbox) FetchLiquidations(symbol string, options ...FetchLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchLiquidations(symbol, options...)
}
func (this *Btcbox) FetchLongShortRatio(symbol string, options ...FetchLongShortRatioOptions) (LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatio(symbol, options...)
}
func (this *Btcbox) FetchLongShortRatioHistory(options ...FetchLongShortRatioHistoryOptions) ([]LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatioHistory(options...)
}
func (this *Btcbox) FetchMarginAdjustmentHistory(options ...FetchMarginAdjustmentHistoryOptions) ([]MarginModification, error) {
	return this.exchangeTyped.FetchMarginAdjustmentHistory(options...)
}
func (this *Btcbox) FetchMarginMode(symbol string, options ...FetchMarginModeOptions) (MarginMode, error) {
	return this.exchangeTyped.FetchMarginMode(symbol, options...)
}
func (this *Btcbox) FetchMarginModes(options ...FetchMarginModesOptions) (MarginModes, error) {
	return this.exchangeTyped.FetchMarginModes(options...)
}
func (this *Btcbox) FetchMarketLeverageTiers(symbol string, options ...FetchMarketLeverageTiersOptions) ([]LeverageTier, error) {
	return this.exchangeTyped.FetchMarketLeverageTiers(symbol, options...)
}
func (this *Btcbox) FetchMarkOHLCV(symbol string, options ...FetchMarkOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchMarkOHLCV(symbol, options...)
}
func (this *Btcbox) FetchMarkPrice(symbol string, options ...FetchMarkPriceOptions) (Ticker, error) {
	return this.exchangeTyped.FetchMarkPrice(symbol, options...)
}
func (this *Btcbox) FetchMarkPrices(options ...FetchMarkPricesOptions) (Tickers, error) {
	return this.exchangeTyped.FetchMarkPrices(options...)
}
func (this *Btcbox) FetchMyLiquidations(options ...FetchMyLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchMyLiquidations(options...)
}
func (this *Btcbox) FetchMyTrades(options ...FetchMyTradesOptions) ([]Trade, error) {
	return this.exchangeTyped.FetchMyTrades(options...)
}
func (this *Btcbox) FetchOHLCV(symbol string, options ...FetchOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchOHLCV(symbol, options...)
}
func (this *Btcbox) FetchOpenInterest(symbol string, options ...FetchOpenInterestOptions) (OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterest(symbol, options...)
}
func (this *Btcbox) FetchOpenInterestHistory(symbol string, options ...FetchOpenInterestHistoryOptions) ([]OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterestHistory(symbol, options...)
}
func (this *Btcbox) FetchOpenInterests(options ...FetchOpenInterestsOptions) (OpenInterests, error) {
	return this.exchangeTyped.FetchOpenInterests(options...)
}
func (this *Btcbox) FetchOption(symbol string, options ...FetchOptionOptions) (Option, error) {
	return this.exchangeTyped.FetchOption(symbol, options...)
}
func (this *Btcbox) FetchOptionChain(code string, options ...FetchOptionChainOptions) (OptionChain, error) {
	return this.exchangeTyped.FetchOptionChain(code, options...)
}
func (this *Btcbox) FetchOrderBooks(options ...FetchOrderBooksOptions) (OrderBooks, error) {
	return this.exchangeTyped.FetchOrderBooks(options...)
}
func (this *Btcbox) FetchOrderStatus(id string, options ...FetchOrderStatusOptions) (string, error) {
	return this.exchangeTyped.FetchOrderStatus(id, options...)
}
func (this *Btcbox) FetchOrderTrades(id string, options ...FetchOrderTradesOptions) ([]Trade, error) {
	return this.exchangeTyped.FetchOrderTrades(id, options...)
}
func (this *Btcbox) FetchPaymentMethods(params ...interface{}) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPaymentMethods(params...)
}
func (this *Btcbox) FetchPosition(symbol string, options ...FetchPositionOptions) (Position, error) {
	return this.exchangeTyped.FetchPosition(symbol, options...)
}
func (this *Btcbox) FetchPositionHistory(symbol string, options ...FetchPositionHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionHistory(symbol, options...)
}
func (this *Btcbox) FetchPositionMode(options ...FetchPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPositionMode(options...)
}
func (this *Btcbox) FetchPositions(options ...FetchPositionsOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositions(options...)
}
func (this *Btcbox) FetchPositionsForSymbol(symbol string, options ...FetchPositionsForSymbolOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsForSymbol(symbol, options...)
}
func (this *Btcbox) FetchPositionsHistory(options ...FetchPositionsHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsHistory(options...)
}
func (this *Btcbox) FetchPositionsRisk(options ...FetchPositionsRiskOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsRisk(options...)
}
func (this *Btcbox) FetchPremiumIndexOHLCV(symbol string, options ...FetchPremiumIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchPremiumIndexOHLCV(symbol, options...)
}
func (this *Btcbox) FetchStatus(params ...interface{}) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchStatus(params...)
}
func (this *Btcbox) FetchTime(params ...interface{}) (int64, error) {
	return this.exchangeTyped.FetchTime(params...)
}
func (this *Btcbox) FetchTradingFee(symbol string, options ...FetchTradingFeeOptions) (TradingFeeInterface, error) {
	return this.exchangeTyped.FetchTradingFee(symbol, options...)
}
func (this *Btcbox) FetchTradingFees(params ...interface{}) (TradingFees, error) {
	return this.exchangeTyped.FetchTradingFees(params...)
}
func (this *Btcbox) FetchTradingLimits(options ...FetchTradingLimitsOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTradingLimits(options...)
}
func (this *Btcbox) FetchTransactionFee(code string, options ...FetchTransactionFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFee(code, options...)
}
func (this *Btcbox) FetchTransactionFees(options ...FetchTransactionFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFees(options...)
}
func (this *Btcbox) FetchTransactions(options ...FetchTransactionsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchTransactions(options...)
}
func (this *Btcbox) FetchTransfer(id string, options ...FetchTransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.FetchTransfer(id, options...)
}
func (this *Btcbox) FetchTransfers(options ...FetchTransfersOptions) ([]TransferEntry, error) {
	return this.exchangeTyped.FetchTransfers(options...)
}
func (this *Btcbox) FetchWithdrawals(options ...FetchWithdrawalsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchWithdrawals(options...)
}
func (this *Btcbox) SetMargin(symbol string, amount float64, options ...SetMarginOptions) (MarginModification, error) {
	return this.exchangeTyped.SetMargin(symbol, amount, options...)
}
func (this *Btcbox) SetMarginMode(marginMode string, options ...SetMarginModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetMarginMode(marginMode, options...)
}
func (this *Btcbox) SetPositionMode(hedged bool, options ...SetPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetPositionMode(hedged, options...)
}
func (this *Btcbox) Transfer(code string, amount float64, fromAccount string, toAccount string, options ...TransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.Transfer(code, amount, fromAccount, toAccount, options...)
}
func (this *Btcbox) Withdraw(code string, amount float64, address string, options ...WithdrawOptions) (Transaction, error) {
	return this.exchangeTyped.Withdraw(code, amount, address, options...)
}
