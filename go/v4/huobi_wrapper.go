package ccxt

type <PERSON>obi struct {
	*huobi
	Core          *huobi
	exchangeTyped *ExchangeTyped
}

func New<PERSON>uobi(userConfig map[string]interface{}) *<PERSON><PERSON>i {
	p := &huobi{}
	p.Init(userConfig)
	return &Huobi{
		huobi:         p,
		Core:          p,
		exchangeTyped: NewExchangeTyped(&p.Exchange),
	}
}

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// missing typed methods from base
// nolint
func (this *Huobi) LoadMarkets(params ...interface{}) (map[string]MarketInterface, error) {
	return this.exchangeTyped.LoadMarkets(params...)
}
func (this *Huobi) CancelAllOrders(options ...CancelAllOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.CancelAllOrders(options...)
}
func (this *Huobi) CancelAllOrdersAfter(timeout int64, options ...CancelAllOrdersAfterOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.CancelAllOrdersAfter(timeout, options...)
}
func (this *Huobi) CancelOrder(id string, options ...CancelOrderOptions) (Order, error) {
	return this.exchangeTyped.CancelOrder(id, options...)
}
func (this *Huobi) CancelOrdersForSymbols(orders []CancellationRequest, options ...CancelOrdersForSymbolsOptions) ([]Order, error) {
	return this.exchangeTyped.CancelOrdersForSymbols(orders, options...)
}
func (this *Huobi) CreateConvertTrade(id string, fromCode string, toCode string, options ...CreateConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.CreateConvertTrade(id, fromCode, toCode, options...)
}
func (this *Huobi) CreateDepositAddress(code string, options ...CreateDepositAddressOptions) (DepositAddress, error) {
	return this.exchangeTyped.CreateDepositAddress(code, options...)
}
func (this *Huobi) CreateLimitBuyOrder(symbol string, amount float64, price float64, options ...CreateLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitBuyOrder(symbol, amount, price, options...)
}
func (this *Huobi) CreateLimitOrder(symbol string, side string, amount float64, price float64, options ...CreateLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitOrder(symbol, side, amount, price, options...)
}
func (this *Huobi) CreateLimitSellOrder(symbol string, amount float64, price float64, options ...CreateLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitSellOrder(symbol, amount, price, options...)
}
func (this *Huobi) CreateMarketBuyOrder(symbol string, amount float64, options ...CreateMarketBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrder(symbol, amount, options...)
}
func (this *Huobi) CreateMarketBuyOrderWithCost(symbol string, cost float64, options ...CreateMarketBuyOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrderWithCost(symbol, cost, options...)
}
func (this *Huobi) CreateMarketOrder(symbol string, side string, amount float64, options ...CreateMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrder(symbol, side, amount, options...)
}
func (this *Huobi) CreateMarketOrderWithCost(symbol string, side string, cost float64, options ...CreateMarketOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrderWithCost(symbol, side, cost, options...)
}
func (this *Huobi) CreateMarketSellOrder(symbol string, amount float64, options ...CreateMarketSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrder(symbol, amount, options...)
}
func (this *Huobi) CreateMarketSellOrderWithCost(symbol string, cost float64, options ...CreateMarketSellOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrderWithCost(symbol, cost, options...)
}
func (this *Huobi) CreateOrder(symbol string, typeVar string, side string, amount float64, options ...CreateOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreateOrders(orders []OrderRequest, options ...CreateOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.CreateOrders(orders, options...)
}
func (this *Huobi) CreateOrderWithTakeProfitAndStopLoss(symbol string, typeVar string, side string, amount float64, options ...CreateOrderWithTakeProfitAndStopLossOptions) (Order, error) {
	return this.exchangeTyped.CreateOrderWithTakeProfitAndStopLoss(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreatePostOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreatePostOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreatePostOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreateReduceOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreateReduceOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateReduceOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreateStopLimitOrder(symbol string, side string, amount float64, price float64, triggerPrice float64, options ...CreateStopLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLimitOrder(symbol, side, amount, price, triggerPrice, options...)
}
func (this *Huobi) CreateStopLossOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopLossOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLossOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreateStopMarketOrder(symbol string, side string, amount float64, triggerPrice float64, options ...CreateStopMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopMarketOrder(symbol, side, amount, triggerPrice, options...)
}
func (this *Huobi) CreateStopOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreateTakeProfitOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTakeProfitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTakeProfitOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreateTrailingAmountOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingAmountOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingAmountOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreateTrailingPercentOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingPercentOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingPercentOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) CreateTriggerOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTriggerOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTriggerOrder(symbol, typeVar, side, amount, options...)
}
func (this *Huobi) EditLimitBuyOrder(id string, symbol string, amount float64, options ...EditLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitBuyOrder(id, symbol, amount, options...)
}
func (this *Huobi) EditLimitOrder(id string, symbol string, side string, amount float64, options ...EditLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitOrder(id, symbol, side, amount, options...)
}
func (this *Huobi) EditLimitSellOrder(id string, symbol string, amount float64, options ...EditLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitSellOrder(id, symbol, amount, options...)
}
func (this *Huobi) EditOrder(id string, symbol string, typeVar string, side string, options ...EditOrderOptions) (Order, error) {
	return this.exchangeTyped.EditOrder(id, symbol, typeVar, side, options...)
}
func (this *Huobi) EditOrders(orders []OrderRequest, options ...EditOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.EditOrders(orders, options...)
}
func (this *Huobi) FetchAccounts(params ...interface{}) ([]Account, error) {
	return this.exchangeTyped.FetchAccounts(params...)
}
func (this *Huobi) FetchAllGreeks(options ...FetchAllGreeksOptions) ([]Greeks, error) {
	return this.exchangeTyped.FetchAllGreeks(options...)
}
func (this *Huobi) FetchBalance(params ...interface{}) (Balances, error) {
	return this.exchangeTyped.FetchBalance(params...)
}
func (this *Huobi) FetchBidsAsks(options ...FetchBidsAsksOptions) (Tickers, error) {
	return this.exchangeTyped.FetchBidsAsks(options...)
}
func (this *Huobi) FetchBorrowInterest(options ...FetchBorrowInterestOptions) ([]BorrowInterest, error) {
	return this.exchangeTyped.FetchBorrowInterest(options...)
}
func (this *Huobi) FetchBorrowRate(code string, amount float64, options ...FetchBorrowRateOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchBorrowRate(code, amount, options...)
}
func (this *Huobi) FetchCanceledAndClosedOrders(options ...FetchCanceledAndClosedOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchCanceledAndClosedOrders(options...)
}
func (this *Huobi) FetchClosedOrders(options ...FetchClosedOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchClosedOrders(options...)
}
func (this *Huobi) FetchConvertCurrencies(params ...interface{}) (Currencies, error) {
	return this.exchangeTyped.FetchConvertCurrencies(params...)
}
func (this *Huobi) FetchConvertQuote(fromCode string, toCode string, options ...FetchConvertQuoteOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertQuote(fromCode, toCode, options...)
}
func (this *Huobi) FetchConvertTrade(id string, options ...FetchConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertTrade(id, options...)
}
func (this *Huobi) FetchConvertTradeHistory(options ...FetchConvertTradeHistoryOptions) ([]Conversion, error) {
	return this.exchangeTyped.FetchConvertTradeHistory(options...)
}
func (this *Huobi) FetchCrossBorrowRate(code string, options ...FetchCrossBorrowRateOptions) (CrossBorrowRate, error) {
	return this.exchangeTyped.FetchCrossBorrowRate(code, options...)
}
func (this *Huobi) FetchCrossBorrowRates(params ...interface{}) (CrossBorrowRates, error) {
	return this.exchangeTyped.FetchCrossBorrowRates(params...)
}
func (this *Huobi) FetchCurrencies(params ...interface{}) (Currencies, error) {
	return this.exchangeTyped.FetchCurrencies(params...)
}
func (this *Huobi) FetchDepositAddress(code string, options ...FetchDepositAddressOptions) (DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddress(code, options...)
}
func (this *Huobi) FetchDepositAddresses(options ...FetchDepositAddressesOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddresses(options...)
}
func (this *Huobi) FetchDepositAddressesByNetwork(code string, options ...FetchDepositAddressesByNetworkOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddressesByNetwork(code, options...)
}
func (this *Huobi) FetchDeposits(options ...FetchDepositsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchDeposits(options...)
}
func (this *Huobi) FetchDepositsWithdrawals(options ...FetchDepositsWithdrawalsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchDepositsWithdrawals(options...)
}
func (this *Huobi) FetchDepositWithdrawFee(code string, options ...FetchDepositWithdrawFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFee(code, options...)
}
func (this *Huobi) FetchDepositWithdrawFees(options ...FetchDepositWithdrawFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFees(options...)
}
func (this *Huobi) FetchFreeBalance(params ...interface{}) (Balance, error) {
	return this.exchangeTyped.FetchFreeBalance(params...)
}
func (this *Huobi) FetchFundingHistory(options ...FetchFundingHistoryOptions) ([]FundingHistory, error) {
	return this.exchangeTyped.FetchFundingHistory(options...)
}
func (this *Huobi) FetchFundingInterval(symbol string, options ...FetchFundingIntervalOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingInterval(symbol, options...)
}
func (this *Huobi) FetchFundingIntervals(options ...FetchFundingIntervalsOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingIntervals(options...)
}
func (this *Huobi) FetchFundingRate(symbol string, options ...FetchFundingRateOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingRate(symbol, options...)
}
func (this *Huobi) FetchFundingRateHistory(options ...FetchFundingRateHistoryOptions) ([]FundingRateHistory, error) {
	return this.exchangeTyped.FetchFundingRateHistory(options...)
}
func (this *Huobi) FetchFundingRates(options ...FetchFundingRatesOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingRates(options...)
}
func (this *Huobi) FetchGreeks(symbol string, options ...FetchGreeksOptions) (Greeks, error) {
	return this.exchangeTyped.FetchGreeks(symbol, options...)
}
func (this *Huobi) FetchIndexOHLCV(symbol string, options ...FetchIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchIndexOHLCV(symbol, options...)
}
func (this *Huobi) FetchIsolatedBorrowRate(symbol string, options ...FetchIsolatedBorrowRateOptions) (IsolatedBorrowRate, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRate(symbol, options...)
}
func (this *Huobi) FetchIsolatedBorrowRates(params ...interface{}) (IsolatedBorrowRates, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRates(params...)
}
func (this *Huobi) FetchLastPrices(options ...FetchLastPricesOptions) (LastPrices, error) {
	return this.exchangeTyped.FetchLastPrices(options...)
}
func (this *Huobi) FetchLedger(options ...FetchLedgerOptions) ([]LedgerEntry, error) {
	return this.exchangeTyped.FetchLedger(options...)
}
func (this *Huobi) FetchLedgerEntry(id string, options ...FetchLedgerEntryOptions) (LedgerEntry, error) {
	return this.exchangeTyped.FetchLedgerEntry(id, options...)
}
func (this *Huobi) FetchLeverage(symbol string, options ...FetchLeverageOptions) (Leverage, error) {
	return this.exchangeTyped.FetchLeverage(symbol, options...)
}
func (this *Huobi) FetchLeverages(options ...FetchLeveragesOptions) (Leverages, error) {
	return this.exchangeTyped.FetchLeverages(options...)
}
func (this *Huobi) FetchLeverageTiers(options ...FetchLeverageTiersOptions) (LeverageTiers, error) {
	return this.exchangeTyped.FetchLeverageTiers(options...)
}
func (this *Huobi) FetchLiquidations(symbol string, options ...FetchLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchLiquidations(symbol, options...)
}
func (this *Huobi) FetchLongShortRatio(symbol string, options ...FetchLongShortRatioOptions) (LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatio(symbol, options...)
}
func (this *Huobi) FetchLongShortRatioHistory(options ...FetchLongShortRatioHistoryOptions) ([]LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatioHistory(options...)
}
func (this *Huobi) FetchMarginAdjustmentHistory(options ...FetchMarginAdjustmentHistoryOptions) ([]MarginModification, error) {
	return this.exchangeTyped.FetchMarginAdjustmentHistory(options...)
}
func (this *Huobi) FetchMarginMode(symbol string, options ...FetchMarginModeOptions) (MarginMode, error) {
	return this.exchangeTyped.FetchMarginMode(symbol, options...)
}
func (this *Huobi) FetchMarginModes(options ...FetchMarginModesOptions) (MarginModes, error) {
	return this.exchangeTyped.FetchMarginModes(options...)
}
func (this *Huobi) FetchMarketLeverageTiers(symbol string, options ...FetchMarketLeverageTiersOptions) ([]LeverageTier, error) {
	return this.exchangeTyped.FetchMarketLeverageTiers(symbol, options...)
}
func (this *Huobi) FetchMarkets(params ...interface{}) ([]MarketInterface, error) {
	return this.exchangeTyped.FetchMarkets(params...)
}
func (this *Huobi) FetchMarkOHLCV(symbol string, options ...FetchMarkOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchMarkOHLCV(symbol, options...)
}
func (this *Huobi) FetchMarkPrice(symbol string, options ...FetchMarkPriceOptions) (Ticker, error) {
	return this.exchangeTyped.FetchMarkPrice(symbol, options...)
}
func (this *Huobi) FetchMarkPrices(options ...FetchMarkPricesOptions) (Tickers, error) {
	return this.exchangeTyped.FetchMarkPrices(options...)
}
func (this *Huobi) FetchMyLiquidations(options ...FetchMyLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchMyLiquidations(options...)
}
func (this *Huobi) FetchMyTrades(options ...FetchMyTradesOptions) ([]Trade, error) {
	return this.exchangeTyped.FetchMyTrades(options...)
}
func (this *Huobi) FetchOHLCV(symbol string, options ...FetchOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchOHLCV(symbol, options...)
}
func (this *Huobi) FetchOpenInterest(symbol string, options ...FetchOpenInterestOptions) (OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterest(symbol, options...)
}
func (this *Huobi) FetchOpenInterestHistory(symbol string, options ...FetchOpenInterestHistoryOptions) ([]OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterestHistory(symbol, options...)
}
func (this *Huobi) FetchOpenInterests(options ...FetchOpenInterestsOptions) (OpenInterests, error) {
	return this.exchangeTyped.FetchOpenInterests(options...)
}
func (this *Huobi) FetchOpenOrders(options ...FetchOpenOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchOpenOrders(options...)
}
func (this *Huobi) FetchOption(symbol string, options ...FetchOptionOptions) (Option, error) {
	return this.exchangeTyped.FetchOption(symbol, options...)
}
func (this *Huobi) FetchOptionChain(code string, options ...FetchOptionChainOptions) (OptionChain, error) {
	return this.exchangeTyped.FetchOptionChain(code, options...)
}
func (this *Huobi) FetchOrder(id string, options ...FetchOrderOptions) (Order, error) {
	return this.exchangeTyped.FetchOrder(id, options...)
}
func (this *Huobi) FetchOrderBook(symbol string, options ...FetchOrderBookOptions) (OrderBook, error) {
	return this.exchangeTyped.FetchOrderBook(symbol, options...)
}
func (this *Huobi) FetchOrderBooks(options ...FetchOrderBooksOptions) (OrderBooks, error) {
	return this.exchangeTyped.FetchOrderBooks(options...)
}
func (this *Huobi) FetchOrders(options ...FetchOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchOrders(options...)
}
func (this *Huobi) FetchOrderStatus(id string, options ...FetchOrderStatusOptions) (string, error) {
	return this.exchangeTyped.FetchOrderStatus(id, options...)
}
func (this *Huobi) FetchOrderTrades(id string, options ...FetchOrderTradesOptions) ([]Trade, error) {
	return this.exchangeTyped.FetchOrderTrades(id, options...)
}
func (this *Huobi) FetchPaymentMethods(params ...interface{}) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPaymentMethods(params...)
}
func (this *Huobi) FetchPosition(symbol string, options ...FetchPositionOptions) (Position, error) {
	return this.exchangeTyped.FetchPosition(symbol, options...)
}
func (this *Huobi) FetchPositionHistory(symbol string, options ...FetchPositionHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionHistory(symbol, options...)
}
func (this *Huobi) FetchPositionMode(options ...FetchPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPositionMode(options...)
}
func (this *Huobi) FetchPositions(options ...FetchPositionsOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositions(options...)
}
func (this *Huobi) FetchPositionsForSymbol(symbol string, options ...FetchPositionsForSymbolOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsForSymbol(symbol, options...)
}
func (this *Huobi) FetchPositionsHistory(options ...FetchPositionsHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsHistory(options...)
}
func (this *Huobi) FetchPositionsRisk(options ...FetchPositionsRiskOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsRisk(options...)
}
func (this *Huobi) FetchPremiumIndexOHLCV(symbol string, options ...FetchPremiumIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchPremiumIndexOHLCV(symbol, options...)
}
func (this *Huobi) FetchStatus(params ...interface{}) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchStatus(params...)
}
func (this *Huobi) FetchTicker(symbol string, options ...FetchTickerOptions) (Ticker, error) {
	return this.exchangeTyped.FetchTicker(symbol, options...)
}
func (this *Huobi) FetchTickers(options ...FetchTickersOptions) (Tickers, error) {
	return this.exchangeTyped.FetchTickers(options...)
}
func (this *Huobi) FetchTime(params ...interface{}) (int64, error) {
	return this.exchangeTyped.FetchTime(params...)
}
func (this *Huobi) FetchTrades(symbol string, options ...FetchTradesOptions) ([]Trade, error) {
	return this.exchangeTyped.FetchTrades(symbol, options...)
}
func (this *Huobi) FetchTradingFee(symbol string, options ...FetchTradingFeeOptions) (TradingFeeInterface, error) {
	return this.exchangeTyped.FetchTradingFee(symbol, options...)
}
func (this *Huobi) FetchTradingFees(params ...interface{}) (TradingFees, error) {
	return this.exchangeTyped.FetchTradingFees(params...)
}
func (this *Huobi) FetchTradingLimits(options ...FetchTradingLimitsOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTradingLimits(options...)
}
func (this *Huobi) FetchTransactionFee(code string, options ...FetchTransactionFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFee(code, options...)
}
func (this *Huobi) FetchTransactionFees(options ...FetchTransactionFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFees(options...)
}
func (this *Huobi) FetchTransactions(options ...FetchTransactionsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchTransactions(options...)
}
func (this *Huobi) FetchTransfer(id string, options ...FetchTransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.FetchTransfer(id, options...)
}
func (this *Huobi) FetchTransfers(options ...FetchTransfersOptions) ([]TransferEntry, error) {
	return this.exchangeTyped.FetchTransfers(options...)
}
func (this *Huobi) FetchWithdrawals(options ...FetchWithdrawalsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchWithdrawals(options...)
}
func (this *Huobi) SetMargin(symbol string, amount float64, options ...SetMarginOptions) (MarginModification, error) {
	return this.exchangeTyped.SetMargin(symbol, amount, options...)
}
func (this *Huobi) SetMarginMode(marginMode string, options ...SetMarginModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetMarginMode(marginMode, options...)
}
func (this *Huobi) SetPositionMode(hedged bool, options ...SetPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetPositionMode(hedged, options...)
}
func (this *Huobi) Transfer(code string, amount float64, fromAccount string, toAccount string, options ...TransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.Transfer(code, amount, fromAccount, toAccount, options...)
}
func (this *Huobi) Withdraw(code string, amount float64, address string, options ...WithdrawOptions) (Transaction, error) {
	return this.exchangeTyped.Withdraw(code, amount, address, options...)
}
