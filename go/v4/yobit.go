package ccxt

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

type yobit struct {
	Exchange
}

func NewYobitCore() *yobit {
	p := &yobit{}
	setDefaults(p)
	return p
}

func (this *yobit) Describe() interface{} {
	return this.DeepExtend(this.Exchange.Describe(), map[string]interface{}{
		"id":        "yobit",
		"name":      "YoBit",
		"countries": []interface{}{"RU"},
		"rateLimit": 2000,
		"version":   "3",
		"pro":       false,
		"has": map[string]interface{}{
			"CORS":                           nil,
			"spot":                           true,
			"margin":                         false,
			"swap":                           false,
			"future":                         false,
			"option":                         false,
			"addMargin":                      false,
			"cancelOrder":                    true,
			"closeAllPositions":              false,
			"closePosition":                  false,
			"createDepositAddress":           true,
			"createMarketOrder":              false,
			"createOrder":                    true,
			"createReduceOnlyOrder":          false,
			"createStopLimitOrder":           false,
			"createStopMarketOrder":          false,
			"createStopOrder":                false,
			"fetchBalance":                   true,
			"fetchBorrowInterest":            false,
			"fetchBorrowRate":                false,
			"fetchBorrowRateHistories":       false,
			"fetchBorrowRateHistory":         false,
			"fetchBorrowRates":               false,
			"fetchBorrowRatesPerSymbol":      false,
			"fetchCrossBorrowRate":           false,
			"fetchCrossBorrowRates":          false,
			"fetchDepositAddress":            true,
			"fetchDepositAddresses":          false,
			"fetchDepositAddressesByNetwork": false,
			"fetchDeposits":                  false,
			"fetchFundingHistory":            false,
			"fetchFundingInterval":           false,
			"fetchFundingIntervals":          false,
			"fetchFundingRate":               false,
			"fetchFundingRateHistory":        false,
			"fetchFundingRates":              false,
			"fetchGreeks":                    false,
			"fetchIndexOHLCV":                false,
			"fetchIsolatedBorrowRate":        false,
			"fetchIsolatedBorrowRates":       false,
			"fetchIsolatedPositions":         false,
			"fetchLeverage":                  false,
			"fetchLeverages":                 false,
			"fetchLeverageTiers":             false,
			"fetchLiquidations":              false,
			"fetchMarginAdjustmentHistory":   false,
			"fetchMarginMode":                false,
			"fetchMarginModes":               false,
			"fetchMarketLeverageTiers":       false,
			"fetchMarkets":                   true,
			"fetchMarkOHLCV":                 false,
			"fetchMarkPrices":                false,
			"fetchMyLiquidations":            false,
			"fetchMySettlementHistory":       false,
			"fetchMyTrades":                  true,
			"fetchOpenInterest":              false,
			"fetchOpenInterestHistory":       false,
			"fetchOpenOrders":                true,
			"fetchOption":                    false,
			"fetchOptionChain":               false,
			"fetchOrder":                     true,
			"fetchOrderBook":                 true,
			"fetchOrderBooks":                true,
			"fetchPosition":                  false,
			"fetchPositionHistory":           false,
			"fetchPositionMode":              false,
			"fetchPositions":                 false,
			"fetchPositionsForSymbol":        false,
			"fetchPositionsHistory":          false,
			"fetchPositionsRisk":             false,
			"fetchPremiumIndexOHLCV":         false,
			"fetchSettlementHistory":         false,
			"fetchTicker":                    true,
			"fetchTickers":                   true,
			"fetchTrades":                    true,
			"fetchTradingFee":                false,
			"fetchTradingFees":               true,
			"fetchTransactions":              false,
			"fetchTransfer":                  false,
			"fetchTransfers":                 false,
			"fetchUnderlyingAssets":          false,
			"fetchVolatilityHistory":         false,
			"fetchWithdrawals":               false,
			"reduceMargin":                   false,
			"repayCrossMargin":               false,
			"repayIsolatedMargin":            false,
			"setLeverage":                    false,
			"setMargin":                      false,
			"setMarginMode":                  false,
			"setPositionMode":                false,
			"transfer":                       false,
			"withdraw":                       true,
			"ws":                             false,
		},
		"urls": map[string]interface{}{
			"logo": "https://user-images.githubusercontent.com/1294454/27766910-cdcbfdae-5eea-11e7-9859-03fea873272d.jpg",
			"api": map[string]interface{}{
				"public":  "https://yobit.net/api",
				"private": "https://yobit.net/tapi",
			},
			"www":  "https://www.yobit.net",
			"doc":  "https://www.yobit.net/en/api/",
			"fees": "https://www.yobit.net/en/fees/",
		},
		"api": map[string]interface{}{
			"public": map[string]interface{}{
				"get": map[string]interface{}{
					"depth/{pair}":  1,
					"info":          1,
					"ticker/{pair}": 1,
					"trades/{pair}": 1,
				},
			},
			"private": map[string]interface{}{
				"post": map[string]interface{}{
					"ActiveOrders":           1,
					"CancelOrder":            1,
					"GetDepositAddress":      1,
					"getInfo":                1,
					"OrderInfo":              1,
					"Trade":                  1,
					"TradeHistory":           1,
					"WithdrawCoinsToAddress": 1,
				},
			},
		},
		"fees": map[string]interface{}{
			"trading": map[string]interface{}{
				"maker": 0.002,
				"taker": 0.002,
			},
			"funding": map[string]interface{}{
				"withdraw": map[string]interface{}{},
			},
		},
		"commonCurrencies": map[string]interface{}{
			"AIR":    "AirCoin",
			"ANI":    "ANICoin",
			"ANT":    "AntsCoin",
			"ATMCHA": "ATM",
			"ASN":    "Ascension",
			"AST":    "Astral",
			"ATM":    "Autumncoin",
			"AUR":    "AuroraCoin",
			"BAB":    "Babel",
			"BAN":    "BANcoin",
			"BCC":    "BCH",
			"BCS":    "BitcoinStake",
			"BITS":   "Bitstar",
			"BLN":    "Bulleon",
			"BNS":    "Benefit Bonus Coin",
			"BOT":    "BOTcoin",
			"BON":    "BONES",
			"BPC":    "BitcoinPremium",
			"BST":    "BitStone",
			"BTS":    "Bitshares2",
			"CAT":    "BitClave",
			"CBC":    "CryptoBossCoin",
			"CMT":    "CometCoin",
			"COIN":   "Coin.com",
			"COV":    "Coven Coin",
			"COVX":   "COV",
			"CPC":    "Capricoin",
			"CREDIT": "Creditbit",
			"CS":     "CryptoSpots",
			"DCT":    "Discount",
			"DFT":    "DraftCoin",
			"DGD":    "DarkGoldCoin",
			"DIRT":   "DIRTY",
			"DROP":   "FaucetCoin",
			"DSH":    "DASH",
			"EGC":    "EverGreenCoin",
			"EGG":    "EggCoin",
			"EKO":    "EkoCoin",
			"ENTER":  "ENTRC",
			"EPC":    "ExperienceCoin",
			"ESC":    "EdwardSnowden",
			"EUROPE": "EUROP",
			"EXT":    "LifeExtension",
			"FUND":   "FUNDChains",
			"FUNK":   "FUNKCoin",
			"FX":     "FCoin",
			"GCC":    "GlobalCryptocurrency",
			"GEN":    "Genstake",
			"GENE":   "Genesiscoin",
			"GMR":    "Gimmer",
			"GOLD":   "GoldMint",
			"GOT":    "Giotto Coin",
			"GSX":    "GlowShares",
			"GT":     "GTcoin",
			"HTML5":  "HTML",
			"HYPERX": "HYPER",
			"ICN":    "iCoin",
			"INSANE": "INSN",
			"JNT":    "JointCoin",
			"JPC":    "JupiterCoin",
			"JWL":    "Jewels",
			"KNC":    "KingN Coin",
			"LBTCX":  "LiteBitcoin",
			"LIZI":   "LiZi",
			"LOC":    "LocoCoin",
			"LOCX":   "LOC",
			"LUNYR":  "LUN",
			"LUN":    "LunarCoin",
			"LUNA":   "Luna Coin",
			"MASK":   "Yobit MASK",
			"MDT":    "Midnight",
			"MEME":   "Memez Token",
			"MIS":    "MIScoin",
			"MM":     "MasterMint",
			"NAV":    "NavajoCoin",
			"NBT":    "NiceBytes",
			"OMG":    "OMGame",
			"ONX":    "Onix",
			"PAC":    "$PAC",
			"PLAY":   "PlayCoin",
			"PIVX":   "Darknet",
			"PURE":   "PurePOS",
			"PUTIN":  "PutinCoin",
			"SPACE":  "Spacecoin",
			"STK":    "StakeCoin",
			"SUB":    "Subscriptio",
			"PAY":    "EPAY",
			"PLC":    "Platin Coin",
			"RAI":    "RaiderCoin",
			"RCN":    "RCoin",
			"REP":    "Republicoin",
			"RUR":    "RUB",
			"SBTC":   "Super Bitcoin",
			"SMC":    "SmartCoin",
			"SOLO":   "SoloCoin",
			"SOUL":   "SoulCoin",
			"STAR":   "StarCoin",
			"SUPER":  "SuperCoin",
			"TNS":    "Transcodium",
			"TTC":    "TittieCoin",
			"UNI":    "Universe",
			"UST":    "Uservice",
			"VOL":    "VolumeCoin",
			"XIN":    "XINCoin",
			"XMT":    "SummitCoin",
			"XRA":    "Ratecoin",
			"BCHN":   "BSV",
		},
		"options": map[string]interface{}{
			"maxUrlLength":              2048,
			"fetchOrdersRequiresSymbol": true,
			"networks": map[string]interface{}{
				"ETH": "ERC20",
				"TRX": "TRC20",
				"BSC": "BEP20",
			},
		},
		"precisionMode": TICK_SIZE,
		"exceptions": map[string]interface{}{
			"exact": map[string]interface{}{
				"803": InvalidOrder,
				"804": InvalidOrder,
				"805": InvalidOrder,
				"806": InvalidOrder,
				"807": InvalidOrder,
				"831": InsufficientFunds,
				"832": InsufficientFunds,
				"833": OrderNotFound,
			},
			"broad": map[string]interface{}{
				"Invalid pair name":                          ExchangeError,
				"invalid api key":                            AuthenticationError,
				"invalid sign":                               AuthenticationError,
				"api key dont have trade permission":         AuthenticationError,
				"invalid parameter":                          InvalidOrder,
				"invalid order":                              InvalidOrder,
				"The given order has already been cancelled": InvalidOrder,
				"Requests too often":                         DDoSProtection,
				"not available":                              ExchangeNotAvailable,
				"data unavailable":                           ExchangeNotAvailable,
				"external service unavailable":               ExchangeNotAvailable,
				"Total transaction amount":                   InvalidOrder,
				"The given order has already been closed and cannot be cancelled": InvalidOrder,
				"Insufficient funds": InsufficientFunds,
				"invalid key":        AuthenticationError,
				"invalid nonce":      InvalidNonce,
				"Total order amount is less than minimal amount": InvalidOrder,
				"Rate Limited": RateLimitExceeded,
			},
		},
		"features": map[string]interface{}{
			"spot": map[string]interface{}{
				"sandbox": false,
				"createOrder": map[string]interface{}{
					"marginMode":                 false,
					"triggerPrice":               false,
					"triggerDirection":           false,
					"triggerPriceType":           nil,
					"stopLossPrice":              false,
					"takeProfitPrice":            false,
					"attachedStopLossTakeProfit": nil,
					"timeInForce": map[string]interface{}{
						"IOC": false,
						"FOK": false,
						"PO":  false,
						"GTD": false,
					},
					"hedged":                 false,
					"trailing":               false,
					"leverage":               false,
					"marketBuyByCost":        false,
					"marketBuyRequiresPrice": false,
					"selfTradePrevention":    false,
					"iceberg":                false,
				},
				"createOrders": nil,
				"fetchMyTrades": map[string]interface{}{
					"marginMode":     false,
					"limit":          1000,
					"daysBack":       100000,
					"untilDays":      100000,
					"symbolRequired": true,
				},
				"fetchOrder": map[string]interface{}{
					"marginMode":     false,
					"trigger":        false,
					"trailing":       false,
					"symbolRequired": false,
				},
				"fetchOpenOrders": map[string]interface{}{
					"marginMode":     false,
					"limit":          nil,
					"trigger":        false,
					"trailing":       false,
					"symbolRequired": true,
				},
				"fetchOrders":       nil,
				"fetchClosedOrders": nil,
				"fetchOHLCV":        nil,
			},
			"swap": map[string]interface{}{
				"linear":  nil,
				"inverse": nil,
			},
			"future": map[string]interface{}{
				"linear":  nil,
				"inverse": nil,
			},
		},
		"orders": map[string]interface{}{},
	})
}
func (this *yobit) ParseBalance(response interface{}) interface{} {
	var balances interface{} = this.SafeDict(response, "return", map[string]interface{}{})
	var timestamp interface{} = this.SafeInteger(balances, "server_time")
	var result interface{} = map[string]interface{}{
		"info":      response,
		"timestamp": timestamp,
		"datetime":  this.Iso8601(timestamp),
	}
	var free interface{} = this.SafeDict(balances, "funds", map[string]interface{}{})
	var total interface{} = this.SafeDict(balances, "funds_incl_orders", map[string]interface{}{})
	var currencyIds interface{} = ObjectKeys(this.Extend(free, total))
	for i := 0; IsLessThan(i, GetArrayLength(currencyIds)); i++ {
		var currencyId interface{} = GetValue(currencyIds, i)
		var code interface{} = this.SafeCurrencyCode(currencyId)
		var account interface{} = this.Account()
		AddElementToObject(account, "free", this.SafeString(free, currencyId))
		AddElementToObject(account, "total", this.SafeString(total, currencyId))
		AddElementToObject(result, code, account)
	}
	return this.SafeBalance(result)
}

/**
 * @method
 * @name yobit#fetchBalance
 * @see https://yobit.net/en/api
 * @description query for balance and get the amount of funds available for trading or funds locked in orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
 */
func (this *yobit) FetchBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes4068 := (<-this.LoadMarkets())
		PanicOnError(retRes4068)

		response := (<-this.PrivatePostGetInfo(params))
		PanicOnError(response)

		//
		//     {
		//         "success":1,
		//         "return":{
		//             "funds":{
		//                 "ltc":22,
		//                 "nvc":423.998,
		//                 "ppc":10,
		//             },
		//             "funds_incl_orders":{
		//                 "ltc":32,
		//                 "nvc":523.998,
		//                 "ppc":20,
		//             },
		//             "rights":{
		//                 "info":1,
		//                 "trade":0,
		//                 "withdraw":0
		//             },
		//             "transaction_count":0,
		//             "open_orders":1,
		//             "server_time":1418654530
		//         }
		//     }
		//
		ch <- this.ParseBalance(response)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchMarkets
 * @see https://yobit.net/en/api
 * @description retrieves data on all markets for yobit
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} an array of objects representing market data
 */
func (this *yobit) FetchMarkets(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		response := (<-this.PublicGetInfo(params))
		PanicOnError(response)
		//
		//     {
		//         "server_time":1615856752,
		//         "pairs":{
		//             "ltc_btc":{
		//                 "decimal_places":8,
		//                 "min_price":0.00000001,
		//                 "max_price":10000,
		//                 "min_amount":0.0001,
		//                 "min_total":0.0001,
		//                 "hidden":0,
		//                 "fee":0.2,
		//                 "fee_buyer":0.2,
		//                 "fee_seller":0.2
		//             },
		//         },
		//     }
		//
		var markets interface{} = this.SafeDict(response, "pairs", map[string]interface{}{})
		var keys interface{} = ObjectKeys(markets)
		var result interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
			var id interface{} = GetValue(keys, i)
			var market interface{} = GetValue(markets, id)
			baseIdquoteIdVariable := Split(id, "_")
			baseId := GetValue(baseIdquoteIdVariable, 0)
			quoteId := GetValue(baseIdquoteIdVariable, 1)
			var base interface{} = ToUpper(baseId)
			var quote interface{} = ToUpper(quoteId)
			base = this.SafeCurrencyCode(base)
			quote = this.SafeCurrencyCode(quote)
			var hidden interface{} = this.SafeInteger(market, "hidden")
			var feeString interface{} = this.SafeString(market, "fee")
			feeString = Precise.StringDiv(feeString, "100")
			// yobit maker = taker
			AppendToArray(&result, map[string]interface{}{
				"id":             id,
				"symbol":         Add(Add(base, "/"), quote),
				"base":           base,
				"quote":          quote,
				"settle":         nil,
				"baseId":         baseId,
				"quoteId":        quoteId,
				"settleId":       nil,
				"type":           "spot",
				"spot":           true,
				"margin":         false,
				"swap":           false,
				"future":         false,
				"option":         false,
				"active":         (IsEqual(hidden, 0)),
				"contract":       false,
				"linear":         nil,
				"inverse":        nil,
				"taker":          this.ParseNumber(feeString),
				"maker":          this.ParseNumber(feeString),
				"contractSize":   nil,
				"expiry":         nil,
				"expiryDatetime": nil,
				"strike":         nil,
				"optionType":     nil,
				"precision": map[string]interface{}{
					"amount": this.ParseNumber(this.ParsePrecision(this.SafeString(market, "decimal_places"))),
					"price":  this.ParseNumber(this.ParsePrecision(this.SafeString(market, "decimal_places"))),
				},
				"limits": map[string]interface{}{
					"leverage": map[string]interface{}{
						"min": nil,
						"max": nil,
					},
					"amount": map[string]interface{}{
						"min": this.SafeNumber(market, "min_amount"),
						"max": this.SafeNumber(market, "max_amount"),
					},
					"price": map[string]interface{}{
						"min": this.SafeNumber(market, "min_price"),
						"max": this.SafeNumber(market, "max_price"),
					},
					"cost": map[string]interface{}{
						"min": this.SafeNumber(market, "min_total"),
						"max": nil,
					},
				},
				"created": nil,
				"info":    market,
			})
		}

		ch <- result
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchOrderBook
 * @see https://yobit.net/en/api
 * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
 * @param {string} symbol unified symbol of the market to fetch the order book for
 * @param {int} [limit] the maximum amount of order book entries to return
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
 */
func (this *yobit) FetchOrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes5458 := (<-this.LoadMarkets())
		PanicOnError(retRes5458)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"pair": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit) // default = 150, max = 2000
		}

		response := (<-this.PublicGetDepthPair(this.Extend(request, params)))
		PanicOnError(response)
		var market_id_in_reponse interface{} = (InOp(response, GetValue(market, "id")))
		if !IsTrue(market_id_in_reponse) {
			panic(ExchangeError(Add(Add(Add(this.Id, " "), GetValue(market, "symbol")), " order book is empty or not available")))
		}
		var orderbook interface{} = GetValue(response, GetValue(market, "id"))

		ch <- this.ParseOrderBook(orderbook, symbol)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchOrderBooks
 * @see https://yobit.net/en/api
 * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data for multiple markets
 * @param {string[]|undefined} symbols list of unified market symbols, all symbols fetched if undefined, default is undefined
 * @param {int} [limit] max number of entries per orderbook to return, default is undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbol
 */
func (this *yobit) FetchOrderBooks(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params

		retRes5738 := (<-this.LoadMarkets())
		PanicOnError(retRes5738)
		var ids interface{} = nil
		if IsTrue(IsEqual(symbols, nil)) {
			ids = Join(this.Ids, "-")
			// max URL length is 2083 symbols, including http schema, hostname, tld, etc...
			if IsTrue(IsGreaterThan(GetArrayLength(ids), 2048)) {
				var numIds interface{} = GetArrayLength(this.Ids)
				panic(ExchangeError(Add(Add(Add(this.Id, " fetchOrderBooks() has "), ToString(numIds)), " symbols exceeding max URL length, you are required to specify a list of symbols in the first argument to fetchOrderBooks")))
			}
		} else {
			ids = this.MarketIds(symbols)
			ids = Join(ids, "-")
		}
		var request interface{} = map[string]interface{}{
			"pair": ids,
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}

		response := (<-this.PublicGetDepthPair(this.Extend(request, params)))
		PanicOnError(response)
		var result interface{} = map[string]interface{}{}
		ids = ObjectKeys(response)
		for i := 0; IsLessThan(i, GetArrayLength(ids)); i++ {
			var id interface{} = GetValue(ids, i)
			var symbol interface{} = this.SafeSymbol(id)
			AddElementToObject(result, symbol, this.ParseOrderBook(GetValue(response, id), symbol))
		}

		ch <- result
		return nil

	}()
	return ch
}
func (this *yobit) ParseTicker(ticker interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "high": 0.03497582,
	//         "low": 0.03248474,
	//         "avg": 0.03373028,
	//         "vol": 120.11485715062999,
	//         "vol_cur": 3572.24914074,
	//         "last": 0.0337611,
	//         "buy": 0.0337442,
	//         "sell": 0.03377798,
	//         "updated": 1537522009
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var timestamp interface{} = this.SafeTimestamp(ticker, "updated")
	var last interface{} = this.SafeString(ticker, "last")
	return this.SafeTicker(map[string]interface{}{
		"symbol":        this.SafeSymbol(nil, market),
		"timestamp":     timestamp,
		"datetime":      this.Iso8601(timestamp),
		"high":          this.SafeString(ticker, "high"),
		"low":           this.SafeString(ticker, "low"),
		"bid":           this.SafeString(ticker, "buy"),
		"bidVolume":     nil,
		"ask":           this.SafeString(ticker, "sell"),
		"askVolume":     nil,
		"vwap":          nil,
		"open":          nil,
		"close":         last,
		"last":          last,
		"previousClose": nil,
		"change":        nil,
		"percentage":    nil,
		"average":       this.SafeString(ticker, "avg"),
		"baseVolume":    this.SafeString(ticker, "vol_cur"),
		"quoteVolume":   this.SafeString(ticker, "vol"),
		"info":          ticker,
	}, market)
}
func (this *yobit) FetchTickersHelper(idsString interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		var request interface{} = map[string]interface{}{
			"pair": idsString,
		}

		tickers := (<-this.PublicGetTickerPair(this.Extend(request, params)))
		PanicOnError(tickers)
		var result interface{} = map[string]interface{}{}
		var keys interface{} = ObjectKeys(tickers)
		for k := 0; IsLessThan(k, GetArrayLength(keys)); k++ {
			var id interface{} = GetValue(keys, k)
			var ticker interface{} = GetValue(tickers, id)
			var market interface{} = this.SafeMarket(id)
			var symbol interface{} = GetValue(market, "symbol")
			AddElementToObject(result, symbol, this.ParseTicker(ticker, market))
		}

		ch <- result
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchTickers
 * @see https://yobit.net/en/api
 * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
 * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {object} [params.all] you can set to `true` for convenience to fetch all tickers from this exchange by sending multiple requests
 * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *yobit) FetchTickers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		var allSymbols interface{} = nil
		allSymbolsparamsVariable := this.HandleParamBool(params, "all", false)
		allSymbols = GetValue(allSymbolsparamsVariable, 0)
		params = GetValue(allSymbolsparamsVariable, 1)
		if IsTrue(IsTrue(IsEqual(symbols, nil)) && !IsTrue(allSymbols)) {
			panic(ArgumentsRequired(Add(this.Id, " fetchTickers() requires \"symbols\" argument or use `params[\"all\"] = true` to send multiple requests for all markets")))
		}

		retRes6778 := (<-this.LoadMarkets())
		PanicOnError(retRes6778)
		var promises interface{} = []interface{}{}
		var maxLength interface{} = this.SafeInteger(this.Options, "maxUrlLength", 2048)
		// max URL length is 2048 symbols, including http schema, hostname, tld, etc...
		var lenghtOfBaseUrl interface{} = 40 // safe space for the url including api-base and endpoint dir is 30 chars
		if IsTrue(allSymbols) {
			symbols = this.Symbols
			var ids interface{} = ""
			for i := 0; IsLessThan(i, GetArrayLength(this.Ids)); i++ {
				var id interface{} = GetValue(this.Ids, i)
				var prefix interface{} = Ternary(IsTrue((IsEqual(ids, ""))), "", "-")
				ids = Add(ids, Add(prefix, id))
				if IsTrue(IsGreaterThan(GetLength(ids), maxLength)) {
					AppendToArray(&promises, this.FetchTickersHelper(ids, params))
					ids = ""
				}
			}
			if IsTrue(!IsEqual(ids, "")) {
				AppendToArray(&promises, this.FetchTickersHelper(ids, params))
			}
		} else {
			symbols = this.MarketSymbols(symbols)
			var ids interface{} = this.MarketIds(symbols)
			var idsLength interface{} = GetArrayLength(ids)
			var idsString interface{} = Join(ids, "-")
			var actualLength interface{} = Add(GetLength(idsString), lenghtOfBaseUrl)
			if IsTrue(IsGreaterThan(actualLength, maxLength)) {
				panic(ArgumentsRequired(Add(Add(Add(Add(Add(Add(Add(this.Id, " fetchTickers() is being requested for "), ToString(idsLength)), " markets (which has an URL length of "), ToString(actualLength)), " characters), but it exceedes max URL length ("), ToString(maxLength)), "), please pass limisted symbols array to fetchTickers to fit in one request")))
			}
			AppendToArray(&promises, this.FetchTickersHelper(idsString, params))
		}

		resultAll := (<-promiseAll(promises))
		PanicOnError(resultAll)
		var finalResult interface{} = map[string]interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(resultAll)); i++ {
			var result interface{} = this.FilterByArrayTickers(GetValue(resultAll, i), "symbol", symbols)
			finalResult = this.Extend(finalResult, result)
		}

		ch <- finalResult
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchTicker
 * @see https://yobit.net/en/api
 * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
 * @param {string} symbol unified symbol of the market to fetch the ticker for
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *yobit) FetchTicker(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		tickers := (<-this.FetchTickers([]interface{}{symbol}, params))
		PanicOnError(tickers)

		ch <- GetValue(tickers, symbol)
		return nil

	}()
	return ch
}
func (this *yobit) ParseTrade(trade interface{}, optionalArgs ...interface{}) interface{} {
	//
	// fetchTrades (public)
	//
	//      {
	//          "type":"bid",
	//          "price":0.14046179,
	//          "amount":0.001,
	//          "tid":200256901,
	//          "timestamp":1649861004
	//      }
	//
	// fetchMyTrades (private)
	//
	//      {
	//          "pair":"doge_usdt",
	//          "type":"sell",
	//          "amount":139,
	//          "rate":0.139,
	//          "order_id":"2101103631773172",
	//          "is_your_order":1,
	//          "timestamp":"1649861561"
	//      }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var timestamp interface{} = this.SafeTimestamp(trade, "timestamp")
	var side interface{} = this.SafeString(trade, "type")
	if IsTrue(IsEqual(side, "ask")) {
		side = "sell"
	} else if IsTrue(IsEqual(side, "bid")) {
		side = "buy"
	}
	var priceString interface{} = this.SafeString2(trade, "rate", "price")
	var id interface{} = this.SafeString2(trade, "trade_id", "tid")
	var order interface{} = this.SafeString(trade, "order_id")
	var marketId interface{} = this.SafeString(trade, "pair")
	var symbol interface{} = this.SafeSymbol(marketId, market)
	var amountString interface{} = this.SafeString(trade, "amount")
	// arguments for calculateFee (need to be numbers)
	var price interface{} = this.ParseNumber(priceString)
	var amount interface{} = this.ParseNumber(amountString)
	var typeVar interface{} = "limit" // all trades are still limit trades
	var fee interface{} = nil
	var feeCostString interface{} = this.SafeNumber(trade, "commission")
	if IsTrue(!IsEqual(feeCostString, nil)) {
		var feeCurrencyId interface{} = this.SafeString(trade, "commissionCurrency")
		var feeCurrencyCode interface{} = this.SafeCurrencyCode(feeCurrencyId)
		fee = map[string]interface{}{
			"cost":     feeCostString,
			"currency": feeCurrencyCode,
		}
	}
	var isYourOrder interface{} = this.SafeString(trade, "is_your_order")
	if IsTrue(!IsEqual(isYourOrder, nil)) {
		if IsTrue(IsEqual(fee, nil)) {
			var feeInNumbers interface{} = this.CalculateFee(symbol, typeVar, side, amount, price, "taker")
			fee = map[string]interface{}{
				"currency": this.SafeString(feeInNumbers, "currency"),
				"cost":     this.SafeString(feeInNumbers, "cost"),
				"rate":     this.SafeString(feeInNumbers, "rate"),
			}
		}
	}
	return this.SafeTrade(map[string]interface{}{
		"id":           id,
		"order":        order,
		"timestamp":    timestamp,
		"datetime":     this.Iso8601(timestamp),
		"symbol":       symbol,
		"type":         typeVar,
		"side":         side,
		"takerOrMaker": nil,
		"price":        priceString,
		"amount":       amountString,
		"cost":         nil,
		"fee":          fee,
		"info":         trade,
	}, market)
}

/**
 * @method
 * @name yobit#fetchTrades
 * @see https://yobit.net/en/api
 * @description get the list of most recent trades for a particular symbol
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch
 * @param {int} [limit] the maximum amount of trades to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *yobit) FetchTrades(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params

		retRes8228 := (<-this.LoadMarkets())
		PanicOnError(retRes8228)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"pair": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}

		response := (<-this.PublicGetTradesPair(this.Extend(request, params)))
		PanicOnError(response)
		//
		//      {
		//          "doge_usdt": [
		//              {
		//                  "type":"ask",
		//                  "price":0.13956743,
		//                  "amount":0.0008,
		//                  "tid":200256900,
		//                  "timestamp":1649860521
		//              },
		//          ]
		//      }
		//
		if IsTrue(IsArray(response)) {
			var numElements interface{} = GetArrayLength(response)
			if IsTrue(IsEqual(numElements, 0)) {

				ch <- []interface{}{}
				return nil
			}
		}
		var result interface{} = this.SafeList(response, GetValue(market, "id"), []interface{}{})

		ch <- this.ParseTrades(result, market, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchTradingFees
 * @see https://yobit.net/en/api
 * @description fetch the trading fees for multiple markets
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
 */
func (this *yobit) FetchTradingFees(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes8638 := (<-this.LoadMarkets())
		PanicOnError(retRes8638)

		response := (<-this.PublicGetInfo(params))
		PanicOnError(response)
		//
		//     {
		//         "server_time":1615856752,
		//         "pairs":{
		//             "ltc_btc":{
		//                 "decimal_places":8,
		//                 "min_price":0.00000001,
		//                 "max_price":10000,
		//                 "min_amount":0.0001,
		//                 "min_total":0.0001,
		//                 "hidden":0,
		//                 "fee":0.2,
		//                 "fee_buyer":0.2,
		//                 "fee_seller":0.2
		//             },
		//             ...
		//         },
		//     }
		//
		var pairs interface{} = this.SafeDict(response, "pairs", map[string]interface{}{})
		var marketIds interface{} = ObjectKeys(pairs)
		var result interface{} = map[string]interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(marketIds)); i++ {
			var marketId interface{} = GetValue(marketIds, i)
			var pair interface{} = this.SafeDict(pairs, marketId, map[string]interface{}{})
			var symbol interface{} = this.SafeSymbol(marketId, nil, "_")
			var takerString interface{} = this.SafeString(pair, "fee_buyer")
			var makerString interface{} = this.SafeString(pair, "fee_seller")
			var taker interface{} = this.ParseNumber(Precise.StringDiv(takerString, "100"))
			var maker interface{} = this.ParseNumber(Precise.StringDiv(makerString, "100"))
			AddElementToObject(result, symbol, map[string]interface{}{
				"info":       pair,
				"symbol":     symbol,
				"taker":      taker,
				"maker":      maker,
				"percentage": true,
				"tierBased":  false,
			})
		}

		ch <- result
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#createOrder
 * @see https://yobit.net/en/api
 * @description create a trade order
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type must be 'limit'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much of currency you want to trade in units of base currency
 * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *yobit) CreateOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(typeVar, "market")) {
			panic(ExchangeError(Add(this.Id, " createOrder() allows limit orders only")))
		}

		retRes9248 := (<-this.LoadMarkets())
		PanicOnError(retRes9248)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"pair":   GetValue(market, "id"),
			"type":   side,
			"amount": this.AmountToPrecision(symbol, amount),
			"rate":   this.PriceToPrecision(symbol, price),
		}

		response := (<-this.PrivatePostTrade(this.Extend(request, params)))
		PanicOnError(response)
		//
		//      {
		//          "success":1,
		//          "return": {
		//              "received":0,
		//              "remains":10,
		//              "order_id":1101103635125179,
		//              "funds": {
		//                  "usdt":27.84756553,
		//                  "usdttrc20":0,
		//                  "doge":19.98327206
		//              },
		//              "funds_incl_orders": {
		//                  "usdt":30.35256553,
		//                  "usdttrc20":0,
		//                  "doge":19.98327206
		//               },
		//               "server_time":1650114256
		//           }
		//       }
		//
		var result interface{} = this.SafeDict(response, "return")

		ch <- this.ParseOrder(result, market)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#cancelOrder
 * @see https://yobit.net/en/api
 * @description cancels an open order
 * @param {string} id order id
 * @param {string} symbol not used by yobit cancelOrder ()
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *yobit) CancelOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes9698 := (<-this.LoadMarkets())
		PanicOnError(retRes9698)
		var request interface{} = map[string]interface{}{
			"order_id": ParseInt(id),
		}

		response := (<-this.PrivatePostCancelOrder(this.Extend(request, params)))
		PanicOnError(response)
		//
		//      {
		//          "success":1,
		//          "return": {
		//              "order_id":1101103632552304,
		//              "funds": {
		//                  "usdt":30.71055443,
		//                  "usdttrc20":0,
		//                  "doge":9.98327206
		//              },
		//              "funds_incl_orders": {
		//                  "usdt":31.81275443,
		//                  "usdttrc20":0,
		//                  "doge":9.98327206
		//              },
		//              "server_time":1649918298
		//          }
		//      }
		//
		var result interface{} = this.SafeDict(response, "return", map[string]interface{}{})

		ch <- this.ParseOrder(result)
		return nil

	}()
	return ch
}
func (this *yobit) ParseOrderStatus(status interface{}) interface{} {
	var statuses interface{} = map[string]interface{}{
		"0": "open",
		"1": "closed",
		"2": "canceled",
		"3": "open",
	}
	return this.SafeString(statuses, status, status)
}
func (this *yobit) ParseOrder(order interface{}, optionalArgs ...interface{}) interface{} {
	//
	// createOrder (private)
	//
	//      {
	//          "received":0,
	//          "remains":10,
	//          "order_id":1101103635125179,
	//          "funds": {
	//              "usdt":27.84756553,
	//              "usdttrc20":0,
	//              "doge":19.98327206
	//          },
	//          "funds_incl_orders": {
	//              "usdt":30.35256553,
	//              "usdttrc20":0,
	//              "doge":19.98327206
	//          },
	//          "server_time":1650114256
	//      }
	//
	// fetchOrder (private)
	//
	//      {
	//          "id: "1101103635103335",  // id-field is manually added in fetchOrder () from exchange response id-order dictionary structure
	//          "pair":"doge_usdt",
	//          "type":"buy",
	//          "start_amount":10,
	//          "amount":10,
	//          "rate":0.05,
	//          "timestamp_created":"1650112553",
	//          "status":0
	//      }
	//
	// fetchOpenOrders (private)
	//
	//      {
	//          "id":"1101103635103335", // id-field is manually added in fetchOpenOrders () from exchange response id-order dictionary structure
	//          "pair":"doge_usdt",
	//          "type":"buy",
	//          "amount":10,
	//          "rate":0.05,
	//          "timestamp_created":"1650112553",
	//          "status":0
	//      }
	//
	// cancelOrder (private)
	//
	//      {
	//          "order_id":1101103634000197,
	//          "funds": {
	//              "usdt":31.81275443,
	//              "usdttrc20":0,
	//              "doge":9.98327206
	//          },
	//          "funds_incl_orders": {
	//              "usdt":31.81275443,
	//              "usdttrc20":0,
	//              "doge":9.98327206
	//          }
	//      }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var id interface{} = this.SafeString2(order, "id", "order_id")
	var status interface{} = this.ParseOrderStatus(this.SafeString(order, "status", "open"))
	if IsTrue(IsEqual(id, "0")) {
		id = this.SafeString(order, "init_order_id")
		status = "closed"
	}
	var timestamp interface{} = this.SafeTimestamp2(order, "timestamp_created", "server_time")
	var marketId interface{} = this.SafeString(order, "pair")
	var symbol interface{} = this.SafeSymbol(marketId, market)
	var amount interface{} = this.SafeString(order, "start_amount")
	var remaining interface{} = this.SafeString2(order, "amount", "remains")
	var filled interface{} = this.SafeString(order, "received", "0.0")
	var price interface{} = this.SafeString(order, "rate")
	var fee interface{} = nil
	var typeVar interface{} = "limit"
	var side interface{} = this.SafeString(order, "type")
	return this.SafeOrder(map[string]interface{}{
		"info":               order,
		"id":                 id,
		"clientOrderId":      nil,
		"symbol":             symbol,
		"timestamp":          timestamp,
		"datetime":           this.Iso8601(timestamp),
		"lastTradeTimestamp": nil,
		"type":               typeVar,
		"timeInForce":        nil,
		"postOnly":           nil,
		"side":               side,
		"price":              price,
		"triggerPrice":       nil,
		"cost":               nil,
		"amount":             amount,
		"remaining":          remaining,
		"filled":             filled,
		"status":             status,
		"fee":                fee,
		"average":            nil,
		"trades":             nil,
	}, market)
}

/**
 * @method
 * @name yobit#fetchOrder
 * @see https://yobit.net/en/api
 * @description fetches information on an order made by the user
 * @param {string} id order id
 * @param {string} symbol not used by yobit fetchOrder
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *yobit) FetchOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes11218 := (<-this.LoadMarkets())
		PanicOnError(retRes11218)
		var request interface{} = map[string]interface{}{
			"order_id": ParseInt(id),
		}

		response := (<-this.PrivatePostOrderInfo(this.Extend(request, params)))
		PanicOnError(response)
		id = ToString(id)
		var orders interface{} = this.SafeDict(response, "return", map[string]interface{}{})

		//
		//      {
		//          "success":1,
		//          "return": {
		//              "1101103635103335": {
		//                  "pair":"doge_usdt",
		//                  "type":"buy",
		//                  "start_amount":10,
		//                  "amount":10,
		//                  "rate":0.05,
		//                  "timestamp_created":"1650112553",
		//                  "status":0
		//              }
		//          }
		//      }
		//
		ch <- this.ParseOrder(this.Extend(map[string]interface{}{
			"id": id,
		}, GetValue(orders, id)))
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchOpenOrders
 * @see https://yobit.net/en/api
 * @description fetch all unfilled currently open orders
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch open orders for
 * @param {int} [limit] the maximum number of open order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *yobit) FetchOpenOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(symbol, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " fetchOpenOrders() requires a symbol argument")))
		}

		retRes11628 := (<-this.LoadMarkets())
		PanicOnError(retRes11628)
		var request interface{} = map[string]interface{}{}
		var market interface{} = nil
		if IsTrue(!IsEqual(symbol, nil)) {
			var marketInner interface{} = this.Market(symbol)
			AddElementToObject(request, "pair", GetValue(marketInner, "id"))
		}

		response := (<-this.PrivatePostActiveOrders(this.Extend(request, params)))
		PanicOnError(response)
		//
		//      {
		//          "success":1,
		//          "return": {
		//              "1101103634006799": {
		//                  "pair":"doge_usdt",
		//                  "type":"buy",
		//                  "amount":10,
		//                  "rate":0.1,
		//                  "timestamp_created":"1650034937",
		//                  "status":0
		//              },
		//              "1101103634006738": {
		//                  "pair":"doge_usdt",
		//                  "type":"buy",
		//                  "amount":10,
		//                  "rate":0.1,
		//                  "timestamp_created":"1650034932",
		//                  "status":0
		//              }
		//          }
		//      }
		//
		var result interface{} = this.SafeDict(response, "return", map[string]interface{}{})

		ch <- this.ParseOrders(result, market, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchMyTrades
 * @see https://yobit.net/en/api
 * @description fetch all trades made by the user
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch trades for
 * @param {int} [limit] the maximum number of trades structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
 */
func (this *yobit) FetchMyTrades(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(symbol, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " fetchMyTrades() requires a symbol argument")))
		}

		retRes12128 := (<-this.LoadMarkets())
		PanicOnError(retRes12128)
		var market interface{} = this.Market(symbol)
		// some derived classes use camelcase notation for request fields
		var request interface{} = map[string]interface{}{
			"pair": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "count", limit)
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "since", this.ParseToInt(Divide(since, 1000)))
		}

		response := (<-this.PrivatePostTradeHistory(this.Extend(request, params)))
		PanicOnError(response)
		//
		//      {
		//          "success":1,
		//          "return": {
		//              "200257004": {
		//                  "pair":"doge_usdt",
		//                  "type":"sell",
		//                  "amount":139,
		//                  "rate":0.139,
		//                  "order_id":"2101103631773172",
		//                  "is_your_order":1,
		//                  "timestamp":"1649861561"
		//              }
		//          }
		//      }
		//
		var trades interface{} = this.SafeDict(response, "return", map[string]interface{}{})
		var ids interface{} = ObjectKeys(trades)
		var result interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(ids)); i++ {
			var id interface{} = this.SafeString(ids, i)
			var trade interface{} = this.ParseTrade(this.Extend(GetValue(trades, id), map[string]interface{}{
				"trade_id": id,
			}), market)
			AppendToArray(&result, trade)
		}

		ch <- this.FilterBySymbolSinceLimit(result, GetValue(market, "symbol"), since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#createDepositAddress
 * @see https://yobit.net/en/api
 * @description create a currency deposit address
 * @param {string} code unified currency code of the currency for the deposit address
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
 */
func (this *yobit) CreateDepositAddress(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		var request interface{} = map[string]interface{}{
			"need_new": 1,
		}

		response := (<-this.FetchDepositAddress(code, this.Extend(request, params)))
		PanicOnError(response)
		var address interface{} = this.SafeString(response, "address")
		this.CheckAddress(address)

		ch <- map[string]interface{}{
			"currency": code,
			"address":  address,
			"tag":      nil,
			"network":  nil,
			"info":     GetValue(response, "info"),
		}
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#fetchDepositAddress
 * @description fetch the deposit address for a currency associated with this account
 * @see https://yobit.net/en/api
 * @param {string} code unified currency code
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
 */
func (this *yobit) FetchDepositAddress(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes12968 := (<-this.LoadMarkets())
		PanicOnError(retRes12968)
		var currency interface{} = this.Currency(code)
		var currencyId interface{} = GetValue(currency, "id")
		var networks interface{} = this.SafeDict(this.Options, "networks", map[string]interface{}{})
		var network interface{} = this.SafeStringUpper(params, "network") // this line allows the user to specify either ERC20 or ETH
		network = this.SafeString(networks, network, network)             // handle ERC20>ETH alias
		if IsTrue(!IsEqual(network, nil)) {
			if IsTrue(!IsEqual(network, "ERC20")) {
				currencyId = Add(currencyId, ToLower(network))
			}
			params = this.Omit(params, "network")
		}
		var request interface{} = map[string]interface{}{
			"coinName": currencyId,
			"need_new": 0,
		}

		response := (<-this.PrivatePostGetDepositAddress(this.Extend(request, params)))
		PanicOnError(response)
		var address interface{} = this.SafeString(GetValue(response, "return"), "address")
		this.CheckAddress(address)

		ch <- map[string]interface{}{
			"info":     response,
			"currency": code,
			"network":  nil,
			"address":  address,
			"tag":      nil,
		}
		return nil

	}()
	return ch
}

/**
 * @method
 * @name yobit#withdraw
 * @see https://yobit.net/en/api
 * @description make a withdrawal
 * @param {string} code unified currency code
 * @param {float} amount the amount to withdraw
 * @param {string} address the address to withdraw to
 * @param {string} tag
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *yobit) Withdraw(code interface{}, amount interface{}, address interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		tag := GetArg(optionalArgs, 0, nil)
		_ = tag
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		tagparamsVariable := this.HandleWithdrawTagAndParams(tag, params)
		tag = GetValue(tagparamsVariable, 0)
		params = GetValue(tagparamsVariable, 1)
		this.CheckAddress(address)

		retRes13398 := (<-this.LoadMarkets())
		PanicOnError(retRes13398)
		var currency interface{} = this.Currency(code)
		var request interface{} = map[string]interface{}{
			"coinName": GetValue(currency, "id"),
			"amount":   amount,
			"address":  address,
		}
		// no docs on the tag, yet...
		if IsTrue(!IsEqual(tag, nil)) {
			panic(ExchangeError(Add(this.Id, " withdraw() does not support the tag argument yet due to a lack of docs on withdrawing with tag/memo on behalf of the exchange.")))
		}

		response := (<-this.PrivatePostWithdrawCoinsToAddress(this.Extend(request, params)))
		PanicOnError(response)

		ch <- map[string]interface{}{
			"info":        response,
			"id":          nil,
			"txid":        nil,
			"type":        nil,
			"currency":    nil,
			"network":     nil,
			"amount":      nil,
			"status":      nil,
			"timestamp":   nil,
			"datetime":    nil,
			"address":     nil,
			"addressFrom": nil,
			"addressTo":   nil,
			"tag":         nil,
			"tagFrom":     nil,
			"tagTo":       nil,
			"updated":     nil,
			"comment":     nil,
			"fee": map[string]interface{}{
				"currency": nil,
				"cost":     nil,
				"rate":     nil,
			},
		}
		return nil

	}()
	return ch
}
func (this *yobit) Sign(path interface{}, optionalArgs ...interface{}) interface{} {
	api := GetArg(optionalArgs, 0, "public")
	_ = api
	method := GetArg(optionalArgs, 1, "GET")
	_ = method
	params := GetArg(optionalArgs, 2, map[string]interface{}{})
	_ = params
	headers := GetArg(optionalArgs, 3, nil)
	_ = headers
	body := GetArg(optionalArgs, 4, nil)
	_ = body
	var url interface{} = GetValue(GetValue(this.Urls, "api"), api)
	var query interface{} = this.Omit(params, this.ExtractParams(path))
	if IsTrue(IsEqual(api, "private")) {
		this.CheckRequiredCredentials()
		var nonce interface{} = this.Nonce()
		body = this.Urlencode(this.Extend(map[string]interface{}{
			"nonce":  nonce,
			"method": path,
		}, query))
		var signature interface{} = this.Hmac(this.Encode(body), this.Encode(this.Secret), sha512)
		headers = map[string]interface{}{
			"Content-Type": "application/x-www-form-urlencoded",
			"Key":          this.ApiKey,
			"Sign":         signature,
		}
	} else if IsTrue(IsEqual(api, "public")) {
		url = Add(url, Add(Add(Add("/", this.Version), "/"), this.ImplodeParams(path, params)))
		if IsTrue(GetArrayLength(ObjectKeys(query))) {
			url = Add(url, Add("?", this.Urlencode(query)))
		}
	} else {
		url = Add(url, Add("/", this.ImplodeParams(path, params)))
		if IsTrue(IsEqual(method, "GET")) {
			if IsTrue(GetArrayLength(ObjectKeys(query))) {
				url = Add(url, Add("?", this.Urlencode(query)))
			}
		} else {
			if IsTrue(GetArrayLength(ObjectKeys(query))) {
				body = this.Json(query)
				headers = map[string]interface{}{
					"Content-Type": "application/json",
				}
			}
		}
	}
	return map[string]interface{}{
		"url":     url,
		"method":  method,
		"body":    body,
		"headers": headers,
	}
}
func (this *yobit) HandleErrors(httpCode interface{}, reason interface{}, url interface{}, method interface{}, headers interface{}, body interface{}, response interface{}, requestHeaders interface{}, requestBody interface{}) interface{} {
	if IsTrue(IsEqual(response, nil)) {
		return nil // fallback to default error handler
	}
	if IsTrue(InOp(response, "success")) {
		//
		// 1 - Liqui only returns the integer 'success' key from their private API
		//
		//     { "success": 1, ... } httpCode === 200
		//     { "success": 0, ... } httpCode === 200
		//
		// 2 - However, exchanges derived from Liqui, can return non-integers
		//
		//     It can be a numeric string
		//     { "sucesss": "1", ... }
		//     { "sucesss": "0", ... }, httpCode >= 200 (can be 403, 502, etc)
		//
		//     Or just a string
		//     { "success": "true", ... }
		//     { "success": "false", ... }, httpCode >= 200
		//
		//     Or a boolean
		//     { "success": true, ... }
		//     { "success": false, ... }, httpCode >= 200
		//
		// 3 - Oversimplified, Python PEP8 forbids comparison operator (===) of different types
		//
		// 4 - We do not want to copy-paste and duplicate the code of this handler to other exchanges derived from Liqui
		//
		// To cover points 1, 2, 3 and 4 combined this handler should work like this:
		//
		var success interface{} = this.SafeValue(response, "success") // don't replace with safeBool here
		if IsTrue(IsString(success)) {
			if IsTrue(IsTrue((IsEqual(success, "true"))) || IsTrue((IsEqual(success, "1")))) {
				success = true
			} else {
				success = false
			}
		}
		if !IsTrue(success) {
			var code interface{} = this.SafeString(response, "code")
			var message interface{} = this.SafeString(response, "error")
			var feedback interface{} = Add(Add(this.Id, " "), body)
			this.ThrowExactlyMatchedException(GetValue(this.Exceptions, "exact"), code, feedback)
			this.ThrowExactlyMatchedException(GetValue(this.Exceptions, "exact"), message, feedback)
			this.ThrowBroadlyMatchedException(GetValue(this.Exceptions, "broad"), message, feedback)
			panic(ExchangeError(feedback))
		}
	}
	return nil
}

func (this *yobit) Init(userConfig map[string]interface{}) {
	this.Exchange = Exchange{}
	this.Exchange.DerivedExchange = this
	this.Exchange.InitParent(userConfig, this.Describe().(map[string]interface{}), this)
}
