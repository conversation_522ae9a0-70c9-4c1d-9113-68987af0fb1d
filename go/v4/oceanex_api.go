// -------------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -------------------------------------------------------------------------------

package ccxt

func (this *oceanex) PublicGetMarkets(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicGetMarkets", args...)
}

func (this *oceanex) PublicGetTickersPair(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicGetTickersPair", args...)
}

func (this *oceanex) PublicGetTickersMulti(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicGetTickersMulti", args...)
}

func (this *oceanex) PublicGetOrderBook(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicGetOrderBook", args...)
}

func (this *oceanex) PublicGetOrderBookMulti(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicGetOrderBookMulti", args...)
}

func (this *oceanex) PublicGetFeesTrading(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicGetFeesTrading", args...)
}

func (this *oceanex) PublicGetTrades(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicGetTrades", args...)
}

func (this *oceanex) PublicGetTimestamp(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicGetTimestamp", args...)
}

func (this *oceanex) PublicPostK(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("publicPostK", args...)
}

func (this *oceanex) PrivateGetKey(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privateGetKey", args...)
}

func (this *oceanex) PrivateGetMembersMe(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privateGetMembersMe", args...)
}

func (this *oceanex) PrivateGetOrders(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privateGetOrders", args...)
}

func (this *oceanex) PrivateGetOrdersFilter(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privateGetOrdersFilter", args...)
}

func (this *oceanex) PrivatePostOrders(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostOrders", args...)
}

func (this *oceanex) PrivatePostOrdersMulti(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostOrdersMulti", args...)
}

func (this *oceanex) PrivatePostOrderDelete(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostOrderDelete", args...)
}

func (this *oceanex) PrivatePostOrderDeleteMulti(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostOrderDeleteMulti", args...)
}

func (this *oceanex) PrivatePostOrdersClear(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostOrdersClear", args...)
}

func (this *oceanex) PrivatePostWithdrawsSpecialNew(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostWithdrawsSpecialNew", args...)
}

func (this *oceanex) PrivatePostDepositAddress(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostDepositAddress", args...)
}

func (this *oceanex) PrivatePostDepositAddresses(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostDepositAddresses", args...)
}

func (this *oceanex) PrivatePostDepositHistory(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostDepositHistory", args...)
}

func (this *oceanex) PrivatePostWithdrawHistory(args ...interface{}) <-chan interface{} {
	return this.callEndpointAsync("privatePostWithdrawHistory", args...)
}
