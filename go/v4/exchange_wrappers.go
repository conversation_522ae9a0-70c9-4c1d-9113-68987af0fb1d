package ccxt

type ExchangeTyped struct {
	*Exchange
}

func NewExchangeTyped(exchangePointer *Exchange) *ExchangeTyped {
	return &ExchangeTyped{
		Exchange: exchangePointer,
	}
}

func (this *ExchangeTyped) LoadMarkets(params ...interface{}) (map[string]MarketInterface, error) {
	res := <-this.Exchange.LoadMarkets(params...)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewMarketsMap(res), nil
}

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

func (this *ExchangeTyped) FetchCurrencies(params ...interface{}) (Currencies, error) {
	res := <-this.Exchange.FetchCurrencies(params...)
	if Is<PERSON>rror(res) {
		return Currencies{}, CreateReturnError(res)
	}
	return NewCurrencies(res), nil
}
func (this *ExchangeTyped) FetchMarkets(params ...interface{}) ([]MarketInterface, error) {
	res := <-this.Exchange.FetchMarkets(params...)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewMarketInterfaceArray(res), nil
}
func (this *ExchangeTyped) FetchAccounts(params ...interface{}) ([]Account, error) {
	res := <-this.Exchange.FetchAccounts(params...)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewAccountArray(res), nil
}
func (this *ExchangeTyped) FetchTrades(symbol string, options ...FetchTradesOptions) ([]Trade, error) {

	opts := FetchTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}
func (this *ExchangeTyped) FetchDepositAddresses(options ...FetchDepositAddressesOptions) ([]DepositAddress, error) {

	opts := FetchDepositAddressesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var codes interface{} = nil
	if opts.Codes != nil {
		codes = *opts.Codes
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchDepositAddresses(codes, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewDepositAddressArray(res), nil
}
func (this *ExchangeTyped) FetchOrderBook(symbol string, options ...FetchOrderBookOptions) (OrderBook, error) {

	opts := FetchOrderBookOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOrderBook(symbol, limit, params)
	if IsError(res) {
		return OrderBook{}, CreateReturnError(res)
	}
	return NewOrderBook(res), nil
}
func (this *ExchangeTyped) FetchMarginMode(symbol string, options ...FetchMarginModeOptions) (MarginMode, error) {

	opts := FetchMarginModeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMarginMode(symbol, params)
	if IsError(res) {
		return MarginMode{}, CreateReturnError(res)
	}
	return NewMarginMode(res), nil
}
func (this *ExchangeTyped) FetchMarginModes(options ...FetchMarginModesOptions) (MarginModes, error) {

	opts := FetchMarginModesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMarginModes(symbols, params)
	if IsError(res) {
		return MarginModes{}, CreateReturnError(res)
	}
	return NewMarginModes(res), nil
}
func (this *ExchangeTyped) FetchRestOrderBookSafe(symbol interface{}, options ...FetchRestOrderBookSafeOptions) (OrderBook, error) {

	opts := FetchRestOrderBookSafeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchRestOrderBookSafe(symbol, limit, params)
	if IsError(res) {
		return OrderBook{}, CreateReturnError(res)
	}
	return NewOrderBook(res), nil
}
func (this *ExchangeTyped) FetchTime(params ...interface{}) (int64, error) {
	res := <-this.Exchange.FetchTime(params...)
	if IsError(res) {
		return -1, CreateReturnError(res)
	}
	return (res).(int64), nil
}
func (this *ExchangeTyped) FetchTradingLimits(options ...FetchTradingLimitsOptions) (map[string]interface{}, error) {

	opts := FetchTradingLimitsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTradingLimits(symbols, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchCrossBorrowRates(params ...interface{}) (CrossBorrowRates, error) {
	res := <-this.Exchange.FetchCrossBorrowRates(params...)
	if IsError(res) {
		return CrossBorrowRates{}, CreateReturnError(res)
	}
	return NewCrossBorrowRates(res), nil
}
func (this *ExchangeTyped) FetchIsolatedBorrowRates(params ...interface{}) (IsolatedBorrowRates, error) {
	res := <-this.Exchange.FetchIsolatedBorrowRates(params...)
	if IsError(res) {
		return IsolatedBorrowRates{}, CreateReturnError(res)
	}
	return NewIsolatedBorrowRates(res), nil
}
func (this *ExchangeTyped) FetchLeverageTiers(options ...FetchLeverageTiersOptions) (LeverageTiers, error) {

	opts := FetchLeverageTiersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLeverageTiers(symbols, params)
	if IsError(res) {
		return LeverageTiers{}, CreateReturnError(res)
	}
	return NewLeverageTiers(res), nil
}
func (this *ExchangeTyped) FetchFundingRates(options ...FetchFundingRatesOptions) (FundingRates, error) {

	opts := FetchFundingRatesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchFundingRates(symbols, params)
	if IsError(res) {
		return FundingRates{}, CreateReturnError(res)
	}
	return NewFundingRates(res), nil
}
func (this *ExchangeTyped) FetchFundingIntervals(options ...FetchFundingIntervalsOptions) (FundingRates, error) {

	opts := FetchFundingIntervalsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchFundingIntervals(symbols, params)
	if IsError(res) {
		return FundingRates{}, CreateReturnError(res)
	}
	return NewFundingRates(res), nil
}
func (this *ExchangeTyped) Transfer(code string, amount float64, fromAccount string, toAccount string, options ...TransferOptions) (TransferEntry, error) {

	opts := TransferOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.Transfer(code, amount, fromAccount, toAccount, params)
	if IsError(res) {
		return TransferEntry{}, CreateReturnError(res)
	}
	return NewTransferEntry(res), nil
}
func (this *ExchangeTyped) Withdraw(code string, amount float64, address string, options ...WithdrawOptions) (Transaction, error) {

	opts := WithdrawOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var tag interface{} = nil
	if opts.Tag != nil {
		tag = *opts.Tag
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.Withdraw(code, amount, address, tag, params)
	if IsError(res) {
		return Transaction{}, CreateReturnError(res)
	}
	return NewTransaction(res), nil
}
func (this *ExchangeTyped) CreateDepositAddress(code string, options ...CreateDepositAddressOptions) (DepositAddress, error) {

	opts := CreateDepositAddressOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateDepositAddress(code, params)
	if IsError(res) {
		return DepositAddress{}, CreateReturnError(res)
	}
	return NewDepositAddress(res), nil
}
func (this *ExchangeTyped) SetLeverage(leverage int64, options ...SetLeverageOptions) (map[string]interface{}, error) {

	opts := SetLeverageOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.SetLeverage(leverage, symbol, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchLeverage(symbol string, options ...FetchLeverageOptions) (Leverage, error) {

	opts := FetchLeverageOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLeverage(symbol, params)
	if IsError(res) {
		return Leverage{}, CreateReturnError(res)
	}
	return NewLeverage(res), nil
}
func (this *ExchangeTyped) FetchLeverages(options ...FetchLeveragesOptions) (Leverages, error) {

	opts := FetchLeveragesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLeverages(symbols, params)
	if IsError(res) {
		return Leverages{}, CreateReturnError(res)
	}
	return NewLeverages(res), nil
}
func (this *ExchangeTyped) SetPositionMode(hedged bool, options ...SetPositionModeOptions) (map[string]interface{}, error) {

	opts := SetPositionModeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.SetPositionMode(hedged, symbol, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) SetMargin(symbol string, amount float64, options ...SetMarginOptions) (MarginModification, error) {

	opts := SetMarginOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.SetMargin(symbol, amount, params)
	if IsError(res) {
		return MarginModification{}, CreateReturnError(res)
	}
	return NewMarginModification(res), nil
}
func (this *ExchangeTyped) FetchLongShortRatio(symbol string, options ...FetchLongShortRatioOptions) (LongShortRatio, error) {

	opts := FetchLongShortRatioOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLongShortRatio(symbol, timeframe, params)
	if IsError(res) {
		return LongShortRatio{}, CreateReturnError(res)
	}
	return NewLongShortRatio(res), nil
}
func (this *ExchangeTyped) FetchLongShortRatioHistory(options ...FetchLongShortRatioHistoryOptions) ([]LongShortRatio, error) {

	opts := FetchLongShortRatioHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLongShortRatioHistory(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewLongShortRatioArray(res), nil
}
func (this *ExchangeTyped) FetchMarginAdjustmentHistory(options ...FetchMarginAdjustmentHistoryOptions) ([]MarginModification, error) {

	opts := FetchMarginAdjustmentHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var typeVar interface{} = nil
	if opts.Type != nil {
		typeVar = *opts.Type
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMarginAdjustmentHistory(symbol, typeVar, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewMarginModificationArray(res), nil
}
func (this *ExchangeTyped) SetMarginMode(marginMode string, options ...SetMarginModeOptions) (map[string]interface{}, error) {

	opts := SetMarginModeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.SetMarginMode(marginMode, symbol, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchDepositAddressesByNetwork(code string, options ...FetchDepositAddressesByNetworkOptions) ([]DepositAddress, error) {

	opts := FetchDepositAddressesByNetworkOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchDepositAddressesByNetwork(code, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewDepositAddressArray(res), nil
}
func (this *ExchangeTyped) FetchOpenInterestHistory(symbol string, options ...FetchOpenInterestHistoryOptions) ([]OpenInterest, error) {

	opts := FetchOpenInterestHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOpenInterestHistory(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOpenInterestArray(res), nil
}
func (this *ExchangeTyped) FetchOpenInterest(symbol string, options ...FetchOpenInterestOptions) (OpenInterest, error) {

	opts := FetchOpenInterestOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOpenInterest(symbol, params)
	if IsError(res) {
		return OpenInterest{}, CreateReturnError(res)
	}
	return NewOpenInterest(res), nil
}
func (this *ExchangeTyped) FetchOpenInterests(options ...FetchOpenInterestsOptions) (OpenInterests, error) {

	opts := FetchOpenInterestsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOpenInterests(symbols, params)
	if IsError(res) {
		return OpenInterests{}, CreateReturnError(res)
	}
	return NewOpenInterests(res), nil
}
func (this *ExchangeTyped) FetchPaymentMethods(params ...interface{}) (map[string]interface{}, error) {
	res := <-this.Exchange.FetchPaymentMethods(params...)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchBorrowRate(code string, amount float64, options ...FetchBorrowRateOptions) (map[string]interface{}, error) {

	opts := FetchBorrowRateOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchBorrowRate(code, amount, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchOHLCV(symbol string, options ...FetchOHLCVOptions) ([]OHLCV, error) {

	opts := FetchOHLCVOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOHLCV(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOHLCVArray(res), nil
}
func (this *ExchangeTyped) FetchWebEndpoint(method interface{}, endpointMethod interface{}, returnAsJson interface{}, options ...FetchWebEndpointOptions) (map[string]interface{}, error) {

	opts := FetchWebEndpointOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var startRegex interface{} = nil
	if opts.StartRegex != nil {
		startRegex = *opts.StartRegex
	}

	var endRegex interface{} = nil
	if opts.EndRegex != nil {
		endRegex = *opts.EndRegex
	}
	res := <-this.Exchange.FetchWebEndpoint(method, endpointMethod, returnAsJson, startRegex, endRegex)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchL2OrderBook(symbol string, options ...FetchL2OrderBookOptions) (map[string]interface{}, error) {

	opts := FetchL2OrderBookOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchL2OrderBook(symbol, limit, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) Fetch2(path interface{}, options ...Fetch2Options) (map[string]interface{}, error) {

	opts := Fetch2OptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var api interface{} = nil
	if opts.Api != nil {
		api = *opts.Api
	}

	var method interface{} = nil
	if opts.Method != nil {
		method = *opts.Method
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}

	var headers interface{} = nil
	if opts.Headers != nil {
		headers = *opts.Headers
	}

	var body interface{} = nil
	if opts.Body != nil {
		body = *opts.Body
	}

	var config interface{} = nil
	if opts.Config != nil {
		config = *opts.Config
	}
	res := <-this.Exchange.Fetch2(path, api, method, params, headers, body, config)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) EditLimitBuyOrder(id string, symbol string, amount float64, options ...EditLimitBuyOrderOptions) (Order, error) {

	opts := EditLimitBuyOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.EditLimitBuyOrder(id, symbol, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) EditLimitSellOrder(id string, symbol string, amount float64, options ...EditLimitSellOrderOptions) (Order, error) {

	opts := EditLimitSellOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.EditLimitSellOrder(id, symbol, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) EditLimitOrder(id string, symbol string, side string, amount float64, options ...EditLimitOrderOptions) (Order, error) {

	opts := EditLimitOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.EditLimitOrder(id, symbol, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) EditOrder(id string, symbol string, typeVar string, side string, options ...EditOrderOptions) (Order, error) {

	opts := EditOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var amount interface{} = nil
	if opts.Amount != nil {
		amount = *opts.Amount
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.EditOrder(id, symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) FetchPosition(symbol string, options ...FetchPositionOptions) (Position, error) {

	opts := FetchPositionOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPosition(symbol, params)
	if IsError(res) {
		return Position{}, CreateReturnError(res)
	}
	return NewPosition(res), nil
}
func (this *ExchangeTyped) FetchPositionsForSymbol(symbol string, options ...FetchPositionsForSymbolOptions) ([]Position, error) {

	opts := FetchPositionsForSymbolOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPositionsForSymbol(symbol, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewPositionArray(res), nil
}
func (this *ExchangeTyped) FetchPositions(options ...FetchPositionsOptions) ([]Position, error) {

	opts := FetchPositionsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPositions(symbols, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewPositionArray(res), nil
}
func (this *ExchangeTyped) FetchPositionsRisk(options ...FetchPositionsRiskOptions) ([]Position, error) {

	opts := FetchPositionsRiskOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPositionsRisk(symbols, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewPositionArray(res), nil
}
func (this *ExchangeTyped) FetchBidsAsks(options ...FetchBidsAsksOptions) (Tickers, error) {

	opts := FetchBidsAsksOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchBidsAsks(symbols, params)
	if IsError(res) {
		return Tickers{}, CreateReturnError(res)
	}
	return NewTickers(res), nil
}
func (this *ExchangeTyped) FetchBorrowInterest(options ...FetchBorrowInterestOptions) ([]BorrowInterest, error) {

	opts := FetchBorrowInterestOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchBorrowInterest(code, symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewBorrowInterestArray(res), nil
}
func (this *ExchangeTyped) FetchLedger(options ...FetchLedgerOptions) ([]LedgerEntry, error) {

	opts := FetchLedgerOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLedger(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewLedgerEntryArray(res), nil
}
func (this *ExchangeTyped) FetchLedgerEntry(id string, options ...FetchLedgerEntryOptions) (LedgerEntry, error) {

	opts := FetchLedgerEntryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLedgerEntry(id, code, params)
	if IsError(res) {
		return LedgerEntry{}, CreateReturnError(res)
	}
	return NewLedgerEntry(res), nil
}
func (this *ExchangeTyped) FetchBalance(params ...interface{}) (Balances, error) {
	res := <-this.Exchange.FetchBalance(params...)
	if IsError(res) {
		return Balances{}, CreateReturnError(res)
	}
	return NewBalances(res), nil
}
func (this *ExchangeTyped) FetchPartialBalance(part interface{}, options ...FetchPartialBalanceOptions) (Balance, error) {

	opts := FetchPartialBalanceOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPartialBalance(part, params)
	if IsError(res) {
		return Balance{}, CreateReturnError(res)
	}
	return NewBalance(res), nil
}
func (this *ExchangeTyped) FetchFreeBalance(params ...interface{}) (Balance, error) {
	res := <-this.Exchange.FetchFreeBalance(params...)
	if IsError(res) {
		return Balance{}, CreateReturnError(res)
	}
	return NewBalance(res), nil
}
func (this *ExchangeTyped) FetchUsedBalance(params ...interface{}) (Balance, error) {
	res := <-this.Exchange.FetchUsedBalance(params...)
	if IsError(res) {
		return Balance{}, CreateReturnError(res)
	}
	return NewBalance(res), nil
}
func (this *ExchangeTyped) FetchTotalBalance(params ...interface{}) (Balance, error) {
	res := <-this.Exchange.FetchTotalBalance(params...)
	if IsError(res) {
		return Balance{}, CreateReturnError(res)
	}
	return NewBalance(res), nil
}
func (this *ExchangeTyped) FetchStatus(params ...interface{}) (map[string]interface{}, error) {
	res := <-this.Exchange.FetchStatus(params...)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchTransactionFee(code string, options ...FetchTransactionFeeOptions) (map[string]interface{}, error) {

	opts := FetchTransactionFeeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTransactionFee(code, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchTransactionFees(options ...FetchTransactionFeesOptions) (map[string]interface{}, error) {

	opts := FetchTransactionFeesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var codes interface{} = nil
	if opts.Codes != nil {
		codes = *opts.Codes
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTransactionFees(codes, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchDepositWithdrawFees(options ...FetchDepositWithdrawFeesOptions) (map[string]interface{}, error) {

	opts := FetchDepositWithdrawFeesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var codes interface{} = nil
	if opts.Codes != nil {
		codes = *opts.Codes
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchDepositWithdrawFees(codes, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return (res).(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchDepositWithdrawFee(code string, options ...FetchDepositWithdrawFeeOptions) (map[string]interface{}, error) {

	opts := FetchDepositWithdrawFeeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchDepositWithdrawFee(code, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return (res).(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchCrossBorrowRate(code string, options ...FetchCrossBorrowRateOptions) (CrossBorrowRate, error) {

	opts := FetchCrossBorrowRateOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchCrossBorrowRate(code, params)
	if IsError(res) {
		return CrossBorrowRate{}, CreateReturnError(res)
	}
	return NewCrossBorrowRate(res), nil
}
func (this *ExchangeTyped) FetchIsolatedBorrowRate(symbol string, options ...FetchIsolatedBorrowRateOptions) (IsolatedBorrowRate, error) {

	opts := FetchIsolatedBorrowRateOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchIsolatedBorrowRate(symbol, params)
	if IsError(res) {
		return IsolatedBorrowRate{}, CreateReturnError(res)
	}
	return NewIsolatedBorrowRate(res), nil
}
func (this *ExchangeTyped) FetchTicker(symbol string, options ...FetchTickerOptions) (Ticker, error) {

	opts := FetchTickerOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTicker(symbol, params)
	if IsError(res) {
		return Ticker{}, CreateReturnError(res)
	}
	return NewTicker(res), nil
}
func (this *ExchangeTyped) FetchMarkPrice(symbol string, options ...FetchMarkPriceOptions) (Ticker, error) {

	opts := FetchMarkPriceOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMarkPrice(symbol, params)
	if IsError(res) {
		return Ticker{}, CreateReturnError(res)
	}
	return NewTicker(res), nil
}
func (this *ExchangeTyped) FetchTickers(options ...FetchTickersOptions) (Tickers, error) {

	opts := FetchTickersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTickers(symbols, params)
	if IsError(res) {
		return Tickers{}, CreateReturnError(res)
	}
	return NewTickers(res), nil
}
func (this *ExchangeTyped) FetchMarkPrices(options ...FetchMarkPricesOptions) (Tickers, error) {

	opts := FetchMarkPricesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMarkPrices(symbols, params)
	if IsError(res) {
		return Tickers{}, CreateReturnError(res)
	}
	return NewTickers(res), nil
}
func (this *ExchangeTyped) FetchOrderBooks(options ...FetchOrderBooksOptions) (OrderBooks, error) {

	opts := FetchOrderBooksOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOrderBooks(symbols, limit, params)
	if IsError(res) {
		return OrderBooks{}, CreateReturnError(res)
	}
	return NewOrderBooks(res), nil
}
func (this *ExchangeTyped) FetchOrder(id string, options ...FetchOrderOptions) (Order, error) {

	opts := FetchOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) FetchOrderStatus(id string, options ...FetchOrderStatusOptions) (string, error) {

	opts := FetchOrderStatusOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOrderStatus(id, symbol, params)
	if IsError(res) {
		return "", CreateReturnError(res)
	}
	return res.(string), nil
}
func (this *ExchangeTyped) FetchUnifiedOrder(order interface{}, options ...FetchUnifiedOrderOptions) (Order, error) {

	opts := FetchUnifiedOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchUnifiedOrder(order, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateOrder(symbol string, typeVar string, side string, amount float64, options ...CreateOrderOptions) (Order, error) {

	opts := CreateOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateOrder(symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateConvertTrade(id string, fromCode string, toCode string, options ...CreateConvertTradeOptions) (Conversion, error) {

	opts := CreateConvertTradeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var amount interface{} = nil
	if opts.Amount != nil {
		amount = *opts.Amount
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateConvertTrade(id, fromCode, toCode, amount, params)
	if IsError(res) {
		return Conversion{}, CreateReturnError(res)
	}
	return NewConversion(res), nil
}
func (this *ExchangeTyped) FetchConvertTrade(id string, options ...FetchConvertTradeOptions) (Conversion, error) {

	opts := FetchConvertTradeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchConvertTrade(id, code, params)
	if IsError(res) {
		return Conversion{}, CreateReturnError(res)
	}
	return NewConversion(res), nil
}
func (this *ExchangeTyped) FetchConvertTradeHistory(options ...FetchConvertTradeHistoryOptions) ([]Conversion, error) {

	opts := FetchConvertTradeHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchConvertTradeHistory(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewConversionArray(res), nil
}
func (this *ExchangeTyped) FetchPositionMode(options ...FetchPositionModeOptions) (map[string]interface{}, error) {

	opts := FetchPositionModeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPositionMode(symbol, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) CreateTrailingAmountOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingAmountOrderOptions) (Order, error) {

	opts := CreateTrailingAmountOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var trailingAmount interface{} = nil
	if opts.TrailingAmount != nil {
		trailingAmount = *opts.TrailingAmount
	}

	var trailingTriggerPrice interface{} = nil
	if opts.TrailingTriggerPrice != nil {
		trailingTriggerPrice = *opts.TrailingTriggerPrice
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateTrailingAmountOrder(symbol, typeVar, side, amount, price, trailingAmount, trailingTriggerPrice, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateTrailingPercentOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingPercentOrderOptions) (Order, error) {

	opts := CreateTrailingPercentOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var trailingPercent interface{} = nil
	if opts.TrailingPercent != nil {
		trailingPercent = *opts.TrailingPercent
	}

	var trailingTriggerPrice interface{} = nil
	if opts.TrailingTriggerPrice != nil {
		trailingTriggerPrice = *opts.TrailingTriggerPrice
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateTrailingPercentOrder(symbol, typeVar, side, amount, price, trailingPercent, trailingTriggerPrice, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateMarketOrderWithCost(symbol string, side string, cost float64, options ...CreateMarketOrderWithCostOptions) (Order, error) {

	opts := CreateMarketOrderWithCostOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateMarketOrderWithCost(symbol, side, cost, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateMarketBuyOrderWithCost(symbol string, cost float64, options ...CreateMarketBuyOrderWithCostOptions) (Order, error) {

	opts := CreateMarketBuyOrderWithCostOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateMarketBuyOrderWithCost(symbol, cost, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateMarketSellOrderWithCost(symbol string, cost float64, options ...CreateMarketSellOrderWithCostOptions) (Order, error) {

	opts := CreateMarketSellOrderWithCostOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateMarketSellOrderWithCost(symbol, cost, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateTriggerOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTriggerOrderOptions) (Order, error) {

	opts := CreateTriggerOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var triggerPrice interface{} = nil
	if opts.TriggerPrice != nil {
		triggerPrice = *opts.TriggerPrice
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateTriggerOrder(symbol, typeVar, side, amount, price, triggerPrice, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateStopLossOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopLossOrderOptions) (Order, error) {

	opts := CreateStopLossOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var stopLossPrice interface{} = nil
	if opts.StopLossPrice != nil {
		stopLossPrice = *opts.StopLossPrice
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateStopLossOrder(symbol, typeVar, side, amount, price, stopLossPrice, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateTakeProfitOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTakeProfitOrderOptions) (Order, error) {

	opts := CreateTakeProfitOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var takeProfitPrice interface{} = nil
	if opts.TakeProfitPrice != nil {
		takeProfitPrice = *opts.TakeProfitPrice
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateTakeProfitOrder(symbol, typeVar, side, amount, price, takeProfitPrice, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateOrderWithTakeProfitAndStopLoss(symbol string, typeVar string, side string, amount float64, options ...CreateOrderWithTakeProfitAndStopLossOptions) (Order, error) {

	opts := CreateOrderWithTakeProfitAndStopLossOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var takeProfit interface{} = nil
	if opts.TakeProfit != nil {
		takeProfit = *opts.TakeProfit
	}

	var stopLoss interface{} = nil
	if opts.StopLoss != nil {
		stopLoss = *opts.StopLoss
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateOrderWithTakeProfitAndStopLoss(symbol, typeVar, side, amount, price, takeProfit, stopLoss, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateOrders(orders []OrderRequest, options ...CreateOrdersOptions) ([]Order, error) {

	opts := CreateOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateOrders(ConvertOrderRequestListToArray(orders), params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *ExchangeTyped) EditOrders(orders []OrderRequest, options ...EditOrdersOptions) ([]Order, error) {

	opts := EditOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.EditOrders(orders, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *ExchangeTyped) CancelOrder(id string, options ...CancelOrderOptions) (Order, error) {

	opts := CancelOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CancelOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CancelAllOrders(options ...CancelAllOrdersOptions) ([]Order, error) {

	opts := CancelAllOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CancelAllOrders(symbol, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *ExchangeTyped) CancelAllOrdersAfter(timeout int64, options ...CancelAllOrdersAfterOptions) (map[string]interface{}, error) {

	opts := CancelAllOrdersAfterOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CancelAllOrdersAfter(timeout, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) CancelOrdersForSymbols(orders []CancellationRequest, options ...CancelOrdersForSymbolsOptions) ([]Order, error) {

	opts := CancelOrdersForSymbolsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CancelOrdersForSymbols(orders, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *ExchangeTyped) CancelUnifiedOrder(order interface{}, options ...CancelUnifiedOrderOptions) (map[string]interface{}, error) {

	opts := CancelUnifiedOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CancelUnifiedOrder(order, params)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchOrders(options ...FetchOrdersOptions) ([]Order, error) {

	opts := FetchOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *ExchangeTyped) FetchOrderTrades(id string, options ...FetchOrderTradesOptions) ([]Trade, error) {

	opts := FetchOrderTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOrderTrades(id, symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}
func (this *ExchangeTyped) FetchOpenOrders(options ...FetchOpenOrdersOptions) ([]Order, error) {

	opts := FetchOpenOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOpenOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *ExchangeTyped) FetchClosedOrders(options ...FetchClosedOrdersOptions) ([]Order, error) {

	opts := FetchClosedOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchClosedOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *ExchangeTyped) FetchCanceledAndClosedOrders(options ...FetchCanceledAndClosedOrdersOptions) ([]Order, error) {

	opts := FetchCanceledAndClosedOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchCanceledAndClosedOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *ExchangeTyped) FetchMyTrades(options ...FetchMyTradesOptions) ([]Trade, error) {

	opts := FetchMyTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMyTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}
func (this *ExchangeTyped) FetchMyLiquidations(options ...FetchMyLiquidationsOptions) ([]Liquidation, error) {

	opts := FetchMyLiquidationsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMyLiquidations(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewLiquidationArray(res), nil
}
func (this *ExchangeTyped) FetchLiquidations(symbol string, options ...FetchLiquidationsOptions) ([]Liquidation, error) {

	opts := FetchLiquidationsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLiquidations(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewLiquidationArray(res), nil
}
func (this *ExchangeTyped) FetchGreeks(symbol string, options ...FetchGreeksOptions) (Greeks, error) {

	opts := FetchGreeksOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchGreeks(symbol, params)
	if IsError(res) {
		return Greeks{}, CreateReturnError(res)
	}
	return NewGreeks(res), nil
}
func (this *ExchangeTyped) FetchAllGreeks(options ...FetchAllGreeksOptions) ([]Greeks, error) {

	opts := FetchAllGreeksOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchAllGreeks(symbols, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewGreeksArray(res), nil
}
func (this *ExchangeTyped) FetchOptionChain(code string, options ...FetchOptionChainOptions) (OptionChain, error) {

	opts := FetchOptionChainOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOptionChain(code, params)
	if IsError(res) {
		return OptionChain{}, CreateReturnError(res)
	}
	return NewOptionChain(res), nil
}
func (this *ExchangeTyped) FetchOption(symbol string, options ...FetchOptionOptions) (Option, error) {

	opts := FetchOptionOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchOption(symbol, params)
	if IsError(res) {
		return Option{}, CreateReturnError(res)
	}
	return NewOption(res), nil
}
func (this *ExchangeTyped) FetchConvertQuote(fromCode string, toCode string, options ...FetchConvertQuoteOptions) (Conversion, error) {

	opts := FetchConvertQuoteOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var amount interface{} = nil
	if opts.Amount != nil {
		amount = *opts.Amount
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchConvertQuote(fromCode, toCode, amount, params)
	if IsError(res) {
		return Conversion{}, CreateReturnError(res)
	}
	return NewConversion(res), nil
}
func (this *ExchangeTyped) FetchDepositsWithdrawals(options ...FetchDepositsWithdrawalsOptions) ([]Transaction, error) {

	opts := FetchDepositsWithdrawalsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchDepositsWithdrawals(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}
func (this *ExchangeTyped) FetchDeposits(options ...FetchDepositsOptions) ([]Transaction, error) {

	opts := FetchDepositsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchDeposits(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}
func (this *ExchangeTyped) FetchWithdrawals(options ...FetchWithdrawalsOptions) ([]Transaction, error) {

	opts := FetchWithdrawalsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchWithdrawals(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}
func (this *ExchangeTyped) FetchFundingRateHistory(options ...FetchFundingRateHistoryOptions) ([]FundingRateHistory, error) {

	opts := FetchFundingRateHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchFundingRateHistory(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewFundingRateHistoryArray(res), nil
}
func (this *ExchangeTyped) FetchFundingHistory(options ...FetchFundingHistoryOptions) ([]FundingHistory, error) {

	opts := FetchFundingHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchFundingHistory(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewFundingHistoryArray(res), nil
}
func (this *ExchangeTyped) FetchL3OrderBook(symbol string, options ...FetchL3OrderBookOptions) (OrderBook, error) {

	opts := FetchL3OrderBookOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchL3OrderBook(symbol, limit, params)
	if IsError(res) {
		return OrderBook{}, CreateReturnError(res)
	}
	return NewOrderBook(res), nil
}
func (this *ExchangeTyped) FetchDepositAddress(code string, options ...FetchDepositAddressOptions) (DepositAddress, error) {

	opts := FetchDepositAddressOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchDepositAddress(code, params)
	if IsError(res) {
		return DepositAddress{}, CreateReturnError(res)
	}
	return NewDepositAddress(res), nil
}
func (this *ExchangeTyped) CreateLimitOrder(symbol string, side string, amount float64, price float64, options ...CreateLimitOrderOptions) (Order, error) {

	opts := CreateLimitOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateLimitOrder(symbol, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateMarketOrder(symbol string, side string, amount float64, options ...CreateMarketOrderOptions) (Order, error) {

	opts := CreateMarketOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateMarketOrder(symbol, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateLimitBuyOrder(symbol string, amount float64, price float64, options ...CreateLimitBuyOrderOptions) (Order, error) {

	opts := CreateLimitBuyOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateLimitBuyOrder(symbol, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateLimitSellOrder(symbol string, amount float64, price float64, options ...CreateLimitSellOrderOptions) (Order, error) {

	opts := CreateLimitSellOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateLimitSellOrder(symbol, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateMarketBuyOrder(symbol string, amount float64, options ...CreateMarketBuyOrderOptions) (Order, error) {

	opts := CreateMarketBuyOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateMarketBuyOrder(symbol, amount, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateMarketSellOrder(symbol string, amount float64, options ...CreateMarketSellOrderOptions) (Order, error) {

	opts := CreateMarketSellOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateMarketSellOrder(symbol, amount, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) FetchMarketLeverageTiers(symbol string, options ...FetchMarketLeverageTiersOptions) ([]LeverageTier, error) {

	opts := FetchMarketLeverageTiersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMarketLeverageTiers(symbol, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewLeverageTierArray(res), nil
}
func (this *ExchangeTyped) CreatePostOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreatePostOnlyOrderOptions) (Order, error) {

	opts := CreatePostOnlyOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreatePostOnlyOrder(symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateReduceOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreateReduceOnlyOrderOptions) (Order, error) {

	opts := CreateReduceOnlyOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateReduceOnlyOrder(symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateStopOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopOrderOptions) (Order, error) {

	opts := CreateStopOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var triggerPrice interface{} = nil
	if opts.TriggerPrice != nil {
		triggerPrice = *opts.TriggerPrice
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateStopOrder(symbol, typeVar, side, amount, price, triggerPrice, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateStopLimitOrder(symbol string, side string, amount float64, price float64, triggerPrice float64, options ...CreateStopLimitOrderOptions) (Order, error) {

	opts := CreateStopLimitOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateStopLimitOrder(symbol, side, amount, price, triggerPrice, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) CreateStopMarketOrder(symbol string, side string, amount float64, triggerPrice float64, options ...CreateStopMarketOrderOptions) (Order, error) {

	opts := CreateStopMarketOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.CreateStopMarketOrder(symbol, side, amount, triggerPrice, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}
func (this *ExchangeTyped) FetchLastPrices(options ...FetchLastPricesOptions) (LastPrices, error) {

	opts := FetchLastPricesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchLastPrices(symbols, params)
	if IsError(res) {
		return LastPrices{}, CreateReturnError(res)
	}
	return NewLastPrices(res), nil
}
func (this *ExchangeTyped) FetchTradingFees(params ...interface{}) (TradingFees, error) {
	res := <-this.Exchange.FetchTradingFees(params...)
	if IsError(res) {
		return TradingFees{}, CreateReturnError(res)
	}
	return NewTradingFees(res), nil
}
func (this *ExchangeTyped) FetchTradingFee(symbol string, options ...FetchTradingFeeOptions) (TradingFeeInterface, error) {

	opts := FetchTradingFeeOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTradingFee(symbol, params)
	if IsError(res) {
		return TradingFeeInterface{}, CreateReturnError(res)
	}
	return NewTradingFeeInterface(res), nil
}
func (this *ExchangeTyped) FetchConvertCurrencies(params ...interface{}) (Currencies, error) {
	res := <-this.Exchange.FetchConvertCurrencies(params...)
	if IsError(res) {
		return Currencies{}, CreateReturnError(res)
	}
	return NewCurrencies(res), nil
}
func (this *ExchangeTyped) FetchFundingRate(symbol string, options ...FetchFundingRateOptions) (FundingRate, error) {

	opts := FetchFundingRateOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchFundingRate(symbol, params)
	if IsError(res) {
		return FundingRate{}, CreateReturnError(res)
	}
	return NewFundingRate(res), nil
}
func (this *ExchangeTyped) FetchFundingInterval(symbol string, options ...FetchFundingIntervalOptions) (FundingRate, error) {

	opts := FetchFundingIntervalOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchFundingInterval(symbol, params)
	if IsError(res) {
		return FundingRate{}, CreateReturnError(res)
	}
	return NewFundingRate(res), nil
}
func (this *ExchangeTyped) FetchMarkOHLCV(symbol string, options ...FetchMarkOHLCVOptions) ([]OHLCV, error) {

	opts := FetchMarkOHLCVOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchMarkOHLCV(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOHLCVArray(res), nil
}
func (this *ExchangeTyped) FetchIndexOHLCV(symbol string, options ...FetchIndexOHLCVOptions) ([]OHLCV, error) {

	opts := FetchIndexOHLCVOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchIndexOHLCV(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOHLCVArray(res), nil
}
func (this *ExchangeTyped) FetchPremiumIndexOHLCV(symbol string, options ...FetchPremiumIndexOHLCVOptions) ([]OHLCV, error) {

	opts := FetchPremiumIndexOHLCVOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPremiumIndexOHLCV(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOHLCVArray(res), nil
}
func (this *ExchangeTyped) FetchTransactions(options ...FetchTransactionsOptions) ([]Transaction, error) {

	opts := FetchTransactionsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTransactions(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}
func (this *ExchangeTyped) FetchPaginatedCallDynamic(method string, options ...FetchPaginatedCallDynamicOptions) (map[string]interface{}, error) {

	opts := FetchPaginatedCallDynamicOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}

	var maxEntriesPerRequest interface{} = nil
	if opts.MaxEntriesPerRequest != nil {
		maxEntriesPerRequest = *opts.MaxEntriesPerRequest
	}

	var removeRepeated interface{} = nil
	if opts.RemoveRepeated != nil {
		removeRepeated = *opts.RemoveRepeated
	}
	res := <-this.Exchange.FetchPaginatedCallDynamic(method, symbol, since, limit, params, maxEntriesPerRequest, removeRepeated)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchPaginatedCallDeterministic(method string, options ...FetchPaginatedCallDeterministicOptions) (map[string]interface{}, error) {

	opts := FetchPaginatedCallDeterministicOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}

	var maxEntriesPerRequest interface{} = nil
	if opts.MaxEntriesPerRequest != nil {
		maxEntriesPerRequest = *opts.MaxEntriesPerRequest
	}
	res := <-this.Exchange.FetchPaginatedCallDeterministic(method, symbol, since, limit, timeframe, params, maxEntriesPerRequest)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchPaginatedCallCursor(method string, options ...FetchPaginatedCallCursorOptions) (map[string]interface{}, error) {

	opts := FetchPaginatedCallCursorOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}

	var cursorReceived interface{} = nil
	if opts.CursorReceived != nil {
		cursorReceived = *opts.CursorReceived
	}

	var cursorSent interface{} = nil
	if opts.CursorSent != nil {
		cursorSent = *opts.CursorSent
	}

	var cursorIncrement interface{} = nil
	if opts.CursorIncrement != nil {
		cursorIncrement = *opts.CursorIncrement
	}

	var maxEntriesPerRequest interface{} = nil
	if opts.MaxEntriesPerRequest != nil {
		maxEntriesPerRequest = *opts.MaxEntriesPerRequest
	}
	res := <-this.Exchange.FetchPaginatedCallCursor(method, symbol, since, limit, params, cursorReceived, cursorSent, cursorIncrement, maxEntriesPerRequest)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchPaginatedCallIncremental(method string, options ...FetchPaginatedCallIncrementalOptions) (map[string]interface{}, error) {

	opts := FetchPaginatedCallIncrementalOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}

	var pageKey interface{} = nil
	if opts.PageKey != nil {
		pageKey = *opts.PageKey
	}

	var maxEntriesPerRequest interface{} = nil
	if opts.MaxEntriesPerRequest != nil {
		maxEntriesPerRequest = *opts.MaxEntriesPerRequest
	}
	res := <-this.Exchange.FetchPaginatedCallIncremental(method, symbol, since, limit, params, pageKey, maxEntriesPerRequest)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}
func (this *ExchangeTyped) FetchPositionHistory(symbol string, options ...FetchPositionHistoryOptions) ([]Position, error) {

	opts := FetchPositionHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPositionHistory(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewPositionArray(res), nil
}
func (this *ExchangeTyped) FetchPositionsHistory(options ...FetchPositionsHistoryOptions) ([]Position, error) {

	opts := FetchPositionsHistoryOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchPositionsHistory(symbols, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewPositionArray(res), nil
}
func (this *ExchangeTyped) FetchTransfer(id string, options ...FetchTransferOptions) (TransferEntry, error) {

	opts := FetchTransferOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTransfer(id, code, params)
	if IsError(res) {
		return TransferEntry{}, CreateReturnError(res)
	}
	return NewTransferEntry(res), nil
}
func (this *ExchangeTyped) FetchTransfers(options ...FetchTransfersOptions) ([]TransferEntry, error) {

	opts := FetchTransfersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Exchange.FetchTransfers(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransferEntryArray(res), nil
}

// missing typed methods from base
//nolint
