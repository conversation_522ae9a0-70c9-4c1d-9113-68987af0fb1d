package ccxt

type Foxbit struct {
	*foxbit
	Core          *foxbit
	exchangeTyped *ExchangeTyped
}

func NewFoxbit(userConfig map[string]interface{}) *Foxbit {
	p := &foxbit{}
	p.Init(userConfig)
	return &Foxbit{
		foxbit:        p,
		Core:          p,
		exchangeTyped: NewExchangeTyped(&p.Exchange),
	}
}

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

func (this *Foxbit) FetchCurrencies(params ...interface{}) (Currencies, error) {
	res := <-this.Core.FetchCurrencies(params...)
	if IsError(res) {
		return Currencies{}, CreateReturnError(res)
	}
	return NewCurrencies(res), nil
}

/**
 * @method
 * @name foxbit#fetchMarkets
 * @description Retrieves data on all markets for foxbit.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Market-Data/operation/MarketsController_index
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} an array of objects representing market data
 */
func (this *Foxbit) FetchMarkets(params ...interface{}) ([]MarketInterface, error) {
	res := <-this.Core.FetchMarkets(params...)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewMarketInterfaceArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchTicker
 * @description Get last 24 hours ticker information, in real-time, for given market.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Market-Data/operation/MarketsController_ticker
 * @param {string} symbol unified symbol of the market to fetch the ticker for
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *Foxbit) FetchTicker(symbol string, options ...FetchTickerOptions) (Ticker, error) {

	opts := FetchTickerOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTicker(symbol, params)
	if IsError(res) {
		return Ticker{}, CreateReturnError(res)
	}
	return NewTicker(res), nil
}

/**
 * @method
 * @name foxbit#fetchTickers
 * @description Retrieve the ticker data of all markets.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Market-Data/operation/MarketsController_tickers
 * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *Foxbit) FetchTickers(options ...FetchTickersOptions) (Tickers, error) {

	opts := FetchTickersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbols interface{} = nil
	if opts.Symbols != nil {
		symbols = *opts.Symbols
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTickers(symbols, params)
	if IsError(res) {
		return Tickers{}, CreateReturnError(res)
	}
	return NewTickers(res), nil
}

/**
 * @method
 * @name foxbit#fetchTradingFees
 * @description fetch the trading fees for multiple markets
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Member-Info/operation/MembersController_listTradingFees
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
 */
func (this *Foxbit) FetchTradingFees(params ...interface{}) (TradingFees, error) {
	res := <-this.Core.FetchTradingFees(params...)
	if IsError(res) {
		return TradingFees{}, CreateReturnError(res)
	}
	return NewTradingFees(res), nil
}

/**
 * @method
 * @name foxbit#fetchOrderBook
 * @description Exports a copy of the order book of a specific market.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Market-Data/operation/MarketsController_findOrderbook
 * @param {string} symbol unified symbol of the market to fetch the order book for
 * @param {int} [limit] the maximum amount of order book entries to return, the maximum is 100
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
 */
func (this *Foxbit) FetchOrderBook(symbol string, options ...FetchOrderBookOptions) (OrderBook, error) {

	opts := FetchOrderBookOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrderBook(symbol, limit, params)
	if IsError(res) {
		return OrderBook{}, CreateReturnError(res)
	}
	return NewOrderBook(res), nil
}

/**
 * @method
 * @name foxbit#fetchTrades
 * @description Retrieve the trades of a specific market.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Market-Data/operation/MarketsController_publicTrades
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch
 * @param {int} [limit] the maximum amount of trades to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *Foxbit) FetchTrades(symbol string, options ...FetchTradesOptions) ([]Trade, error) {

	opts := FetchTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchOHLCV
 * @description Fetch historical candlestick data containing the open, high, low, and close price, and the volume of a market.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Market-Data/operation/MarketsController_findCandlesticks
 * @param {string} symbol unified symbol of the market to fetch OHLCV data for
 * @param {string} timeframe the length of time each candle represents
 * @param {int} [since] timestamp in ms of the earliest candle to fetch
 * @param {int} [limit] the maximum amount of candles to fetch
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
 */
func (this *Foxbit) FetchOHLCV(symbol string, options ...FetchOHLCVOptions) ([]OHLCV, error) {

	opts := FetchOHLCVOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var timeframe interface{} = nil
	if opts.Timeframe != nil {
		timeframe = *opts.Timeframe
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOHLCV(symbol, timeframe, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOHLCVArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchBalance
 * @description Query for balance and get the amount of funds available for trading or funds locked in orders.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Account/operation/AccountsController_all
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
 */
func (this *Foxbit) FetchBalance(params ...interface{}) (Balances, error) {
	res := <-this.Core.FetchBalance(params...)
	if IsError(res) {
		return Balances{}, CreateReturnError(res)
	}
	return NewBalances(res), nil
}

/**
 * @method
 * @name foxbit#fetchOpenOrders
 * @description Fetch all unfilled currently open orders.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/OrdersController_listOrders
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch open orders for
 * @param {int} [limit] the maximum number of open order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) FetchOpenOrders(options ...FetchOpenOrdersOptions) ([]Order, error) {

	opts := FetchOpenOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOpenOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchClosedOrders
 * @description Fetch all currently closed orders.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/OrdersController_listOrders
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] the earliest time in ms to fetch orders for
 * @param {int} [limit] the maximum number of order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) FetchClosedOrders(options ...FetchClosedOrdersOptions) ([]Order, error) {

	opts := FetchClosedOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchClosedOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *Foxbit) FetchCanceledOrders(options ...FetchCanceledOrdersOptions) ([]Order, error) {

	opts := FetchCanceledOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchCanceledOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}
func (this *Foxbit) FetchOrdersByStatus(status string, options ...FetchOrdersByStatusOptions) ([]Order, error) {

	opts := FetchOrdersByStatusOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrdersByStatus(status, symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name foxbit#createOrder
 * @description Create an order with the specified characteristics
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/OrdersController_create
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type 'market', 'limit', 'stop_market', 'stop_limit', 'instant'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much you want to trade in units of the base currency
 * @param {float} [price] the price at which the order is to be fullfilled, in units of the quote currency, ignored in market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {string} [params.timeInForce] "GTC", "FOK", "IOC", "PO"
 * @param {float} [params.triggerPrice] The time in force for the order. One of GTC, FOK, IOC, PO. See .features or foxbit's doc to see more details.
 * @param {bool} [params.postOnly] true or false whether the order is post-only
 * @param {string} [params.clientOrderId] a unique identifier for the order
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) CreateOrder(symbol string, typeVar string, side string, amount float64, options ...CreateOrderOptions) (Order, error) {

	opts := CreateOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CreateOrder(symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name foxbit#createOrders
 * @description create a list of trade orders
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/createBatch
 * @param {Array} orders list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) CreateOrders(orders []OrderRequest, options ...CreateOrdersOptions) ([]Order, error) {

	opts := CreateOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CreateOrders(ConvertOrderRequestListToArray(orders), params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name foxbit#cancelOrder
 * @description Cancel open orders.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/OrdersController_cancel
 * @param {string} id order id
 * @param {string} symbol unified symbol of the market the order was made in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) CancelOrder(id string, options ...CancelOrderOptions) (Order, error) {

	opts := CancelOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CancelOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name foxbit#cancelAllOrders
 * @description Cancel all open orders or all open orders for a specific market.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/OrdersController_cancel
 * @param {string} symbol unified market symbol of the market to cancel orders in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) CancelAllOrders(options ...CancelAllOrdersOptions) ([]Order, error) {

	opts := CancelAllOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.CancelAllOrders(symbol, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchOrder
 * @description Get an order by ID.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/OrdersController_findByOrderId
 * @param id
 * @param {string} symbol it is not used in the foxbit API
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) FetchOrder(id string, options ...FetchOrderOptions) (Order, error) {

	opts := FetchOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrder(id, symbol, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name foxbit#fetchOrders
 * @description fetches information on multiple orders made by the user
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/OrdersController_listOrders
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] the earliest time in ms to fetch orders for
 * @param {int} [limit] the maximum number of order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {string} [params.state] Enum: ACTIVE, CANCELED, FILLED, PARTIALLY_CANCELED, PARTIALLY_FILLED
 * @param {string} [params.side] Enum: BUY, SELL
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) FetchOrders(options ...FetchOrdersOptions) ([]Order, error) {

	opts := FetchOrdersOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchOrders(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewOrderArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchMyTrades
 * @description Trade history queries will only have data available for the last 3 months, in descending order (most recents trades first).
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/TradesController_all
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch trades for
 * @param {int} [limit] the maximum number of trade structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
 */
func (this *Foxbit) FetchMyTrades(options ...FetchMyTradesOptions) ([]Trade, error) {

	opts := FetchMyTradesOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var symbol interface{} = nil
	if opts.Symbol != nil {
		symbol = *opts.Symbol
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchMyTrades(symbol, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTradeArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchDepositAddress
 * @description Fetch the deposit address for a currency associated with this account.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Deposit/operation/DepositsController_depositAddress
 * @param {string} code unified currency code
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {string} [params.networkCode] the blockchain network to create a deposit address on
 * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
 */
func (this *Foxbit) FetchDepositAddress(code string, options ...FetchDepositAddressOptions) (DepositAddress, error) {

	opts := FetchDepositAddressOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchDepositAddress(code, params)
	if IsError(res) {
		return DepositAddress{}, CreateReturnError(res)
	}
	return NewDepositAddress(res), nil
}

/**
 * @method
 * @name foxbit#fetchDeposits
 * @description Fetch all deposits made to an account.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Deposit/operation/DepositsController_listOrders
 * @param {string} [code] unified currency code
 * @param {int} [since] the earliest time in ms to fetch deposits for
 * @param {int} [limit] the maximum number of deposit structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Foxbit) FetchDeposits(options ...FetchDepositsOptions) ([]Transaction, error) {

	opts := FetchDepositsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchDeposits(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchWithdrawals
 * @description Fetch all withdrawals made from an account.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Withdrawal/operation/WithdrawalsController_listWithdrawals
 * @param {string} [code] unified currency code
 * @param {int} [since] the earliest time in ms to fetch withdrawals for
 * @param {int} [limit] the maximum number of withdrawal structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Foxbit) FetchWithdrawals(options ...FetchWithdrawalsOptions) ([]Transaction, error) {

	opts := FetchWithdrawalsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchWithdrawals(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchTransactions
 * @description Fetch all transactions (deposits and withdrawals) made from an account.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Withdrawal/operation/WithdrawalsController_listWithdrawals
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Deposit/operation/DepositsController_listOrders
 * @param {string} [code] unified currency code
 * @param {int} [since] the earliest time in ms to fetch withdrawals for
 * @param {int} [limit] the maximum number of withdrawal structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Foxbit) FetchTransactions(options ...FetchTransactionsOptions) ([]Transaction, error) {

	opts := FetchTransactionsOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchTransactions(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewTransactionArray(res), nil
}

/**
 * @method
 * @name foxbit#fetchStatus
 * @description The latest known information on the availability of the exchange API.
 * @see https://status.foxbit.com/
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [status structure]{@link https://docs.ccxt.com/#/?id=exchange-status-structure}
 */
func (this *Foxbit) FetchStatus(params ...interface{}) (map[string]interface{}, error) {
	res := <-this.Core.FetchStatus(params...)
	if IsError(res) {
		return map[string]interface{}{}, CreateReturnError(res)
	}
	return res.(map[string]interface{}), nil
}

/**
 * @method
 * @name foxbit#editOrder
 * @description Simultaneously cancel an existing order and create a new one.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Trading/operation/OrdersController_cancelReplace
 * @param {string} id order id
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type 'market' or 'limit'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much of the currency you want to trade in units of the base currency
 * @param {float} [price] the price at which the order is to be fullfilled, in units of the quote currency, ignored in market orders, used as stop_price on stop market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *Foxbit) EditOrder(id string, symbol string, typeVar string, side string, options ...EditOrderOptions) (Order, error) {

	opts := EditOrderOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var amount interface{} = nil
	if opts.Amount != nil {
		amount = *opts.Amount
	}

	var price interface{} = nil
	if opts.Price != nil {
		price = *opts.Price
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.EditOrder(id, symbol, typeVar, side, amount, price, params)
	if IsError(res) {
		return Order{}, CreateReturnError(res)
	}
	return NewOrder(res), nil
}

/**
 * @method
 * @name foxbit#withdraw
 * @description Make a withdrawal.
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Withdrawal/operation/WithdrawalsController_createWithdrawal
 * @param {string} code unified currency code
 * @param {float} amount the amount to withdraw
 * @param {string} address the address to withdraw to
 * @param {string} tag
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *Foxbit) Withdraw(code string, amount float64, address string, options ...WithdrawOptions) (Transaction, error) {

	opts := WithdrawOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var tag interface{} = nil
	if opts.Tag != nil {
		tag = *opts.Tag
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.Withdraw(code, amount, address, tag, params)
	if IsError(res) {
		return Transaction{}, CreateReturnError(res)
	}
	return NewTransaction(res), nil
}

/**
 * @method
 * @name foxbit#fetchLedger
 * @description fetch the history of changes, actions done by the user or operations that altered balance of the user
 * @see https://docs.foxbit.com.br/rest/v3/#tag/Account/operation/AccountsController_getTransactions
 * @param {string} code unified currency code, default is undefined
 * @param {int} [since] timestamp in ms of the earliest ledger entry, default is undefined
 * @param {int} [limit] max number of ledger entrys to return, default is undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ledger structure]{@link https://docs.ccxt.com/#/?id=ledger-structure}
 */
func (this *Foxbit) FetchLedger(options ...FetchLedgerOptions) ([]LedgerEntry, error) {

	opts := FetchLedgerOptionsStruct{}

	for _, opt := range options {
		opt(&opts)
	}

	var code interface{} = nil
	if opts.Code != nil {
		code = *opts.Code
	}

	var since interface{} = nil
	if opts.Since != nil {
		since = *opts.Since
	}

	var limit interface{} = nil
	if opts.Limit != nil {
		limit = *opts.Limit
	}

	var params interface{} = nil
	if opts.Params != nil {
		params = *opts.Params
	}
	res := <-this.Core.FetchLedger(code, since, limit, params)
	if IsError(res) {
		return nil, CreateReturnError(res)
	}
	return NewLedgerEntryArray(res), nil
}

// missing typed methods from base
// nolint
func (this *Foxbit) LoadMarkets(params ...interface{}) (map[string]MarketInterface, error) {
	return this.exchangeTyped.LoadMarkets(params...)
}
func (this *Foxbit) CancelAllOrdersAfter(timeout int64, options ...CancelAllOrdersAfterOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.CancelAllOrdersAfter(timeout, options...)
}
func (this *Foxbit) CancelOrdersForSymbols(orders []CancellationRequest, options ...CancelOrdersForSymbolsOptions) ([]Order, error) {
	return this.exchangeTyped.CancelOrdersForSymbols(orders, options...)
}
func (this *Foxbit) CreateConvertTrade(id string, fromCode string, toCode string, options ...CreateConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.CreateConvertTrade(id, fromCode, toCode, options...)
}
func (this *Foxbit) CreateDepositAddress(code string, options ...CreateDepositAddressOptions) (DepositAddress, error) {
	return this.exchangeTyped.CreateDepositAddress(code, options...)
}
func (this *Foxbit) CreateLimitBuyOrder(symbol string, amount float64, price float64, options ...CreateLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitBuyOrder(symbol, amount, price, options...)
}
func (this *Foxbit) CreateLimitOrder(symbol string, side string, amount float64, price float64, options ...CreateLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitOrder(symbol, side, amount, price, options...)
}
func (this *Foxbit) CreateLimitSellOrder(symbol string, amount float64, price float64, options ...CreateLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateLimitSellOrder(symbol, amount, price, options...)
}
func (this *Foxbit) CreateMarketBuyOrder(symbol string, amount float64, options ...CreateMarketBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrder(symbol, amount, options...)
}
func (this *Foxbit) CreateMarketBuyOrderWithCost(symbol string, cost float64, options ...CreateMarketBuyOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketBuyOrderWithCost(symbol, cost, options...)
}
func (this *Foxbit) CreateMarketOrder(symbol string, side string, amount float64, options ...CreateMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrder(symbol, side, amount, options...)
}
func (this *Foxbit) CreateMarketOrderWithCost(symbol string, side string, cost float64, options ...CreateMarketOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketOrderWithCost(symbol, side, cost, options...)
}
func (this *Foxbit) CreateMarketSellOrder(symbol string, amount float64, options ...CreateMarketSellOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrder(symbol, amount, options...)
}
func (this *Foxbit) CreateMarketSellOrderWithCost(symbol string, cost float64, options ...CreateMarketSellOrderWithCostOptions) (Order, error) {
	return this.exchangeTyped.CreateMarketSellOrderWithCost(symbol, cost, options...)
}
func (this *Foxbit) CreateOrderWithTakeProfitAndStopLoss(symbol string, typeVar string, side string, amount float64, options ...CreateOrderWithTakeProfitAndStopLossOptions) (Order, error) {
	return this.exchangeTyped.CreateOrderWithTakeProfitAndStopLoss(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) CreatePostOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreatePostOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreatePostOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) CreateReduceOnlyOrder(symbol string, typeVar string, side string, amount float64, options ...CreateReduceOnlyOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateReduceOnlyOrder(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) CreateStopLimitOrder(symbol string, side string, amount float64, price float64, triggerPrice float64, options ...CreateStopLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLimitOrder(symbol, side, amount, price, triggerPrice, options...)
}
func (this *Foxbit) CreateStopLossOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopLossOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopLossOrder(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) CreateStopMarketOrder(symbol string, side string, amount float64, triggerPrice float64, options ...CreateStopMarketOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopMarketOrder(symbol, side, amount, triggerPrice, options...)
}
func (this *Foxbit) CreateStopOrder(symbol string, typeVar string, side string, amount float64, options ...CreateStopOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateStopOrder(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) CreateTakeProfitOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTakeProfitOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTakeProfitOrder(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) CreateTrailingAmountOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingAmountOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingAmountOrder(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) CreateTrailingPercentOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTrailingPercentOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTrailingPercentOrder(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) CreateTriggerOrder(symbol string, typeVar string, side string, amount float64, options ...CreateTriggerOrderOptions) (Order, error) {
	return this.exchangeTyped.CreateTriggerOrder(symbol, typeVar, side, amount, options...)
}
func (this *Foxbit) EditLimitBuyOrder(id string, symbol string, amount float64, options ...EditLimitBuyOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitBuyOrder(id, symbol, amount, options...)
}
func (this *Foxbit) EditLimitOrder(id string, symbol string, side string, amount float64, options ...EditLimitOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitOrder(id, symbol, side, amount, options...)
}
func (this *Foxbit) EditLimitSellOrder(id string, symbol string, amount float64, options ...EditLimitSellOrderOptions) (Order, error) {
	return this.exchangeTyped.EditLimitSellOrder(id, symbol, amount, options...)
}
func (this *Foxbit) EditOrders(orders []OrderRequest, options ...EditOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.EditOrders(orders, options...)
}
func (this *Foxbit) FetchAccounts(params ...interface{}) ([]Account, error) {
	return this.exchangeTyped.FetchAccounts(params...)
}
func (this *Foxbit) FetchAllGreeks(options ...FetchAllGreeksOptions) ([]Greeks, error) {
	return this.exchangeTyped.FetchAllGreeks(options...)
}
func (this *Foxbit) FetchBidsAsks(options ...FetchBidsAsksOptions) (Tickers, error) {
	return this.exchangeTyped.FetchBidsAsks(options...)
}
func (this *Foxbit) FetchBorrowInterest(options ...FetchBorrowInterestOptions) ([]BorrowInterest, error) {
	return this.exchangeTyped.FetchBorrowInterest(options...)
}
func (this *Foxbit) FetchBorrowRate(code string, amount float64, options ...FetchBorrowRateOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchBorrowRate(code, amount, options...)
}
func (this *Foxbit) FetchCanceledAndClosedOrders(options ...FetchCanceledAndClosedOrdersOptions) ([]Order, error) {
	return this.exchangeTyped.FetchCanceledAndClosedOrders(options...)
}
func (this *Foxbit) FetchConvertCurrencies(params ...interface{}) (Currencies, error) {
	return this.exchangeTyped.FetchConvertCurrencies(params...)
}
func (this *Foxbit) FetchConvertQuote(fromCode string, toCode string, options ...FetchConvertQuoteOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertQuote(fromCode, toCode, options...)
}
func (this *Foxbit) FetchConvertTrade(id string, options ...FetchConvertTradeOptions) (Conversion, error) {
	return this.exchangeTyped.FetchConvertTrade(id, options...)
}
func (this *Foxbit) FetchConvertTradeHistory(options ...FetchConvertTradeHistoryOptions) ([]Conversion, error) {
	return this.exchangeTyped.FetchConvertTradeHistory(options...)
}
func (this *Foxbit) FetchCrossBorrowRate(code string, options ...FetchCrossBorrowRateOptions) (CrossBorrowRate, error) {
	return this.exchangeTyped.FetchCrossBorrowRate(code, options...)
}
func (this *Foxbit) FetchCrossBorrowRates(params ...interface{}) (CrossBorrowRates, error) {
	return this.exchangeTyped.FetchCrossBorrowRates(params...)
}
func (this *Foxbit) FetchDepositAddresses(options ...FetchDepositAddressesOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddresses(options...)
}
func (this *Foxbit) FetchDepositAddressesByNetwork(code string, options ...FetchDepositAddressesByNetworkOptions) ([]DepositAddress, error) {
	return this.exchangeTyped.FetchDepositAddressesByNetwork(code, options...)
}
func (this *Foxbit) FetchDepositsWithdrawals(options ...FetchDepositsWithdrawalsOptions) ([]Transaction, error) {
	return this.exchangeTyped.FetchDepositsWithdrawals(options...)
}
func (this *Foxbit) FetchDepositWithdrawFee(code string, options ...FetchDepositWithdrawFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFee(code, options...)
}
func (this *Foxbit) FetchDepositWithdrawFees(options ...FetchDepositWithdrawFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchDepositWithdrawFees(options...)
}
func (this *Foxbit) FetchFreeBalance(params ...interface{}) (Balance, error) {
	return this.exchangeTyped.FetchFreeBalance(params...)
}
func (this *Foxbit) FetchFundingHistory(options ...FetchFundingHistoryOptions) ([]FundingHistory, error) {
	return this.exchangeTyped.FetchFundingHistory(options...)
}
func (this *Foxbit) FetchFundingInterval(symbol string, options ...FetchFundingIntervalOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingInterval(symbol, options...)
}
func (this *Foxbit) FetchFundingIntervals(options ...FetchFundingIntervalsOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingIntervals(options...)
}
func (this *Foxbit) FetchFundingRate(symbol string, options ...FetchFundingRateOptions) (FundingRate, error) {
	return this.exchangeTyped.FetchFundingRate(symbol, options...)
}
func (this *Foxbit) FetchFundingRateHistory(options ...FetchFundingRateHistoryOptions) ([]FundingRateHistory, error) {
	return this.exchangeTyped.FetchFundingRateHistory(options...)
}
func (this *Foxbit) FetchFundingRates(options ...FetchFundingRatesOptions) (FundingRates, error) {
	return this.exchangeTyped.FetchFundingRates(options...)
}
func (this *Foxbit) FetchGreeks(symbol string, options ...FetchGreeksOptions) (Greeks, error) {
	return this.exchangeTyped.FetchGreeks(symbol, options...)
}
func (this *Foxbit) FetchIndexOHLCV(symbol string, options ...FetchIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchIndexOHLCV(symbol, options...)
}
func (this *Foxbit) FetchIsolatedBorrowRate(symbol string, options ...FetchIsolatedBorrowRateOptions) (IsolatedBorrowRate, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRate(symbol, options...)
}
func (this *Foxbit) FetchIsolatedBorrowRates(params ...interface{}) (IsolatedBorrowRates, error) {
	return this.exchangeTyped.FetchIsolatedBorrowRates(params...)
}
func (this *Foxbit) FetchLastPrices(options ...FetchLastPricesOptions) (LastPrices, error) {
	return this.exchangeTyped.FetchLastPrices(options...)
}
func (this *Foxbit) FetchLedgerEntry(id string, options ...FetchLedgerEntryOptions) (LedgerEntry, error) {
	return this.exchangeTyped.FetchLedgerEntry(id, options...)
}
func (this *Foxbit) FetchLeverage(symbol string, options ...FetchLeverageOptions) (Leverage, error) {
	return this.exchangeTyped.FetchLeverage(symbol, options...)
}
func (this *Foxbit) FetchLeverages(options ...FetchLeveragesOptions) (Leverages, error) {
	return this.exchangeTyped.FetchLeverages(options...)
}
func (this *Foxbit) FetchLeverageTiers(options ...FetchLeverageTiersOptions) (LeverageTiers, error) {
	return this.exchangeTyped.FetchLeverageTiers(options...)
}
func (this *Foxbit) FetchLiquidations(symbol string, options ...FetchLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchLiquidations(symbol, options...)
}
func (this *Foxbit) FetchLongShortRatio(symbol string, options ...FetchLongShortRatioOptions) (LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatio(symbol, options...)
}
func (this *Foxbit) FetchLongShortRatioHistory(options ...FetchLongShortRatioHistoryOptions) ([]LongShortRatio, error) {
	return this.exchangeTyped.FetchLongShortRatioHistory(options...)
}
func (this *Foxbit) FetchMarginAdjustmentHistory(options ...FetchMarginAdjustmentHistoryOptions) ([]MarginModification, error) {
	return this.exchangeTyped.FetchMarginAdjustmentHistory(options...)
}
func (this *Foxbit) FetchMarginMode(symbol string, options ...FetchMarginModeOptions) (MarginMode, error) {
	return this.exchangeTyped.FetchMarginMode(symbol, options...)
}
func (this *Foxbit) FetchMarginModes(options ...FetchMarginModesOptions) (MarginModes, error) {
	return this.exchangeTyped.FetchMarginModes(options...)
}
func (this *Foxbit) FetchMarketLeverageTiers(symbol string, options ...FetchMarketLeverageTiersOptions) ([]LeverageTier, error) {
	return this.exchangeTyped.FetchMarketLeverageTiers(symbol, options...)
}
func (this *Foxbit) FetchMarkOHLCV(symbol string, options ...FetchMarkOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchMarkOHLCV(symbol, options...)
}
func (this *Foxbit) FetchMarkPrice(symbol string, options ...FetchMarkPriceOptions) (Ticker, error) {
	return this.exchangeTyped.FetchMarkPrice(symbol, options...)
}
func (this *Foxbit) FetchMarkPrices(options ...FetchMarkPricesOptions) (Tickers, error) {
	return this.exchangeTyped.FetchMarkPrices(options...)
}
func (this *Foxbit) FetchMyLiquidations(options ...FetchMyLiquidationsOptions) ([]Liquidation, error) {
	return this.exchangeTyped.FetchMyLiquidations(options...)
}
func (this *Foxbit) FetchOpenInterest(symbol string, options ...FetchOpenInterestOptions) (OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterest(symbol, options...)
}
func (this *Foxbit) FetchOpenInterestHistory(symbol string, options ...FetchOpenInterestHistoryOptions) ([]OpenInterest, error) {
	return this.exchangeTyped.FetchOpenInterestHistory(symbol, options...)
}
func (this *Foxbit) FetchOpenInterests(options ...FetchOpenInterestsOptions) (OpenInterests, error) {
	return this.exchangeTyped.FetchOpenInterests(options...)
}
func (this *Foxbit) FetchOption(symbol string, options ...FetchOptionOptions) (Option, error) {
	return this.exchangeTyped.FetchOption(symbol, options...)
}
func (this *Foxbit) FetchOptionChain(code string, options ...FetchOptionChainOptions) (OptionChain, error) {
	return this.exchangeTyped.FetchOptionChain(code, options...)
}
func (this *Foxbit) FetchOrderBooks(options ...FetchOrderBooksOptions) (OrderBooks, error) {
	return this.exchangeTyped.FetchOrderBooks(options...)
}
func (this *Foxbit) FetchOrderStatus(id string, options ...FetchOrderStatusOptions) (string, error) {
	return this.exchangeTyped.FetchOrderStatus(id, options...)
}
func (this *Foxbit) FetchOrderTrades(id string, options ...FetchOrderTradesOptions) ([]Trade, error) {
	return this.exchangeTyped.FetchOrderTrades(id, options...)
}
func (this *Foxbit) FetchPaymentMethods(params ...interface{}) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPaymentMethods(params...)
}
func (this *Foxbit) FetchPosition(symbol string, options ...FetchPositionOptions) (Position, error) {
	return this.exchangeTyped.FetchPosition(symbol, options...)
}
func (this *Foxbit) FetchPositionHistory(symbol string, options ...FetchPositionHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionHistory(symbol, options...)
}
func (this *Foxbit) FetchPositionMode(options ...FetchPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchPositionMode(options...)
}
func (this *Foxbit) FetchPositions(options ...FetchPositionsOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositions(options...)
}
func (this *Foxbit) FetchPositionsForSymbol(symbol string, options ...FetchPositionsForSymbolOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsForSymbol(symbol, options...)
}
func (this *Foxbit) FetchPositionsHistory(options ...FetchPositionsHistoryOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsHistory(options...)
}
func (this *Foxbit) FetchPositionsRisk(options ...FetchPositionsRiskOptions) ([]Position, error) {
	return this.exchangeTyped.FetchPositionsRisk(options...)
}
func (this *Foxbit) FetchPremiumIndexOHLCV(symbol string, options ...FetchPremiumIndexOHLCVOptions) ([]OHLCV, error) {
	return this.exchangeTyped.FetchPremiumIndexOHLCV(symbol, options...)
}
func (this *Foxbit) FetchTime(params ...interface{}) (int64, error) {
	return this.exchangeTyped.FetchTime(params...)
}
func (this *Foxbit) FetchTradingFee(symbol string, options ...FetchTradingFeeOptions) (TradingFeeInterface, error) {
	return this.exchangeTyped.FetchTradingFee(symbol, options...)
}
func (this *Foxbit) FetchTradingLimits(options ...FetchTradingLimitsOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTradingLimits(options...)
}
func (this *Foxbit) FetchTransactionFee(code string, options ...FetchTransactionFeeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFee(code, options...)
}
func (this *Foxbit) FetchTransactionFees(options ...FetchTransactionFeesOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.FetchTransactionFees(options...)
}
func (this *Foxbit) FetchTransfer(id string, options ...FetchTransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.FetchTransfer(id, options...)
}
func (this *Foxbit) FetchTransfers(options ...FetchTransfersOptions) ([]TransferEntry, error) {
	return this.exchangeTyped.FetchTransfers(options...)
}
func (this *Foxbit) SetMargin(symbol string, amount float64, options ...SetMarginOptions) (MarginModification, error) {
	return this.exchangeTyped.SetMargin(symbol, amount, options...)
}
func (this *Foxbit) SetMarginMode(marginMode string, options ...SetMarginModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetMarginMode(marginMode, options...)
}
func (this *Foxbit) SetPositionMode(hedged bool, options ...SetPositionModeOptions) (map[string]interface{}, error) {
	return this.exchangeTyped.SetPositionMode(hedged, options...)
}
func (this *Foxbit) Transfer(code string, amount float64, fromAccount string, toAccount string, options ...TransferOptions) (TransferEntry, error) {
	return this.exchangeTyped.Transfer(code, amount, fromAccount, toAccount, options...)
}
