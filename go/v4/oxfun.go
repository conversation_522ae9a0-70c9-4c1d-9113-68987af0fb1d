package ccxt

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

type oxfun struct {
	Exchange
}

func NewOxfunCore() *oxfun {
	p := &oxfun{}
	setDefaults(p)
	return p
}

func (this *oxfun) Describe() interface{} {
	return this.DeepExtend(this.Exchange.Describe(), map[string]interface{}{
		"id":        "oxfun",
		"name":      "OXFUN",
		"countries": []interface{}{"PA"},
		"version":   "v3",
		"rateLimit": 120,
		"pro":       true,
		"has": map[string]interface{}{
			"CORS":                           nil,
			"spot":                           true,
			"margin":                         false,
			"swap":                           true,
			"future":                         false,
			"option":                         false,
			"addMargin":                      false,
			"cancelAllOrders":                true,
			"cancelOrder":                    true,
			"cancelOrders":                   true,
			"closeAllPositions":              false,
			"closePosition":                  false,
			"createDepositAddress":           false,
			"createMarketBuyOrderWithCost":   true,
			"createMarketOrderWithCost":      false,
			"createMarketSellOrderWithCost":  false,
			"createOrder":                    true,
			"createOrders":                   true,
			"createPostOnlyOrder":            true,
			"createReduceOnlyOrder":          false,
			"createStopLimitOrder":           true,
			"createStopMarketOrder":          true,
			"createStopOrder":                true,
			"deposit":                        false,
			"editOrder":                      false,
			"fetchAccounts":                  true,
			"fetchBalance":                   true,
			"fetchBidsAsks":                  false,
			"fetchBorrowInterest":            false,
			"fetchBorrowRateHistories":       false,
			"fetchBorrowRateHistory":         false,
			"fetchCanceledOrders":            false,
			"fetchClosedOrder":               false,
			"fetchClosedOrders":              false,
			"fetchCrossBorrowRate":           false,
			"fetchCrossBorrowRates":          false,
			"fetchCurrencies":                true,
			"fetchDeposit":                   false,
			"fetchDepositAddress":            true,
			"fetchDepositAddresses":          false,
			"fetchDepositAddressesByNetwork": false,
			"fetchDeposits":                  true,
			"fetchDepositWithdrawFee":        false,
			"fetchDepositWithdrawFees":       false,
			"fetchFundingHistory":            true,
			"fetchFundingRate":               true,
			"fetchFundingRateHistory":        true,
			"fetchFundingRates":              true,
			"fetchIndexOHLCV":                false,
			"fetchIsolatedBorrowRate":        false,
			"fetchIsolatedBorrowRates":       false,
			"fetchL3OrderBook":               false,
			"fetchLedger":                    false,
			"fetchLeverage":                  false,
			"fetchLeverageTiers":             true,
			"fetchMarketLeverageTiers":       "emulated",
			"fetchMarkets":                   true,
			"fetchMarkOHLCV":                 false,
			"fetchMyTrades":                  true,
			"fetchOHLCV":                     true,
			"fetchOpenInterestHistory":       false,
			"fetchOpenOrder":                 false,
			"fetchOpenOrders":                true,
			"fetchOrder":                     true,
			"fetchOrderBook":                 true,
			"fetchOrderBooks":                false,
			"fetchOrders":                    false,
			"fetchOrderTrades":               false,
			"fetchPosition":                  false,
			"fetchPositionHistory":           false,
			"fetchPositionMode":              false,
			"fetchPositions":                 true,
			"fetchPositionsForSymbol":        false,
			"fetchPositionsHistory":          false,
			"fetchPositionsRisk":             false,
			"fetchPremiumIndexOHLCV":         false,
			"fetchStatus":                    false,
			"fetchTicker":                    true,
			"fetchTickers":                   true,
			"fetchTime":                      false,
			"fetchTrades":                    true,
			"fetchTradingFee":                false,
			"fetchTradingFees":               false,
			"fetchTradingLimits":             false,
			"fetchTransactionFee":            false,
			"fetchTransactionFees":           false,
			"fetchTransactions":              false,
			"fetchTransfers":                 true,
			"fetchWithdrawal":                false,
			"fetchWithdrawals":               true,
			"fetchWithdrawalWhitelist":       false,
			"reduceMargin":                   false,
			"repayCrossMargin":               false,
			"repayIsolatedMargin":            false,
			"sandbox":                        true,
			"setLeverage":                    false,
			"setMargin":                      false,
			"setMarginMode":                  false,
			"setPositionMode":                false,
			"signIn":                         false,
			"transfer":                       true,
			"withdraw":                       true,
			"ws":                             true,
		},
		"timeframes": map[string]interface{}{
			"1m":  "60s",
			"5m":  "300s",
			"15m": "900s",
			"30m": "1800s",
			"1h":  "3600s",
			"2h":  "7200s",
			"4h":  "14400s",
			"1d":  "86400s",
		},
		"urls": map[string]interface{}{
			"logo":     "https://github.com/ccxt/ccxt/assets/********/6a196124-c1ee-4fae-8573-962071b61a85",
			"referral": "https://ox.fun/register?shareAccountId=5ZUD4a7G",
			"api": map[string]interface{}{
				"public":  "https://api.ox.fun",
				"private": "https://api.ox.fun",
			},
			"test": map[string]interface{}{
				"public":  "https://stgapi.ox.fun",
				"private": "https://stgapi.ox.fun",
			},
			"www":  "https://ox.fun/",
			"doc":  "https://docs.ox.fun/",
			"fees": "https://support.ox.fun/en/articles/8819866-trading-fees",
		},
		"api": map[string]interface{}{
			"public": map[string]interface{}{
				"get": map[string]interface{}{
					"v3/markets":             1,
					"v3/assets":              1,
					"v3/tickers":             1,
					"v3/funding/estimates":   1,
					"v3/candles":             1,
					"v3/depth":               1,
					"v3/markets/operational": 1,
					"v3/exchange-trades":     1,
					"v3/funding/rates":       1,
					"v3/leverage/tiers":      1,
				},
			},
			"private": map[string]interface{}{
				"get": map[string]interface{}{
					"v3/account":              1,
					"v3/account/names":        1,
					"v3/wallet":               1,
					"v3/transfer":             1,
					"v3/balances":             1,
					"v3/positions":            1,
					"v3/funding":              1,
					"v3/deposit-addresses":    1,
					"v3/deposit":              1,
					"v3/withdrawal-addresses": 1,
					"v3/withdrawal":           1,
					"v3/withdrawal-fees":      1,
					"v3/orders/status":        1,
					"v3/orders/working":       1,
					"v3/trades":               1,
				},
				"post": map[string]interface{}{
					"v3/transfer":     1,
					"v3/withdrawal":   1,
					"v3/orders/place": 1,
				},
				"delete": map[string]interface{}{
					"v3/orders/cancel":     1,
					"v3/orders/cancel-all": 1,
				},
			},
		},
		"fees": map[string]interface{}{
			"trading": map[string]interface{}{
				"tierBased":  true,
				"percentage": true,
				"maker":      this.ParseNumber("0.00020"),
				"taker":      this.ParseNumber("0.00070"),
				"tiers": map[string]interface{}{
					"maker": []interface{}{[]interface{}{this.ParseNumber("0"), this.ParseNumber("0.00020")}, []interface{}{this.ParseNumber("2500000"), this.ParseNumber("0.00010")}, []interface{}{this.ParseNumber("25000000"), this.ParseNumber("0")}},
					"taker": []interface{}{[]interface{}{this.ParseNumber("0"), this.ParseNumber("0.00070")}, []interface{}{this.ParseNumber("2500000"), this.ParseNumber("0.00050")}, []interface{}{this.ParseNumber("25000000"), this.ParseNumber("0.00040")}},
				},
			},
		},
		"precisionMode": TICK_SIZE,
		"options": map[string]interface{}{
			"sandboxMode": false,
			"networks": map[string]interface{}{
				"BTC":      "Bitcoin",
				"ERC20":    "Ethereum",
				"AVAX":     "Avalanche",
				"SOL":      "Solana",
				"ARB":      "Arbitrum",
				"MATIC":    "Polygon",
				"FTM":      "Fantom",
				"BNB":      "BNBSmartChain",
				"OPTIMISM": "Optimism",
			},
			"networksById": map[string]interface{}{
				"Bitcoin":       "BTC",
				"Ethereum":      "ERC20",
				"Avalanche":     "AVAX",
				"Solana":        "SOL",
				"Arbitrum":      "ARB",
				"Polygon":       "MATIC",
				"Fantom":        "FTM",
				"Base":          "BASE",
				"BNBSmartChain": "BNB",
				"Optimism":      "OPTIMISM",
			},
		},
		"features": map[string]interface{}{
			"default": map[string]interface{}{
				"sandbox": true,
				"createOrder": map[string]interface{}{
					"marginMode":                 false,
					"triggerPrice":               true,
					"triggerDirection":           false,
					"triggerPriceType":           nil,
					"stopLossPrice":              false,
					"takeProfitPrice":            false,
					"attachedStopLossTakeProfit": nil,
					"timeInForce": map[string]interface{}{
						"IOC": true,
						"FOK": true,
						"PO":  true,
						"GTD": false,
					},
					"hedged":                 false,
					"trailing":               false,
					"leverage":               false,
					"marketBuyByCost":        true,
					"marketBuyRequiresPrice": false,
					"selfTradePrevention":    true,
					"iceberg":                true,
				},
				"createOrders": map[string]interface{}{
					"max": 10,
				},
				"fetchMyTrades": map[string]interface{}{
					"marginMode":     false,
					"limit":          500,
					"daysBack":       100000,
					"untilDays":      7,
					"symbolRequired": false,
				},
				"fetchOrder": map[string]interface{}{
					"marginMode":     false,
					"trigger":        false,
					"trailing":       false,
					"symbolRequired": false,
				},
				"fetchOpenOrders": map[string]interface{}{
					"marginMode":     false,
					"limit":          nil,
					"trigger":        false,
					"trailing":       false,
					"symbolRequired": false,
				},
				"fetchOrders":       nil,
				"fetchClosedOrders": nil,
				"fetchOHLCV": map[string]interface{}{
					"limit": 500,
				},
			},
			"spot": map[string]interface{}{
				"extends": "default",
			},
			"swap": map[string]interface{}{
				"linear": map[string]interface{}{
					"extends": "default",
				},
				"inverse": nil,
			},
			"future": map[string]interface{}{
				"linear":  nil,
				"inverse": nil,
			},
		},
		"exceptions": map[string]interface{}{
			"exact": map[string]interface{}{
				"-0010":   OperationFailed,
				"-429":    RateLimitExceeded,
				"-05001":  AuthenticationError,
				"-10001":  ExchangeError,
				"-20000":  BadRequest,
				"-20001":  BadRequest,
				"-20002":  BadRequest,
				"-20003":  NotSupported,
				"-20005":  AuthenticationError,
				"-20006":  BadRequest,
				"-20007":  AuthenticationError,
				"-20008":  BadRequest,
				"-20009":  BadRequest,
				"-20010":  ArgumentsRequired,
				"-20011":  ArgumentsRequired,
				"-20012":  ArgumentsRequired,
				"-20013":  ArgumentsRequired,
				"-20014":  BadRequest,
				"-20015":  BadSymbol,
				"-20016":  BadRequest,
				"-20017":  BadRequest,
				"-20018":  BadRequest,
				"-20019":  BadRequest,
				"-20020":  BadRequest,
				"-20021":  BadRequest,
				"-20022":  ArgumentsRequired,
				"-20023":  ArgumentsRequired,
				"-20024":  ExchangeError,
				"-20025":  AuthenticationError,
				"-20026":  BadRequest,
				"-20027":  BadRequest,
				"-20028":  BadRequest,
				"-20029":  BadRequest,
				"-20030":  BadRequest,
				"-20031":  MarketClosed,
				"-20032":  NetworkError,
				"-20033":  BadRequest,
				"-20034":  BadRequest,
				"-20050":  ExchangeError,
				"-30001":  BadRequest,
				"-35034":  AuthenticationError,
				"-35046":  AuthenticationError,
				"-40001":  ExchangeError,
				"-50001":  ExchangeError,
				"-300001": AccountNotEnabled,
				"-300011": InvalidOrder,
				"-300012": InvalidOrder,
				"-100005": OrderNotFound,
				"-100006": InvalidOrder,
				"-100008": BadRequest,
				"-100015": NetworkError,
				"-710001": ExchangeError,
				"-710002": BadRequest,
				"-710003": BadRequest,
				"-710004": BadRequest,
				"-710005": InsufficientFunds,
				"-710006": InsufficientFunds,
				"-710007": InsufficientFunds,
				"-000101": NetworkError,
				"-000201": NetworkError,
			},
			"broad": map[string]interface{}{
				"-20001":  OperationFailed,
				"-200050": RequestTimeout,
			},
		},
	})
}

/**
 * @method
 * @name oxfun#fetchMarkets
 * @description retrieves data on all markets for bitmex
 * @see https://docs.ox.fun/?json#get-v3-markets
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} an array of objects representing market data
 */
func (this *oxfun) FetchMarkets(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		responseFromMarketsresponseFromTickersVariable := (<-promiseAll([]interface{}{this.PublicGetV3Markets(params), this.PublicGetV3Tickers(params)}))
		responseFromMarkets := GetValue(responseFromMarketsresponseFromTickersVariable, 0)
		responseFromTickers := GetValue(responseFromMarketsresponseFromTickersVariable, 1)
		var marketsFromMarkets interface{} = this.SafeList(responseFromMarkets, "data", []interface{}{})
		//
		//         {
		//             success: true,
		//             data: [
		//                 {
		//                     marketCode: 'OX-USD-SWAP-LIN',
		//                     name: 'OX/USD Perp',
		//                     referencePair: 'OX/USDT',
		//                     base: 'OX',
		//                     counter: 'USD',
		//                     type: 'FUTURE',
		//                     tickSize: '0.00001',
		//                     minSize: '1',
		//                     listedAt: '1704766320000',
		//                     upperPriceBound: '0.02122',
		//                     lowerPriceBound: '0.01142',
		//                     markPrice: '0.01632',
		//                     indexPrice: '0.01564',
		//                     lastUpdatedAt: '1714762235569'
		//                 },
		//                 {
		//                     marketCode: 'BTC-USD-SWAP-LIN',
		//                     name: 'BTC/USD Perp',
		//                     referencePair: 'BTC/USDT',
		//                     base: 'BTC',
		//                     counter: 'USD',
		//                     type: 'FUTURE',
		//                     tickSize: '1',
		//                     minSize: '0.0001',
		//                     listedAt: '1704686640000',
		//                     upperPriceBound: '67983',
		//                     lowerPriceBound: '55621',
		//                     markPrice: '61802',
		//                     indexPrice: '61813',
		//                     lastUpdatedAt: '1714762234765'
		//                 },
		//                 {
		//                     "marketCode": "MILK-OX",
		//                     "name": "MILK/OX",
		//                     "referencePair": "MILK/OX",
		//                     "base": "MILK",
		//                     "counter": "OX",
		//                     "type": "SPOT",
		//                     "tickSize": "0.0001",
		//                     "minSize": "1",
		//                     "listedAt": "1706608500000",
		//                     "upperPriceBound": "1.0000",
		//                     "lowerPriceBound": "-1.0000",
		//                     "markPrice": "0.0269",
		//                     "indexPrice": "0.0269",
		//                     "lastUpdatedAt": "1714757402185"
		//                 },
		//                 ...
		//             ]
		//         }
		//
		var marketsFromTickers interface{} = this.SafeList(responseFromTickers, "data", []interface{}{})
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "marketCode": "DYM-USD-SWAP-LIN",
		//                 "markPrice": "3.321",
		//                 "open24h": "3.315",
		//                 "high24h": "3.356",
		//                 "low24h": "3.255",
		//                 "volume24h": "0",
		//                 "currencyVolume24h": "0",
		//                 "openInterest": "1768.1",
		//                 "lastTradedPrice": "3.543",
		//                 "lastTradedQuantity": "1.0",
		//                 "lastUpdatedAt": "1714853388102"
		//             },
		//             ...
		//         ]
		//     }
		//
		var markets interface{} = this.ArrayConcat(marketsFromMarkets, marketsFromTickers)

		ch <- this.ParseMarkets(markets)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseMarkets(markets interface{}) interface{} {
	var marketIds interface{} = []interface{}{}
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(markets)); i++ {
		var market interface{} = GetValue(markets, i)
		var marketId interface{} = this.SafeString(market, "marketCode")
		if !IsTrue((this.InArray(marketId, marketIds))) {
			AppendToArray(&marketIds, marketId)
			AppendToArray(&result, this.ParseMarket(market))
		}
	}
	return result
}
func (this *oxfun) ParseMarket(market interface{}) interface{} {
	var id interface{} = this.SafeString(market, "marketCode", "")
	var parts interface{} = Split(id, "-")
	var baseId interface{} = this.SafeString(parts, 0)
	var quoteId interface{} = this.SafeString(parts, 1)
	var base interface{} = this.SafeCurrencyCode(baseId)
	var quote interface{} = this.SafeCurrencyCode(quoteId)
	var symbol interface{} = Add(Add(base, "/"), quote)
	var typeVar interface{} = this.SafeStringLower(market, "type", "spot") // markets from v3/tickers are spot and have no type
	var settleId interface{} = nil
	var settle interface{} = nil
	var isFuture interface{} = (IsEqual(typeVar, "future")) // the exchange has only perpetual futures
	if IsTrue(isFuture) {
		typeVar = "swap"
		settleId = "OX"
		settle = this.SafeCurrencyCode("OX")
		symbol = Add(Add(symbol, ":"), settle)
	}
	var isSpot interface{} = IsEqual(typeVar, "spot")
	return this.SafeMarketStructure(map[string]interface{}{
		"id":             id,
		"numericId":      nil,
		"symbol":         symbol,
		"base":           base,
		"quote":          quote,
		"settle":         settle,
		"baseId":         baseId,
		"quoteId":        quoteId,
		"settleId":       settleId,
		"type":           typeVar,
		"spot":           isSpot,
		"margin":         false,
		"swap":           isFuture,
		"future":         false,
		"option":         false,
		"active":         true,
		"contract":       isFuture,
		"linear":         Ternary(IsTrue(isFuture), true, nil),
		"inverse":        Ternary(IsTrue(isFuture), false, nil),
		"taker":          GetValue(GetValue(this.Fees, "trading"), "taker"),
		"maker":          GetValue(GetValue(this.Fees, "trading"), "maker"),
		"contractSize":   Ternary(IsTrue(isFuture), 1, nil),
		"expiry":         nil,
		"expiryDatetime": nil,
		"strike":         nil,
		"optionType":     nil,
		"precision": map[string]interface{}{
			"amount": nil,
			"price":  this.SafeNumber(market, "tickSize"),
		},
		"limits": map[string]interface{}{
			"leverage": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"amount": map[string]interface{}{
				"min": this.SafeNumber(market, "minSize"),
				"max": nil,
			},
			"price": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
			"cost": map[string]interface{}{
				"min": nil,
				"max": nil,
			},
		},
		"created": this.SafeInteger(market, "listedAt"),
		"index":   nil,
		"info":    market,
	})
}

/**
 * @method
 * @name oxfun#fetchCurrencies
 * @description fetches all available currencies on an exchange
 * @see https://docs.ox.fun/?json#get-v3-assets
 * @param {dict} [params] extra parameters specific to the exchange API endpoint
 * @returns {dict} an associative dictionary of currencies
 */
func (this *oxfun) FetchCurrencies(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		response := (<-this.PublicGetV3Assets(params))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data":  [
		//             {
		//                 "asset": "OX",
		//                 "isCollateral": true,
		//                 "loanToValue": "1.000000000",
		//                 "loanToValueFactor": "0.000000000",
		//                 "networkList":  [
		//                     {
		//                         "network": "BNBSmartChain",
		//                         "tokenId": "0x78a0A62Fba6Fb21A83FE8a3433d44C73a4017A6f",
		//                         "transactionPrecision": "18",
		//                         "isWithdrawalFeeChargedToUser": true,
		//                         "canDeposit": true,
		//                         "canWithdraw": false,
		//                         "minDeposit": "0.00010",
		//                         "minWithdrawal": "0.00010"
		//                     },
		//                     {
		//                         "network": "Polygon",
		//                         "tokenId": "0x78a0A62Fba6Fb21A83FE8a3433d44C73a4017A6f",
		//                         "transactionPrecision": "18",
		//                         "isWithdrawalFeeChargedToUser": true,
		//                         "canDeposit": true,
		//                         "canWithdraw": false,
		//                         "minDeposit": "0.00010",
		//                         "minWithdrawal": "0.00010"
		//                     },
		//                     ...
		//                 ]
		//             },
		//             {
		//                 "asset": "BTC",
		//                 "isCollateral": true,
		//                 "loanToValue": "0.950000000",
		//                 "loanToValueFactor": "0.000000000",
		//                 "networkList":  [
		//                     {
		//                         "network": "Bitcoin",
		//                         "transactionPrecision": "8",
		//                         "isWithdrawalFeeChargedToUser": true,
		//                         "canDeposit": true,
		//                         "canWithdraw": true,
		//                         "minDeposit": "0.00010",
		//                         "minWithdrawal": "0.00010"
		//                     }
		//                 ]
		//             },
		//             {
		//                 "asset": "USDT.ARB",
		//                 "isCollateral": true,
		//                 "loanToValue": "0.950000000",
		//                 "loanToValueFactor": "0.000000000",
		//                 "networkList": [
		//                     {
		//                         "network": "Arbitrum",
		//                         "tokenId": "******************************************",
		//                         "transactionPrecision": "18",
		//                         "isWithdrawalFeeChargedToUser": true,
		//                         "canDeposit": true,
		//                         "canWithdraw": true,
		//                         "minDeposit": "0.00010",
		//                         "minWithdrawal": "0.00010"
		//                     }
		//                 ]
		//             },
		//             ...
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		var result interface{} = map[string]interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(data)); i++ {
			var currency interface{} = GetValue(data, i)
			var fullId interface{} = this.SafeString(currency, "asset", "")
			var parts interface{} = Split(fullId, ".")
			var id interface{} = GetValue(parts, 0)
			var code interface{} = this.SafeCurrencyCode(id)
			if !IsTrue((InOp(result, code))) {
				AddElementToObject(result, code, map[string]interface{}{
					"id":        id,
					"code":      code,
					"precision": nil,
					"type":      nil,
					"name":      nil,
					"active":    nil,
					"deposit":   nil,
					"withdraw":  nil,
					"fee":       nil,
					"limits": map[string]interface{}{
						"withdraw": map[string]interface{}{
							"min": nil,
							"max": nil,
						},
						"deposit": map[string]interface{}{
							"min": nil,
							"max": nil,
						},
					},
					"networks": map[string]interface{}{},
					"info":     []interface{}{},
				})
			}
			var chains interface{} = this.SafeList(currency, "networkList", []interface{}{})
			for j := 0; IsLessThan(j, GetArrayLength(chains)); j++ {
				var chain interface{} = GetValue(chains, j)
				var networkId interface{} = this.SafeString(chain, "network")
				var networkCode interface{} = this.NetworkIdToCode(networkId)
				AddElementToObject(GetValue(GetValue(result, code), "networks"), networkCode, map[string]interface{}{
					"id":        networkId,
					"network":   networkCode,
					"margin":    nil,
					"deposit":   this.SafeBool(chain, "canDeposit"),
					"withdraw":  this.SafeBool(chain, "canWithdraw"),
					"active":    nil,
					"fee":       nil,
					"precision": this.ParseNumber(this.ParsePrecision(this.SafeString(chain, "transactionPrecision"))),
					"limits": map[string]interface{}{
						"deposit": map[string]interface{}{
							"min": this.SafeNumber(chain, "minDeposit"),
							"max": nil,
						},
						"withdraw": map[string]interface{}{
							"min": this.SafeNumber(chain, "minWithdrawal"),
							"max": nil,
						},
					},
					"info": chain,
				})
			}
			var infos interface{} = this.SafeList(GetValue(result, code), "info", []interface{}{})
			AppendToArray(&infos, currency)
			AddElementToObject(GetValue(result, code), "info", infos)
		}
		// only after all entries are formed in currencies, restructure each entry
		var allKeys interface{} = ObjectKeys(result)
		for i := 0; IsLessThan(i, GetArrayLength(allKeys)); i++ {
			var code interface{} = GetValue(allKeys, i)
			AddElementToObject(result, code, this.SafeCurrencyStructure(GetValue(result, code))) // this is needed after adding network entry
		}

		ch <- result
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchTickers
 * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
 * @see https://docs.ox.fun/?json#get-v3-tickers
 * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *oxfun) FetchTickers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes7318 := (<-this.LoadMarkets())
		PanicOnError(retRes7318)
		symbols = this.MarketSymbols(symbols)

		response := (<-this.PublicGetV3Tickers(params))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "marketCode": "NII-USDT",
		//                 "markPrice": "0",
		//                 "open24h": "0",
		//                 "high24h": "0",
		//                 "low24h": "0",
		//                 "volume24h": "0",
		//                 "currencyVolume24h": "0",
		//                 "openInterest": "0",
		//                 "lastTradedPrice": "0",
		//                 "lastTradedQuantity": "0",
		//                 "lastUpdatedAt": "1714853388621"
		//             },
		//             {
		//                 "marketCode": "GEC-USDT",
		//                 "markPrice": "0",
		//                 "open24h": "0",
		//                 "high24h": "0",
		//                 "low24h": "0",
		//                 "volume24h": "0",
		//                 "currencyVolume24h": "0",
		//                 "openInterest": "0",
		//                 "lastTradedPrice": "0",
		//                 "lastTradedQuantity": "0",
		//                 "lastUpdatedAt": "1714853388621"
		//             },
		//             {
		//                 "marketCode": "DYM-USD-SWAP-LIN",
		//                 "markPrice": "3.321",
		//                 "open24h": "3.315",
		//                 "high24h": "3.356",
		//                 "low24h": "3.255",
		//                 "volume24h": "0",
		//                 "currencyVolume24h": "0",
		//                 "openInterest": "1768.1",
		//                 "lastTradedPrice": "3.543",
		//                 "lastTradedQuantity": "1.0",
		//                 "lastUpdatedAt": "1714853388102"
		//             },
		//             ...
		//         ]
		//     }
		//
		var tickers interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTickers(tickers, symbols)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchTicker
 * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
 * @see https://docs.ox.fun/?json#get-v3-tickers
 * @param {string} symbol unified symbol of the market to fetch the ticker for
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *oxfun) FetchTicker(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes7958 := (<-this.LoadMarkets())
		PanicOnError(retRes7958)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"marketCode": GetValue(market, "id"),
		}

		response := (<-this.PublicGetV3Tickers(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "marketCode": "BTC-USD-SWAP-LIN",
		//                 "markPrice": "64276",
		//                 "open24h": "63674",
		//                 "high24h": "64607",
		//                 "low24h": "62933",
		//                 "volume24h": "306317655.80000",
		//                 "currencyVolume24h": "48.06810",
		//                 "openInterest": "72.39250",
		//                 "lastTradedPrice": "64300.0",
		//                 "lastTradedQuantity": "1.0",
		//                 "lastUpdatedAt": "1714925196034"
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		var ticker interface{} = this.SafeDict(data, 0, map[string]interface{}{})

		ch <- this.ParseTicker(ticker, market)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseTicker(ticker interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "marketCode": "BTC-USD-SWAP-LIN",
	//         "markPrice": "64276",
	//         "open24h": "63674",
	//         "high24h": "64607",
	//         "low24h": "62933",
	//         "volume24h": "306317655.80000",
	//         "currencyVolume24h": "48.06810",
	//         "openInterest": "72.39250",
	//         "lastTradedPrice": "64300.0",
	//         "lastTradedQuantity": "1.0",
	//         "lastUpdatedAt": "1714925196034"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var timestamp interface{} = this.SafeInteger(ticker, "lastUpdatedAt")
	var marketId interface{} = this.SafeString(ticker, "marketCode")
	market = this.SafeMarket(marketId, market)
	var symbol interface{} = GetValue(market, "symbol")
	var last interface{} = this.SafeString(ticker, "lastTradedPrice")
	return this.SafeTicker(map[string]interface{}{
		"symbol":        symbol,
		"timestamp":     timestamp,
		"datetime":      this.Iso8601(timestamp),
		"high":          this.SafeString(ticker, "high24h"),
		"low":           this.SafeString(ticker, "low24h"),
		"bid":           nil,
		"bidVolume":     nil,
		"ask":           nil,
		"askVolume":     nil,
		"vwap":          nil,
		"open":          this.SafeString(ticker, "open24h"),
		"close":         last,
		"last":          last,
		"previousClose": nil,
		"change":        nil,
		"percentage":    nil,
		"average":       nil,
		"baseVolume":    this.SafeString(ticker, "currencyVolume24h"),
		"quoteVolume":   nil,
		"markPrice":     this.SafeString(ticker, "markPrice"),
		"info":          ticker,
	}, market)
}

/**
 * @method
 * @name oxfun#fetchOHLCV
 * @description fetches historical candlestick data containing the open, high, low, and close price, and the volume of a market
 * @see https://docs.ox.fun/?json#get-v3-candles
 * @param {string} symbol unified symbol of the market to fetch OHLCV data for
 * @param {string} timeframe the length of time each candle represents
 * @param {int} [since] timestamp in ms of the earliest candle to fetch (default 24 hours ago)
 * @param {int} [limit] the maximum amount of candles to fetch (default 200, max 500)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] timestamp in ms of the latest candle to fetch (default now)
 * @returns {int[][]} A list of candles ordered as timestamp, open, high, low, close, volume
 */
func (this *oxfun) FetchOHLCV(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		timeframe := GetArg(optionalArgs, 0, "1m")
		_ = timeframe
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes8868 := (<-this.LoadMarkets())
		PanicOnError(retRes8868)
		var market interface{} = this.Market(symbol)
		timeframe = this.SafeString(this.Timeframes, timeframe, timeframe)
		var request interface{} = map[string]interface{}{
			"marketCode": GetValue(market, "id"),
			"timeframe":  timeframe,
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "startTime", since) // startTime and endTime must be within 7 days of each other
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var until interface{} = this.SafeInteger(params, "until")
		if IsTrue(!IsEqual(until, nil)) {
			AddElementToObject(request, "endTime", until)
			params = this.Omit(params, "until")
		} else if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "endTime", this.Sum(since, Multiply(Multiply(Multiply(Multiply(7, 24), 60), 60), 1000))) // for the exchange not to throw an exception if since is younger than 7 days
		}

		response := (<-this.PublicGetV3Candles(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "timeframe": "3600s",
		//         "data": [
		//             {
		//                 "open": "0.03240000",
		//                 "high": "0.03240000",
		//                 "low": "0.03240000",
		//                 "close": "0.03240000",
		//                 "volume": "0",
		//                 "currencyVolume": "0",
		//                 "openedAt": "1714906800000"
		//             },
		//             {
		//                 "open": "0.03240000",
		//                 "high": "0.03240000",
		//                 "low": "0.03240000",
		//                 "close": "0.03240000",
		//                 "volume": "0",
		//                 "currencyVolume": "0",
		//                 "openedAt": "1714903200000"
		//             },
		//             ...
		//         ]
		//     }
		//
		var result interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseOHLCVs(result, market, timeframe, since, limit)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseOHLCV(ohlcv interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "open": "0.03240000",
	//         "high": "0.03240000",
	//         "low": "0.03240000",
	//         "close": "0.03240000",
	//         "volume": "0",
	//         "currencyVolume": "0",
	//         "openedAt": "1714906800000"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	return []interface{}{this.SafeInteger(ohlcv, "openedAt"), this.SafeNumber(ohlcv, "open"), this.SafeNumber(ohlcv, "high"), this.SafeNumber(ohlcv, "low"), this.SafeNumber(ohlcv, "close"), this.SafeNumber(ohlcv, "currencyVolume")}
}

/**
 * @method
 * @name oxfun#fetchOrderBook
 * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
 * @see https://docs.ox.fun/?json#get-v3-depth
 * @param {string} symbol unified symbol of the market to fetch the order book for
 * @param {int} [limit] the maximum amount of order book entries to return (default 5, max 100)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
 */
func (this *oxfun) FetchOrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes9718 := (<-this.LoadMarkets())
		PanicOnError(retRes9718)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"marketCode": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "level", limit)
		}

		response := (<-this.PublicGetV3Depth(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "level": "5",
		//         "data": {
		//             "marketCode": "BTC-USD-SWAP-LIN",
		//             "lastUpdatedAt": "1714933499266",
		//             "asks": [
		//                 [ 64073.0, 8.4622 ],
		//                 [ 64092.0, 8.1912 ],
		//                 [ 64111.0, 8.0669 ],
		//                 [ 64130.0, 11.7195 ],
		//                 [ 64151.0, 10.1798 ]
		//             ],
		//             "bids": [
		//                 [ 64022.0, 10.1292 ],
		//                 [ 64003.0, 8.1619 ],
		//                 [ 64000.0, 1.0 ],
		//                 [ 63984.0, 12.7724 ],
		//                 [ 63963.0, 11.0073 ]
		//             ]
		//         }
		//     }
		//
		var data interface{} = this.SafeDict(response, "data", map[string]interface{}{})
		var timestamp interface{} = this.SafeInteger(data, "lastUpdatedAt")

		ch <- this.ParseOrderBook(data, GetValue(market, "symbol"), timestamp)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchFundingRates
 * @description fetch the current funding rates for multiple markets
 * @see https://docs.ox.fun/?json#get-v3-funding-estimates
 * @param {string[]} symbols unified market symbols
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} an array of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-structure}
 */
func (this *oxfun) FetchFundingRates(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes10198 := (<-this.LoadMarkets())
		PanicOnError(retRes10198)
		symbols = this.MarketSymbols(symbols)

		response := (<-this.PublicGetV3FundingEstimates(params))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "marketCode": "OX-USD-SWAP-LIN",
		//                 "fundingAt": "1715515200000",
		//                 "estFundingRate": "0.000200000"
		//             },
		//             {
		//                 "marketCode": "BTC-USD-SWAP-LIN",
		//                 "fundingAt": "1715515200000",
		//                 "estFundingRate": "0.000003"
		//             },
		//             ...
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseFundingRates(data, symbols)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchFundingRate
 * @description fetch the current funding rates for a symbol
 * @see https://docs.ox.fun/?json#get-v3-funding-estimates
 * @param {string} symbol unified market symbols
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} an array of [funding rate structures]{@link https://docs.ccxt.com/#/?id=funding-rate-structure}
 */
func (this *oxfun) FetchFundingRate(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes10548 := (<-this.LoadMarkets())
		PanicOnError(retRes10548)
		var request interface{} = map[string]interface{}{
			"marketCode": this.MarketId(symbol),
		}

		response := (<-this.PublicGetV3FundingEstimates(this.Extend(request, params)))
		PanicOnError(response)
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		var first interface{} = this.SafeDict(data, 0, map[string]interface{}{})

		ch <- this.ParseFundingRate(first, this.Market(symbol))
		return nil

	}()
	return ch
}
func (this *oxfun) ParseFundingRate(fundingRate interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "marketCode": "OX-USD-SWAP-LIN",
	//         "fundingAt": "1715515200000",
	//         "estFundingRate": "0.000200000"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var symbol interface{} = this.SafeString(fundingRate, "marketCode")
	market = this.Market(symbol)
	var estFundingRateTimestamp interface{} = this.SafeInteger(fundingRate, "fundingAt")
	return map[string]interface{}{
		"info":                     fundingRate,
		"symbol":                   GetValue(market, "symbol"),
		"markPrice":                nil,
		"indexPrice":               nil,
		"interestRate":             this.ParseNumber("0"),
		"estimatedSettlePrice":     nil,
		"timestamp":                estFundingRateTimestamp,
		"datetime":                 this.Iso8601(estFundingRateTimestamp),
		"fundingRate":              this.SafeNumber(fundingRate, "estFundingRate"),
		"fundingTimestamp":         nil,
		"fundingDatetime":          nil,
		"nextFundingRate":          nil,
		"nextFundingTimestamp":     nil,
		"nextFundingDatetime":      nil,
		"previousFundingRate":      nil,
		"previousFundingTimestamp": nil,
		"previousFundingDatetime":  nil,
		"interval":                 nil,
	}
}

/**
 * @method
 * @name oxfun#fetchFundingRateHistory
 * @description Fetches the history of funding rates
 * @see https://docs.ox.fun/?json#get-v3-funding-rates
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch (default 24 hours ago)
 * @param {int} [limit] the maximum amount of trades to fetch (default 200, max 500)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] timestamp in ms of the latest trade to fetch (default now)
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *oxfun) FetchFundingRateHistory(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes11118 := (<-this.LoadMarkets())
		PanicOnError(retRes11118)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"marketCode": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "startTime", since) // startTime and endTime must be within 7 days of each other
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var until interface{} = this.SafeInteger(params, "until")
		if IsTrue(!IsEqual(until, nil)) {
			AddElementToObject(request, "endTime", until)
			params = this.Omit(params, "until")
		}

		response := (<-this.PublicGetV3FundingRates(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         success: true,
		//         data: [
		//         {
		//             marketCode: 'NEAR-USD-SWAP-LIN',
		//             fundingRate: '-0.000010000',
		//             createdAt: '1715428870755'
		//         },
		//         {
		//             marketCode: 'ENA-USD-SWAP-LIN',
		//             fundingRate: '0.000150000',
		//             createdAt: '1715428868616'
		//         },
		//         ...
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseFundingRateHistories(data, market, since, limit)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseFundingRateHistory(info interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         success: true,
	//         data: [
	//         {
	//             marketCode: 'NEAR-USD-SWAP-LIN',
	//             fundingRate: '-0.000010000',
	//             createdAt: '1715428870755'
	//         },
	//         {
	//             marketCode: 'ENA-USD-SWAP-LIN',
	//             fundingRate: '0.000150000',
	//             createdAt: '1715428868616'
	//         },
	//         ...
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var marketId interface{} = this.SafeString(info, "marketCode")
	market = this.SafeMarket(marketId, market)
	var symbol interface{} = GetValue(market, "symbol")
	var timestamp interface{} = this.SafeInteger(info, "createdAt")
	return map[string]interface{}{
		"info":        info,
		"symbol":      symbol,
		"fundingRate": this.SafeNumber(info, "fundingRate"),
		"timestamp":   timestamp,
		"datetime":    this.Iso8601(timestamp),
	}
}

/**
 * @method
 * @name oxfun#fetchFundingHistory
 * @description fetches the history of funding payments
 * @see https://docs.ox.fun/?json#get-v3-funding
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch (default 24 hours ago)
 * @param {int} [limit] the maximum amount of trades to fetch (default 200, max 500)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] timestamp in ms of the latest trade to fetch (default now)
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *oxfun) FetchFundingHistory(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes11938 := (<-this.LoadMarkets())
		PanicOnError(retRes11938)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"marketCode": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "startTime", since) // startTime and endTime must be within 7 days of each other
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var until interface{} = this.SafeInteger(params, "until")
		if IsTrue(!IsEqual(until, nil)) {
			AddElementToObject(request, "endTime", until)
			params = this.Omit(params, "until")
		}

		response := (<-this.PrivateGetV3Funding(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         success: true,
		//         data: [
		//             {
		//                 id: '966709913041305605',
		//                 marketCode: 'ETH-USD-SWAP-LIN',
		//                 payment: '-0.00430822',
		//                 fundingRate: '0.000014',
		//                 position: '0.001',
		//                 indexPrice: '3077.3',
		//                 createdAt: '1715086852890'
		//             },
		//             {
		//                 id: '966698111997509637',
		//                 marketCode: 'ETH-USD-SWAP-LIN',
		//                 payment: '-0.0067419',
		//                 fundingRate: '0.000022',
		//                 position: '0.001',
		//                 indexPrice: '3064.5',
		//                 createdAt: '1715083251516'
		//             },
		//             ...
		//         ]
		//     }
		//
		var result interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseIncomes(result, market, since, limit)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseIncome(income interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         id: '966709913041305605',
	//         marketCode: 'ETH-USD-SWAP-LIN',
	//         payment: '-0.00430822',
	//         fundingRate: '0.000014',
	//         position: '0.001',
	//         indexPrice: '3077.3',
	//         createdAt: '1715086852890'
	//     },
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var marketId interface{} = this.SafeString(income, "marketCode")
	var symbol interface{} = this.SafeSymbol(marketId, market)
	var amount interface{} = this.SafeNumber(income, "payment")
	var code interface{} = this.SafeCurrencyCode("OX")
	var id interface{} = this.SafeString(income, "id")
	var timestamp interface{} = this.SafeTimestamp(income, "createdAt")
	var rate interface{} = this.SafeNumber(income, "fundingRate")
	return map[string]interface{}{
		"info":      income,
		"symbol":    symbol,
		"code":      code,
		"timestamp": timestamp,
		"datetime":  this.Iso8601(timestamp),
		"id":        id,
		"amount":    amount,
		"rate":      rate,
	}
}

/**
 * @method
 * @name oxfun#fetchLeverageTiers
 * @description retrieve information on the maximum leverage, and maintenance margin for trades of varying trade sizes, if a market has a leverage tier of 0, then the leverage tiers cannot be obtained for this market
 * @see https://docs.ox.fun/?json#get-v3-leverage-tiers
 * @param {string[]} [symbols] list of unified market symbols
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [leverage tiers structures]{@link https://docs.ccxt.com/#/?id=leverage-tiers-structure}, indexed by market symbols
 */
func (this *oxfun) FetchLeverageTiers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes12818 := (<-this.LoadMarkets())
		PanicOnError(retRes12818)

		response := (<-this.PublicGetV3LeverageTiers(params))
		PanicOnError(response)
		//
		//     {
		//         success: true,
		//         data: [
		//            {
		//                 marketCode: 'SOL-USD-SWAP-LIN',
		//                 tiers: [
		//                     {
		//                         tier: '1',
		//                         leverage: '10',
		//                         positionFloor: '0',
		//                         positionCap: '200000000',
		//                         initialMargin: '0.1',
		//                         maintenanceMargin: '0.05',
		//                         maintenanceAmount: '0'
		//                     },
		//                     {
		//                         tier: '2',
		//                         leverage: '5',
		//                         positionFloor: '200000000',
		//                         positionCap: '280000000',
		//                         initialMargin: '0.2',
		//                         maintenanceMargin: '0.1',
		//                         maintenanceAmount: '7000000'
		//                     },
		//                     {
		//                         tier: '3',
		//                         leverage: '4',
		//                         positionFloor: '280000000',
		//                         positionCap: '460000000',
		//                         initialMargin: '0.25',
		//                         maintenanceMargin: '0.125',
		//                         maintenanceAmount: '14000000'
		//                     },
		//                     ...
		//                 ]
		//             },
		//             ...
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseLeverageTiers(data, symbols, "marketCode")
		return nil

	}()
	return ch
}
func (this *oxfun) ParseMarketLeverageTiers(info interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         marketCode: 'SOL-USD-SWAP-LIN',
	//         tiers: [
	//             {
	//                 tier: '1',
	//                 leverage: '10',
	//                 positionFloor: '0',
	//                 positionCap: '200000000',
	//                 initialMargin: '0.1',
	//                 maintenanceMargin: '0.05',
	//                 maintenanceAmount: '0'
	//             ...
	//         ]
	//     },
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var marketId interface{} = this.SafeString(info, "marketCode")
	market = this.SafeMarket(marketId, market)
	var listOfTiers interface{} = this.SafeList(info, "tiers", []interface{}{})
	var tiers interface{} = []interface{}{}
	for j := 0; IsLessThan(j, GetArrayLength(listOfTiers)); j++ {
		var tier interface{} = GetValue(listOfTiers, j)
		AppendToArray(&tiers, map[string]interface{}{
			"tier":                  this.SafeNumber(tier, "tier"),
			"symbol":                this.SafeSymbol(marketId, market),
			"currency":              GetValue(market, "settle"),
			"minNotional":           this.SafeNumber(tier, "positionFloor"),
			"maxNotional":           this.SafeNumber(tier, "positionCap"),
			"maintenanceMarginRate": this.SafeNumber(tier, "maintenanceMargin"),
			"maxLeverage":           this.SafeNumber(tier, "leverage"),
			"info":                  tier,
		})
	}
	return tiers
}

/**
 * @method
 * @name oxfun#fetchTrades
 * @description get the list of most recent trades for a particular symbol
 * @see https://docs.ox.fun/?json#get-v3-exchange-trades
 * @param {string} symbol unified symbol of the market to fetch trades for
 * @param {int} [since] timestamp in ms of the earliest trade to fetch (default 24 hours ago)
 * @param {int} [limit] the maximum amount of trades to fetch (default 200, max 500)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] timestamp in ms of the latest trade to fetch (default now)
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=public-trades}
 */
func (this *oxfun) FetchTrades(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		since := GetArg(optionalArgs, 0, nil)
		_ = since
		limit := GetArg(optionalArgs, 1, nil)
		_ = limit
		params := GetArg(optionalArgs, 2, map[string]interface{}{})
		_ = params

		retRes13788 := (<-this.LoadMarkets())
		PanicOnError(retRes13788)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"marketCode": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "startTime", since) // startTime and endTime must be within 7 days of each other
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var until interface{} = this.SafeInteger(params, "until")
		if IsTrue(!IsEqual(until, nil)) {
			AddElementToObject(request, "endTime", until)
			params = this.Omit(params, "until")
		} else if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "endTime", this.Sum(since, Multiply(Multiply(Multiply(Multiply(7, 24), 60), 60), 1000))) // for the exchange not to throw an exception if since is younger than 7 days
		}

		response := (<-this.PublicGetV3ExchangeTrades(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "marketCode": "BTC-USD-SWAP-LIN",
		//                 "matchPrice": "63900",
		//                 "matchQuantity": "1",
		//                 "side": "SELL",
		//                 "matchType": "TAKER",
		//                 "matchedAt": "1714934112352"
		//             },
		//             ...
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTrades(data, market, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchMyTrades
 * @description fetch all trades made by the user
 * @see https://docs.ox.fun/?json#get-v3-trades
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch trades for
 * @param {int} [limit] the maximum amount of trades to fetch (default 200, max 500)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] timestamp in ms of the latest trade to fetch (default now)
 * @returns {Trade[]} a list of [trade structures]{@link https://github.com/ccxt/ccxt/wiki/Manual#trade-structure}
 */
func (this *oxfun) FetchMyTrades(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes14308 := (<-this.LoadMarkets())
		PanicOnError(retRes14308)
		var request interface{} = map[string]interface{}{}
		var market interface{} = nil
		if IsTrue(!IsEqual(symbol, nil)) {
			market = this.Market(symbol)
			AddElementToObject(request, "marketCode", GetValue(market, "id"))
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "startTime", since)
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var until interface{} = this.SafeInteger(params, "until")
		if IsTrue(!IsEqual(until, nil)) {
			AddElementToObject(request, "endTime", until)
			params = this.Omit(params, "until")
		} else if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "endTime", this.Sum(since, Multiply(Multiply(Multiply(Multiply(7, 24), 60), 60), 1000))) // for the exchange not to throw an exception if since is younger than 7 days
		}

		response := (<-this.PrivateGetV3Trades(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "orderId": "1000104903698",
		//                 "clientOrderId": "1715000260094",
		//                 "matchId": "400017129522773178",
		//                 "marketCode": "ETH-USD-SWAP-LIN",
		//                 "side": "BUY",
		//                 "matchedQuantity": "0.001",
		//                 "matchPrice": "3100.2",
		//                 "total": "310.02",
		//                 "orderMatchType": "MAKER",
		//                 "feeAsset": "OX",
		//                 "fee": "0.062004",
		//                 "source": "0",
		//                 "matchedAt": "1715000267420"
		//             }
		//         ]
		//     }
		//
		var result interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTrades(result, market, since, limit)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseTrade(trade interface{}, optionalArgs ...interface{}) interface{} {
	//
	// public fetchTrades
	//
	//     {
	//         "marketCode": "BTC-USD-SWAP-LIN",
	//         "matchPrice": "63900",
	//         "matchQuantity": "1",
	//         "side": "SELL",
	//         "matchType": "TAKER",
	//         "matchedAt": "1714934112352"
	//     }
	//
	//
	// private fetchMyTrades
	//
	//     {
	//         "orderId": "1000104903698",
	//         "clientOrderId": "1715000260094",
	//         "matchId": "400017129522773178",
	//         "marketCode": "ETH-USD-SWAP-LIN",
	//         "side": "BUY",
	//         "matchedQuantity": "0.001",
	//         "matchPrice": "3100.2",
	//         "total": "310.02",
	//         "orderMatchType": "MAKER",
	//         "feeAsset": "OX",
	//         "fee": "0.062004",
	//         "source": "0",
	//         "matchedAt": "1715000267420"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var marketId interface{} = this.SafeString(trade, "marketCode")
	market = this.SafeMarket(marketId, market)
	var symbol interface{} = GetValue(market, "symbol")
	var timestamp interface{} = this.SafeInteger(trade, "matchedAt")
	var fee interface{} = map[string]interface{}{
		"cost":     this.SafeString(trade, "fee"),
		"currency": this.SafeCurrencyCode(this.SafeString(trade, "feeAsset")),
	}
	return this.SafeTrade(map[string]interface{}{
		"id":           this.SafeString(trade, "matchId"),
		"timestamp":    timestamp,
		"datetime":     this.Iso8601(timestamp),
		"symbol":       symbol,
		"type":         nil,
		"order":        this.SafeString(trade, "orderId"),
		"side":         this.SafeStringLower(trade, "side"),
		"takerOrMaker": this.SafeStringLower2(trade, "matchType", "orderMatchType"),
		"price":        this.SafeString(trade, "matchPrice"),
		"amount":       this.SafeString2(trade, "matchQuantity", "matchedQuantity"),
		"cost":         nil,
		"fee":          fee,
		"info":         trade,
	}, market)
}

/**
 * @method
 * @name oxfun#fetchBalance
 * @description query for balance and get the amount of funds available for trading or funds locked in orders
 * @see https://docs.ox.fun/?json#get-v3-balances
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {string} [params.asset] currency id, if empty the exchange returns info about all currencies
 * @param {string} [params.subAcc] Name of sub account. If no subAcc is given, then the response contains only the account linked to the API-Key.
 * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
 */
func (this *oxfun) FetchBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes15458 := (<-this.LoadMarkets())
		PanicOnError(retRes15458)

		response := (<-this.PrivateGetV3Balances(params))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "accountId": "106490",
		//                 "name": "main",
		//                 "balances": [
		//                     {
		//                         "asset": "OX",
		//                         "total": "-7.***********",
		//                         "available": "-71.***********",
		//                         "reserved": "0",
		//                         "lastUpdatedAt": "*************"
		//                     },
		//                     {
		//                         "asset": "ETH",
		//                         "total": "0.01",
		//                         "available": "0.01",
		//                         "reserved": "0",
		//                         "lastUpdatedAt": "*************"
		//                     },
		//                     ...
		//                 ]
		//             },
		//             ...
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		var balance interface{} = GetValue(data, 0)
		var subAcc interface{} = this.SafeString(params, "subAcc")
		if IsTrue(!IsEqual(subAcc, nil)) {
			for i := 0; IsLessThan(i, GetArrayLength(data)); i++ {
				var b interface{} = GetValue(data, i)
				var name interface{} = this.SafeString(b, "name")
				if IsTrue(IsEqual(name, subAcc)) {
					balance = b
					break
				}
			}
		}

		ch <- this.ParseBalance(balance)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseBalance(balance interface{}) interface{} {
	//
	//     {
	//         "accountId": "106490",
	//         "name": "main",
	//         "balances": [
	//             {
	//                 "asset": "OX",
	//                 "total": "-7.***********",
	//                 "available": "-71.***********",
	//                 "reserved": "0",
	//                 "lastUpdatedAt": "*************"
	//             },
	//             {
	//                 "asset": "ETH",
	//                 "total": "0.01",
	//                 "available": "0.01",
	//                 "reserved": "0",
	//                 "lastUpdatedAt": "*************"
	//             },
	//             ...
	//         ]
	//     }
	//
	var result interface{} = map[string]interface{}{
		"info": balance,
	}
	var balances interface{} = this.SafeList(balance, "balances", []interface{}{})
	for i := 0; IsLessThan(i, GetArrayLength(balances)); i++ {
		var balanceEntry interface{} = GetValue(balances, i)
		var currencyId interface{} = this.SafeString(balanceEntry, "asset")
		var code interface{} = this.SafeCurrencyCode(currencyId)
		var account interface{} = this.Account()
		AddElementToObject(account, "total", this.SafeString(balanceEntry, "total"))
		AddElementToObject(account, "free", this.SafeString(balanceEntry, "available"))
		AddElementToObject(account, "used", this.SafeString(balanceEntry, "reserved"))
		AddElementToObject(result, code, account)
	}
	return this.SafeBalance(result)
}

/**
 * @method
 * @name oxfun#fetchAccounts
 * @description fetch subaccounts associated with a profile
 * @see https://docs.ox.fun/?json#get-v3-account-names
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [account structures]{@link https://docs.ccxt.com/#/?id=account-structure} indexed by the account type
 */
func (this *oxfun) FetchAccounts(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes16428 := (<-this.LoadMarkets())
		PanicOnError(retRes16428)
		// this endpoint can only be called using API keys paired with the parent account! Returns all active subaccounts.

		response := (<-this.PrivateGetV3AccountNames(params))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "accountId": "106526",
		//                 "name": "testSubAccount"
		//             },
		//             ...
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseAccounts(data, params)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseAccount(account interface{}) interface{} {
	//
	//     {
	//         "accountId": "106526",
	//         "name": "testSubAccount"
	//     },
	//
	return map[string]interface{}{
		"id":   this.SafeString(account, "accountId"),
		"type": nil,
		"code": nil,
		"info": account,
	}
}

/**
 * @method
 * @name oxfun#transfer
 * @description transfer currency internally between wallets on the same account
 * @see https://docs.ox.fun/?json#post-v3-transfer
 * @param {string} code unified currency code
 * @param {float} amount amount to transfer
 * @param {string} fromAccount account id to transfer from
 * @param {string} toAccount account id to transfer to
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transfer structure]{@link https://docs.ccxt.com/#/?id=transfer-structure}
 */
func (this *oxfun) Transfer(code interface{}, amount interface{}, fromAccount interface{}, toAccount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		// transferring funds between sub-accounts is restricted to API keys linked to the parent account.
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes16908 := (<-this.LoadMarkets())
		PanicOnError(retRes16908)
		var currency interface{} = this.Currency(code)
		var request interface{} = map[string]interface{}{
			"asset":       GetValue(currency, "id"),
			"quantity":    this.CurrencyToPrecision(code, amount),
			"fromAccount": fromAccount,
			"toAccount":   toAccount,
		}

		response := (<-this.PrivatePostV3Transfer(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         timestamp: *************,
		//         datetime: '2024-05-11T12:20:36.267Z',
		//         currency: 'OX',
		//         amount: 10,
		//         fromAccount: '106464',
		//         toAccount: '106570',
		//         info: {
		//         asset: 'OX',
		//         quantity: '10',
		//         fromAccount: '106464',
		//         toAccount: '106570',
		//         transferredAt: '*************'
		//         }
		//     }
		//
		var data interface{} = this.SafeDict(response, "data", map[string]interface{}{})

		ch <- this.ParseTransfer(data, currency)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchTransfers
 * @description fetch a history of internal transfers made on an account
 * @see https://docs.ox.fun/?json#get-v3-transfer
 * @param {string} code unified currency code of the currency transferred
 * @param {int} [since] the earliest time in ms to fetch transfers for (default 24 hours ago)
 * @param {int} [limit] the maximum number of transfer structures to retrieve (default 50, max 200)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] the latest time in ms to fetch transfers for (default time now)
 * @returns {object[]} a list of [transfer structures]{@link https://docs.ccxt.com/#/?id=transfer-structure}
 */
func (this *oxfun) FetchTransfers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		// API keys linked to the parent account can get all account transfers, while API keys linked to a sub-account can only see transfers where the sub-account is either the "fromAccount" or "toAccount"
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes17348 := (<-this.LoadMarkets())
		PanicOnError(retRes17348)
		var request interface{} = map[string]interface{}{}
		var currency interface{} = nil
		if IsTrue(!IsEqual(code, nil)) {
			currency = this.Currency(code)
			AddElementToObject(request, "asset", GetValue(currency, "id"))
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "startTime", since) // startTime and endTime must be within 7 days of each other
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var until interface{} = this.SafeInteger(params, "until")
		if IsTrue(!IsEqual(until, nil)) {
			AddElementToObject(request, "endTime", until)
			params = this.Omit(params, "until")
		} else if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "endTime", this.Sum(since, Multiply(Multiply(Multiply(Multiply(7, 24), 60), 60), 1000))) // for the exchange not to throw an exception if since is younger than 7 days
		}

		response := (<-this.PrivateGetV3Transfer(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "asset": "USDT",
		//                 "quantity": "5",
		//                 "fromAccount": "106490",
		//                 "toAccount": "106526",
		//                 "id": "966706320886267905",
		//                 "status": "COMPLETED",
		//                 "transferredAt": "*************"
		//             },
		//             ...
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseTransfers(data, currency, since, limit)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseTransfer(transfer interface{}, optionalArgs ...interface{}) interface{} {
	//
	// fetchTransfers
	//
	//     {
	//         "asset": "USDT",
	//         "quantity": "5",
	//         "fromAccount": "106490",
	//         "toAccount": "106526",
	//         "id": "966706320886267905",
	//         "status": "COMPLETED",
	//         "transferredAt": "*************"
	//     }
	//
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var timestamp interface{} = this.SafeInteger(transfer, "transferredAt")
	var currencyId interface{} = this.SafeString(transfer, "asset")
	return map[string]interface{}{
		"id":          this.SafeString(transfer, "id"),
		"timestamp":   timestamp,
		"datetime":    this.Iso8601(timestamp),
		"currency":    this.SafeCurrencyCode(currencyId, currency),
		"amount":      this.SafeNumber(transfer, "quantity"),
		"fromAccount": this.SafeString(transfer, "fromAccount"),
		"toAccount":   this.SafeString(transfer, "toAccount"),
		"status":      this.ParseTransferStatus(this.SafeString(transfer, "status")),
		"info":        transfer,
	}
}
func (this *oxfun) ParseTransferStatus(status interface{}) interface{} {
	var statuses interface{} = map[string]interface{}{
		"COMPLETED": "ok",
	}
	return this.SafeString(statuses, status, status)
}

/**
 * @method
 * @name oxfun#fetchDepositAddress
 * @description fetch the deposit address for a currency associated with this account
 * @see https://docs.ox.fun/?json#get-v3-deposit-addresses
 * @param {string} code unified currency code
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {string} [params.network] network for fetch deposit address
 * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
 */
func (this *oxfun) FetchDepositAddress(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params
		var networkCode interface{} = this.SafeString(params, "network")
		var networkId interface{} = this.NetworkCodeToId(networkCode, code)
		if IsTrue(IsEqual(networkId, nil)) {
			panic(BadRequest(Add(this.Id, " fetchDepositAddress() require network parameter")))
		}

		retRes18288 := (<-this.LoadMarkets())
		PanicOnError(retRes18288)
		var currency interface{} = this.Currency(code)
		var request interface{} = map[string]interface{}{
			"asset":   GetValue(currency, "id"),
			"network": networkId,
		}
		params = this.Omit(params, "network")

		response := (<-this.PrivateGetV3DepositAddresses(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {"success":true,"data":{"address":"******************************************"}}
		//
		var data interface{} = this.SafeDict(response, "data", map[string]interface{}{})

		ch <- this.ParseDepositAddress(data, currency)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseDepositAddress(depositAddress interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {"address":"******************************************"}
	//
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var address interface{} = this.SafeString(depositAddress, "address")
	this.CheckAddress(address)
	return map[string]interface{}{
		"info":     depositAddress,
		"currency": GetValue(currency, "code"),
		"network":  nil,
		"address":  address,
		"tag":      nil,
	}
}

/**
 * @method
 * @name oxfun#fetchDeposits
 * @description fetch all deposits made to an account
 * @see https://docs.ox.fun/?json#get-v3-deposit
 * @param {string} code unified currency code of the currency transferred
 * @param {int} [since] the earliest time in ms to fetch transfers for (default 24 hours ago)
 * @param {int} [limit] the maximum number of transfer structures to retrieve (default 50, max 200)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] the latest time in ms to fetch transfers for (default time now)
 * @returns {object[]} a list of [transfer structures]{@link https://docs.ccxt.com/#/?id=transfer-structure}
 */
func (this *oxfun) FetchDeposits(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes18718 := (<-this.LoadMarkets())
		PanicOnError(retRes18718)
		var request interface{} = map[string]interface{}{}
		var currency interface{} = nil
		if IsTrue(!IsEqual(code, nil)) {
			currency = this.Currency(code)
			AddElementToObject(request, "asset", GetValue(currency, "id"))
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "startTime", since) // startTime and endTime must be within 7 days of each other
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var until interface{} = this.SafeInteger(params, "until")
		if IsTrue(!IsEqual(until, nil)) {
			AddElementToObject(request, "endTime", until)
			params = this.Omit(params, "until")
		}

		response := (<-this.PrivateGetV3Deposit(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "asset":"USDC",
		//                 "network":"Ethereum",
		//                 "address": "******************************************",
		//                 "quantity":"50",
		//                 "id":"5914",
		//                 "status": "COMPLETED",
		//                 "txId":"0xf5e79663830a0c6f94d46638dcfbc134566c12facf1832396f81ecb55d3c75dc",
		//                 "creditedAt":"*************"
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		for i := 0; IsLessThan(i, GetArrayLength(data)); i++ {
			AddElementToObject(GetValue(data, i), "type", "deposit")
		}

		ch <- this.ParseTransactions(data, currency, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchWithdrawals
 * @description fetch all withdrawals made from an account
 * @see https://docs.ox.fun/?json#get-v3-withdrawal
 * @param {string} code unified currency code of the currency transferred
 * @param {int} [since] the earliest time in ms to fetch transfers for (default 24 hours ago)
 * @param {int} [limit] the maximum number of transfer structures to retrieve (default 50, max 200)
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.until] the latest time in ms to fetch transfers for (default time now)
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *oxfun) FetchWithdrawals(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes19278 := (<-this.LoadMarkets())
		PanicOnError(retRes19278)
		var request interface{} = map[string]interface{}{}
		var currency interface{} = nil
		if IsTrue(!IsEqual(code, nil)) {
			currency = this.Currency(code)
			AddElementToObject(request, "asset", GetValue(currency, "id"))
		}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "startTime", since) // startTime and endTime must be within 7 days of each other
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var until interface{} = this.SafeInteger(params, "until")
		if IsTrue(!IsEqual(until, nil)) {
			AddElementToObject(request, "endTime", until)
			params = this.Omit(params, "until")
		}

		response := (<-this.PrivateGetV3Withdrawal(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         success: true,
		//         data: [
		//             {
		//                 id: '968163212989431811',
		//                 asset: 'OX',
		//                 network: 'Arbitrum',
		//                 address: '******************************************',
		//                 quantity: '11.7444',
		//                 fee: '1.744400000',
		//                 status: 'COMPLETED',
		//                 txId: '0xe96b2d128b737fdbca927edf355cff42202e65b0fb960e64ffb9bd68c121f69f',
		//                 requestedAt: '1715530365450',
		//                 completedAt: '1715530527000'
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		for i := 0; IsLessThan(i, GetArrayLength(data)); i++ {
			AddElementToObject(GetValue(data, i), "type", "withdrawal")
		}

		ch <- this.ParseTransactions(data, currency, since, limit)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseTransactions(transactions interface{}, optionalArgs ...interface{}) interface{} {
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	since := GetArg(optionalArgs, 1, nil)
	_ = since
	limit := GetArg(optionalArgs, 2, nil)
	_ = limit
	params := GetArg(optionalArgs, 3, map[string]interface{}{})
	_ = params
	var result interface{} = []interface{}{}
	for i := 0; IsLessThan(i, GetArrayLength(transactions)); i++ {
		AddElementToObject(transactions, i, this.Extend(GetValue(transactions, i), params))
		var transaction interface{} = this.ParseTransaction(GetValue(transactions, i), currency)
		AppendToArray(&result, transaction)
	}
	result = this.SortBy(result, "timestamp")
	var code interface{} = Ternary(IsTrue((!IsEqual(currency, nil))), GetValue(currency, "code"), nil)
	return this.FilterByCurrencySinceLimit(result, code, since, limit)
}
func (this *oxfun) ParseTransaction(transaction interface{}, optionalArgs ...interface{}) interface{} {
	//
	//  fetchDeposits
	//     {
	//         "asset":"USDC",
	//         "network":"Ethereum",
	//         "address": "******************************************",
	//         "quantity":"50",
	//         "id":"5914",
	//         "status": "COMPLETED",
	//         "txId":"0xf5e79663830a0c6f94d46638dcfbc134566c12facf1832396f81ecb55d3c75dc",
	//         "creditedAt":"*************"
	//     }
	//
	// fetchWithdrawals
	//     {
	//         id: '968163212989431811',
	//         asset: 'OX',
	//         network: 'Arbitrum',
	//         address: '******************************************',
	//         quantity: '11.7444',
	//         fee: '1.744400000',
	//         status: 'COMPLETED',
	//         txId: '0xe96b2d128b737fdbca927edf355cff42202e65b0fb960e64ffb9bd68c121f69f',
	//         requestedAt: '1715530365450',
	//         completedAt: '1715530527000'
	//     }
	//
	// withdraw
	//     {
	//         "id": "968364664449302529",
	//         "asset": "OX",
	//         "network": "Arbitrum",
	//         "address": "******************************************",
	//         "quantity": "10",
	//         "externalFee": false,
	//         "fee": "1.6728",
	//         "status": "PENDING",
	//         "requestedAt": "1715591843616"
	//     }
	//
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var id interface{} = this.SafeString(transaction, "id")
	var typeVar interface{} = this.SafeString(transaction, "type")
	transaction = this.Omit(transaction, "type")
	var address interface{} = nil
	var addressTo interface{} = nil
	var status interface{} = nil
	if IsTrue(IsEqual(typeVar, "deposit")) {
		address = this.SafeString(transaction, "address")
		status = this.ParseDepositStatus(this.SafeString(transaction, "status"))
	} else if IsTrue(IsEqual(typeVar, "withdrawal")) {
		addressTo = this.SafeString(transaction, "address")
		status = this.ParseWithdrawalStatus(this.SafeString(transaction, "status"))
	}
	var txid interface{} = this.SafeString(transaction, "txId")
	var currencyId interface{} = this.SafeString(transaction, "asset")
	var code interface{} = this.SafeCurrencyCode(currencyId, currency)
	var network interface{} = this.SafeString(transaction, "network")
	var networkCode interface{} = this.NetworkIdToCode(network)
	var timestamp interface{} = this.SafeInteger2(transaction, "creditedAt", "requestedAt")
	var amount interface{} = this.SafeNumber(transaction, "quantity")
	var feeCost interface{} = this.SafeNumber(transaction, "fee")
	var fee interface{} = nil
	if IsTrue(!IsEqual(feeCost, nil)) {
		fee = map[string]interface{}{
			"cost":     feeCost,
			"currency": code,
		}
	}
	return map[string]interface{}{
		"info":        transaction,
		"id":          id,
		"txid":        txid,
		"timestamp":   timestamp,
		"datetime":    this.Iso8601(timestamp),
		"network":     networkCode,
		"address":     address,
		"addressTo":   addressTo,
		"addressFrom": nil,
		"tag":         nil,
		"tagTo":       nil,
		"tagFrom":     nil,
		"type":        typeVar,
		"amount":      amount,
		"currency":    code,
		"status":      status,
		"updated":     nil,
		"internal":    nil,
		"comment":     nil,
		"fee":         fee,
	}
}
func (this *oxfun) ParseDepositStatus(status interface{}) interface{} {
	var statuses interface{} = map[string]interface{}{
		"COMPLETED": "ok",
	}
	return this.SafeString(statuses, status, status)
}
func (this *oxfun) ParseWithdrawalStatus(status interface{}) interface{} {
	var statuses interface{} = map[string]interface{}{
		"COMPLETED":   "ok",
		"PROCESSING":  "pending",
		"IN SWEEPING": "pending",
		"PENDING":     "pending",
		"ON HOLD":     "pending",
		"CANCELED":    "canceled",
		"FAILED":      "failed",
	}
	return this.SafeString(statuses, status, status)
}

/**
 * @method
 * @name oxfun#withdraw
 * @description make a withdrawal
 * @see https://docs.ox.fun/?json#post-v3-withdrawal
 * @param {string} code unified currency code
 * @param {float} amount the amount to withdraw
 * @param {string} address the address to withdraw to
 * @param {string} tag
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {string} [params.network] network for withdraw
 * @param {bool} [params.externalFee] if false, then the fee is taken from the quantity, also with the burn fee for asset SOLO
 *
 * EXCHANGE SPECIFIC PARAMETERS
 * @param {string} [params.tfaType] GOOGLE, or AUTHY_SECRET, or YUBIKEY, for 2FA
 * @param {string} [params.code] 2FA code
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *oxfun) Withdraw(code interface{}, amount interface{}, address interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		tag := GetArg(optionalArgs, 0, nil)
		_ = tag
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		tagparamsVariable := this.HandleWithdrawTagAndParams(tag, params)
		tag = GetValue(tagparamsVariable, 0)
		params = GetValue(tagparamsVariable, 1)

		retRes21178 := (<-this.LoadMarkets())
		PanicOnError(retRes21178)
		var currency interface{} = this.Currency(code)
		var stringAmount interface{} = this.CurrencyToPrecision(code, amount)
		var request interface{} = map[string]interface{}{
			"asset":    GetValue(currency, "id"),
			"address":  address,
			"quantity": stringAmount,
		}
		if IsTrue(!IsEqual(tag, nil)) {
			AddElementToObject(request, "memo", tag)
		}
		var networkCode interface{} = nil
		networkCodeparamsVariable := this.HandleNetworkCodeAndParams(params)
		networkCode = GetValue(networkCodeparamsVariable, 0)
		params = GetValue(networkCodeparamsVariable, 1)
		if IsTrue(!IsEqual(networkCode, nil)) {
			AddElementToObject(request, "network", this.NetworkCodeToId(networkCode))
		}
		AddElementToObject(request, "externalFee", false)

		response := (<-this.PrivatePostV3Withdrawal(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": {
		//             "id": "968364664449302529",
		//             "asset": "OX",
		//             "network": "Arbitrum",
		//             "address": "******************************************",
		//             "quantity": "10",
		//             "externalFee": false,
		//             "fee": "1.6728",
		//             "status": "PENDING",
		//             "requestedAt": "1715591843616"
		//         }
		//     }
		//
		var data interface{} = this.SafeDict(response, "data", map[string]interface{}{})
		AddElementToObject(data, "type", "withdrawal")

		ch <- this.ParseTransaction(data, currency)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchPositions
 * @description fetch all open positions
 * @see https://docs.ox.fun/?json#get-v3-positions
 * @param {string[]|undefined} symbols list of unified market symbols
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {boolean} [params.subAcc]
 * @returns {object[]} a list of [position structure]{@link https://docs.ccxt.com/#/?id=position-structure}
 */
func (this *oxfun) FetchPositions(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		// Calling this endpoint using an API key pair linked to the parent account with the parameter "subAcc"
		// allows the caller to include positions of additional sub-accounts in the response.
		// This feature does not work when using API key pairs linked to a sub-account
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes21708 := (<-this.LoadMarkets())
		PanicOnError(retRes21708)
		symbols = this.MarketSymbols(symbols)

		response := (<-this.PrivateGetV3Positions(params))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "accountId": "106490",
		//                 "name": "main",
		//                 "positions": [
		//                     {
		//                         "marketCode": "BTC-USD-SWAP-LIN",
		//                         "baseAsset": "BTC",
		//                         "counterAsset": "USD",
		//                         "position": "0.00010",
		//                         "entryPrice": "64300.0",
		//                         "markPrice": "63278",
		//                         "positionPnl": "-10.1900",
		//                         "estLiquidationPrice": "0",
		//                         "lastUpdatedAt": "*************"
		//                     },
		//                     ...
		//                 ]
		//             },
		//             {
		//                 "accountId": "106526",
		//                 "name": "testSubAccount",
		//                 "positions": [
		//                     {
		//                         "marketCode": "ETH-USD-SWAP-LIN",
		//                         "baseAsset": "ETH",
		//                         "counterAsset": "USD",
		//                         "position": "0.001",
		//                         "entryPrice": "3080.5",
		//                         "markPrice": "3062.0",
		//                         "positionPnl": "-1.8500",
		//                         "estLiquidationPrice": "0",
		//                         "lastUpdatedAt": "*************"
		//                     },
		//                     ...
		//                 ]
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		var allPositions interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(data)); i++ {
			var account interface{} = GetValue(data, i)
			var positions interface{} = this.SafeList(account, "positions", []interface{}{})
			allPositions = this.ArrayConcat(allPositions, positions)
		}

		ch <- this.ParsePositions(allPositions, symbols)
		return nil

	}()
	return ch
}
func (this *oxfun) ParsePosition(position interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "marketCode": "ETH-USD-SWAP-LIN",
	//         "baseAsset": "ETH",
	//         "counterAsset": "USD",
	//         "position": "0.001",
	//         "entryPrice": "3080.5",
	//         "markPrice": "3062.0",
	//         "positionPnl": "-1.8500",
	//         "estLiquidationPrice": "0",
	//         "lastUpdatedAt": "*************"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var marketId interface{} = this.SafeString(position, "marketCode")
	market = this.SafeMarket(marketId, market)
	return this.SafePosition(map[string]interface{}{
		"info":                        position,
		"id":                          nil,
		"symbol":                      GetValue(market, "symbol"),
		"notional":                    nil,
		"marginMode":                  "cross",
		"liquidationPrice":            this.SafeNumber(position, "estLiquidationPrice"),
		"entryPrice":                  this.SafeNumber(position, "entryPrice"),
		"unrealizedPnl":               this.SafeNumber(position, "positionPnl"),
		"realizedPnl":                 nil,
		"percentage":                  nil,
		"contracts":                   this.SafeNumber(position, "position"),
		"contractSize":                nil,
		"markPrice":                   this.SafeNumber(position, "markPrice"),
		"lastPrice":                   nil,
		"side":                        nil,
		"hedged":                      nil,
		"timestamp":                   nil,
		"datetime":                    nil,
		"lastUpdateTimestamp":         this.SafeInteger(position, "lastUpdatedAt"),
		"maintenanceMargin":           nil,
		"maintenanceMarginPercentage": nil,
		"collateral":                  nil,
		"initialMargin":               nil,
		"initialMarginPercentage":     nil,
		"leverage":                    nil,
		"marginRatio":                 nil,
		"stopLossPrice":               nil,
		"takeProfitPrice":             nil,
	})
}

/**
 * @method
 * @name oxfun#createOrder
 * @description create a trade order
 * @see https://docs.ox.fun/?json#post-v3-orders-place
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type 'market', 'limit', 'STOP_LIMIT' or 'STOP_MARKET'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much of currency you want to trade in units of base currency
 * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.clientOrderId] a unique id for the order
 * @param {int} [params.timestamp] in milliseconds. If an order reaches the matching engine and the current timestamp exceeds timestamp + recvWindow, then the order will be rejected.
 * @param {int} [params.recvWindow] in milliseconds. If an order reaches the matching engine and the current timestamp exceeds timestamp + recvWindow, then the order will be rejected. If timestamp is provided without recvWindow, then a default recvWindow of 1000ms is used.
 * @param {string} [params.responseType] FULL or ACK
 * @param {float} [params.cost] the quote quantity that can be used as an alternative for the amount for market buy orders
 * @param {float} [params.triggerPrice] The price at which a trigger order is triggered at
 * @param {float} [params.limitPrice] Limit price for the STOP_LIMIT order
 * @param {bool} [params.postOnly] if true, the order will only be posted if it will be a maker order
 * @param {string} [params.timeInForce] GTC (default), IOC, FOK, PO, MAKER_ONLY or MAKER_ONLY_REPRICE (reprices order to the best maker only price if the specified price were to lead to a taker trade)
 * @param {string} [params.selfTradePreventionMode] NONE, EXPIRE_MAKER, EXPIRE_TAKER or EXPIRE_BOTH for more info check here {@link https://docs.ox.fun/?json#self-trade-prevention-modes}
 * @param {string} [params.displayQuantity] for an iceberg order, pass both quantity and displayQuantity fields in the order request
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *oxfun) CreateOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes22998 := (<-this.LoadMarkets())
		PanicOnError(retRes22998)
		var request interface{} = map[string]interface{}{
			"responseType": this.SafeString(params, "responseType", "FULL"),
			"timestamp":    this.SafeInteger(params, "timestamp", this.Milliseconds()),
		}
		params = this.Omit(params, []interface{}{"responseType", "timestamp"})
		var recvWindow interface{} = this.SafeInteger(params, "recvWindow")
		if IsTrue(!IsEqual(recvWindow, nil)) {
			AddElementToObject(request, "recvWindow", recvWindow)
			params = this.Omit(params, "recvWindow")
		}
		var orderRequest interface{} = this.CreateOrderRequest(symbol, typeVar, side, amount, price, params)
		AddElementToObject(request, "orders", []interface{}{orderRequest})

		response := (<-this.PrivatePostV3OrdersPlace(request))
		PanicOnError(response)
		//
		// accepted market order responseType FULL
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "notice": "OrderMatched",
		//                 "accountId": "106490",
		//                 "orderId": "*************",
		//                 "submitted": true,
		//                 "clientOrderId": "0",
		//                 "marketCode": "OX-USDT",
		//                 "status": "FILLED",
		//                 "side": "SELL",
		//                 "isTriggered": false,
		//                 "quantity": "150.0",
		//                 "amount": "0.0",
		//                 "remainQuantity": "0.0",
		//                 "matchId": "100017047880451399",
		//                 "matchPrice": "0.01465",
		//                 "matchQuantity": "150.0",
		//                 "feeInstrumentId": "USDT",
		//                 "fees": "0.**********",
		//                 "orderType": "MARKET",
		//                 "createdAt": "*************",
		//                 "lastMatchedAt": "*************",
		//                 "displayQuantity": "150.0"
		//             }
		//         ]
		//     }
		//
		// accepted limit order responseType FULL
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "notice": "OrderOpened",
		//                 "accountId": "106490",
		//                 "orderId": "*************",
		//                 "submitted": true,
		//                 "clientOrderId": "0",
		//                 "marketCode": "ETH-USD-SWAP-LIN",
		//                 "status": "OPEN",
		//                 "side": "SELL",
		//                 "price": "4000.0",
		//                 "isTriggered": false,
		//                 "quantity": "0.01",
		//                 "amount": "0.0",
		//                 "orderType": "LIMIT",
		//                 "timeInForce": "GTC",
		//                 "createdAt": "*************",
		//                 "displayQuantity": "0.01"
		//             }
		//         ]
		//     }
		//
		// accepted order responseType ACK
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "accountId": "106490",
		//                 "orderId": "*************",
		//                 "submitted": true,
		//                 "marketCode": "OX-USDT",
		//                 "side": "BUY",
		//                 "price": "0.01961",
		//                 "isTriggered": false,
		//                 "quantity": "100",
		//                 "orderType": "MARKET",
		//                 "timeInForce": "IOC",
		//                 "createdAt": "*************",
		//                 "selfTradePreventionMode": "NONE"
		//             }
		//         ]
		//     }
		//
		//  rejected order (balance insufficient)
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "code": "710001",
		//                 "message": "System failure, exception thrown -> null",
		//                 "submitted": false,
		//                 "marketCode": "OX-USDT",
		//                 "side": "BUY",
		//                 "price": "0.01961",
		//                 "amount": "100",
		//                 "orderType": "MARKET",
		//                 "timeInForce": "IOC",
		//                 "createdAt": "*************",
		//                 "source": 11,
		//                 "selfTradePreventionMode": "NONE"
		//             }
		//         ]
		//     }
		//
		// rejected order (bad request)
		//     {
		//         "success": true,
		//         "data": [
		//             {
		//                 "code": "20044",
		//                 "message": "Amount is not supported for this order type",
		//                 "submitted": false,
		//                 "marketCode": "OX-USDT",
		//                 "side": "SELL",
		//                 "amount": "200",
		//                 "orderType": "MARKET",
		//                 "createdAt": "1715592079986",
		//                 "source": 11
		//             }
		//         ]
		//     }
		//
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		var order interface{} = this.SafeDict(data, 0, map[string]interface{}{})

		ch <- this.ParseOrder(order)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#createOrders
 * @description create a list of trade orders
 * @see https://docs.ox.fun/?json#post-v3-orders-place
 * @param {Array} orders list of orders to create, each object should contain the parameters required by createOrder, namely symbol, type, side, amount, price and params
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.timestamp] *for all orders* in milliseconds. If orders reach the matching engine and the current timestamp exceeds timestamp + recvWindow, then all orders will be rejected.
 * @param {int} [params.recvWindow] *for all orders* in milliseconds. If orders reach the matching engine and the current timestamp exceeds timestamp + recvWindow, then all orders will be rejected. If timestamp is provided without recvWindow, then a default recvWindow of 1000ms is used.
 * @param {string} [params.responseType] *for all orders* FULL or ACK
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *oxfun) CreateOrders(orders interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes24478 := (<-this.LoadMarkets())
		PanicOnError(retRes24478)
		var ordersRequests interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(orders)); i++ {
			var rawOrder interface{} = GetValue(orders, i)
			var symbol interface{} = this.SafeString(rawOrder, "symbol")
			var typeVar interface{} = this.SafeString(rawOrder, "type")
			var side interface{} = this.SafeString(rawOrder, "side")
			var amount interface{} = this.SafeNumber(rawOrder, "amount")
			var price interface{} = this.SafeNumber(rawOrder, "price")
			var orderParams interface{} = this.SafeDict(rawOrder, "params", map[string]interface{}{})
			var orderRequest interface{} = this.CreateOrderRequest(symbol, typeVar, side, amount, price, orderParams)
			AppendToArray(&ordersRequests, orderRequest)
		}
		var request interface{} = map[string]interface{}{
			"responseType": "FULL",
			"timestamp":    this.Milliseconds(),
			"orders":       ordersRequests,
		}

		response := (<-this.PrivatePostV3OrdersPlace(this.Extend(request, params)))
		PanicOnError(response)
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseOrders(data)
		return nil

	}()
	return ch
}
func (this *oxfun) CreateOrderRequest(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) interface{} {
	/**
	 * @param {string} symbol unified symbol of the market to create an order in
	 * @param {string} type 'market', 'limit', 'STOP_LIMIT' or 'STOP_MARKET'
	 * @param {string} side 'buy' or 'sell'
	 * @param {float} amount how much of currency you want to trade in units of base currency
	 * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
	 * @param {object} [params] extra parameters specific to the exchange API endpoint
	 * @param {int} [params.clientOrderId] a unique id for the order
	 * @param {float} [params.cost] the quote quantity that can be used as an alternative for the amount for market buy orders
	 * @param {float} [params.triggerPrice] The price at which a trigger order is triggered at
	 * @param {float} [params.limitPrice] Limit price for the STOP_LIMIT order
	 * @param {bool} [params.postOnly] if true, the order will only be posted if it will be a maker order
	 * @param {string} [params.timeInForce] GTC (default), IOC, FOK, PO, MAKER_ONLY or MAKER_ONLY_REPRICE (reprices order to the best maker only price if the specified price were to lead to a taker trade)
	 * @param {string} [params.selfTradePreventionMode] NONE, EXPIRE_MAKER, EXPIRE_TAKER or EXPIRE_BOTH for more info check here {@link https://docs.ox.fun/?json#self-trade-prevention-modes}
	 * @param {string} [params.displayQuantity] for an iceberg order, pass both quantity and displayQuantity fields in the order request
	 */
	price := GetArg(optionalArgs, 0, nil)
	_ = price
	params := GetArg(optionalArgs, 1, map[string]interface{}{})
	_ = params
	var market interface{} = this.Market(symbol)
	var request interface{} = map[string]interface{}{
		"marketCode": GetValue(market, "id"),
		"side":       ToUpper(side),
		"source":     1000,
	}
	var cost interface{} = this.SafeString2(params, "cost", "amount")
	if IsTrue(!IsEqual(cost, nil)) {
		AddElementToObject(request, "amount", cost) // todo costToPrecision
		params = this.Omit(params, []interface{}{"cost", "amount"})
	} else {
		AddElementToObject(request, "quantity", amount) // todo amountToPrecision
	}
	var triggerPrice interface{} = this.SafeString2(params, "triggerPrice", "stopPrice")
	var orderType interface{} = ToUpper(typeVar)
	if IsTrue(!IsEqual(triggerPrice, nil)) {
		if IsTrue(IsEqual(orderType, "MARKET")) {
			orderType = "STOP_MARKET"
		} else if IsTrue(IsEqual(orderType, "LIMIT")) {
			orderType = "STOP_LIMIT"
		}
		AddElementToObject(request, "stopPrice", triggerPrice) // todo priceToPrecision
		params = this.Omit(params, []interface{}{"triggerPrice", "stopPrice"})
	}
	AddElementToObject(request, "orderType", orderType)
	if IsTrue(IsEqual(orderType, "STOP_LIMIT")) {
		AddElementToObject(request, "limitPrice", price) // todo priceToPrecision
	} else if IsTrue(!IsEqual(price, nil)) {
		AddElementToObject(request, "price", price) // todo priceToPrecision
	}
	var postOnly interface{} = nil
	var isMarketOrder interface{} = IsTrue((IsEqual(orderType, "MARKET"))) || IsTrue((IsEqual(orderType, "STOP_MARKET")))
	postOnlyparamsVariable := this.HandlePostOnly(isMarketOrder, false, params)
	postOnly = GetValue(postOnlyparamsVariable, 0)
	params = GetValue(postOnlyparamsVariable, 1)
	var timeInForce interface{} = this.SafeStringUpper(params, "timeInForce")
	if IsTrue(IsTrue(postOnly) && IsTrue((!IsEqual(timeInForce, "MAKER_ONLY_REPRICE")))) {
		AddElementToObject(request, "timeInForce", "MAKER_ONLY")
	}
	return this.Extend(request, params)
}

/**
 * @method
 * @name oxfun#createMarketBuyOrderWithCost
 * @description create a market buy order by providing the symbol and cost
 * @see https://open.big.one/docs/spot_orders.html#create-order
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {float} cost how much you want to trade in units of the quote currency
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *oxfun) CreateMarketBuyOrderWithCost(symbol interface{}, cost interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes25388 := (<-this.LoadMarkets())
		PanicOnError(retRes25388)
		var market interface{} = this.Market(symbol)
		if !IsTrue(GetValue(market, "spot")) {
			panic(NotSupported(Add(this.Id, " createMarketBuyOrderWithCost() supports spot orders only")))
		}
		var request interface{} = map[string]interface{}{
			"cost": cost,
		}

		retRes254615 := (<-this.CreateOrder(symbol, "market", "buy", nil, nil, this.Extend(request, params)))
		PanicOnError(retRes254615)
		ch <- retRes254615
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchOrder
 * @see https://docs.ox.fun/?json#get-v3-orders-status
 * @description fetches information on an order made by the user
 * @param {string} id a unique id for the order
 * @param {string} [symbol] not used by oxfun fetchOrder
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.clientOrderId] the client order id of the order
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *oxfun) FetchOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes25618 := (<-this.LoadMarkets())
		PanicOnError(retRes25618)
		var request interface{} = map[string]interface{}{
			"orderId": id,
		}

		response := (<-this.PrivateGetV3OrdersStatus(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "success": true,
		//         "data": {
		//             "orderId": "1000111762980",
		//             "clientOrderId": "0",
		//             "marketCode": "ETH-USD-SWAP-LIN",
		//             "status": "OPEN",
		//             "side": "BUY",
		//             "price": "2700.0",
		//             "isTriggered": false,
		//             "remainQuantity": "0.01",
		//             "totalQuantity": "0.01",
		//             "amount": "0",
		//             "displayQuantity": "0.01",
		//             "cumulativeMatchedQuantity": "0",
		//             "orderType": "STOP_LIMIT",
		//             "timeInForce": "GTC",
		//             "source": "11",
		//             "createdAt": "1715794191277"
		//         }
		//     }
		//
		var data interface{} = this.SafeDict(response, "data", map[string]interface{}{})

		ch <- this.ParseOrder(data)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#fetchOpenOrders
 * @description fetch all unfilled currently open orders
 * @see https://docs.ox.fun/?json#get-v3-orders-working
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch open orders for
 * @param {int} [limit] the maximum number of  open orders structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.orderId] a unique id for the order
 * @param {int} [params.clientOrderId] the client order id of the order
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *oxfun) FetchOpenOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes26078 := (<-this.LoadMarkets())
		PanicOnError(retRes26078)
		var request interface{} = map[string]interface{}{}
		var market interface{} = nil
		if IsTrue(!IsEqual(symbol, nil)) {
			market = this.Market(symbol)
		}

		response := (<-this.PrivateGetV3OrdersWorking(this.Extend(request, params)))
		PanicOnError(response)
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseOrders(data, market, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#cancelOrder
 * @description cancels an open order
 * @see https://docs.ox.fun/?json#delete-v3-orders-cancel
 * @param {string} id order id
 * @param {string} symbol unified symbol of the market the order was made in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.clientOrderId] a unique id for the order
 * @param {int} [params.timestamp] in milliseconds
 * @param {int} [params.recvWindow] in milliseconds
 * @param {string} [params.responseType] 'FULL' or 'ACK'
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *oxfun) CancelOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(symbol, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " cancelOrder() requires a symbol argument")))
		}
		var market interface{} = this.Market(symbol)
		var marketId interface{} = GetValue(market, "id")
		var request interface{} = map[string]interface{}{
			"timestamp":    this.Milliseconds(),
			"responseType": "FULL",
		}
		var orderRequest interface{} = map[string]interface{}{
			"marketCode": marketId,
			"orderId":    id,
		}
		var clientOrderId interface{} = this.SafeInteger(params, "clientOrderId")
		if IsTrue(!IsEqual(clientOrderId, nil)) {
			AddElementToObject(orderRequest, "clientOrderId", clientOrderId)
		}
		AddElementToObject(request, "orders", []interface{}{orderRequest})

		response := (<-this.PrivateDeleteV3OrdersCancel(this.Extend(request, params)))
		PanicOnError(response)
		var data interface{} = this.SafeList(response, "data", []interface{}{})
		var order interface{} = this.SafeDict(data, 0, map[string]interface{}{})

		ch <- this.ParseOrder(order)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#cancelAllOrders
 * @description cancel all open orders
 * @see https://docs.ox.fun/?json#delete-v3-orders-cancel-all
 * @param {string} symbol unified market symbol, only orders in the market of this symbol are cancelled when symbol is not undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} response from exchange
 */
func (this *oxfun) CancelAllOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		var request interface{} = map[string]interface{}{}
		if IsTrue(!IsEqual(symbol, nil)) {
			var market interface{} = this.Market(symbol)
			AddElementToObject(request, "marketCode", GetValue(market, "id"))
		}
		//
		//     {
		//         "success": true,
		//         "data": { "notice": "Orders queued for cancelation" }
		//     }
		//
		//     {
		//         "success": true,
		//         "data": { "notice": "No working orders found" }
		//     }
		//

		response := (<-this.PrivateDeleteV3OrdersCancelAll(this.Extend(request, params)))
		PanicOnError(response)

		ch <- []interface{}{this.SafeOrder(map[string]interface{}{
			"info": response,
		})}
		return nil

	}()
	return ch
}

/**
 * @method
 * @name oxfun#cancelOrders
 * @description cancel multiple orders
 * @see https://docs.ox.fun/?json#delete-v3-orders-cancel
 * @param {string[]} ids order ids
 * @param {string} [symbol] unified market symbol
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @param {int} [params.timestamp] in milliseconds
 * @param {int} [params.recvWindow] in milliseconds
 * @param {string} [params.responseType] 'FULL' or 'ACK'
 * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *oxfun) CancelOrders(ids interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		if IsTrue(IsEqual(symbol, nil)) {
			panic(ArgumentsRequired(Add(this.Id, " cancelOrders() requires a symbol argument")))
		}

		retRes27048 := (<-this.LoadMarkets())
		PanicOnError(retRes27048)
		var market interface{} = this.Market(symbol)
		var marketId interface{} = GetValue(market, "id")
		var request interface{} = map[string]interface{}{
			"timestamp":    this.Milliseconds(),
			"responseType": "FULL",
		}
		var orders interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(ids)); i++ {
			var order interface{} = map[string]interface{}{
				"marketCode": marketId,
				"orderId":    GetValue(ids, i),
			}
			AppendToArray(&orders, order)
		}
		AddElementToObject(request, "orders", orders)

		response := (<-this.PrivateDeleteV3OrdersCancel(this.Extend(request, params)))
		PanicOnError(response)
		var data interface{} = this.SafeList(response, "data", []interface{}{})

		ch <- this.ParseOrders(data, market)
		return nil

	}()
	return ch
}
func (this *oxfun) ParseOrder(order interface{}, optionalArgs ...interface{}) interface{} {
	//
	// accepted market order responseType FULL
	//     {
	//         "notice": "OrderMatched",
	//         "accountId": "106490",
	//         "orderId": "*************",
	//         "submitted": true,
	//         "clientOrderId": "0",
	//         "marketCode": "OX-USDT",
	//         "status": "FILLED",
	//         "side": "SELL",
	//         "isTriggered": false,
	//         "quantity": "150.0",
	//         "amount": "0.0",
	//         "remainQuantity": "0.0",
	//         "matchId": "100017047880451399",
	//         "matchPrice": "0.01465",
	//         "matchQuantity": "150.0",
	//         "feeInstrumentId": "USDT",
	//         "fees": "0.**********",
	//         "orderType": "MARKET",
	//         "createdAt": "*************",
	//         "lastMatchedAt": "*************",
	//         "displayQuantity": "150.0"
	//     }
	//
	// accepted limit order responseType FULL
	//     {
	//         "notice": "OrderOpened",
	//         "accountId": "106490",
	//         "orderId": "*************",
	//         "submitted": true,
	//         "clientOrderId": "0",
	//         "marketCode": "ETH-USD-SWAP-LIN",
	//         "status": "OPEN",
	//         "side": "SELL",
	//         "price": "4000.0",
	//         "isTriggered": false,
	//         "quantity": "0.01",
	//         "amount": "0.0",
	//         "orderType": "LIMIT",
	//         "timeInForce": "GTC",
	//         "createdAt": "*************",
	//         "displayQuantity": "0.01"
	//     }
	//
	// accepted order responseType ACK
	//     {
	//         "accountId": "106490",
	//         "orderId": "*************",
	//         "submitted": true,
	//         "marketCode": "OX-USDT",
	//         "side": "BUY",
	//         "price": "0.01961",
	//         "isTriggered": false,
	//         "quantity": "100",
	//         "orderType": "MARKET",
	//         "timeInForce": "IOC",
	//         "createdAt": "*************",
	//         "selfTradePreventionMode": "NONE"
	//     }
	//
	//  rejected order (balance insufficient)
	//     {
	//         "code": "710001",
	//         "message": "System failure, exception thrown -> null",
	//         "submitted": false,
	//         "marketCode": "OX-USDT",
	//         "side": "BUY",
	//         "price": "0.01961",
	//         "amount": "100",
	//         "orderType": "MARKET",
	//         "timeInForce": "IOC",
	//         "createdAt": "*************",
	//         "source": 11,
	//         "selfTradePreventionMode": "NONE"
	//     }
	//
	// rejected order (bad request)
	//     {
	//         "code": "20044",
	//         "message": "Amount is not supported for this order type",
	//         "submitted": false,
	//         "marketCode": "OX-USDT",
	//         "side": "SELL",
	//         "amount": "200",
	//         "orderType": "MARKET",
	//         "createdAt": "1715592079986",
	//         "source": 11
	//     }
	//
	// fetchOrder
	//     {
	//         "orderId": "1000111762980",
	//         "clientOrderId": "0",
	//         "marketCode": "ETH-USD-SWAP-LIN",
	//         "status": "OPEN",
	//         "side": "BUY",
	//         "price": "2700.0",
	//         "isTriggered": false,
	//         "remainQuantity": "0.01",
	//         "totalQuantity": "0.01",
	//         "amount": "0",
	//         "displayQuantity": "0.01",
	//         "cumulativeMatchedQuantity": "0",
	//         "orderType": "STOP_LIMIT",
	//         "timeInForce": "GTC",
	//         "source": "11",
	//         "createdAt": "1715794191277"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var marketId interface{} = this.SafeString(order, "marketCode")
	market = this.SafeMarket(marketId, market)
	var timestamp interface{} = this.SafeInteger(order, "createdAt")
	var fee interface{} = nil
	var feeCurrency interface{} = this.SafeString(order, "feeInstrumentId")
	if IsTrue(!IsEqual(feeCurrency, nil)) {
		fee = map[string]interface{}{
			"currency": this.SafeCurrencyCode(feeCurrency),
			"cost":     this.SafeNumber(order, "fees"),
		}
	}
	var status interface{} = this.SafeString(order, "status")
	var code interface{} = this.SafeInteger(order, "code") // rejected orders have code of the error
	if IsTrue(!IsEqual(code, nil)) {
		status = "rejected"
	}
	var triggerPrice interface{} = this.SafeString(order, "stopPrice")
	return this.SafeOrder(map[string]interface{}{
		"id":                  this.SafeString(order, "orderId"),
		"clientOrderId":       this.SafeString(order, "clientOrderId"),
		"timestamp":           timestamp,
		"datetime":            this.Iso8601(timestamp),
		"lastTradeTimestamp":  this.SafeInteger(order, "lastMatchedAt"),
		"lastUpdateTimestamp": this.SafeInteger(order, "lastModifiedAt"),
		"status":              this.ParseOrderStatus(status),
		"symbol":              GetValue(market, "symbol"),
		"type":                this.ParseOrderType(this.SafeString(order, "orderType")),
		"timeInForce":         this.ParseOrderTimeInForce(this.SafeString(order, "timeInForce")),
		"side":                this.SafeStringLower(order, "side"),
		"price":               this.SafeStringN(order, []interface{}{"price", "matchPrice", "limitPrice"}),
		"average":             nil,
		"amount":              this.SafeString2(order, "totalQuantity", "quantity"),
		"filled":              this.SafeString2(order, "cumulativeMatchedQuantity", "matchQuantity"),
		"remaining":           this.SafeString(order, "remainQuantity"),
		"triggerPrice":        triggerPrice,
		"stopLossPrice":       triggerPrice,
		"cost":                this.OmitZero(this.SafeString(order, "amount")),
		"trades":              nil,
		"fee":                 fee,
		"info":                order,
	}, market)
}
func (this *oxfun) ParseOrderStatus(status interface{}) interface{} {
	var statuses interface{} = map[string]interface{}{
		"OPEN":                              "open",
		"PARTIALLY_FILLED":                  "open",
		"PARTIAL_FILL":                      "open",
		"FILLED":                            "closed",
		"CANCELED":                          "canceled",
		"CANCELED_BY_USER":                  "canceled",
		"CANCELED_BY_MAKER_ONLY":            "rejected",
		"CANCELED_BY_FOK":                   "rejected",
		"CANCELED_ALL_BY_IOC":               "rejected",
		"CANCELED_PARTIAL_BY_IOC":           "canceled",
		"CANCELED_BY_SELF_TRADE_PROTECTION": "rejected",
	}
	return this.SafeString(statuses, status, status)
}
func (this *oxfun) ParseOrderType(typeVar interface{}) interface{} {
	var types interface{} = map[string]interface{}{
		"LIMIT":       "limit",
		"STOP_LIMIT":  "limit",
		"MARKET":      "market",
		"STOP_MARKET": "market",
	}
	return this.SafeString(types, typeVar, typeVar)
}
func (this *oxfun) ParseOrderTimeInForce(typeVar interface{}) interface{} {
	var types interface{} = map[string]interface{}{
		"GTC":                "GTC",
		"IOC":                "IOC",
		"FOK":                "FOK",
		"MAKER_ONLY":         "PO",
		"MAKER_ONLY_REPRICE": "PO",
	}
	return this.SafeString(types, typeVar, typeVar)
}
func (this *oxfun) Sign(path interface{}, optionalArgs ...interface{}) interface{} {
	api := GetArg(optionalArgs, 0, "public")
	_ = api
	method := GetArg(optionalArgs, 1, "GET")
	_ = method
	params := GetArg(optionalArgs, 2, map[string]interface{}{})
	_ = params
	headers := GetArg(optionalArgs, 3, nil)
	_ = headers
	body := GetArg(optionalArgs, 4, nil)
	_ = body
	var baseUrl interface{} = GetValue(GetValue(this.Urls, "api"), api)
	var url interface{} = Add(Add(baseUrl, "/"), path)
	var queryString interface{} = ""
	if IsTrue(IsEqual(method, "GET")) {
		queryString = this.Urlencode(params)
		if IsTrue(!IsEqual(GetLength(queryString), 0)) {
			url = Add(url, Add("?", queryString))
		}
	}
	if IsTrue(IsEqual(api, "private")) {
		this.CheckRequiredCredentials()
		var timestamp interface{} = this.Milliseconds()
		var isoDatetime interface{} = this.Iso8601(timestamp)
		var datetimeParts interface{} = Split(isoDatetime, ".")
		var datetime interface{} = GetValue(datetimeParts, 0)
		var nonce interface{} = this.Nonce()
		var urlParts interface{} = Split(baseUrl, "//")
		if IsTrue(IsTrue((IsEqual(method, "POST"))) || IsTrue((IsEqual(method, "DELETE")))) {
			body = this.Json(params)
			queryString = body
		}
		var msgString interface{} = Add(Add(Add(Add(Add(Add(Add(Add(Add(Add(datetime, "\n"), ToString(nonce)), "\n"), method), "\n"), GetValue(urlParts, 1)), "\n/"), path), "\n"), queryString)
		var signature interface{} = this.Hmac(this.Encode(msgString), this.Encode(this.Secret), sha256, "base64")
		headers = map[string]interface{}{
			"Content-Type": "application/json",
			"AccessKey":    this.ApiKey,
			"Timestamp":    datetime,
			"Signature":    signature,
			"Nonce":        ToString(nonce),
		}
	}
	return map[string]interface{}{
		"url":     url,
		"method":  method,
		"body":    body,
		"headers": headers,
	}
}
func (this *oxfun) HandleErrors(code interface{}, reason interface{}, url interface{}, method interface{}, headers interface{}, body interface{}, response interface{}, requestHeaders interface{}, requestBody interface{}) interface{} {
	if IsTrue(IsEqual(response, nil)) {
		return nil
	}
	if IsTrue(!IsEqual(code, 200)) {
		var responseCode interface{} = this.SafeString(response, "code", nil)
		var feedback interface{} = Add(Add(this.Id, " "), body)
		this.ThrowBroadlyMatchedException(GetValue(this.Exceptions, "broad"), body, feedback)
		this.ThrowExactlyMatchedException(GetValue(this.Exceptions, "exact"), responseCode, feedback)
		panic(ExchangeError(feedback))
	}
	return nil
}

func (this *oxfun) Init(userConfig map[string]interface{}) {
	this.Exchange = Exchange{}
	this.Exchange.DerivedExchange = this
	this.Exchange.InitParent(userConfig, this.Describe().(map[string]interface{}), this)
}
