package ccxt

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

type blockchaincom struct {
	Exchange
}

func NewBlockchaincomCore() *blockchaincom {
	p := &blockchaincom{}
	setDefaults(p)
	return p
}

func (this *blockchaincom) Describe() interface{} {
	return this.DeepExtend(this.Exchange.Describe(), map[string]interface{}{
		"id":        "blockchaincom",
		"secret":    nil,
		"name":      "Blockchain.com",
		"countries": []interface{}{"LX"},
		"rateLimit": 500,
		"version":   "v3",
		"pro":       true,
		"has": map[string]interface{}{
			"CORS":                           false,
			"spot":                           true,
			"margin":                         nil,
			"swap":                           false,
			"future":                         false,
			"option":                         false,
			"cancelAllOrders":                true,
			"cancelOrder":                    true,
			"createOrder":                    true,
			"createStopLimitOrder":           true,
			"createStopMarketOrder":          true,
			"createStopOrder":                true,
			"fetchBalance":                   true,
			"fetchCanceledOrders":            true,
			"fetchClosedOrders":              true,
			"fetchDeposit":                   true,
			"fetchDepositAddress":            true,
			"fetchDepositAddresses":          false,
			"fetchDepositAddressesByNetwork": false,
			"fetchDeposits":                  true,
			"fetchFundingHistory":            false,
			"fetchFundingRate":               false,
			"fetchFundingRateHistory":        false,
			"fetchFundingRates":              false,
			"fetchIndexOHLCV":                false,
			"fetchL2OrderBook":               true,
			"fetchL3OrderBook":               true,
			"fetchLedger":                    false,
			"fetchMarginMode":                false,
			"fetchMarkets":                   true,
			"fetchMarkOHLCV":                 false,
			"fetchMyTrades":                  true,
			"fetchOHLCV":                     false,
			"fetchOpenInterestHistory":       false,
			"fetchOpenOrders":                true,
			"fetchOrder":                     true,
			"fetchOrderBook":                 true,
			"fetchPositionMode":              false,
			"fetchPremiumIndexOHLCV":         false,
			"fetchTicker":                    true,
			"fetchTickers":                   true,
			"fetchTrades":                    false,
			"fetchTradingFee":                false,
			"fetchTradingFees":               true,
			"fetchTransfer":                  false,
			"fetchTransfers":                 false,
			"fetchWithdrawal":                true,
			"fetchWithdrawals":               true,
			"fetchWithdrawalWhitelist":       true,
			"transfer":                       false,
			"withdraw":                       true,
		},
		"timeframes": nil,
		"urls": map[string]interface{}{
			"logo": "https://github.com/user-attachments/assets/975e3054-3399-4363-bcee-ec3c6d63d4e8",
			"test": map[string]interface{}{
				"public":  "https://testnet-api.delta.exchange",
				"private": "https://testnet-api.delta.exchange",
			},
			"api": map[string]interface{}{
				"public":  "https://api.blockchain.com/v3/exchange",
				"private": "https://api.blockchain.com/v3/exchange",
			},
			"www":  "https://blockchain.com",
			"doc":  []interface{}{"https://api.blockchain.com/v3"},
			"fees": "https://exchange.blockchain.com/fees",
		},
		"api": map[string]interface{}{
			"public": map[string]interface{}{
				"get": map[string]interface{}{
					"tickers":          1,
					"tickers/{symbol}": 1,
					"symbols":          1,
					"symbols/{symbol}": 1,
					"l2/{symbol}":      1,
					"l3/{symbol}":      1,
				},
			},
			"private": map[string]interface{}{
				"get": map[string]interface{}{
					"fees":                          1,
					"orders":                        1,
					"orders/{orderId}":              1,
					"trades":                        1,
					"fills":                         1,
					"deposits":                      1,
					"deposits/{depositId}":          1,
					"accounts":                      1,
					"accounts/{account}/{currency}": 1,
					"whitelist":                     1,
					"whitelist/{currency}":          1,
					"withdrawals":                   1,
					"withdrawals/{withdrawalId}":    1,
				},
				"post": map[string]interface{}{
					"orders":              1,
					"deposits/{currency}": 1,
					"withdrawals":         1,
				},
				"delete": map[string]interface{}{
					"orders":           1,
					"orders/{orderId}": 1,
				},
			},
		},
		"fees": map[string]interface{}{
			"trading": map[string]interface{}{
				"feeSide":    "get",
				"tierBased":  true,
				"percentage": true,
				"tiers": map[string]interface{}{
					"taker": []interface{}{[]interface{}{this.ParseNumber("0"), this.ParseNumber("0.0045")}, []interface{}{this.ParseNumber("10000"), this.ParseNumber("0.0035")}, []interface{}{this.ParseNumber("50000"), this.ParseNumber("0.0018")}, []interface{}{this.ParseNumber("100000"), this.ParseNumber("0.0018")}, []interface{}{this.ParseNumber("500000"), this.ParseNumber("0.0018")}, []interface{}{this.ParseNumber("1000000"), this.ParseNumber("0.0018")}, []interface{}{this.ParseNumber("2500000"), this.ParseNumber("0.0018")}, []interface{}{this.ParseNumber("5000000"), this.ParseNumber("0.0016")}, []interface{}{this.ParseNumber("25000000"), this.ParseNumber("0.0014")}, []interface{}{this.ParseNumber("100000000"), this.ParseNumber("0.0011")}, []interface{}{this.ParseNumber("500000000"), this.ParseNumber("0.0008")}, []interface{}{this.ParseNumber("1000000000"), this.ParseNumber("0.0006")}},
					"maker": []interface{}{[]interface{}{this.ParseNumber("0"), this.ParseNumber("0.004")}, []interface{}{this.ParseNumber("10000"), this.ParseNumber("0.0017")}, []interface{}{this.ParseNumber("50000"), this.ParseNumber("0.0015")}, []interface{}{this.ParseNumber("100000"), this.ParseNumber("0.0008")}, []interface{}{this.ParseNumber("500000"), this.ParseNumber("0.0007")}, []interface{}{this.ParseNumber("1000000"), this.ParseNumber("0.0006")}, []interface{}{this.ParseNumber("2500000"), this.ParseNumber("0.0005")}, []interface{}{this.ParseNumber("5000000"), this.ParseNumber("0.0004")}, []interface{}{this.ParseNumber("25000000"), this.ParseNumber("0.0003")}, []interface{}{this.ParseNumber("100000000"), this.ParseNumber("0.0002")}, []interface{}{this.ParseNumber("500000000"), this.ParseNumber("0.0001")}, []interface{}{this.ParseNumber("1000000000"), this.ParseNumber("0")}},
				},
			},
		},
		"requiredCredentials": map[string]interface{}{
			"apiKey": false,
			"secret": true,
		},
		"options": map[string]interface{}{
			"networks": map[string]interface{}{
				"ERC20": "ETH",
				"TRC20": "TRX",
				"ALGO":  "ALGO",
				"ADA":   "ADA",
				"AR":    "AR",
				"ATOM":  "ATOM",
				"AVAXC": "AVAX",
				"BCH":   "BCH",
				"BSV":   "BSV",
				"BTC":   "BTC",
				"DCR":   "DCR",
				"DESO":  "DESO",
				"DASH":  "DASH",
				"CELO":  "CELO",
				"CHZ":   "CHZ",
				"MATIC": "MATIC",
				"SOL":   "SOL",
				"DOGE":  "DOGE",
				"DOT":   "DOT",
				"EOS":   "EOS",
				"ETC":   "ETC",
				"FIL":   "FIL",
				"KAVA":  "KAVA",
				"LTC":   "LTC",
				"IOTA":  "MIOTA",
				"NEAR":  "NEAR",
				"STX":   "STX",
				"XLM":   "XLM",
				"XMR":   "XMR",
				"XRP":   "XRP",
				"XTZ":   "XTZ",
				"ZEC":   "ZEC",
				"ZIL":   "ZIL",
			},
		},
		"features": map[string]interface{}{
			"spot": map[string]interface{}{
				"sandbox": false,
				"createOrder": map[string]interface{}{
					"marginMode":                 false,
					"triggerPrice":               true,
					"triggerPriceType":           nil,
					"triggerDirection":           false,
					"stopLossPrice":              false,
					"takeProfitPrice":            false,
					"attachedStopLossTakeProfit": nil,
					"timeInForce": map[string]interface{}{
						"IOC": true,
						"FOK": true,
						"PO":  false,
						"GTD": true,
					},
					"hedged":                 false,
					"leverage":               false,
					"marketBuyRequiresPrice": false,
					"marketBuyByCost":        false,
					"selfTradePrevention":    false,
					"trailing":               false,
					"iceberg":                false,
				},
				"createOrders": nil,
				"fetchMyTrades": map[string]interface{}{
					"marginMode":     false,
					"limit":          1000,
					"daysBack":       100000,
					"untilDays":      100000,
					"symbolRequired": false,
				},
				"fetchOrder": map[string]interface{}{
					"marginMode":     false,
					"trigger":        false,
					"symbolRequired": false,
					"trailing":       false,
				},
				"fetchOpenOrders": map[string]interface{}{
					"marginMode":     false,
					"limit":          1000,
					"trigger":        false,
					"trailing":       false,
					"symbolRequired": false,
				},
				"fetchOrders": nil,
				"fetchClosedOrders": map[string]interface{}{
					"marginMode":       false,
					"limit":            1000,
					"daysBack":         100000,
					"daysBackCanceled": 1,
					"untilDays":        100000,
					"trigger":          false,
					"trailing":         false,
					"symbolRequired":   false,
				},
				"fetchOHLCV": nil,
			},
			"swap": map[string]interface{}{
				"linear":  nil,
				"inverse": nil,
			},
			"future": map[string]interface{}{
				"linear":  nil,
				"inverse": nil,
			},
		},
		"precisionMode": TICK_SIZE,
		"exceptions": map[string]interface{}{
			"exact": map[string]interface{}{
				"401": AuthenticationError,
				"404": OrderNotFound,
			},
			"broad": map[string]interface{}{},
		},
	})
}

/**
 * @method
 * @name blockchaincom#fetchMarkets
 * @description retrieves data on all markets for blockchaincom
 * @see https://api.blockchain.com/v3/#getsymbols
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} an array of objects representing market data
 */
func (this *blockchaincom) FetchMarkets(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		//
		//     "USDC-GBP": {
		//         "base_currency": "USDC",
		//         "base_currency_scale": 6,
		//         "counter_currency": "GBP",
		//         "counter_currency_scale": 2,
		//         "min_price_increment": 10000,
		//         "min_price_increment_scale": 8,
		//         "min_order_size": 500000000,
		//         "min_order_size_scale": 8,
		//         "max_order_size": 0,
		//         "max_order_size_scale": 8,
		//         "lot_size": 10000,
		//         "lot_size_scale": 8,
		//         "status": "open",
		//         "id": 68,
		//         "auction_price": 0,
		//         "auction_size": 0,
		//         "auction_time": "",
		//         "imbalance": 0
		//     }
		//
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		markets := (<-this.PublicGetSymbols(params))
		PanicOnError(markets)
		var marketIds interface{} = ObjectKeys(markets)
		var result interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(marketIds)); i++ {
			var marketId interface{} = GetValue(marketIds, i)
			var market interface{} = this.SafeValue(markets, marketId)
			var baseId interface{} = this.SafeString(market, "base_currency")
			var quoteId interface{} = this.SafeString(market, "counter_currency")
			var base interface{} = this.SafeCurrencyCode(baseId)
			var quote interface{} = this.SafeCurrencyCode(quoteId)
			var numericId interface{} = this.SafeNumber(market, "id")
			var active interface{} = nil
			var marketState interface{} = this.SafeString(market, "status")
			if IsTrue(IsEqual(marketState, "open")) {
				active = true
			} else {
				active = false
			}
			// price precision
			var minPriceIncrementString interface{} = this.SafeString(market, "min_price_increment")
			var minPriceIncrementScaleString interface{} = this.SafeString(market, "min_price_increment_scale")
			var minPriceScalePrecisionString interface{} = this.ParsePrecision(minPriceIncrementScaleString)
			var pricePrecisionString interface{} = Precise.StringMul(minPriceIncrementString, minPriceScalePrecisionString)
			// amount precision
			var lotSizeString interface{} = this.SafeString(market, "lot_size")
			var lotSizeScaleString interface{} = this.SafeString(market, "lot_size_scale")
			var lotSizeScalePrecisionString interface{} = this.ParsePrecision(lotSizeScaleString)
			var amountPrecisionString interface{} = Precise.StringMul(lotSizeString, lotSizeScalePrecisionString)
			// minimum order size
			var minOrderSizeString interface{} = this.SafeString(market, "min_order_size")
			var minOrderSizeScaleString interface{} = this.SafeString(market, "min_order_size_scale")
			var minOrderSizeScalePrecisionString interface{} = this.ParsePrecision(minOrderSizeScaleString)
			var minOrderSizePreciseString interface{} = Precise.StringMul(minOrderSizeString, minOrderSizeScalePrecisionString)
			var minOrderSize interface{} = this.ParseNumber(minOrderSizePreciseString)
			// maximum order size
			var maxOrderSize interface{} = nil
			maxOrderSize = this.SafeString(market, "max_order_size")
			if IsTrue(!IsEqual(maxOrderSize, "0")) {
				var maxOrderSizeScaleString interface{} = this.SafeString(market, "max_order_size_scale")
				var maxOrderSizeScalePrecisionString interface{} = this.ParsePrecision(maxOrderSizeScaleString)
				var maxOrderSizeString interface{} = Precise.StringMul(maxOrderSize, maxOrderSizeScalePrecisionString)
				maxOrderSize = this.ParseNumber(maxOrderSizeString)
			} else {
				maxOrderSize = nil
			}
			AppendToArray(&result, map[string]interface{}{
				"info":           market,
				"id":             marketId,
				"numericId":      numericId,
				"symbol":         Add(Add(base, "/"), quote),
				"base":           base,
				"quote":          quote,
				"settle":         nil,
				"baseId":         baseId,
				"quoteId":        quoteId,
				"settleId":       nil,
				"type":           "spot",
				"spot":           true,
				"margin":         false,
				"swap":           false,
				"future":         false,
				"option":         false,
				"active":         active,
				"contract":       false,
				"linear":         nil,
				"inverse":        nil,
				"contractSize":   nil,
				"expiry":         nil,
				"expiryDatetime": nil,
				"strike":         nil,
				"optionType":     nil,
				"precision": map[string]interface{}{
					"amount": this.ParseNumber(amountPrecisionString),
					"price":  this.ParseNumber(pricePrecisionString),
				},
				"limits": map[string]interface{}{
					"leverage": map[string]interface{}{
						"min": nil,
						"max": nil,
					},
					"amount": map[string]interface{}{
						"min": minOrderSize,
						"max": maxOrderSize,
					},
					"price": map[string]interface{}{
						"min": nil,
						"max": nil,
					},
					"cost": map[string]interface{}{
						"min": nil,
						"max": nil,
					},
				},
				"created": nil,
			})
		}

		ch <- result
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchOrderBook
 * @description fetches information on open orders with bid (buy) and ask (sell) prices, volumes and other data
 * @see https://api.blockchain.com/v3/#getl3orderbook
 * @param {string} symbol unified symbol of the market to fetch the order book for
 * @param {int} [limit] the maximum amount of order book entries to return
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} A dictionary of [order book structures]{@link https://docs.ccxt.com/#/?id=order-book-structure} indexed by market symbols
 */
func (this *blockchaincom) FetchOrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes43515 := (<-this.FetchL3OrderBook(symbol, limit, params))
		PanicOnError(retRes43515)
		ch <- retRes43515
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchL3OrderBook
 * @description fetches level 3 information on open orders with bid (buy) and ask (sell) prices, volumes and other data
 * @see https://api.blockchain.com/v3/#getl3orderbook
 * @param {string} symbol unified market symbol
 * @param {int} [limit] max number of orders to return, default is undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order book structure]{@link https://docs.ccxt.com/#/?id=order-book-structure}
 */
func (this *blockchaincom) FetchL3OrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes4498 := (<-this.LoadMarkets())
		PanicOnError(retRes4498)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"symbol": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "depth", limit)
		}

		response := (<-this.PublicGetL3Symbol(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.ParseOrderBook(response, GetValue(market, "symbol"), nil, "bids", "asks", "px", "qty")
		return nil

	}()
	return ch
}
func (this *blockchaincom) FetchL2OrderBook(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		limit := GetArg(optionalArgs, 0, nil)
		_ = limit
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes4628 := (<-this.LoadMarkets())
		PanicOnError(retRes4628)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"symbol": GetValue(market, "id"),
		}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "depth", limit)
		}

		response := (<-this.PublicGetL2Symbol(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.ParseOrderBook(response, GetValue(market, "symbol"), nil, "bids", "asks", "px", "qty")
		return nil

	}()
	return ch
}
func (this *blockchaincom) ParseTicker(ticker interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//     "symbol": "BTC-USD",
	//     "price_24h": 47791.86,
	//     "volume_24h": 362.88635738,
	//     "last_trade_price": 47587.75
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var marketId interface{} = this.SafeString(ticker, "symbol")
	var symbol interface{} = this.SafeSymbol(marketId, market, "-")
	var last interface{} = this.SafeString(ticker, "last_trade_price")
	var baseVolume interface{} = this.SafeString(ticker, "volume_24h")
	var open interface{} = this.SafeString(ticker, "price_24h")
	return this.SafeTicker(map[string]interface{}{
		"symbol":        symbol,
		"timestamp":     nil,
		"datetime":      nil,
		"high":          nil,
		"low":           nil,
		"bid":           nil,
		"bidVolume":     nil,
		"ask":           nil,
		"askVolume":     nil,
		"vwap":          nil,
		"open":          open,
		"close":         nil,
		"last":          last,
		"previousClose": nil,
		"change":        nil,
		"percentage":    nil,
		"average":       nil,
		"baseVolume":    baseVolume,
		"quoteVolume":   nil,
		"info":          ticker,
	}, market)
}

/**
 * @method
 * @name blockchaincom#fetchTicker
 * @description fetches a price ticker, a statistical calculation with the information calculated over the past 24 hours for a specific market
 * @see https://api.blockchain.com/v3/#gettickerbysymbol
 * @param {string} symbol unified symbol of the market to fetch the ticker for
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [ticker structure]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *blockchaincom) FetchTicker(symbol interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes5228 := (<-this.LoadMarkets())
		PanicOnError(retRes5228)
		var market interface{} = this.Market(symbol)
		var request interface{} = map[string]interface{}{
			"symbol": GetValue(market, "id"),
		}

		response := (<-this.PublicGetTickersSymbol(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.ParseTicker(response, market)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchTickers
 * @description fetches price tickers for multiple markets, statistical information calculated over the past 24 hours for each market
 * @see https://api.blockchain.com/v3/#gettickers
 * @param {string[]|undefined} symbols unified symbols of the markets to fetch the ticker for, all market tickers are returned if not assigned
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [ticker structures]{@link https://docs.ccxt.com/#/?id=ticker-structure}
 */
func (this *blockchaincom) FetchTickers(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbols := GetArg(optionalArgs, 0, nil)
		_ = symbols
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes5418 := (<-this.LoadMarkets())
		PanicOnError(retRes5418)

		tickers := (<-this.PublicGetTickers(params))
		PanicOnError(tickers)

		ch <- this.ParseTickers(tickers, symbols)
		return nil

	}()
	return ch
}
func (this *blockchaincom) ParseOrderState(state interface{}) interface{} {
	var states interface{} = map[string]interface{}{
		"OPEN":        "open",
		"REJECTED":    "rejected",
		"FILLED":      "closed",
		"CANCELED":    "canceled",
		"PART_FILLED": "open",
		"EXPIRED":     "expired",
	}
	return this.SafeString(states, state, state)
}
func (this *blockchaincom) ParseOrder(order interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "clOrdId": "00001",
	//         "ordType": "MARKET",
	//         "ordStatus": "FILLED",
	//         "side": "BUY",
	//         "symbol": "USDC-USDT",
	//         "exOrdId": "281775861306290",
	//         "price": null,
	//         "text": "Fill",
	//         "lastShares": "30.0",
	//         "lastPx": "0.9999",
	//         "leavesQty": "0.0",
	//         "cumQty": "30.0",
	//         "avgPx": "0.9999",
	//         "timestamp": "1633940339619"
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var clientOrderId interface{} = this.SafeString(order, "clOrdId")
	var typeVar interface{} = this.SafeStringLower(order, "ordType")
	var statusId interface{} = this.SafeString(order, "ordStatus")
	var state interface{} = this.ParseOrderState(statusId)
	var side interface{} = this.SafeStringLower(order, "side")
	var marketId interface{} = this.SafeString(order, "symbol")
	var symbol interface{} = this.SafeSymbol(marketId, market, "-")
	var exchangeOrderId interface{} = this.SafeString(order, "exOrdId")
	var price interface{} = Ternary(IsTrue((!IsEqual(typeVar, "market"))), this.SafeString(order, "price"), nil)
	var average interface{} = this.SafeNumber(order, "avgPx")
	var timestamp interface{} = this.SafeInteger(order, "timestamp")
	var datetime interface{} = this.Iso8601(timestamp)
	var filled interface{} = this.SafeString(order, "cumQty")
	var remaining interface{} = this.SafeString(order, "leavesQty")
	var result interface{} = this.SafeOrder(map[string]interface{}{
		"id":                 exchangeOrderId,
		"clientOrderId":      clientOrderId,
		"datetime":           datetime,
		"timestamp":          timestamp,
		"lastTradeTimestamp": nil,
		"status":             state,
		"symbol":             symbol,
		"type":               typeVar,
		"timeInForce":        nil,
		"side":               side,
		"price":              price,
		"average":            average,
		"amount":             nil,
		"filled":             filled,
		"remaining":          remaining,
		"cost":               nil,
		"trades":             []interface{}{},
		"fees":               []interface{}{},
		"info":               order,
	})
	return result
}

/**
 * @method
 * @name blockchaincom#createOrder
 * @description create a trade order
 * @see https://api.blockchain.com/v3/#createorder
 * @param {string} symbol unified symbol of the market to create an order in
 * @param {string} type 'market' or 'limit'
 * @param {string} side 'buy' or 'sell'
 * @param {float} amount how much of currency you want to trade in units of base currency
 * @param {float} [price] the price at which the order is to be fulfilled, in units of the quote currency, ignored in market orders
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *blockchaincom) CreateOrder(symbol interface{}, typeVar interface{}, side interface{}, amount interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		price := GetArg(optionalArgs, 0, nil)
		_ = price
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes6298 := (<-this.LoadMarkets())
		PanicOnError(retRes6298)
		var market interface{} = this.Market(symbol)
		var orderType interface{} = this.SafeString(params, "ordType", typeVar)
		var uppercaseOrderType interface{} = ToUpper(orderType)
		var clientOrderId interface{} = this.SafeString2(params, "clientOrderId", "clOrdId", this.Uuid16())
		params = this.Omit(params, []interface{}{"ordType", "clientOrderId", "clOrdId"})
		var request interface{} = map[string]interface{}{
			"ordType":  uppercaseOrderType,
			"symbol":   GetValue(market, "id"),
			"side":     ToUpper(side),
			"orderQty": this.AmountToPrecision(symbol, amount),
			"clOrdId":  clientOrderId,
		}
		var triggerPrice interface{} = this.SafeValueN(params, []interface{}{"triggerPrice", "stopPx", "stopPrice"})
		params = this.Omit(params, []interface{}{"triggerPrice", "stopPx", "stopPrice"})
		if IsTrue(IsTrue(IsEqual(uppercaseOrderType, "STOP")) || IsTrue(IsEqual(uppercaseOrderType, "STOPLIMIT"))) {
			if IsTrue(IsEqual(triggerPrice, nil)) {
				panic(ArgumentsRequired(Add(Add(Add(this.Id, " createOrder() requires a stopPx or triggerPrice param for a "), uppercaseOrderType), " order")))
			}
		}
		if IsTrue(!IsEqual(triggerPrice, nil)) {
			if IsTrue(IsEqual(uppercaseOrderType, "MARKET")) {
				AddElementToObject(request, "ordType", "STOP")
			} else if IsTrue(IsEqual(uppercaseOrderType, "LIMIT")) {
				AddElementToObject(request, "ordType", "STOPLIMIT")
			}
		}
		var priceRequired interface{} = false
		var stopPriceRequired interface{} = false
		if IsTrue(IsTrue(IsEqual(GetValue(request, "ordType"), "LIMIT")) || IsTrue(IsEqual(GetValue(request, "ordType"), "STOPLIMIT"))) {
			priceRequired = true
		}
		if IsTrue(IsTrue(IsEqual(GetValue(request, "ordType"), "STOP")) || IsTrue(IsEqual(GetValue(request, "ordType"), "STOPLIMIT"))) {
			stopPriceRequired = true
		}
		if IsTrue(priceRequired) {
			AddElementToObject(request, "price", this.PriceToPrecision(symbol, price))
		}
		if IsTrue(stopPriceRequired) {
			AddElementToObject(request, "stopPx", this.PriceToPrecision(symbol, triggerPrice))
		}

		response := (<-this.PrivatePostOrders(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.ParseOrder(response, market)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#cancelOrder
 * @description cancels an open order
 * @see https://api.blockchain.com/v3/#deleteorder
 * @param {string} id order id
 * @param {string} symbol unified symbol of the market the order was made in
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *blockchaincom) CancelOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params
		var request interface{} = map[string]interface{}{
			"orderId": id,
		}

		response := (<-this.PrivateDeleteOrdersOrderId(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.SafeOrder(map[string]interface{}{
			"id":   id,
			"info": response,
		})
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#cancelAllOrders
 * @description cancel all open orders
 * @see https://api.blockchain.com/v3/#deleteallorders
 * @param {string} symbol unified market symbol of the market to cancel orders in, all markets are used if undefined, default is undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *blockchaincom) CancelAllOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		// cancels all open orders if no symbol specified
		// cancels all open orders of specified symbol, if symbol is specified
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes7118 := (<-this.LoadMarkets())
		PanicOnError(retRes7118)
		var request interface{} = map[string]interface{}{}
		if IsTrue(!IsEqual(symbol, nil)) {
			var marketId interface{} = this.MarketId(symbol)
			AddElementToObject(request, "symbol", marketId)
		}

		response := (<-this.PrivateDeleteOrders(this.Extend(request, params)))
		PanicOnError(response)

		//
		// {}
		//
		ch <- []interface{}{this.SafeOrder(map[string]interface{}{
			"info": response,
		})}
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchTradingFees
 * @description fetch the trading fees for multiple markets
 * @see https://api.blockchain.com/v3/#getfees
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a dictionary of [fee structures]{@link https://docs.ccxt.com/#/?id=fee-structure} indexed by market symbols
 */
func (this *blockchaincom) FetchTradingFees(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes7398 := (<-this.LoadMarkets())
		PanicOnError(retRes7398)

		response := (<-this.PrivateGetFees(params))
		PanicOnError(response)
		//
		//     {
		//         "makerRate": "0.002",
		//         "takerRate": "0.004",
		//         "volumeInUSD": "0.0"
		//     }
		//
		var makerFee interface{} = this.SafeNumber(response, "makerRate")
		var takerFee interface{} = this.SafeNumber(response, "takerRate")
		var result interface{} = map[string]interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(this.Symbols)); i++ {
			var symbol interface{} = GetValue(this.Symbols, i)
			AddElementToObject(result, symbol, map[string]interface{}{
				"info":   response,
				"symbol": symbol,
				"maker":  makerFee,
				"taker":  takerFee,
			})
		}

		ch <- result
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchCanceledOrders
 * @description fetches information on multiple canceled orders made by the user
 * @see https://api.blockchain.com/v3/#getorders
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] timestamp in ms of the earliest order, default is undefined
 * @param {int} [limit] max number of orders to return, default is undefined
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *blockchaincom) FetchCanceledOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		var state interface{} = "CANCELED"

		retRes77615 := (<-this.FetchOrdersByState(state, symbol, since, limit, params))
		PanicOnError(retRes77615)
		ch <- retRes77615
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchClosedOrders
 * @description fetches information on multiple closed orders made by the user
 * @see https://api.blockchain.com/v3/#getorders
 * @param {string} symbol unified market symbol of the market orders were made in
 * @param {int} [since] the earliest time in ms to fetch orders for
 * @param {int} [limit] the maximum number of order structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *blockchaincom) FetchClosedOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		var state interface{} = "FILLED"

		retRes79215 := (<-this.FetchOrdersByState(state, symbol, since, limit, params))
		PanicOnError(retRes79215)
		ch <- retRes79215
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchOpenOrders
 * @description fetch all unfilled currently open orders
 * @see https://api.blockchain.com/v3/#getorders
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch open orders for
 * @param {int} [limit] the maximum number of  open orders structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Order[]} a list of [order structures]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *blockchaincom) FetchOpenOrders(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params
		var state interface{} = "OPEN"

		retRes80815 := (<-this.FetchOrdersByState(state, symbol, since, limit, params))
		PanicOnError(retRes80815)
		ch <- retRes80815
		return nil

	}()
	return ch
}
func (this *blockchaincom) FetchOrdersByState(state interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes8128 := (<-this.LoadMarkets())
		PanicOnError(retRes8128)
		var request interface{} = map[string]interface{}{
			"status": state,
			"limit":  100,
		}
		var market interface{} = nil
		if IsTrue(!IsEqual(symbol, nil)) {
			market = this.Market(symbol)
			AddElementToObject(request, "symbol", GetValue(market, "id"))
		}

		response := (<-this.PrivateGetOrders(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.ParseOrders(response, market, since, limit)
		return nil

	}()
	return ch
}
func (this *blockchaincom) ParseTrade(trade interface{}, optionalArgs ...interface{}) interface{} {
	//
	//     {
	//         "exOrdId":281685751028507,
	//         "tradeId":281685434947633,
	//         "execId":8847494003,
	//         "side":"BUY",
	//         "symbol":"AAVE-USDT",
	//         "price":405.34,
	//         "qty":0.1,
	//         "fee":0.162136,
	//         "timestamp":1634559249687
	//     }
	//
	market := GetArg(optionalArgs, 0, nil)
	_ = market
	var orderId interface{} = this.SafeString(trade, "exOrdId")
	var tradeId interface{} = this.SafeString(trade, "tradeId")
	var side interface{} = ToLower(this.SafeString(trade, "side"))
	var marketId interface{} = this.SafeString(trade, "symbol")
	var priceString interface{} = this.SafeString(trade, "price")
	var amountString interface{} = this.SafeString(trade, "qty")
	var timestamp interface{} = this.SafeInteger(trade, "timestamp")
	var datetime interface{} = this.Iso8601(timestamp)
	market = this.SafeMarket(marketId, market, "-")
	var symbol interface{} = GetValue(market, "symbol")
	var fee interface{} = nil
	var feeCostString interface{} = this.SafeString(trade, "fee")
	if IsTrue(!IsEqual(feeCostString, nil)) {
		var feeCurrency interface{} = GetValue(market, "quote")
		fee = map[string]interface{}{
			"cost":     feeCostString,
			"currency": feeCurrency,
		}
	}
	return this.SafeTrade(map[string]interface{}{
		"id":           tradeId,
		"timestamp":    timestamp,
		"datetime":     datetime,
		"symbol":       symbol,
		"order":        orderId,
		"type":         nil,
		"side":         side,
		"takerOrMaker": nil,
		"price":        priceString,
		"amount":       amountString,
		"cost":         nil,
		"fee":          fee,
		"info":         trade,
	}, market)
}

/**
 * @method
 * @name blockchaincom#fetchMyTrades
 * @description fetch all trades made by the user
 * @see https://api.blockchain.com/v3/#getfills
 * @param {string} symbol unified market symbol
 * @param {int} [since] the earliest time in ms to fetch trades for
 * @param {int} [limit] the maximum number of trades structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {Trade[]} a list of [trade structures]{@link https://docs.ccxt.com/#/?id=trade-structure}
 */
func (this *blockchaincom) FetchMyTrades(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes8878 := (<-this.LoadMarkets())
		PanicOnError(retRes8878)
		var request interface{} = map[string]interface{}{}
		if IsTrue(!IsEqual(limit, nil)) {
			AddElementToObject(request, "limit", limit)
		}
		var market interface{} = nil
		if IsTrue(!IsEqual(symbol, nil)) {
			AddElementToObject(request, "symbol", this.MarketId(symbol))
			market = this.Market(symbol)
		}

		trades := (<-this.PrivateGetFills(this.Extend(request, params)))
		PanicOnError(trades)

		ch <- this.ParseTrades(trades, market, since, limit, params) // need to define
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchDepositAddress
 * @description fetch the deposit address for a currency associated with this account
 * @see https://api.blockchain.com/v3/#getdepositaddress
 * @param {string} code unified currency code
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} an [address structure]{@link https://docs.ccxt.com/#/?id=address-structure}
 */
func (this *blockchaincom) FetchDepositAddress(code interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes9118 := (<-this.LoadMarkets())
		PanicOnError(retRes9118)
		var currency interface{} = this.Currency(code)
		var request interface{} = map[string]interface{}{
			"currency": GetValue(currency, "id"),
		}

		response := (<-this.PrivatePostDepositsCurrency(this.Extend(request, params)))
		PanicOnError(response)
		var rawAddress interface{} = this.SafeString(response, "address")
		var tag interface{} = nil
		var address interface{} = nil
		if IsTrue(!IsEqual(rawAddress, nil)) {
			var addressParts interface{} = Split(rawAddress, ";")
			// if a tag or memo is used it is separated by a colon in the 'address' value
			tag = this.SafeString(addressParts, 0)
			address = this.SafeString(addressParts, 1)
		}

		ch <- map[string]interface{}{
			"info":     response,
			"currency": GetValue(currency, "code"),
			"network":  nil,
			"address":  address,
			"tag":      tag,
		}
		return nil

	}()
	return ch
}
func (this *blockchaincom) ParseTransactionState(state interface{}) interface{} {
	var states interface{} = map[string]interface{}{
		"COMPLETED": "ok",
		"REJECTED":  "failed",
		"PENDING":   "pending",
		"FAILED":    "failed",
		"REFUNDED":  "refunded",
	}
	return this.SafeString(states, state, state)
}
func (this *blockchaincom) ParseTransaction(transaction interface{}, optionalArgs ...interface{}) interface{} {
	//
	// deposit
	//
	//     {
	//         "depositId":"748e9180-be0d-4a80-e175-0156150efc95",
	//         "amount":0.009,
	//         "currency":"ETH",
	//         "address":"******************************************",
	//         "state":"COMPLETED",
	//         "txHash":"582114562140e51a80b481c2dfebaf62b4ab9769b8ff54820bb67e34d4a3ab0c",
	//         "timestamp":1633697196241
	//     }
	//
	// withdrawal
	//
	//     {
	//         "amount":30.0,
	//         "currency":"USDT",
	//         "beneficiary":"cab00d11-6e7f-46b7-b453-2e8ef6f101fa", // blockchain specific id
	//         "withdrawalId":"99df5ef7-eab6-4033-be49-312930fbd1ea",
	//         "fee":34.005078,
	//         "state":"COMPLETED",
	//         "timestamp":1634218452549
	//     }
	//
	currency := GetArg(optionalArgs, 0, nil)
	_ = currency
	var typeVar interface{} = nil
	var id interface{} = nil
	var amount interface{} = this.SafeNumber(transaction, "amount")
	var timestamp interface{} = this.SafeInteger(transaction, "timestamp")
	var currencyId interface{} = this.SafeString(transaction, "currency")
	var code interface{} = this.SafeCurrencyCode(currencyId, currency)
	var state interface{} = this.SafeString(transaction, "state")
	if IsTrue(InOp(transaction, "depositId")) {
		typeVar = "deposit"
		id = this.SafeString(transaction, "depositId")
	} else if IsTrue(InOp(transaction, "withdrawalId")) {
		typeVar = "withdrawal"
		id = this.SafeString(transaction, "withdrawalId")
	}
	var feeCost interface{} = Ternary(IsTrue((IsEqual(typeVar, "withdrawal"))), this.SafeNumber(transaction, "fee"), nil)
	var fee interface{} = nil
	if IsTrue(!IsEqual(feeCost, nil)) {
		fee = map[string]interface{}{
			"currency": code,
			"cost":     feeCost,
		}
	}
	var address interface{} = this.SafeString(transaction, "address")
	var txid interface{} = this.SafeString(transaction, "txhash")
	return map[string]interface{}{
		"info":        transaction,
		"id":          id,
		"txid":        txid,
		"timestamp":   timestamp,
		"datetime":    this.Iso8601(timestamp),
		"network":     nil,
		"addressFrom": nil,
		"address":     address,
		"addressTo":   address,
		"tagFrom":     nil,
		"tag":         nil,
		"tagTo":       nil,
		"type":        typeVar,
		"amount":      amount,
		"currency":    code,
		"status":      this.ParseTransactionState(state),
		"updated":     nil,
		"comment":     nil,
		"internal":    nil,
		"fee":         fee,
	}
}

/**
 * @method
 * @name blockchaincom#withdraw
 * @description make a withdrawal
 * @see https://api.blockchain.com/v3/#createwithdrawal
 * @param {string} code unified currency code
 * @param {float} amount the amount to withdraw
 * @param {string} address the address to withdraw to
 * @param {string} tag
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *blockchaincom) Withdraw(code interface{}, amount interface{}, address interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		tag := GetArg(optionalArgs, 0, nil)
		_ = tag
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes10308 := (<-this.LoadMarkets())
		PanicOnError(retRes10308)
		var currency interface{} = this.Currency(code)
		var request interface{} = map[string]interface{}{
			"amount":      amount,
			"currency":    GetValue(currency, "id"),
			"beneficiary": address,
			"sendMax":     false,
		}

		response := (<-this.PrivatePostWithdrawals(this.Extend(request, params)))
		PanicOnError(response)

		//
		//     {
		//         "amount": "30.0",
		//         "currency": "USDT",
		//         "beneficiary": "adcd43fb-9ba6-41f7-8c0d-7013482cb88f",
		//         "withdrawalId": "99df5ef7-eab6-4033-be49-312930fbd1ea",
		//         "fee": "34.005078",
		//         "state": "PENDING",
		//         "timestamp": "*************"
		//     },
		//
		ch <- this.ParseTransaction(response, currency)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchWithdrawals
 * @description fetch all withdrawals made from an account
 * @see https://api.blockchain.com/v3/#getwithdrawals
 * @param {string} code unified currency code
 * @param {int} [since] the earliest time in ms to fetch withdrawals for
 * @param {int} [limit] the maximum number of withdrawals structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *blockchaincom) FetchWithdrawals(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes10658 := (<-this.LoadMarkets())
		PanicOnError(retRes10658)
		var request interface{} = map[string]interface{}{}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "from", since)
		}
		var currency interface{} = nil
		if IsTrue(!IsEqual(code, nil)) {
			currency = this.Currency(code)
		}

		response := (<-this.PrivateGetWithdrawals(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.ParseTransactions(response, currency, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchWithdrawal
 * @description fetch data on a currency withdrawal via the withdrawal id
 * @see https://api.blockchain.com/v3/#getwithdrawalbyid
 * @param {string} id withdrawal id
 * @param {string} code not used by blockchaincom.fetchWithdrawal
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *blockchaincom) FetchWithdrawal(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes10928 := (<-this.LoadMarkets())
		PanicOnError(retRes10928)
		var request interface{} = map[string]interface{}{
			"withdrawalId": id,
		}

		response := (<-this.PrivateGetWithdrawalsWithdrawalId(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.ParseTransaction(response)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchDeposits
 * @description fetch all deposits made to an account
 * @see https://api.blockchain.com/v3/#getdeposits
 * @param {string} code unified currency code
 * @param {int} [since] the earliest time in ms to fetch deposits for
 * @param {int} [limit] the maximum number of deposits structures to retrieve
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object[]} a list of [transaction structures]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *blockchaincom) FetchDeposits(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		since := GetArg(optionalArgs, 1, nil)
		_ = since
		limit := GetArg(optionalArgs, 2, nil)
		_ = limit
		params := GetArg(optionalArgs, 3, map[string]interface{}{})
		_ = params

		retRes11128 := (<-this.LoadMarkets())
		PanicOnError(retRes11128)
		var request interface{} = map[string]interface{}{}
		if IsTrue(!IsEqual(since, nil)) {
			AddElementToObject(request, "from", since)
		}
		var currency interface{} = nil
		if IsTrue(!IsEqual(code, nil)) {
			currency = this.Currency(code)
		}

		response := (<-this.PrivateGetDeposits(this.Extend(request, params)))
		PanicOnError(response)

		ch <- this.ParseTransactions(response, currency, since, limit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchDeposit
 * @description fetch information on a deposit
 * @see https://api.blockchain.com/v3/#getdepositbyid
 * @param {string} id deposit id
 * @param {string} code not used by blockchaincom fetchDeposit ()
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [transaction structure]{@link https://docs.ccxt.com/#/?id=transaction-structure}
 */
func (this *blockchaincom) FetchDeposit(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		code := GetArg(optionalArgs, 0, nil)
		_ = code
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes11398 := (<-this.LoadMarkets())
		PanicOnError(retRes11398)
		var depositId interface{} = this.SafeString(params, "depositId", id)
		var request interface{} = map[string]interface{}{
			"depositId": depositId,
		}

		deposit := (<-this.PrivateGetDepositsDepositId(this.Extend(request, params)))
		PanicOnError(deposit)

		ch <- this.ParseTransaction(deposit)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchBalance
 * @description query for balance and get the amount of funds available for trading or funds locked in orders
 * @see https://api.blockchain.com/v3/#getaccounts
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} a [balance structure]{@link https://docs.ccxt.com/#/?id=balance-structure}
 */
func (this *blockchaincom) FetchBalance(optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		params := GetArg(optionalArgs, 0, map[string]interface{}{})
		_ = params

		retRes11578 := (<-this.LoadMarkets())
		PanicOnError(retRes11578)
		var accountName interface{} = this.SafeString(params, "account", "primary")
		params = this.Omit(params, "account")
		var request interface{} = map[string]interface{}{
			"account": accountName,
		}

		response := (<-this.PrivateGetAccounts(this.Extend(request, params)))
		PanicOnError(response)
		//
		//     {
		//         "primary": [
		//             {
		//                 "currency":"ETH",
		//                 "balance":0.009,
		//                 "available":0.009,
		//                 "balance_local":30.82869,
		//                 "available_local":30.82869,
		//                 "rate":3425.41
		//             },
		//             ...
		//         ]
		//     }
		//
		var balances interface{} = this.SafeValue(response, accountName)
		if IsTrue(IsEqual(balances, nil)) {
			panic(ExchangeError(Add(Add(Add(this.Id, " fetchBalance() could not find the \""), accountName), "\" account")))
		}
		var result interface{} = map[string]interface{}{
			"info": response,
		}
		for i := 0; IsLessThan(i, GetArrayLength(balances)); i++ {
			var entry interface{} = GetValue(balances, i)
			var currencyId interface{} = this.SafeString(entry, "currency")
			var code interface{} = this.SafeCurrencyCode(currencyId)
			var account interface{} = this.Account()
			AddElementToObject(account, "free", this.SafeString(entry, "available"))
			AddElementToObject(account, "total", this.SafeString(entry, "balance"))
			AddElementToObject(result, code, account)
		}

		ch <- this.SafeBalance(result)
		return nil

	}()
	return ch
}

/**
 * @method
 * @name blockchaincom#fetchOrder
 * @description fetches information on an order made by the user
 * @see https://api.blockchain.com/v3/#getorderbyid
 * @param {string} id the order id
 * @param {string} symbol not used by blockchaincom fetchOrder
 * @param {object} [params] extra parameters specific to the exchange API endpoint
 * @returns {object} An [order structure]{@link https://docs.ccxt.com/#/?id=order-structure}
 */
func (this *blockchaincom) FetchOrder(id interface{}, optionalArgs ...interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		// note: only works with exchange-order-id
		// does not work with clientOrderId
		symbol := GetArg(optionalArgs, 0, nil)
		_ = symbol
		params := GetArg(optionalArgs, 1, map[string]interface{}{})
		_ = params

		retRes12098 := (<-this.LoadMarkets())
		PanicOnError(retRes12098)
		var request interface{} = map[string]interface{}{
			"orderId": id,
		}

		response := (<-this.PrivateGetOrdersOrderId(this.Extend(request, params)))
		PanicOnError(response)

		//
		//     {
		//         "exOrdId": 11111111,
		//         "clOrdId": "ABC",
		//         "ordType": "MARKET",
		//         "ordStatus": "FILLED",
		//         "side": "BUY",
		//         "price": 0.12345,
		//         "text": "string",
		//         "symbol": "BTC-USD",
		//         "lastShares": 0.5678,
		//         "lastPx": 3500.12,
		//         "leavesQty": 10,
		//         "cumQty": 0.123345,
		//         "avgPx": 345.33,
		//         "timestamp": 1592830770594
		//     }
		//
		ch <- this.ParseOrder(response)
		return nil

	}()
	return ch
}
func (this *blockchaincom) Sign(path interface{}, optionalArgs ...interface{}) interface{} {
	api := GetArg(optionalArgs, 0, "public")
	_ = api
	method := GetArg(optionalArgs, 1, "GET")
	_ = method
	params := GetArg(optionalArgs, 2, map[string]interface{}{})
	_ = params
	headers := GetArg(optionalArgs, 3, nil)
	_ = headers
	body := GetArg(optionalArgs, 4, nil)
	_ = body
	var requestPath interface{} = Add("/", this.ImplodeParams(path, params))
	var url interface{} = Add(GetValue(GetValue(this.Urls, "api"), api), requestPath)
	var query interface{} = this.Omit(params, this.ExtractParams(path))
	if IsTrue(IsEqual(api, "public")) {
		if IsTrue(GetArrayLength(ObjectKeys(query))) {
			url = Add(url, Add("?", this.Urlencode(query)))
		}
	} else if IsTrue(IsEqual(api, "private")) {
		this.CheckRequiredCredentials()
		headers = map[string]interface{}{
			"X-API-Token": this.Secret,
		}
		if IsTrue((IsEqual(method, "GET"))) {
			if IsTrue(GetArrayLength(ObjectKeys(query))) {
				url = Add(url, Add("?", this.Urlencode(query)))
			}
		} else {
			body = this.Json(query)
			AddElementToObject(headers, "Content-Type", "application/json")
		}
	}
	return map[string]interface{}{
		"url":     url,
		"method":  method,
		"body":    body,
		"headers": headers,
	}
}
func (this *blockchaincom) HandleErrors(code interface{}, reason interface{}, url interface{}, method interface{}, headers interface{}, body interface{}, response interface{}, requestHeaders interface{}, requestBody interface{}) interface{} {
	// {"timestamp":"2021-10-21T15:13:58.837+00:00","status":404,"error":"Not Found","message":"","path":"/orders/505050"
	if IsTrue(IsEqual(response, nil)) {
		return nil
	}
	var text interface{} = this.SafeString(response, "text")
	if IsTrue(!IsEqual(text, nil)) {
		if IsTrue(IsEqual(text, "Insufficient Balance")) {
			panic(InsufficientFunds(Add(Add(this.Id, " "), body)))
		}
	}
	var errorCode interface{} = this.SafeString(response, "status")
	var errorMessage interface{} = this.SafeString(response, "error")
	if IsTrue(!IsEqual(code, nil)) {
		var feedback interface{} = Add(Add(this.Id, " "), this.Json(response))
		this.ThrowExactlyMatchedException(GetValue(this.Exceptions, "exact"), errorCode, feedback)
		this.ThrowBroadlyMatchedException(GetValue(this.Exceptions, "broad"), errorMessage, feedback)
	}
	return nil
}

func (this *blockchaincom) Init(userConfig map[string]interface{}) {
	this.Exchange = Exchange{}
	this.Exchange.DerivedExchange = this
	this.Exchange.InitParent(userConfig, this.Describe().(map[string]interface{}), this)
}
