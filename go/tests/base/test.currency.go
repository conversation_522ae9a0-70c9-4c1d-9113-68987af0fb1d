package base

import "github.com/ccxt/ccxt/go/v4"

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

func TestCurrency(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}) {
	var format interface{} = map[string]interface{}{
		"id":   "btc",
		"code": "BTC",
	}
	// todo: remove fee from empty
	var emptyAllowedFor interface{} = []interface{}{"name", "fee"}
	// todo: info key needs to be added in base, when exchange does not have fetchCurrencies
	var isNative interface{} = IsTrue(GetValue(exchange.GetHas(), "fetchCurrencies")) && IsTrue(!IsEqual(GetValue(exchange.GetHas(), "fetchCurrencies"), "emulated"))
	var currencyType interface{} = exchange.SafeString(entry, "type")
	if IsTrue(isNative) {
		AddElementToObject(format, "info", map[string]interface{}{})
		// todo: 'name': 'Bitcoin', // uppercase string, base currency, 2 or more letters
		AddElementToObject(format, "withdraw", true)                            // withdraw enabled
		AddElementToObject(format, "deposit", true)                             // deposit enabled
		AddElementToObject(format, "precision", exchange.ParseNumber("0.0001")) // in case of SIGNIFICANT_DIGITS it will be 4 - number of digits "after the dot"
		AddElementToObject(format, "fee", exchange.ParseNumber("0.001"))
		AddElementToObject(format, "networks", map[string]interface{}{})
		AddElementToObject(format, "limits", map[string]interface{}{
			"withdraw": map[string]interface{}{
				"min": exchange.ParseNumber("0.01"),
				"max": exchange.ParseNumber("1000"),
			},
			"deposit": map[string]interface{}{
				"min": exchange.ParseNumber("0.01"),
				"max": exchange.ParseNumber("1000"),
			},
		})
		AddElementToObject(format, "type", "crypto")                                                                                  // crypto, fiat, leverage, other
		AssertInArray(exchange, skippedProperties, method, entry, "type", []interface{}{"fiat", "crypto", "leveraged", "other", nil}) // todo: remove undefined
		// only require "deposit" & "withdraw" values, when currency is not fiat, or when it's fiat, but not skipped
		if IsTrue(IsTrue(!IsEqual(currencyType, "crypto")) && IsTrue((InOp(skippedProperties, "depositForNonCrypto")))) {
			AppendToArray(&emptyAllowedFor, "deposit")
		}
		if IsTrue(IsTrue(!IsEqual(currencyType, "crypto")) && IsTrue((InOp(skippedProperties, "withdrawForNonCrypto")))) {
			AppendToArray(&emptyAllowedFor, "withdraw")
		}
		if IsTrue(IsTrue(IsEqual(currencyType, "leveraged")) || IsTrue(IsEqual(currencyType, "other"))) {
			AppendToArray(&emptyAllowedFor, "precision")
		}
	}
	//
	AssertCurrencyCode(exchange, skippedProperties, method, entry, GetValue(entry, "code"))
	// check if empty networks should be skipped
	var networks interface{} = exchange.SafeDict(entry, "networks", map[string]interface{}{})
	var networkKeys interface{} = ObjectKeys(networks)
	var networkKeysLength interface{} = GetArrayLength(networkKeys)
	if IsTrue(IsTrue(IsEqual(networkKeysLength, 0)) && IsTrue((InOp(skippedProperties, "skipCurrenciesWithoutNetworks")))) {
		return
	}
	//
	AssertStructure(exchange, skippedProperties, method, entry, format, emptyAllowedFor)
	//
	CheckPrecisionAccuracy(exchange, skippedProperties, method, entry, "precision")
	AssertGreaterOrEqual(exchange, skippedProperties, method, entry, "fee", "0")
	if !IsTrue((InOp(skippedProperties, "limits"))) {
		var limits interface{} = exchange.SafeValue(entry, "limits", map[string]interface{}{})
		var withdrawLimits interface{} = exchange.SafeValue(limits, "withdraw", map[string]interface{}{})
		var depositLimits interface{} = exchange.SafeValue(limits, "deposit", map[string]interface{}{})
		AssertGreaterOrEqual(exchange, skippedProperties, method, withdrawLimits, "min", "0")
		AssertGreaterOrEqual(exchange, skippedProperties, method, withdrawLimits, "max", "0")
		AssertGreaterOrEqual(exchange, skippedProperties, method, depositLimits, "min", "0")
		AssertGreaterOrEqual(exchange, skippedProperties, method, depositLimits, "max", "0")
		// max should be more than min (withdrawal limits)
		var minStringWithdrawal interface{} = exchange.SafeString(withdrawLimits, "min")
		if IsTrue(!IsEqual(minStringWithdrawal, nil)) {
			AssertGreaterOrEqual(exchange, skippedProperties, method, withdrawLimits, "max", minStringWithdrawal)
		}
		// max should be more than min (deposit limits)
		var minStringDeposit interface{} = exchange.SafeString(depositLimits, "min")
		if IsTrue(!IsEqual(minStringDeposit, nil)) {
			AssertGreaterOrEqual(exchange, skippedProperties, method, depositLimits, "max", minStringDeposit)
		}
		// check valid ID & CODE
		AssertValidCurrencyIdAndCode(exchange, skippedProperties, method, entry, GetValue(entry, "id"), GetValue(entry, "code"))
	}
}
