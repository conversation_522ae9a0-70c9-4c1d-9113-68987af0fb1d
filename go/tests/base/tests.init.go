package base

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

func BaseTestsInit() {
	TestLanguageSpecific()
	TestAfterConstructor()
	TestExtend()
	TestDeepExtend()
	TestCryptography()
	TestDatetime()
	TestDecimalToPrecision()
	TestNumberToString()
	TestPrecise()
	TestSafeMethods()
	TestSafeTicker()
	// testJson ();
	TestSortBy()
	TestSum()
	TestOmit()
	TestGroupBy()
	TestFilterBy()
	TestHandleMethods()
	TestRemoveRepeatedElementsFromArray()
	TestParsePrecision()
	TestArraysConcat()
}
