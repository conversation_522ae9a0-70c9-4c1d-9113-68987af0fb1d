package base

import "github.com/ccxt/ccxt/go/v4"

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

func LogTemplate(exchange ccxt.ICoreExchange, method interface{}, entry interface{}) interface{} {
	// there are cases when exchange is undefined (eg. base tests)
	var id interface{} = Ternary(IsTrue((!IsEqual(exchange, nil))), exchange.GetId(), "undefined")
	var methodString interface{} = Ternary(IsTrue((!IsEqual(method, nil))), method, "undefined")
	var entryString interface{} = Ternary(IsTrue((!IsEqual(exchange, nil))), exchange.Json(entry), "")
	return Add(Add(Add(Add(Add(Add(" <<< ", id), " "), methodString), " ::: "), entryString), " >>> ")
}
func IsTemporaryFailure(e interface{}) interface{} {
	return IsTrue((IsInstance(e, OperationFailed))) && IsTrue((!IsTrue((IsInstance(e, OnMaintenance)))))
}
func StringValue(value interface{}) interface{} {
	var stringVal interface{} = nil
	if IsTrue(IsString(value)) {
		stringVal = value
	} else if IsTrue(IsEqual(value, nil)) {
		stringVal = "undefined"
	} else {
		stringVal = ToString(value)
	}
	return stringVal
}
func AssertType(exchange ccxt.ICoreExchange, skippedProperties interface{}, entry interface{}, key interface{}, format interface{}) interface{} {
	if IsTrue(InOp(skippedProperties, key)) {
		return nil
	}
	// because "typeof" string is not transpilable without === 'name', we list them manually at this moment
	var entryKeyVal interface{} = exchange.SafeValue(entry, key)
	var formatKeyVal interface{} = exchange.SafeValue(format, key)
	var same_string interface{} = IsTrue((IsString(entryKeyVal))) && IsTrue((IsString(formatKeyVal)))
	var same_numeric interface{} = IsTrue((IsNumber(entryKeyVal))) && IsTrue((IsNumber(formatKeyVal)))
	var same_boolean interface{} = IsTrue((IsTrue((IsEqual(entryKeyVal, true))) || IsTrue((IsEqual(entryKeyVal, false))))) && IsTrue((IsTrue((IsEqual(formatKeyVal, true))) || IsTrue((IsEqual(formatKeyVal, false)))))
	var same_array interface{} = IsTrue(IsArray(entryKeyVal)) && IsTrue(IsArray(formatKeyVal))
	var same_object interface{} = IsTrue((IsObject(entryKeyVal))) && IsTrue((IsObject(formatKeyVal)))
	var result interface{} = IsTrue(IsTrue(IsTrue(IsTrue(IsTrue((IsEqual(entryKeyVal, nil))) || IsTrue(same_string)) || IsTrue(same_numeric)) || IsTrue(same_boolean)) || IsTrue(same_array)) || IsTrue(same_object)
	return result
}
func AssertStructure(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, format interface{}, optionalArgs ...interface{}) {
	emptyAllowedFor := GetArg(optionalArgs, 0, nil)
	_ = emptyAllowedFor
	deep := GetArg(optionalArgs, 1, false)
	_ = deep
	var logText interface{} = LogTemplate(exchange, method, entry)
	Assert(!IsEqual(entry, nil), Add("item is null/undefined", logText))
	// get all expected & predefined keys for this specific item and ensure thos ekeys exist in parsed structure
	var allowEmptySkips interface{} = exchange.SafeList(skippedProperties, "allowNull", []interface{}{})
	if IsTrue(!IsEqual(emptyAllowedFor, nil)) {
		emptyAllowedFor = Concat(emptyAllowedFor, allowEmptySkips)
	}
	if IsTrue(IsArray(format)) {
		Assert(IsArray(entry), Add("entry is not an array", logText))
		var realLength interface{} = GetArrayLength(entry)
		var expectedLength interface{} = GetArrayLength(format)
		Assert(IsEqual(realLength, expectedLength), Add(Add("entry length is not equal to expected length of ", ToString(expectedLength)), logText))
		for i := 0; IsLessThan(i, GetArrayLength(format)); i++ {
			var emptyAllowedForThisKey interface{} = IsTrue((IsEqual(emptyAllowedFor, nil))) || IsTrue(exchange.InArray(i, emptyAllowedFor))
			var value interface{} = GetValue(entry, i)
			if IsTrue(InOp(skippedProperties, i)) {
				continue
			}
			// check when:
			// - it's not inside "allowe empty values" list
			// - it's not undefined
			if IsTrue(IsTrue(emptyAllowedForThisKey) && IsTrue((IsEqual(value, nil)))) {
				continue
			}
			Assert(!IsEqual(value, nil), Add(Add(ToString(i), " index is expected to have a value"), logText))
			// because of other langs, this is needed for arrays
			var typeAssertion interface{} = AssertType(exchange, skippedProperties, entry, i, format)
			Assert(typeAssertion, Add(Add(ToString(i), " index does not have an expected type "), logText))
		}
	} else {
		Assert(IsObject(entry), Add("entry is not an object", logText))
		var keys interface{} = ObjectKeys(format)
		for i := 0; IsLessThan(i, GetArrayLength(keys)); i++ {
			var key interface{} = GetValue(keys, i)
			if IsTrue(InOp(skippedProperties, key)) {
				continue
			}
			Assert(InOp(entry, key), Add(Add(Add("\"", StringValue(key)), "\" key is missing from structure"), logText))
			if IsTrue(InOp(skippedProperties, key)) {
				continue
			}
			var emptyAllowedForThisKey interface{} = IsTrue((IsEqual(emptyAllowedFor, nil))) || IsTrue(exchange.InArray(key, emptyAllowedFor))
			var value interface{} = GetValue(entry, key)
			// check when:
			// - it's not inside "allowe empty values" list
			// - it's not undefined
			if IsTrue(IsTrue(emptyAllowedForThisKey) && IsTrue((IsEqual(value, nil)))) {
				continue
			}
			// if it was in needed keys, then it should have value.
			Assert(!IsEqual(value, nil), Add(Add(Add("\"", StringValue(key)), "\" key is expected to have a value"), logText))
			// add exclusion for info key, as it can be any type
			if IsTrue(!IsEqual(key, "info")) {
				var typeAssertion interface{} = AssertType(exchange, skippedProperties, entry, key, format)
				Assert(typeAssertion, Add(Add(Add("\"", StringValue(key)), "\" key is neither undefined, neither of expected type"), logText))
				if IsTrue(deep) {
					if IsTrue(IsObject(value)) {
						AssertStructure(exchange, skippedProperties, method, value, GetValue(format, key), emptyAllowedFor, deep)
					}
				}
			}
		}
	}
}
func AssertTimestamp(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, optionalArgs ...interface{}) {
	nowToCheck := GetArg(optionalArgs, 0, nil)
	_ = nowToCheck
	keyNameOrIndex := GetArg(optionalArgs, 1, "timestamp")
	_ = keyNameOrIndex
	allowNull := GetArg(optionalArgs, 2, true)
	_ = allowNull
	var logText interface{} = LogTemplate(exchange, method, entry)
	var skipValue interface{} = exchange.SafeValue(skippedProperties, keyNameOrIndex)
	if IsTrue(!IsEqual(skipValue, nil)) {
		return // skipped
	}
	var isDateTimeObject interface{} = IsString(keyNameOrIndex)
	if IsTrue(isDateTimeObject) {
		Assert((InOp(entry, keyNameOrIndex)), Add(Add(Add("timestamp key \"", keyNameOrIndex), "\" is missing from structure"), logText))
	} else {
		// if index was provided (mostly from fetchOHLCV) then we check if it exists, as mandatory
		Assert(!IsTrue((IsEqual(GetValue(entry, keyNameOrIndex), nil))), Add(Add(Add("timestamp index ", StringValue(keyNameOrIndex)), " is undefined"), logText))
	}
	var ts interface{} = GetValue(entry, keyNameOrIndex)
	Assert(IsTrue(!IsEqual(ts, nil)) || IsTrue(allowNull), Add("timestamp is null", logText))
	if IsTrue(!IsEqual(ts, nil)) {
		Assert(IsNumber(ts), Add("timestamp is not numeric", logText))
		Assert(IsInt(ts), Add("timestamp should be an integer", logText))
		var minTs interface{} = 1230940800000                                                                                               // 03 Jan 2009 - first block
		var maxTs interface{} = 2147483648000                                                                                               // 19 Jan 2038 - max int
		Assert(IsGreaterThan(ts, minTs), Add(Add(Add("timestamp is impossible to be before ", ToString(minTs)), " (03.01.2009)"), logText)) // 03 Jan 2009 - first block
		Assert(IsLessThan(ts, maxTs), Add(Add(Add("timestamp more than ", ToString(maxTs)), " (19.01.2038)"), logText))                     // 19 Jan 2038 - int32 overflows // 7258118400000  -> Jan 1 2200
		if IsTrue(!IsEqual(nowToCheck, nil)) {
			var maxMsOffset interface{} = 60000 // 1 min
			Assert(IsLessThan(ts, Add(nowToCheck, maxMsOffset)), Add(Add(Add(Add(Add("returned item timestamp (", exchange.Iso8601(ts)), ") is ahead of the current time ("), exchange.Iso8601(nowToCheck)), ")"), logText))
		}
	}
}
func AssertTimestampAndDatetime(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, optionalArgs ...interface{}) {
	nowToCheck := GetArg(optionalArgs, 0, nil)
	_ = nowToCheck
	keyNameOrIndex := GetArg(optionalArgs, 1, "timestamp")
	_ = keyNameOrIndex
	allowNull := GetArg(optionalArgs, 2, true)
	_ = allowNull
	var logText interface{} = LogTemplate(exchange, method, entry)
	var skipValue interface{} = exchange.SafeValue(skippedProperties, keyNameOrIndex)
	if IsTrue(!IsEqual(skipValue, nil)) {
		return
	}
	AssertTimestamp(exchange, skippedProperties, method, entry, nowToCheck, keyNameOrIndex)
	var isDateTimeObject interface{} = IsString(keyNameOrIndex)
	// only in case if the entry is a dictionary, thus it must have 'timestamp' & 'datetime' string keys
	if IsTrue(isDateTimeObject) {
		// we also test 'datetime' here because it's certain sibling of 'timestamp'
		Assert((InOp(entry, "datetime")), Add("\"datetime\" key is missing from structure", logText))
		var dt interface{} = GetValue(entry, "datetime")
		Assert(IsTrue(!IsEqual(dt, nil)) || IsTrue(allowNull), Add("timestamp is null", logText))
		if IsTrue(!IsEqual(dt, nil)) {
			Assert(IsString(dt), Add("\"datetime\" key does not have a string value", logText))
			// there are exceptional cases, like getting microsecond-targeted string '2022-08-08T22:03:19.014680Z', so parsed unified timestamp, which carries only 13 digits (millisecond precision) can not be stringified back to microsecond accuracy, causing the bellow Assertion to fail
			//    Assert (dt === exchange.Getiso8601() (entry['timestamp']))
			// so, we have to compare with millisecond accururacy
			var dtParsed interface{} = exchange.Parse8601(dt)
			var dtParsedString interface{} = exchange.Iso8601(dtParsed)
			var dtEntryString interface{} = exchange.Iso8601(GetValue(entry, "timestamp"))
			Assert(IsEqual(dtParsedString, dtEntryString), Add(Add(Add(Add(Add("datetime is not iso8601 of timestamp:", dtParsedString), "(string) != "), dtEntryString), "(from ts)"), logText))
		}
	}
}
func AssertCurrencyCode(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, actualCode interface{}, optionalArgs ...interface{}) {
	expectedCode := GetArg(optionalArgs, 0, nil)
	_ = expectedCode
	allowNull := GetArg(optionalArgs, 1, true)
	_ = allowNull
	if IsTrue(IsTrue((InOp(skippedProperties, "currency"))) || IsTrue((InOp(skippedProperties, "currencyIdAndCode")))) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	Assert(IsTrue(!IsEqual(actualCode, nil)) || IsTrue(allowNull), Add("currency code is null", logText))
	if IsTrue(!IsEqual(actualCode, nil)) {
		Assert(IsString(actualCode), Add("currency code should be either undefined or a string", logText))
		Assert((InOp(exchange.GetCurrencies(), actualCode)), Add(Add(Add("currency code (\"", actualCode), "\") should be present in exchange.currencies"), logText))
		if IsTrue(!IsEqual(expectedCode, nil)) {
			Assert(IsEqual(actualCode, expectedCode), Add(Add(Add(Add(Add("currency code in response (\"", StringValue(actualCode)), "\") should be equal to expected code (\""), StringValue(expectedCode)), "\")"), logText))
		}
	}
}
func AssertValidCurrencyIdAndCode(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, currencyId interface{}, currencyCode interface{}, optionalArgs ...interface{}) {
	// this is exclusive exceptional key name to be used in `skip-tests.json`, to skip check for currency id and code
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(IsTrue((InOp(skippedProperties, "currency"))) || IsTrue((InOp(skippedProperties, "currencyIdAndCode")))) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var undefinedValues interface{} = IsTrue(IsEqual(currencyId, nil)) && IsTrue(IsEqual(currencyCode, nil))
	var definedValues interface{} = IsTrue(!IsEqual(currencyId, nil)) && IsTrue(!IsEqual(currencyCode, nil))
	Assert(IsTrue(undefinedValues) || IsTrue(definedValues), Add("currencyId and currencyCode should be either both defined or both undefined", logText))
	Assert(IsTrue(definedValues) || IsTrue(allowNull), Add("currency code and id is not defined", logText))
	if IsTrue(definedValues) {
		// check by code
		var currencyByCode interface{} = exchange.Currency(currencyCode)
		Assert(IsEqual(GetValue(currencyByCode, "id"), currencyId), Add(Add(Add(Add(Add("currencyId \"", StringValue(currencyId)), "\" does not match currency id from instance: \""), StringValue(GetValue(currencyByCode, "id"))), "\""), logText))
		// check by id
		var currencyById interface{} = exchange.SafeCurrency(currencyId)
		Assert(IsEqual(GetValue(currencyById, "code"), currencyCode), Add(Add(Add(Add("currencyCode ", StringValue(currencyCode)), " does not match currency of id: "), StringValue(currencyId)), logText))
	}
}
func AssertSymbol(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, optionalArgs ...interface{}) {
	expectedSymbol := GetArg(optionalArgs, 0, nil)
	_ = expectedSymbol
	allowNull := GetArg(optionalArgs, 1, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var actualSymbol interface{} = exchange.SafeString(entry, key)
	if IsTrue(!IsEqual(actualSymbol, nil)) {
		Assert(IsString(actualSymbol), Add("symbol should be either undefined or a string", logText))
	}
	if IsTrue(!IsEqual(expectedSymbol, nil)) {
		Assert(IsEqual(actualSymbol, expectedSymbol), Add(Add(Add(Add(Add("symbol in response (\"", StringValue(actualSymbol)), "\") should be equal to expected symbol (\""), StringValue(expectedSymbol)), "\")"), logText))
	}
	var definedValues interface{} = IsTrue(!IsEqual(actualSymbol, nil)) && IsTrue(!IsEqual(expectedSymbol, nil))
	Assert(IsTrue(definedValues) || IsTrue(allowNull), Add("symbols are not defined", logText))
}
func AssertSymbolInMarkets(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, symbol interface{}) {
	var logText interface{} = LogTemplate(exchange, method, map[string]interface{}{})
	Assert((InOp(exchange.GetMarkets(), symbol)), Add("symbol should be present in exchange.symbols", logText))
}
func AssertGreater(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, compareTo interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var value interface{} = exchange.SafeString(entry, key)
	Assert(IsTrue(!IsEqual(value, nil)) || IsTrue(allowNull), Add("value is null", logText))
	if IsTrue(!IsEqual(value, nil)) {
		Assert(ccxt.Precise.StringGt(value, compareTo), Add(Add(Add(Add(Add(StringValue(key), " key (with a value of "), StringValue(value)), ") was expected to be > "), StringValue(compareTo)), logText))
	}
}
func AssertGreaterOrEqual(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, compareTo interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var value interface{} = exchange.SafeString(entry, key)
	Assert(IsTrue(!IsEqual(value, nil)) || IsTrue(allowNull), Add("value is null", logText))
	if IsTrue(IsTrue(!IsEqual(value, nil)) && IsTrue(!IsEqual(compareTo, nil))) {
		Assert(ccxt.Precise.StringGe(value, compareTo), Add(Add(Add(Add(Add(StringValue(key), " key (with a value of "), StringValue(value)), ") was expected to be >= "), StringValue(compareTo)), logText))
	}
}
func AssertLess(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, compareTo interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var value interface{} = exchange.SafeString(entry, key)
	Assert(IsTrue(!IsEqual(value, nil)) || IsTrue(allowNull), Add("value is null", logText))
	if IsTrue(IsTrue(!IsEqual(value, nil)) && IsTrue(!IsEqual(compareTo, nil))) {
		Assert(ccxt.Precise.StringLt(value, compareTo), Add(Add(Add(Add(Add(StringValue(key), " key (with a value of "), StringValue(value)), ") was expected to be < "), StringValue(compareTo)), logText))
	}
}
func AssertLessOrEqual(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, compareTo interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var value interface{} = exchange.SafeString(entry, key)
	Assert(IsTrue(!IsEqual(value, nil)) || IsTrue(allowNull), Add("value is null", logText))
	if IsTrue(IsTrue(!IsEqual(value, nil)) && IsTrue(!IsEqual(compareTo, nil))) {
		Assert(ccxt.Precise.StringLe(value, compareTo), Add(Add(Add(Add(Add(StringValue(key), " key (with a value of "), StringValue(value)), ") was expected to be <= "), StringValue(compareTo)), logText))
	}
}
func AssertEqual(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, compareTo interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var value interface{} = exchange.SafeString(entry, key)
	Assert(IsTrue(!IsEqual(value, nil)) || IsTrue(allowNull), Add("value is null", logText))
	if IsTrue(IsTrue(!IsEqual(value, nil)) && IsTrue(!IsEqual(compareTo, nil))) {
		Assert(ccxt.Precise.StringEq(value, compareTo), Add(Add(Add(Add(Add(StringValue(key), " key (with a value of "), StringValue(value)), ") was expected to be equal to "), StringValue(compareTo)), logText))
	}
}
func AssertNonEqual(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, compareTo interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var value interface{} = exchange.SafeString(entry, key)
	Assert(IsTrue(!IsEqual(value, nil)) || IsTrue(allowNull), Add("value is null", logText))
	if IsTrue(!IsEqual(value, nil)) {
		Assert(!IsTrue(ccxt.Precise.StringEq(value, compareTo)), Add(Add(Add(Add(Add(StringValue(key), " key (with a value of "), StringValue(value)), ") was expected not to be equal to "), StringValue(compareTo)), logText))
	}
}
func AssertInArray(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, expectedArray interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var value interface{} = exchange.SafeValue(entry, key)
	Assert(IsTrue(!IsEqual(value, nil)) || IsTrue(allowNull), Add("value is null", logText))
	// todo: remove undefined check
	if IsTrue(!IsEqual(value, nil)) {
		var stingifiedArrayValue interface{} = exchange.Json(expectedArray) // don't use expectedArray.join (','), as it bugs in other languages, if values are bool, undefined or etc..
		Assert(exchange.InArray(value, expectedArray), Add(Add(Add(Add(Add(Add(Add("\"", StringValue(key)), "\" key (value \""), StringValue(value)), "\") is not from the expected list : ["), stingifiedArrayValue), "]"), logText))
	}
}
func AssertFeeStructure(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	var logText interface{} = LogTemplate(exchange, method, entry)
	var keyString interface{} = StringValue(key)
	if IsTrue(IsInt(key)) {
		Assert(IsArray(entry), Add("fee container is expected to be an array", logText))
		Assert(IsLessThan(key, GetArrayLength(entry)), Add(Add(Add("fee key ", keyString), " was expected to be present in entry"), logText))
	} else {
		Assert(IsObject(entry), Add("fee container is expected to be an object", logText))
		Assert(InOp(entry, key), Add(Add(Add("fee key \"", key), "\" was expected to be present in entry"), logText))
	}
	var feeObject interface{} = exchange.SafeValue(entry, key)
	Assert(IsTrue(!IsEqual(feeObject, nil)) || IsTrue(allowNull), Add("fee object is null", logText))
	// todo: remove undefined check to make stricter
	if IsTrue(!IsEqual(feeObject, nil)) {
		Assert(InOp(feeObject, "cost"), Add(Add(keyString, " fee object should contain \"cost\" key"), logText))
		if IsTrue(IsEqual(GetValue(feeObject, "cost"), nil)) {
			return // todo: remove undefined check to make stricter
		}
		Assert(IsNumber(GetValue(feeObject, "cost")), Add(Add(keyString, " \"cost\" must be numeric type"), logText))
		// AssertGreaterOrEqual (exchange, skippedProperties, method, feeObject, 'cost', '0'); // fee might be negative in the case of a rebate or reward
		Assert(InOp(feeObject, "currency"), Add(Add(Add("\"", keyString), "\" fee object should contain \"currency\" key"), logText))
		AssertCurrencyCode(exchange, skippedProperties, method, entry, GetValue(feeObject, "currency"))
	}
}
func AssertTimestampOrder(exchange ccxt.ICoreExchange, method interface{}, codeOrSymbol interface{}, items interface{}, optionalArgs ...interface{}) {
	ascending := GetArg(optionalArgs, 0, true)
	_ = ascending
	for i := 0; IsLessThan(i, GetArrayLength(items)); i++ {
		if IsTrue(IsGreaterThan(i, 0)) {
			var currentTs interface{} = GetValue(GetValue(items, Subtract(i, 1)), "timestamp")
			var nextTs interface{} = GetValue(GetValue(items, i), "timestamp")
			if IsTrue(IsTrue(!IsEqual(currentTs, nil)) && IsTrue(!IsEqual(nextTs, nil))) {
				var ascendingOrDescending interface{} = Ternary(IsTrue(ascending), "ascending", "descending")
				var comparison interface{} = Ternary(IsTrue(ascending), (IsLessThanOrEqual(currentTs, nextTs)), (IsGreaterThanOrEqual(currentTs, nextTs)))
				Assert(comparison, Add(Add(Add(Add(Add(Add(Add(Add(Add(Add(Add(Add(exchange.GetId(), " "), method), " "), StringValue(codeOrSymbol)), " must return a "), ascendingOrDescending), " sorted array of items by timestamp, but "), ToString(currentTs)), " is opposite with its next "), ToString(nextTs)), " "), exchange.Json(items)))
			}
		}
	}
}
func AssertInteger(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}, optionalArgs ...interface{}) {
	allowNull := GetArg(optionalArgs, 0, true)
	_ = allowNull
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	if IsTrue(!IsEqual(entry, nil)) {
		var value interface{} = exchange.SafeValue(entry, key)
		Assert(IsTrue(!IsEqual(value, nil)) || IsTrue(allowNull), Add("value is null", logText))
		if IsTrue(!IsEqual(value, nil)) {
			var isInteger interface{} = IsInt(value)
			Assert(isInteger, Add(Add(Add(Add(Add("\"", StringValue(key)), "\" key (value \""), StringValue(value)), "\") is not an integer"), logText))
		}
	}
}
func CheckPrecisionAccuracy(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}) {
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	if IsTrue(exchange.IsTickPrecision()) {
		// TICK_SIZE should be above zero
		AssertGreater(exchange, skippedProperties, method, entry, key, "0")
		// the below array of integers are inexistent tick-sizes (theoretically technically possible, but not in real-world cases), so their existence in our case indicates to incorrectly implemented tick-sizes, which might mistakenly be implemented with DECIMAL_PLACES, so we throw error
		var decimalNumbers interface{} = []interface{}{"2", "3", "4", "5", "6", "7", "8", "9", "11", "12", "13", "14", "15", "16"}
		for i := 0; IsLessThan(i, GetArrayLength(decimalNumbers)); i++ {
			var num interface{} = GetValue(decimalNumbers, i)
			var numStr interface{} = num
			AssertNonEqual(exchange, skippedProperties, method, entry, key, numStr)
		}
	} else {
		// todo: significant-digits return doubles from `this.parseNumber`, so for now can't Assert against integer atm
		// AssertInteger (exchange, skippedProperties, method, entry, key); // should be integer
		AssertLessOrEqual(exchange, skippedProperties, method, entry, key, "18")    // should be under 18 decimals
		AssertGreaterOrEqual(exchange, skippedProperties, method, entry, key, "-8") // in real-world cases, there would not be less than that
	}
}
func FetchBestBidAsk(exchange ccxt.ICoreExchange, method interface{}, symbol interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		var logText interface{} = LogTemplate(exchange, method, map[string]interface{}{})
		// find out best bid/ask price
		var bestBid interface{} = nil
		var bestAsk interface{} = nil
		var usedMethod interface{} = nil
		if IsTrue(GetValue(exchange.GetHas(), "fetchOrderBook")) {
			usedMethod = "fetchOrderBook"

			orderbook := (<-exchange.FetchOrderBook(symbol))
			PanicOnError(orderbook)
			var bids interface{} = exchange.SafeList(orderbook, "bids")
			var asks interface{} = exchange.SafeList(orderbook, "asks")
			var bestBidArray interface{} = exchange.SafeList(bids, 0)
			var bestAskArray interface{} = exchange.SafeList(asks, 0)
			bestBid = exchange.SafeNumber(bestBidArray, 0)
			bestAsk = exchange.SafeNumber(bestAskArray, 0)
		} else if IsTrue(GetValue(exchange.GetHas(), "fetchBidsAsks")) {
			usedMethod = "fetchBidsAsks"

			tickers := (<-exchange.FetchBidsAsks([]interface{}{symbol}))
			PanicOnError(tickers)
			var ticker interface{} = exchange.SafeDict(tickers, symbol)
			bestBid = exchange.SafeNumber(ticker, "bid")
			bestAsk = exchange.SafeNumber(ticker, "ask")
		} else if IsTrue(GetValue(exchange.GetHas(), "fetchTicker")) {
			usedMethod = "fetchTicker"

			ticker := (<-exchange.FetchTicker(symbol))
			PanicOnError(ticker)
			bestBid = exchange.SafeNumber(ticker, "bid")
			bestAsk = exchange.SafeNumber(ticker, "ask")
		} else if IsTrue(GetValue(exchange.GetHas(), "fetchTickers")) {
			usedMethod = "fetchTickers"

			tickers := (<-exchange.FetchTickers([]interface{}{symbol}))
			PanicOnError(tickers)
			var ticker interface{} = exchange.SafeDict(tickers, symbol)
			bestBid = exchange.SafeNumber(ticker, "bid")
			bestAsk = exchange.SafeNumber(ticker, "ask")
		}
		//
		Assert(IsTrue(!IsEqual(bestBid, nil)) && IsTrue(!IsEqual(bestAsk, nil)), Add(Add(Add(Add(Add(Add(Add(Add(logText, " "), exchange.GetId()), " could not get best bid/ask for "), symbol), " using "), usedMethod), " while testing "), method))

		ch <- []interface{}{bestBid, bestAsk}
		return nil

	}()
	return ch
}
func FetchOrder(exchange ccxt.ICoreExchange, symbol interface{}, orderId interface{}, skippedProperties interface{}) <-chan interface{} {
	ch := make(chan interface{})
	go func() interface{} {
		defer close(ch)
		defer ReturnPanicError(ch)
		var fetchedOrder interface{} = nil
		var originalId interface{} = orderId
		// set 'since' to 5 minute ago for optimal results
		var sinceTime interface{} = Subtract(exchange.Milliseconds(), Multiply(Multiply(1000, 60), 5))
		// iterate
		var methods_singular interface{} = []interface{}{"fetchOrder", "fetchOpenOrder", "fetchClosedOrder", "fetchCanceledOrder"}
		for i := 0; IsLessThan(i, GetArrayLength(methods_singular)); i++ {
			var singularFetchName interface{} = GetValue(methods_singular, i)
			if IsTrue(GetValue(exchange.GetHas(), singularFetchName)) {

				currentOrder := (<-callDynamically(singularFetchName, originalId, symbol))
				PanicOnError(currentOrder)
				// if there is an id inside the order, it means the order was fetched successfully
				if IsTrue(IsEqual(GetValue(currentOrder, "id"), originalId)) {
					fetchedOrder = currentOrder
					break
				}
			}
		}
		//
		// search through plural methods
		if IsTrue(IsEqual(fetchedOrder, nil)) {
			var methods_plural interface{} = []interface{}{"fetchOrders", "fetchOpenOrders", "fetchClosedOrders", "fetchCanceledOrders"}
			for i := 0; IsLessThan(i, GetArrayLength(methods_plural)); i++ {
				var pluralFetchName interface{} = GetValue(methods_plural, i)
				if IsTrue(GetValue(exchange.GetHas(), pluralFetchName)) {

					orders := (<-callDynamically(pluralFetchName, symbol, sinceTime))
					PanicOnError(orders)
					var found interface{} = false
					for j := 0; IsLessThan(j, GetArrayLength(orders)); j++ {
						var currentOrder interface{} = GetValue(orders, j)
						if IsTrue(IsEqual(GetValue(currentOrder, "id"), originalId)) {
							fetchedOrder = currentOrder
							found = true
							break
						}
					}
					if IsTrue(found) {
						break
					}
				}
			}
		}

		ch <- fetchedOrder
		return nil

	}()
	return ch
}
func AssertOrderState(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, order interface{}, AssertedStatus interface{}, strictCheck interface{}) {
	// note, `strictCheck` is `true` only from "fetchOrder" cases
	var logText interface{} = LogTemplate(exchange, method, order)
	var msg interface{} = Add(Add(Add("order should be ", AssertedStatus), ", but it was not Asserted"), logText)
	var filled interface{} = exchange.SafeString(order, "filled")
	var amount interface{} = exchange.SafeString(order, "amount")
	// shorthand variables
	var statusUndefined interface{} = (IsEqual(GetValue(order, "status"), nil))
	var statusOpen interface{} = (IsEqual(GetValue(order, "status"), "open"))
	var statusClosed interface{} = (IsEqual(GetValue(order, "status"), "closed"))
	var statusClanceled interface{} = (IsEqual(GetValue(order, "status"), "canceled"))
	var filledDefined interface{} = (!IsEqual(filled, nil))
	var amountDefined interface{} = (!IsEqual(amount, nil))
	var condition interface{} = nil
	//
	// ### OPEN STATUS
	//
	// if strict check, then 'status' must be 'open' and filled amount should be less then whole order amount
	var strictOpen interface{} = IsTrue(statusOpen) && IsTrue((IsTrue(IsTrue(filledDefined) && IsTrue(amountDefined)) && IsTrue(IsLessThan(filled, amount))))
	// if non-strict check, then accept & ignore undefined values
	var nonstrictOpen interface{} = IsTrue((IsTrue(statusOpen) || IsTrue(statusUndefined))) && IsTrue((IsTrue((!IsTrue(filledDefined) || !IsTrue(amountDefined))) || IsTrue(ccxt.Precise.StringLt(filled, amount))))
	// check
	if IsTrue(IsEqual(AssertedStatus, "open")) {
		condition = Ternary(IsTrue(strictCheck), strictOpen, nonstrictOpen)
		Assert(condition, msg)
		return
	}
	//
	// ### CLOSED STATUS
	//
	// if strict check, then 'status' must be 'closed' and filled amount should be equal to the whole order amount
	var closedStrict interface{} = IsTrue(statusClosed) && IsTrue((IsTrue(IsTrue(filledDefined) && IsTrue(amountDefined)) && IsTrue(ccxt.Precise.StringEq(filled, amount))))
	// if non-strict check, then accept & ignore undefined values
	var closedNonStrict interface{} = IsTrue((IsTrue(statusClosed) || IsTrue(statusUndefined))) && IsTrue((IsTrue((!IsTrue(filledDefined) || !IsTrue(amountDefined))) || IsTrue(ccxt.Precise.StringEq(filled, amount))))
	// check
	if IsTrue(IsEqual(AssertedStatus, "closed")) {
		condition = Ternary(IsTrue(strictCheck), closedStrict, closedNonStrict)
		Assert(condition, msg)
		return
	}
	//
	// ### CANCELED STATUS
	//
	// if strict check, then 'status' must be 'canceled' and filled amount should be less then whole order amount
	var canceledStrict interface{} = IsTrue(statusClanceled) && IsTrue((IsTrue(IsTrue(filledDefined) && IsTrue(amountDefined)) && IsTrue(ccxt.Precise.StringLt(filled, amount))))
	// if non-strict check, then accept & ignore undefined values
	var canceledNonStrict interface{} = IsTrue((IsTrue(statusClanceled) || IsTrue(statusUndefined))) && IsTrue((IsTrue((!IsTrue(filledDefined) || !IsTrue(amountDefined))) || IsTrue(ccxt.Precise.StringLt(filled, amount))))
	// check
	if IsTrue(IsEqual(AssertedStatus, "canceled")) {
		condition = Ternary(IsTrue(strictCheck), canceledStrict, canceledNonStrict)
		Assert(condition, msg)
		return
	}
	//
	// ### CLOSED_or_CANCELED STATUS
	//
	if IsTrue(IsEqual(AssertedStatus, "closed_or_canceled")) {
		condition = Ternary(IsTrue(strictCheck), (IsTrue(closedStrict) || IsTrue(canceledStrict)), (IsTrue(closedNonStrict) || IsTrue(canceledNonStrict)))
		Assert(condition, msg)
		return
	}
}
func GetActiveMarkets(exchange ccxt.ICoreExchange, optionalArgs ...interface{}) interface{} {
	includeUnknown := GetArg(optionalArgs, 0, true)
	_ = includeUnknown
	var filteredActive interface{} = exchange.FilterBy(exchange.GetMarkets(), "active", true)
	if IsTrue(includeUnknown) {
		var filteredUndefined interface{} = exchange.FilterBy(exchange.GetMarkets(), "active", nil)
		return exchange.ArrayConcat(filteredActive, filteredUndefined)
	}
	return filteredActive
}
func RemoveProxyOptions(exchange ccxt.ICoreExchange, skippedProperties interface{}) interface{} {
	var proxyUrl interface{} = exchange.CheckProxyUrlSettings()
	httpProxyhttpsProxysocksProxyVariable := exchange.CheckProxySettings()
	httpProxy := GetValue(httpProxyhttpsProxysocksProxyVariable, 0)
	httpsProxy := GetValue(httpProxyhttpsProxysocksProxyVariable, 1)
	socksProxy := GetValue(httpProxyhttpsProxysocksProxyVariable, 2)
	// because of bug in transpiled, about `.proxyUrl` being transpiled into `.proxy_url`, we have to use this workaround
	exchange.SetProperty(exchange, "proxyUrl", nil)
	exchange.SetProperty(exchange, "proxy_url", nil)
	exchange.SetProperty(exchange, "httpProxy", nil)
	exchange.SetProperty(exchange, "http_proxy", nil)
	exchange.SetProperty(exchange, "httpsProxy", nil)
	exchange.SetProperty(exchange, "https_proxy", nil)
	exchange.SetProperty(exchange, "socksProxy", nil)
	exchange.SetProperty(exchange, "socks_proxy", nil)
	return []interface{}{proxyUrl, httpProxy, httpsProxy, socksProxy}
}
func SetProxyOptions(exchange ccxt.ICoreExchange, skippedProperties interface{}, proxyUrl interface{}, httpProxy interface{}, httpsProxy interface{}, socksProxy interface{}) {
	exchange.SetProxyUrl(proxyUrl)
	exchange.SetHttpProxy(httpProxy)
	exchange.SetHttpsProxy(httpsProxy)
	exchange.SetSocksProxy(socksProxy)
}
func Concat(optionalArgs ...interface{}) interface{} {
	// we use this method temporarily, because of ast-transpiler issue across langs
	a := GetArg(optionalArgs, 0, nil)
	_ = a
	b := GetArg(optionalArgs, 1, nil)
	_ = b
	if IsTrue(IsEqual(a, nil)) {
		return b
	} else if IsTrue(IsEqual(b, nil)) {
		return a
	} else {
		var result interface{} = []interface{}{}
		for i := 0; IsLessThan(i, GetArrayLength(a)); i++ {
			AppendToArray(&result, GetValue(a, i))
		}
		for j := 0; IsLessThan(j, GetArrayLength(b)); j++ {
			AppendToArray(&result, GetValue(b, j))
		}
		return result
	}
}
func AssertNonEmtpyArray(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, optionalArgs ...interface{}) {
	hint := GetArg(optionalArgs, 0, nil)
	_ = hint
	var logText interface{} = LogTemplate(exchange, method, entry)
	if IsTrue(!IsEqual(hint, nil)) {
		logText = Add(Add(logText, " "), hint)
	}
	Assert(IsArray(entry), Add("response is expected to be an array", logText))
	if !IsTrue((InOp(skippedProperties, "emptyResponse"))) {
		return
	}
	Assert(IsGreaterThan(GetArrayLength(entry), 0), Add(Add("response is expected to be a non-empty array", logText), " (add \"emptyResponse\" in skip-tests.json to skip this check)"))
}
func AssertRoundMinuteTimestamp(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, entry interface{}, key interface{}) {
	if IsTrue(InOp(skippedProperties, key)) {
		return
	}
	var logText interface{} = LogTemplate(exchange, method, entry)
	var ts interface{} = exchange.SafeString(entry, key)
	Assert(IsEqual(ccxt.Precise.StringMod(ts, "60000"), "0"), Add("timestamp should be a multiple of 60 seconds (1 minute)", logText))
}
func DeepEqual(a interface{}, b interface{}) interface{} {
	return IsEqual(JsonStringify(a), JsonStringify(b))
}
func AssertDeepEqual(exchange ccxt.ICoreExchange, skippedProperties interface{}, method interface{}, a interface{}, b interface{}) {
	var logText interface{} = LogTemplate(exchange, method, map[string]interface{}{})
	Assert(DeepEqual(a, b), Add(Add(Add(Add("two dicts do not match: ", JsonStringify(a)), " != "), JsonStringify(b)), logText))
}
