<?php
namespace ccxt;

// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------
use React\Async;
use React\Promise;
include_once PATH_TO_CCXT . '/test/exchange/base/test_ohlcv.php';
include_once PATH_TO_CCXT . '/test/exchange/base/test_shared_methods.php';

function test_watch_ohlcv_for_symbols($exchange, $skipped_properties, $symbol) {
    return Async\async(function () use ($exchange, $skipped_properties, $symbol) {
        $method = 'watchOHLCVForSymbols';
        $now = $exchange->milliseconds();
        $ends = $now + 15000;
        $timeframe_keys = is_array($exchange->timeframes) ? array_keys($exchange->timeframes) : array();
        assert(count($timeframe_keys), $exchange->id . ' ' . $method . ' - no timeframes found');
        // prefer 1m timeframe if available, otherwise return the first one
        $chosen_timeframe_key = '1m';
        if (!$exchange->in_array($chosen_timeframe_key, $timeframe_keys)) {
            $chosen_timeframe_key = $timeframe_keys[0];
        }
        $limit = 10;
        $duration = $exchange->parse_timeframe($chosen_timeframe_key);
        $since = $exchange->milliseconds() - $duration * $limit * 1000 - 1000;
        while ($now < $ends) {
            $response = null;
            $success = true;
            try {
                $response = Async\await($exchange->watch_ohlcv_for_symbols([[$symbol, $chosen_timeframe_key]], $since, $limit));
            } catch(\Throwable $e) {
                if (!is_temporary_failure($e)) {
                    throw $e;
                }
                $now = $exchange->milliseconds();
                // continue;
                $success = false;
            }
            if ($success === true) {
                $assertion_message = $exchange->id . ' ' . $method . ' ' . $symbol . ' ' . $chosen_timeframe_key . ' | ' . $exchange->json($response);
                assert(is_array($response), 'Response must be a dictionary. ' . $assertion_message);
                assert(is_array($response) && array_key_exists($symbol, $response), 'Response should contain the symbol as key. ' . $assertion_message);
                $symbol_obj = $response[$symbol];
                assert(is_array($symbol_obj), 'Response.Symbol should be a dictionary. ' . $assertion_message);
                assert(is_array($symbol_obj) && array_key_exists($chosen_timeframe_key, $symbol_obj), 'Response.symbol should contain the timeframe key. ' . $assertion_message);
                $ohlcvs = $symbol_obj[$chosen_timeframe_key];
                assert(gettype($ohlcvs) === 'array' && array_is_list($ohlcvs), 'Response.symbol.timeframe should be an array. ' . $assertion_message);
                $now = $exchange->milliseconds();
                for ($i = 0; $i < count($ohlcvs); $i++) {
                    test_ohlcv($exchange, $skipped_properties, $method, $ohlcvs[$i], $symbol, $now);
                }
            }
        }
        return true;
    }) ();
}
