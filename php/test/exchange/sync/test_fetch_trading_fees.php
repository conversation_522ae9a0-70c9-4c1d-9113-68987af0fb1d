<?php
namespace ccxt;

// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code

// -----------------------------------------------------------------------------
include_once PATH_TO_CCXT . '/test/exchange/base/test_trading_fee.php';

function test_fetch_trading_fees($exchange, $skipped_properties) {
    $method = 'fetchTradingFees';
    $fees = $exchange->fetch_trading_fees();
    $symbols = is_array($fees) ? array_keys($fees) : array();
    assert_non_emtpy_array($exchange, $skipped_properties, $method, $symbols);
    for ($i = 0; $i < count($symbols); $i++) {
        $symbol = $symbols[$i];
        test_trading_fee($exchange, $skipped_properties, $method, $symbol, $fees[$symbol]);
    }
    return true;
}
