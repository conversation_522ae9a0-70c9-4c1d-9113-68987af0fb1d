<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: PublicAggreDealsV3Api.proto

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PublicAggreDealsV3ApiItem</code>
 */
class PublicAggreDealsV3ApiItem extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string price = 1;</code>
     */
    protected $price = '';
    /**
     * Generated from protobuf field <code>string quantity = 2;</code>
     */
    protected $quantity = '';
    /**
     * Generated from protobuf field <code>int32 tradeType = 3;</code>
     */
    protected $tradeType = 0;
    /**
     * Generated from protobuf field <code>int64 time = 4;</code>
     */
    protected $time = 0;

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $price
     *     @type string $quantity
     *     @type int $tradeType
     *     @type int|string $time
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\PublicAggreDealsV3Api::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string price = 1;</code>
     * @return string
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * Generated from protobuf field <code>string price = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->price = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string quantity = 2;</code>
     * @return string
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * Generated from protobuf field <code>string quantity = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setQuantity($var)
    {
        GPBUtil::checkString($var, True);
        $this->quantity = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int32 tradeType = 3;</code>
     * @return int
     */
    public function getTradeType()
    {
        return $this->tradeType;
    }

    /**
     * Generated from protobuf field <code>int32 tradeType = 3;</code>
     * @param int $var
     * @return $this
     */
    public function setTradeType($var)
    {
        GPBUtil::checkInt32($var);
        $this->tradeType = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>int64 time = 4;</code>
     * @return int|string
     */
    public function getTime()
    {
        return $this->time;
    }

    /**
     * Generated from protobuf field <code>int64 time = 4;</code>
     * @param int|string $var
     * @return $this
     */
    public function setTime($var)
    {
        GPBUtil::checkInt64($var);
        $this->time = $var;

        return $this;
    }

}

