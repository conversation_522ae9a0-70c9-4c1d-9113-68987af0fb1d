<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: PublicLimitDepthsV3Api.proto

use Google\Protobuf\Internal\GPBType;
use Google\Protobuf\Internal\RepeatedField;
use Google\Protobuf\Internal\GPBUtil;

/**
 * Generated from protobuf message <code>PublicLimitDepthV3ApiItem</code>
 */
class PublicLimitDepthV3ApiItem extends \Google\Protobuf\Internal\Message
{
    /**
     * Generated from protobuf field <code>string price = 1;</code>
     */
    protected $price = '';
    /**
     * Generated from protobuf field <code>string quantity = 2;</code>
     */
    protected $quantity = '';

    /**
     * Constructor.
     *
     * @param array $data {
     *     Optional. Data for populating the Message object.
     *
     *     @type string $price
     *     @type string $quantity
     * }
     */
    public function __construct($data = NULL) {
        \GPBMetadata\PublicLimitDepthsV3Api::initOnce();
        parent::__construct($data);
    }

    /**
     * Generated from protobuf field <code>string price = 1;</code>
     * @return string
     */
    public function getPrice()
    {
        return $this->price;
    }

    /**
     * Generated from protobuf field <code>string price = 1;</code>
     * @param string $var
     * @return $this
     */
    public function setPrice($var)
    {
        GPBUtil::checkString($var, True);
        $this->price = $var;

        return $this;
    }

    /**
     * Generated from protobuf field <code>string quantity = 2;</code>
     * @return string
     */
    public function getQuantity()
    {
        return $this->quantity;
    }

    /**
     * Generated from protobuf field <code>string quantity = 2;</code>
     * @param string $var
     * @return $this
     */
    public function setQuantity($var)
    {
        GPBUtil::checkString($var, True);
        $this->quantity = $var;

        return $this;
    }

}

