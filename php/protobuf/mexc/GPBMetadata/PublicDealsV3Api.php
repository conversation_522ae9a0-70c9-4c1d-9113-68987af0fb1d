<?php
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: PublicDealsV3Api.proto

namespace GPBMetadata;

class PublicDealsV3Api
{
    public static $is_initialized = false;

    public static function initOnce() {
        $pool = \Google\Protobuf\Internal\DescriptorPool::getGeneratedPool();

        if (static::$is_initialized == true) {
          return;
        }
        $pool->internalAddGeneratedFile(
            "\x0A\x82\x02\x0A\x16PublicDealsV3Api.proto\"K\x0A\x10PublicDealsV3Api\x12\$\x0A\x05deals\x18\x01 \x03(\x0B2\x15.PublicDealsV3ApiItem\x12\x11\x0A\x09eventType\x18\x02 \x01(\x09\"X\x0A\x14PublicDealsV3ApiItem\x12\x0D\x0A\x05price\x18\x01 \x01(\x09\x12\x10\x0A\x08quantity\x18\x02 \x01(\x09\x12\x11\x0A\x09tradeType\x18\x03 \x01(\x05\x12\x0C\x0A\x04time\x18\x04 \x01(\x03B9\x0A\x1Ccom.mxc.push.common.protobufB\x15PublicDealsV3ApiProtoH\x01P\x01b\x06proto3"
        , true);

        static::$is_initialized = true;
    }
}

