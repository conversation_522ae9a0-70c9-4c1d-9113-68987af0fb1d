<?php

namespace ccxt\async\abstract;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code


abstract class bitfinex extends \ccxt\async\Exchange {
    public function public_get_conf_config($params = array()) {
        return $this->request('conf/{config}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_action_object($params = array()) {
        return $this->request('conf/pub:{action}:{object}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_action_object_detail($params = array()) {
        return $this->request('conf/pub:{action}:{object}:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_object($params = array()) {
        return $this->request('conf/pub:map:{object}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_object_detail($params = array()) {
        return $this->request('conf/pub:map:{object}:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_currency_detail($params = array()) {
        return $this->request('conf/pub:map:currency:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_currency_sym($params = array()) {
        return $this->request('conf/pub:map:currency:sym', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_currency_label($params = array()) {
        return $this->request('conf/pub:map:currency:label', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_currency_unit($params = array()) {
        return $this->request('conf/pub:map:currency:unit', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_currency_undl($params = array()) {
        return $this->request('conf/pub:map:currency:undl', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_currency_pool($params = array()) {
        return $this->request('conf/pub:map:currency:pool', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_currency_explorer($params = array()) {
        return $this->request('conf/pub:map:currency:explorer', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_currency_tx_fee($params = array()) {
        return $this->request('conf/pub:map:currency:tx:fee', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_map_tx_method($params = array()) {
        return $this->request('conf/pub:map:tx:method', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_list_object($params = array()) {
        return $this->request('conf/pub:list:{object}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_list_object_detail($params = array()) {
        return $this->request('conf/pub:list:{object}:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_list_currency($params = array()) {
        return $this->request('conf/pub:list:currency', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_list_pair_exchange($params = array()) {
        return $this->request('conf/pub:list:pair:exchange', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_list_pair_margin($params = array()) {
        return $this->request('conf/pub:list:pair:margin', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_list_pair_futures($params = array()) {
        return $this->request('conf/pub:list:pair:futures', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_list_competitions($params = array()) {
        return $this->request('conf/pub:list:competitions', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_info_object($params = array()) {
        return $this->request('conf/pub:info:{object}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_info_object_detail($params = array()) {
        return $this->request('conf/pub:info:{object}:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_info_pair($params = array()) {
        return $this->request('conf/pub:info:pair', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_info_pair_futures($params = array()) {
        return $this->request('conf/pub:info:pair:futures', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_info_tx_status($params = array()) {
        return $this->request('conf/pub:info:tx:status', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_conf_pub_fees($params = array()) {
        return $this->request('conf/pub:fees', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_platform_status($params = array()) {
        return $this->request('platform/status', 'public', 'GET', $params, null, null, array("cost" => 8));
    }
    public function public_get_tickers($params = array()) {
        return $this->request('tickers', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_ticker_symbol($params = array()) {
        return $this->request('ticker/{symbol}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_tickers_hist($params = array()) {
        return $this->request('tickers/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_trades_symbol_hist($params = array()) {
        return $this->request('trades/{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_book_symbol_precision($params = array()) {
        return $this->request('book/{symbol}/{precision}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_book_symbol_p0($params = array()) {
        return $this->request('book/{symbol}/P0', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_book_symbol_p1($params = array()) {
        return $this->request('book/{symbol}/P1', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_book_symbol_p2($params = array()) {
        return $this->request('book/{symbol}/P2', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_book_symbol_p3($params = array()) {
        return $this->request('book/{symbol}/P3', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_book_symbol_r0($params = array()) {
        return $this->request('book/{symbol}/R0', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_stats1_key_size_symbol_side_section($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:{side}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_side_last($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:{side}/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_side_hist($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:{side}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_section($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_last($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_hist($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_long_last($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:long/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_long_hist($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:long/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_short_last($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:short/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_stats1_key_size_symbol_short_hist($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:short/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_candles_trade_timeframe_symbol_period_section($params = array()) {
        return $this->request('candles/trade:{timeframe}:{symbol}:{period}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_candles_trade_timeframe_symbol_section($params = array()) {
        return $this->request('candles/trade:{timeframe}:{symbol}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_candles_trade_timeframe_symbol_last($params = array()) {
        return $this->request('candles/trade:{timeframe}:{symbol}/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_candles_trade_timeframe_symbol_hist($params = array()) {
        return $this->request('candles/trade:{timeframe}:{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_status_type($params = array()) {
        return $this->request('status/{type}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_status_deriv($params = array()) {
        return $this->request('status/deriv', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_status_deriv_symbol_hist($params = array()) {
        return $this->request('status/deriv/{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_liquidations_hist($params = array()) {
        return $this->request('liquidations/hist', 'public', 'GET', $params, null, null, array("cost" => 80));
    }
    public function public_get_rankings_key_timeframe_symbol_section($params = array()) {
        return $this->request('rankings/{key}:{timeframe}:{symbol}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_rankings_key_timeframe_symbol_hist($params = array()) {
        return $this->request('rankings/{key}:{timeframe}:{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_pulse_hist($params = array()) {
        return $this->request('pulse/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_pulse_profile_nickname($params = array()) {
        return $this->request('pulse/profile/{nickname}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function public_get_funding_stats_symbol_hist($params = array()) {
        return $this->request('funding/stats/{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 10));
    }
    public function public_get_ext_vasps($params = array()) {
        return $this->request('ext/vasps', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_post_calc_trade_avg($params = array()) {
        return $this->request('calc/trade/avg', 'public', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function public_post_calc_fx($params = array()) {
        return $this->request('calc/fx', 'public', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_wallets($params = array()) {
        return $this->request('auth/r/wallets', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_wallets_hist($params = array()) {
        return $this->request('auth/r/wallets/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_orders($params = array()) {
        return $this->request('auth/r/orders', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_orders_symbol($params = array()) {
        return $this->request('auth/r/orders/{symbol}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_order_submit($params = array()) {
        return $this->request('auth/w/order/submit', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_order_update($params = array()) {
        return $this->request('auth/w/order/update', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_order_cancel($params = array()) {
        return $this->request('auth/w/order/cancel', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_order_multi($params = array()) {
        return $this->request('auth/w/order/multi', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_order_cancel_multi($params = array()) {
        return $this->request('auth/w/order/cancel/multi', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_orders_symbol_hist($params = array()) {
        return $this->request('auth/r/orders/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_orders_hist($params = array()) {
        return $this->request('auth/r/orders/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_order_symbol_id_trades($params = array()) {
        return $this->request('auth/r/order/{symbol}:{id}/trades', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_trades_symbol_hist($params = array()) {
        return $this->request('auth/r/trades/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_trades_hist($params = array()) {
        return $this->request('auth/r/trades/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_ledgers_currency_hist($params = array()) {
        return $this->request('auth/r/ledgers/{currency}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_ledgers_hist($params = array()) {
        return $this->request('auth/r/ledgers/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_info_margin_key($params = array()) {
        return $this->request('auth/r/info/margin/{key}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_info_margin_base($params = array()) {
        return $this->request('auth/r/info/margin/base', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_info_margin_sym_all($params = array()) {
        return $this->request('auth/r/info/margin/sym_all', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_positions($params = array()) {
        return $this->request('auth/r/positions', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_position_claim($params = array()) {
        return $this->request('auth/w/position/claim', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_position_increase($params = array()) {
        return $this->request('auth/w/position/increase:', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_position_increase_info($params = array()) {
        return $this->request('auth/r/position/increase/info', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_positions_hist($params = array()) {
        return $this->request('auth/r/positions/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_positions_audit($params = array()) {
        return $this->request('auth/r/positions/audit', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_positions_snap($params = array()) {
        return $this->request('auth/r/positions/snap', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_deriv_collateral_set($params = array()) {
        return $this->request('auth/w/deriv/collateral/set', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_deriv_collateral_limits($params = array()) {
        return $this->request('auth/w/deriv/collateral/limits', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_offers($params = array()) {
        return $this->request('auth/r/funding/offers', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_offers_symbol($params = array()) {
        return $this->request('auth/r/funding/offers/{symbol}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_funding_offer_submit($params = array()) {
        return $this->request('auth/w/funding/offer/submit', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_funding_offer_cancel($params = array()) {
        return $this->request('auth/w/funding/offer/cancel', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_funding_offer_cancel_all($params = array()) {
        return $this->request('auth/w/funding/offer/cancel/all', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_funding_close($params = array()) {
        return $this->request('auth/w/funding/close', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_funding_auto($params = array()) {
        return $this->request('auth/w/funding/auto', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_funding_keep($params = array()) {
        return $this->request('auth/w/funding/keep', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_offers_symbol_hist($params = array()) {
        return $this->request('auth/r/funding/offers/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_offers_hist($params = array()) {
        return $this->request('auth/r/funding/offers/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_loans($params = array()) {
        return $this->request('auth/r/funding/loans', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_loans_hist($params = array()) {
        return $this->request('auth/r/funding/loans/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_loans_symbol($params = array()) {
        return $this->request('auth/r/funding/loans/{symbol}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_loans_symbol_hist($params = array()) {
        return $this->request('auth/r/funding/loans/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_credits($params = array()) {
        return $this->request('auth/r/funding/credits', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_credits_hist($params = array()) {
        return $this->request('auth/r/funding/credits/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_credits_symbol($params = array()) {
        return $this->request('auth/r/funding/credits/{symbol}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_credits_symbol_hist($params = array()) {
        return $this->request('auth/r/funding/credits/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_trades_symbol_hist($params = array()) {
        return $this->request('auth/r/funding/trades/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_funding_trades_hist($params = array()) {
        return $this->request('auth/r/funding/trades/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_info_funding_key($params = array()) {
        return $this->request('auth/r/info/funding/{key}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_info_user($params = array()) {
        return $this->request('auth/r/info/user', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_summary($params = array()) {
        return $this->request('auth/r/summary', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_logins_hist($params = array()) {
        return $this->request('auth/r/logins/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_permissions($params = array()) {
        return $this->request('auth/r/permissions', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_token($params = array()) {
        return $this->request('auth/w/token', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_audit_hist($params = array()) {
        return $this->request('auth/r/audit/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_transfer($params = array()) {
        return $this->request('auth/w/transfer', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_deposit_address($params = array()) {
        return $this->request('auth/w/deposit/address', 'private', 'POST', $params, null, null, array("cost" => 24));
    }
    public function private_post_auth_w_deposit_invoice($params = array()) {
        return $this->request('auth/w/deposit/invoice', 'private', 'POST', $params, null, null, array("cost" => 24));
    }
    public function private_post_auth_w_withdraw($params = array()) {
        return $this->request('auth/w/withdraw', 'private', 'POST', $params, null, null, array("cost" => 24));
    }
    public function private_post_auth_r_movements_currency_hist($params = array()) {
        return $this->request('auth/r/movements/{currency}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_movements_hist($params = array()) {
        return $this->request('auth/r/movements/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_alerts($params = array()) {
        return $this->request('auth/r/alerts', 'private', 'POST', $params, null, null, array("cost" => 5.34));
    }
    public function private_post_auth_w_alert_set($params = array()) {
        return $this->request('auth/w/alert/set', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_alert_price_symbol_price_del($params = array()) {
        return $this->request('auth/w/alert/price:{symbol}:{price}/del', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_alert_type_symbol_price_del($params = array()) {
        return $this->request('auth/w/alert/{type}:{symbol}:{price}/del', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_calc_order_avail($params = array()) {
        return $this->request('auth/calc/order/avail', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_settings_set($params = array()) {
        return $this->request('auth/w/settings/set', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_settings($params = array()) {
        return $this->request('auth/r/settings', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_settings_del($params = array()) {
        return $this->request('auth/w/settings/del', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_r_pulse_hist($params = array()) {
        return $this->request('auth/r/pulse/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function private_post_auth_w_pulse_add($params = array()) {
        return $this->request('auth/w/pulse/add', 'private', 'POST', $params, null, null, array("cost" => 16));
    }
    public function private_post_auth_w_pulse_del($params = array()) {
        return $this->request('auth/w/pulse/del', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfConfig($params = array()) {
        return $this->request('conf/{config}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubActionObject($params = array()) {
        return $this->request('conf/pub:{action}:{object}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubActionObjectDetail($params = array()) {
        return $this->request('conf/pub:{action}:{object}:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapObject($params = array()) {
        return $this->request('conf/pub:map:{object}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapObjectDetail($params = array()) {
        return $this->request('conf/pub:map:{object}:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapCurrencyDetail($params = array()) {
        return $this->request('conf/pub:map:currency:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapCurrencySym($params = array()) {
        return $this->request('conf/pub:map:currency:sym', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapCurrencyLabel($params = array()) {
        return $this->request('conf/pub:map:currency:label', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapCurrencyUnit($params = array()) {
        return $this->request('conf/pub:map:currency:unit', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapCurrencyUndl($params = array()) {
        return $this->request('conf/pub:map:currency:undl', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapCurrencyPool($params = array()) {
        return $this->request('conf/pub:map:currency:pool', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapCurrencyExplorer($params = array()) {
        return $this->request('conf/pub:map:currency:explorer', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapCurrencyTxFee($params = array()) {
        return $this->request('conf/pub:map:currency:tx:fee', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubMapTxMethod($params = array()) {
        return $this->request('conf/pub:map:tx:method', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubListObject($params = array()) {
        return $this->request('conf/pub:list:{object}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubListObjectDetail($params = array()) {
        return $this->request('conf/pub:list:{object}:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubListCurrency($params = array()) {
        return $this->request('conf/pub:list:currency', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubListPairExchange($params = array()) {
        return $this->request('conf/pub:list:pair:exchange', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubListPairMargin($params = array()) {
        return $this->request('conf/pub:list:pair:margin', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubListPairFutures($params = array()) {
        return $this->request('conf/pub:list:pair:futures', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubListCompetitions($params = array()) {
        return $this->request('conf/pub:list:competitions', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubInfoObject($params = array()) {
        return $this->request('conf/pub:info:{object}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubInfoObjectDetail($params = array()) {
        return $this->request('conf/pub:info:{object}:{detail}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubInfoPair($params = array()) {
        return $this->request('conf/pub:info:pair', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubInfoPairFutures($params = array()) {
        return $this->request('conf/pub:info:pair:futures', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubInfoTxStatus($params = array()) {
        return $this->request('conf/pub:info:tx:status', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetConfPubFees($params = array()) {
        return $this->request('conf/pub:fees', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetPlatformStatus($params = array()) {
        return $this->request('platform/status', 'public', 'GET', $params, null, null, array("cost" => 8));
    }
    public function publicGetTickers($params = array()) {
        return $this->request('tickers', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetTickerSymbol($params = array()) {
        return $this->request('ticker/{symbol}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetTickersHist($params = array()) {
        return $this->request('tickers/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetTradesSymbolHist($params = array()) {
        return $this->request('trades/{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetBookSymbolPrecision($params = array()) {
        return $this->request('book/{symbol}/{precision}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetBookSymbolP0($params = array()) {
        return $this->request('book/{symbol}/P0', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetBookSymbolP1($params = array()) {
        return $this->request('book/{symbol}/P1', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetBookSymbolP2($params = array()) {
        return $this->request('book/{symbol}/P2', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetBookSymbolP3($params = array()) {
        return $this->request('book/{symbol}/P3', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetBookSymbolR0($params = array()) {
        return $this->request('book/{symbol}/R0', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetStats1KeySizeSymbolSideSection($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:{side}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolSideLast($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:{side}/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolSideHist($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:{side}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolSection($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolLast($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolHist($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolLongLast($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:long/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolLongHist($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:long/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolShortLast($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:short/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStats1KeySizeSymbolShortHist($params = array()) {
        return $this->request('stats1/{key}:{size}:{symbol}:short/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetCandlesTradeTimeframeSymbolPeriodSection($params = array()) {
        return $this->request('candles/trade:{timeframe}:{symbol}:{period}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetCandlesTradeTimeframeSymbolSection($params = array()) {
        return $this->request('candles/trade:{timeframe}:{symbol}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetCandlesTradeTimeframeSymbolLast($params = array()) {
        return $this->request('candles/trade:{timeframe}:{symbol}/last', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetCandlesTradeTimeframeSymbolHist($params = array()) {
        return $this->request('candles/trade:{timeframe}:{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStatusType($params = array()) {
        return $this->request('status/{type}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStatusDeriv($params = array()) {
        return $this->request('status/deriv', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetStatusDerivSymbolHist($params = array()) {
        return $this->request('status/deriv/{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetLiquidationsHist($params = array()) {
        return $this->request('liquidations/hist', 'public', 'GET', $params, null, null, array("cost" => 80));
    }
    public function publicGetRankingsKeyTimeframeSymbolSection($params = array()) {
        return $this->request('rankings/{key}:{timeframe}:{symbol}/{section}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetRankingsKeyTimeframeSymbolHist($params = array()) {
        return $this->request('rankings/{key}:{timeframe}:{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetPulseHist($params = array()) {
        return $this->request('pulse/hist', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetPulseProfileNickname($params = array()) {
        return $this->request('pulse/profile/{nickname}', 'public', 'GET', $params, null, null, array("cost" => 2.7));
    }
    public function publicGetFundingStatsSymbolHist($params = array()) {
        return $this->request('funding/stats/{symbol}/hist', 'public', 'GET', $params, null, null, array("cost" => 10));
    }
    public function publicGetExtVasps($params = array()) {
        return $this->request('ext/vasps', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicPostCalcTradeAvg($params = array()) {
        return $this->request('calc/trade/avg', 'public', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function publicPostCalcFx($params = array()) {
        return $this->request('calc/fx', 'public', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRWallets($params = array()) {
        return $this->request('auth/r/wallets', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRWalletsHist($params = array()) {
        return $this->request('auth/r/wallets/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthROrders($params = array()) {
        return $this->request('auth/r/orders', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthROrdersSymbol($params = array()) {
        return $this->request('auth/r/orders/{symbol}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWOrderSubmit($params = array()) {
        return $this->request('auth/w/order/submit', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWOrderUpdate($params = array()) {
        return $this->request('auth/w/order/update', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWOrderCancel($params = array()) {
        return $this->request('auth/w/order/cancel', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWOrderMulti($params = array()) {
        return $this->request('auth/w/order/multi', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWOrderCancelMulti($params = array()) {
        return $this->request('auth/w/order/cancel/multi', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthROrdersSymbolHist($params = array()) {
        return $this->request('auth/r/orders/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthROrdersHist($params = array()) {
        return $this->request('auth/r/orders/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthROrderSymbolIdTrades($params = array()) {
        return $this->request('auth/r/order/{symbol}:{id}/trades', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRTradesSymbolHist($params = array()) {
        return $this->request('auth/r/trades/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRTradesHist($params = array()) {
        return $this->request('auth/r/trades/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRLedgersCurrencyHist($params = array()) {
        return $this->request('auth/r/ledgers/{currency}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRLedgersHist($params = array()) {
        return $this->request('auth/r/ledgers/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRInfoMarginKey($params = array()) {
        return $this->request('auth/r/info/margin/{key}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRInfoMarginBase($params = array()) {
        return $this->request('auth/r/info/margin/base', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRInfoMarginSymAll($params = array()) {
        return $this->request('auth/r/info/margin/sym_all', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRPositions($params = array()) {
        return $this->request('auth/r/positions', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWPositionClaim($params = array()) {
        return $this->request('auth/w/position/claim', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWPositionIncrease($params = array()) {
        return $this->request('auth/w/position/increase:', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRPositionIncreaseInfo($params = array()) {
        return $this->request('auth/r/position/increase/info', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRPositionsHist($params = array()) {
        return $this->request('auth/r/positions/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRPositionsAudit($params = array()) {
        return $this->request('auth/r/positions/audit', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRPositionsSnap($params = array()) {
        return $this->request('auth/r/positions/snap', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWDerivCollateralSet($params = array()) {
        return $this->request('auth/w/deriv/collateral/set', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWDerivCollateralLimits($params = array()) {
        return $this->request('auth/w/deriv/collateral/limits', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingOffers($params = array()) {
        return $this->request('auth/r/funding/offers', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingOffersSymbol($params = array()) {
        return $this->request('auth/r/funding/offers/{symbol}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWFundingOfferSubmit($params = array()) {
        return $this->request('auth/w/funding/offer/submit', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWFundingOfferCancel($params = array()) {
        return $this->request('auth/w/funding/offer/cancel', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWFundingOfferCancelAll($params = array()) {
        return $this->request('auth/w/funding/offer/cancel/all', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWFundingClose($params = array()) {
        return $this->request('auth/w/funding/close', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWFundingAuto($params = array()) {
        return $this->request('auth/w/funding/auto', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWFundingKeep($params = array()) {
        return $this->request('auth/w/funding/keep', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingOffersSymbolHist($params = array()) {
        return $this->request('auth/r/funding/offers/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingOffersHist($params = array()) {
        return $this->request('auth/r/funding/offers/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingLoans($params = array()) {
        return $this->request('auth/r/funding/loans', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingLoansHist($params = array()) {
        return $this->request('auth/r/funding/loans/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingLoansSymbol($params = array()) {
        return $this->request('auth/r/funding/loans/{symbol}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingLoansSymbolHist($params = array()) {
        return $this->request('auth/r/funding/loans/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingCredits($params = array()) {
        return $this->request('auth/r/funding/credits', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingCreditsHist($params = array()) {
        return $this->request('auth/r/funding/credits/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingCreditsSymbol($params = array()) {
        return $this->request('auth/r/funding/credits/{symbol}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingCreditsSymbolHist($params = array()) {
        return $this->request('auth/r/funding/credits/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingTradesSymbolHist($params = array()) {
        return $this->request('auth/r/funding/trades/{symbol}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRFundingTradesHist($params = array()) {
        return $this->request('auth/r/funding/trades/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRInfoFundingKey($params = array()) {
        return $this->request('auth/r/info/funding/{key}', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRInfoUser($params = array()) {
        return $this->request('auth/r/info/user', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRSummary($params = array()) {
        return $this->request('auth/r/summary', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRLoginsHist($params = array()) {
        return $this->request('auth/r/logins/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRPermissions($params = array()) {
        return $this->request('auth/r/permissions', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWToken($params = array()) {
        return $this->request('auth/w/token', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRAuditHist($params = array()) {
        return $this->request('auth/r/audit/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWTransfer($params = array()) {
        return $this->request('auth/w/transfer', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWDepositAddress($params = array()) {
        return $this->request('auth/w/deposit/address', 'private', 'POST', $params, null, null, array("cost" => 24));
    }
    public function privatePostAuthWDepositInvoice($params = array()) {
        return $this->request('auth/w/deposit/invoice', 'private', 'POST', $params, null, null, array("cost" => 24));
    }
    public function privatePostAuthWWithdraw($params = array()) {
        return $this->request('auth/w/withdraw', 'private', 'POST', $params, null, null, array("cost" => 24));
    }
    public function privatePostAuthRMovementsCurrencyHist($params = array()) {
        return $this->request('auth/r/movements/{currency}/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRMovementsHist($params = array()) {
        return $this->request('auth/r/movements/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRAlerts($params = array()) {
        return $this->request('auth/r/alerts', 'private', 'POST', $params, null, null, array("cost" => 5.34));
    }
    public function privatePostAuthWAlertSet($params = array()) {
        return $this->request('auth/w/alert/set', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWAlertPriceSymbolPriceDel($params = array()) {
        return $this->request('auth/w/alert/price:{symbol}:{price}/del', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWAlertTypeSymbolPriceDel($params = array()) {
        return $this->request('auth/w/alert/{type}:{symbol}:{price}/del', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthCalcOrderAvail($params = array()) {
        return $this->request('auth/calc/order/avail', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWSettingsSet($params = array()) {
        return $this->request('auth/w/settings/set', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRSettings($params = array()) {
        return $this->request('auth/r/settings', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWSettingsDel($params = array()) {
        return $this->request('auth/w/settings/del', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthRPulseHist($params = array()) {
        return $this->request('auth/r/pulse/hist', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
    public function privatePostAuthWPulseAdd($params = array()) {
        return $this->request('auth/w/pulse/add', 'private', 'POST', $params, null, null, array("cost" => 16));
    }
    public function privatePostAuthWPulseDel($params = array()) {
        return $this->request('auth/w/pulse/del', 'private', 'POST', $params, null, null, array("cost" => 2.7));
    }
}
