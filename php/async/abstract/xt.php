<?php

namespace ccxt\async\abstract;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code


abstract class xt extends \ccxt\async\Exchange {
    public function public_spot_get_currencies($params = array()) {
        return $this->request('currencies', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_depth($params = array()) {
        return $this->request('depth', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 10));
    }
    public function public_spot_get_kline($params = array()) {
        return $this->request('kline', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_symbol($params = array()) {
        return $this->request('symbol', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_ticker($params = array()) {
        return $this->request('ticker', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_ticker_book($params = array()) {
        return $this->request('ticker/book', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_ticker_price($params = array()) {
        return $this->request('ticker/price', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_ticker_24h($params = array()) {
        return $this->request('ticker/24h', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_time($params = array()) {
        return $this->request('time', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_trade_history($params = array()) {
        return $this->request('trade/history', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_trade_recent($params = array()) {
        return $this->request('trade/recent', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_spot_get_wallet_support_currency($params = array()) {
        return $this->request('wallet/support/currency', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_contract_risk_balance($params = array()) {
        return $this->request('future/market/v1/public/contract/risk-balance', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_contract_open_interest($params = array()) {
        return $this->request('future/market/v1/public/contract/open-interest', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_leverage_bracket_detail($params = array()) {
        return $this->request('future/market/v1/public/leverage/bracket/detail', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_leverage_bracket_list($params = array()) {
        return $this->request('future/market/v1/public/leverage/bracket/list', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_agg_ticker($params = array()) {
        return $this->request('future/market/v1/public/q/agg-ticker', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_agg_tickers($params = array()) {
        return $this->request('future/market/v1/public/q/agg-tickers', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_deal($params = array()) {
        return $this->request('future/market/v1/public/q/deal', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_depth($params = array()) {
        return $this->request('future/market/v1/public/q/depth', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_funding_rate($params = array()) {
        return $this->request('future/market/v1/public/q/funding-rate', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_funding_rate_record($params = array()) {
        return $this->request('future/market/v1/public/q/funding-rate-record', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_index_price($params = array()) {
        return $this->request('future/market/v1/public/q/index-price', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_kline($params = array()) {
        return $this->request('future/market/v1/public/q/kline', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_mark_price($params = array()) {
        return $this->request('future/market/v1/public/q/mark-price', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_symbol_index_price($params = array()) {
        return $this->request('future/market/v1/public/q/symbol-index-price', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_symbol_mark_price($params = array()) {
        return $this->request('future/market/v1/public/q/symbol-mark-price', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_ticker($params = array()) {
        return $this->request('future/market/v1/public/q/ticker', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_q_tickers($params = array()) {
        return $this->request('future/market/v1/public/q/tickers', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_linear_get_future_market_v1_public_symbol_coins($params = array()) {
        return $this->request('future/market/v1/public/symbol/coins', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 3.33));
    }
    public function public_linear_get_future_market_v1_public_symbol_detail($params = array()) {
        return $this->request('future/market/v1/public/symbol/detail', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 3.33));
    }
    public function public_linear_get_future_market_v1_public_symbol_list($params = array()) {
        return $this->request('future/market/v1/public/symbol/list', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_contract_risk_balance($params = array()) {
        return $this->request('future/market/v1/public/contract/risk-balance', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_contract_open_interest($params = array()) {
        return $this->request('future/market/v1/public/contract/open-interest', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_leverage_bracket_detail($params = array()) {
        return $this->request('future/market/v1/public/leverage/bracket/detail', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_leverage_bracket_list($params = array()) {
        return $this->request('future/market/v1/public/leverage/bracket/list', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_agg_ticker($params = array()) {
        return $this->request('future/market/v1/public/q/agg-ticker', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_agg_tickers($params = array()) {
        return $this->request('future/market/v1/public/q/agg-tickers', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_deal($params = array()) {
        return $this->request('future/market/v1/public/q/deal', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_depth($params = array()) {
        return $this->request('future/market/v1/public/q/depth', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_funding_rate($params = array()) {
        return $this->request('future/market/v1/public/q/funding-rate', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_funding_rate_record($params = array()) {
        return $this->request('future/market/v1/public/q/funding-rate-record', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_index_price($params = array()) {
        return $this->request('future/market/v1/public/q/index-price', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_kline($params = array()) {
        return $this->request('future/market/v1/public/q/kline', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_mark_price($params = array()) {
        return $this->request('future/market/v1/public/q/mark-price', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_symbol_index_price($params = array()) {
        return $this->request('future/market/v1/public/q/symbol-index-price', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_symbol_mark_price($params = array()) {
        return $this->request('future/market/v1/public/q/symbol-mark-price', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_ticker($params = array()) {
        return $this->request('future/market/v1/public/q/ticker', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_q_tickers($params = array()) {
        return $this->request('future/market/v1/public/q/tickers', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_inverse_get_future_market_v1_public_symbol_coins($params = array()) {
        return $this->request('future/market/v1/public/symbol/coins', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 3.33));
    }
    public function public_inverse_get_future_market_v1_public_symbol_detail($params = array()) {
        return $this->request('future/market/v1/public/symbol/detail', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 3.33));
    }
    public function public_inverse_get_future_market_v1_public_symbol_list($params = array()) {
        return $this->request('future/market/v1/public/symbol/list', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_balance($params = array()) {
        return $this->request('balance', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_balances($params = array()) {
        return $this->request('balances', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_batch_order($params = array()) {
        return $this->request('batch-order', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_deposit_address($params = array()) {
        return $this->request('deposit/address', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_deposit_history($params = array()) {
        return $this->request('deposit/history', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_history_order($params = array()) {
        return $this->request('history-order', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_open_order($params = array()) {
        return $this->request('open-order', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_order($params = array()) {
        return $this->request('order', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_order_orderid($params = array()) {
        return $this->request('order/{orderId}', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_trade($params = array()) {
        return $this->request('trade', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_get_withdraw_history($params = array()) {
        return $this->request('withdraw/history', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_spot_post_order($params = array()) {
        return $this->request('order', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 0.2));
    }
    public function private_spot_post_withdraw($params = array()) {
        return $this->request('withdraw', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 10));
    }
    public function private_spot_post_balance_transfer($params = array()) {
        return $this->request('balance/transfer', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_spot_post_balance_account_transfer($params = array()) {
        return $this->request('balance/account/transfer', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_spot_post_ws_token($params = array()) {
        return $this->request('ws-token', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_spot_delete_batch_order($params = array()) {
        return $this->request('batch-order', array('private', 'spot'), 'DELETE', $params, null, null, array("cost" => 1));
    }
    public function private_spot_delete_open_order($params = array()) {
        return $this->request('open-order', array('private', 'spot'), 'DELETE', $params, null, null, array("cost" => 1));
    }
    public function private_spot_delete_order_orderid($params = array()) {
        return $this->request('order/{orderId}', array('private', 'spot'), 'DELETE', $params, null, null, array("cost" => 1));
    }
    public function private_spot_put_order_orderid($params = array()) {
        return $this->request('order/{orderId}', array('private', 'spot'), 'PUT', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_entrust_plan_detail($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-detail', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_entrust_plan_list($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_entrust_plan_list_history($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-list-history', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_entrust_profit_detail($params = array()) {
        return $this->request('future/trade/v1/entrust/profit-detail', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_entrust_profit_list($params = array()) {
        return $this->request('future/trade/v1/entrust/profit-list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_order_detail($params = array()) {
        return $this->request('future/trade/v1/order/detail', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_order_list($params = array()) {
        return $this->request('future/trade/v1/order/list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_order_list_history($params = array()) {
        return $this->request('future/trade/v1/order/list-history', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_trade_v1_order_trade_list($params = array()) {
        return $this->request('future/trade/v1/order/trade-list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_account_info($params = array()) {
        return $this->request('future/user/v1/account/info', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_balance_bills($params = array()) {
        return $this->request('future/user/v1/balance/bills', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_balance_detail($params = array()) {
        return $this->request('future/user/v1/balance/detail', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_balance_funding_rate_list($params = array()) {
        return $this->request('future/user/v1/balance/funding-rate-list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_balance_list($params = array()) {
        return $this->request('future/user/v1/balance/list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_position_adl($params = array()) {
        return $this->request('future/user/v1/position/adl', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_position_list($params = array()) {
        return $this->request('future/user/v1/position/list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_user_collection_list($params = array()) {
        return $this->request('future/user/v1/user/collection/list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_get_future_user_v1_user_listen_key($params = array()) {
        return $this->request('future/user/v1/user/listen-key', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_entrust_cancel_all_plan($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-all-plan', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_entrust_cancel_all_profit_stop($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-all-profit-stop', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_entrust_cancel_plan($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-plan', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_entrust_cancel_profit_stop($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-profit-stop', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_entrust_create_plan($params = array()) {
        return $this->request('future/trade/v1/entrust/create-plan', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_entrust_create_profit($params = array()) {
        return $this->request('future/trade/v1/entrust/create-profit', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_entrust_update_profit_stop($params = array()) {
        return $this->request('future/trade/v1/entrust/update-profit-stop', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_order_cancel($params = array()) {
        return $this->request('future/trade/v1/order/cancel', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_order_cancel_all($params = array()) {
        return $this->request('future/trade/v1/order/cancel-all', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_order_create($params = array()) {
        return $this->request('future/trade/v1/order/create', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_order_create_batch($params = array()) {
        return $this->request('future/trade/v1/order/create-batch', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_trade_v1_order_update($params = array()) {
        return $this->request('future/trade/v1/order/update', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_user_v1_account_open($params = array()) {
        return $this->request('future/user/v1/account/open', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_user_v1_position_adjust_leverage($params = array()) {
        return $this->request('future/user/v1/position/adjust-leverage', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_user_v1_position_auto_margin($params = array()) {
        return $this->request('future/user/v1/position/auto-margin', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_user_v1_position_close_all($params = array()) {
        return $this->request('future/user/v1/position/close-all', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_user_v1_position_margin($params = array()) {
        return $this->request('future/user/v1/position/margin', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_user_v1_user_collection_add($params = array()) {
        return $this->request('future/user/v1/user/collection/add', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_user_v1_user_collection_cancel($params = array()) {
        return $this->request('future/user/v1/user/collection/cancel', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_linear_post_future_user_v1_position_change_type($params = array()) {
        return $this->request('future/user/v1/position/change-type', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_entrust_plan_detail($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-detail', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_entrust_plan_list($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_entrust_plan_list_history($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-list-history', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_entrust_profit_detail($params = array()) {
        return $this->request('future/trade/v1/entrust/profit-detail', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_entrust_profit_list($params = array()) {
        return $this->request('future/trade/v1/entrust/profit-list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_order_detail($params = array()) {
        return $this->request('future/trade/v1/order/detail', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_order_list($params = array()) {
        return $this->request('future/trade/v1/order/list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_order_list_history($params = array()) {
        return $this->request('future/trade/v1/order/list-history', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_trade_v1_order_trade_list($params = array()) {
        return $this->request('future/trade/v1/order/trade-list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_account_info($params = array()) {
        return $this->request('future/user/v1/account/info', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_balance_bills($params = array()) {
        return $this->request('future/user/v1/balance/bills', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_balance_detail($params = array()) {
        return $this->request('future/user/v1/balance/detail', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_balance_funding_rate_list($params = array()) {
        return $this->request('future/user/v1/balance/funding-rate-list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_balance_list($params = array()) {
        return $this->request('future/user/v1/balance/list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_position_adl($params = array()) {
        return $this->request('future/user/v1/position/adl', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_position_list($params = array()) {
        return $this->request('future/user/v1/position/list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_user_collection_list($params = array()) {
        return $this->request('future/user/v1/user/collection/list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_get_future_user_v1_user_listen_key($params = array()) {
        return $this->request('future/user/v1/user/listen-key', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_entrust_cancel_all_plan($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-all-plan', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_entrust_cancel_all_profit_stop($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-all-profit-stop', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_entrust_cancel_plan($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-plan', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_entrust_cancel_profit_stop($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-profit-stop', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_entrust_create_plan($params = array()) {
        return $this->request('future/trade/v1/entrust/create-plan', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_entrust_create_profit($params = array()) {
        return $this->request('future/trade/v1/entrust/create-profit', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_entrust_update_profit_stop($params = array()) {
        return $this->request('future/trade/v1/entrust/update-profit-stop', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_order_cancel($params = array()) {
        return $this->request('future/trade/v1/order/cancel', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_order_cancel_all($params = array()) {
        return $this->request('future/trade/v1/order/cancel-all', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_order_create($params = array()) {
        return $this->request('future/trade/v1/order/create', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_order_create_batch($params = array()) {
        return $this->request('future/trade/v1/order/create-batch', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_trade_v1_order_update($params = array()) {
        return $this->request('future/trade/v1/order/update', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_user_v1_account_open($params = array()) {
        return $this->request('future/user/v1/account/open', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_user_v1_position_adjust_leverage($params = array()) {
        return $this->request('future/user/v1/position/adjust-leverage', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_user_v1_position_auto_margin($params = array()) {
        return $this->request('future/user/v1/position/auto-margin', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_user_v1_position_close_all($params = array()) {
        return $this->request('future/user/v1/position/close-all', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_user_v1_position_margin($params = array()) {
        return $this->request('future/user/v1/position/margin', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_user_v1_user_collection_add($params = array()) {
        return $this->request('future/user/v1/user/collection/add', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_inverse_post_future_user_v1_user_collection_cancel($params = array()) {
        return $this->request('future/user/v1/user/collection/cancel', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_user_get_user_account($params = array()) {
        return $this->request('user/account', array('private', 'user'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_user_get_user_account_api_key($params = array()) {
        return $this->request('user/account/api-key', array('private', 'user'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_user_post_user_account($params = array()) {
        return $this->request('user/account', array('private', 'user'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_user_post_user_account_api_key($params = array()) {
        return $this->request('user/account/api-key', array('private', 'user'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function private_user_put_user_account_api_key($params = array()) {
        return $this->request('user/account/api-key', array('private', 'user'), 'PUT', $params, null, null, array("cost" => 1));
    }
    public function private_user_delete_user_account_apikeyid($params = array()) {
        return $this->request('user/account/{apikeyId}', array('private', 'user'), 'DELETE', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetCurrencies($params = array()) {
        return $this->request('currencies', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetDepth($params = array()) {
        return $this->request('depth', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 10));
    }
    public function publicSpotGetKline($params = array()) {
        return $this->request('kline', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetSymbol($params = array()) {
        return $this->request('symbol', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetTicker($params = array()) {
        return $this->request('ticker', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetTickerBook($params = array()) {
        return $this->request('ticker/book', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetTickerPrice($params = array()) {
        return $this->request('ticker/price', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetTicker24h($params = array()) {
        return $this->request('ticker/24h', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetTime($params = array()) {
        return $this->request('time', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetTradeHistory($params = array()) {
        return $this->request('trade/history', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetTradeRecent($params = array()) {
        return $this->request('trade/recent', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicSpotGetWalletSupportCurrency($params = array()) {
        return $this->request('wallet/support/currency', array('public', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicContractRiskBalance($params = array()) {
        return $this->request('future/market/v1/public/contract/risk-balance', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicContractOpenInterest($params = array()) {
        return $this->request('future/market/v1/public/contract/open-interest', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicLeverageBracketDetail($params = array()) {
        return $this->request('future/market/v1/public/leverage/bracket/detail', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicLeverageBracketList($params = array()) {
        return $this->request('future/market/v1/public/leverage/bracket/list', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQAggTicker($params = array()) {
        return $this->request('future/market/v1/public/q/agg-ticker', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQAggTickers($params = array()) {
        return $this->request('future/market/v1/public/q/agg-tickers', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQDeal($params = array()) {
        return $this->request('future/market/v1/public/q/deal', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQDepth($params = array()) {
        return $this->request('future/market/v1/public/q/depth', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQFundingRate($params = array()) {
        return $this->request('future/market/v1/public/q/funding-rate', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQFundingRateRecord($params = array()) {
        return $this->request('future/market/v1/public/q/funding-rate-record', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQIndexPrice($params = array()) {
        return $this->request('future/market/v1/public/q/index-price', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQKline($params = array()) {
        return $this->request('future/market/v1/public/q/kline', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQMarkPrice($params = array()) {
        return $this->request('future/market/v1/public/q/mark-price', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQSymbolIndexPrice($params = array()) {
        return $this->request('future/market/v1/public/q/symbol-index-price', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQSymbolMarkPrice($params = array()) {
        return $this->request('future/market/v1/public/q/symbol-mark-price', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQTicker($params = array()) {
        return $this->request('future/market/v1/public/q/ticker', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicQTickers($params = array()) {
        return $this->request('future/market/v1/public/q/tickers', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicLinearGetFutureMarketV1PublicSymbolCoins($params = array()) {
        return $this->request('future/market/v1/public/symbol/coins', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 3.33));
    }
    public function publicLinearGetFutureMarketV1PublicSymbolDetail($params = array()) {
        return $this->request('future/market/v1/public/symbol/detail', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 3.33));
    }
    public function publicLinearGetFutureMarketV1PublicSymbolList($params = array()) {
        return $this->request('future/market/v1/public/symbol/list', array('public', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicContractRiskBalance($params = array()) {
        return $this->request('future/market/v1/public/contract/risk-balance', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicContractOpenInterest($params = array()) {
        return $this->request('future/market/v1/public/contract/open-interest', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicLeverageBracketDetail($params = array()) {
        return $this->request('future/market/v1/public/leverage/bracket/detail', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicLeverageBracketList($params = array()) {
        return $this->request('future/market/v1/public/leverage/bracket/list', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQAggTicker($params = array()) {
        return $this->request('future/market/v1/public/q/agg-ticker', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQAggTickers($params = array()) {
        return $this->request('future/market/v1/public/q/agg-tickers', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQDeal($params = array()) {
        return $this->request('future/market/v1/public/q/deal', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQDepth($params = array()) {
        return $this->request('future/market/v1/public/q/depth', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQFundingRate($params = array()) {
        return $this->request('future/market/v1/public/q/funding-rate', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQFundingRateRecord($params = array()) {
        return $this->request('future/market/v1/public/q/funding-rate-record', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQIndexPrice($params = array()) {
        return $this->request('future/market/v1/public/q/index-price', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQKline($params = array()) {
        return $this->request('future/market/v1/public/q/kline', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQMarkPrice($params = array()) {
        return $this->request('future/market/v1/public/q/mark-price', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQSymbolIndexPrice($params = array()) {
        return $this->request('future/market/v1/public/q/symbol-index-price', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQSymbolMarkPrice($params = array()) {
        return $this->request('future/market/v1/public/q/symbol-mark-price', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQTicker($params = array()) {
        return $this->request('future/market/v1/public/q/ticker', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicQTickers($params = array()) {
        return $this->request('future/market/v1/public/q/tickers', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicInverseGetFutureMarketV1PublicSymbolCoins($params = array()) {
        return $this->request('future/market/v1/public/symbol/coins', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 3.33));
    }
    public function publicInverseGetFutureMarketV1PublicSymbolDetail($params = array()) {
        return $this->request('future/market/v1/public/symbol/detail', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 3.33));
    }
    public function publicInverseGetFutureMarketV1PublicSymbolList($params = array()) {
        return $this->request('future/market/v1/public/symbol/list', array('public', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetBalance($params = array()) {
        return $this->request('balance', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetBalances($params = array()) {
        return $this->request('balances', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetBatchOrder($params = array()) {
        return $this->request('batch-order', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetDepositAddress($params = array()) {
        return $this->request('deposit/address', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetDepositHistory($params = array()) {
        return $this->request('deposit/history', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetHistoryOrder($params = array()) {
        return $this->request('history-order', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetOpenOrder($params = array()) {
        return $this->request('open-order', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetOrder($params = array()) {
        return $this->request('order', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetOrderOrderId($params = array()) {
        return $this->request('order/{orderId}', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetTrade($params = array()) {
        return $this->request('trade', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotGetWithdrawHistory($params = array()) {
        return $this->request('withdraw/history', array('private', 'spot'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateSpotPostOrder($params = array()) {
        return $this->request('order', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 0.2));
    }
    public function privateSpotPostWithdraw($params = array()) {
        return $this->request('withdraw', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 10));
    }
    public function privateSpotPostBalanceTransfer($params = array()) {
        return $this->request('balance/transfer', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateSpotPostBalanceAccountTransfer($params = array()) {
        return $this->request('balance/account/transfer', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateSpotPostWsToken($params = array()) {
        return $this->request('ws-token', array('private', 'spot'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateSpotDeleteBatchOrder($params = array()) {
        return $this->request('batch-order', array('private', 'spot'), 'DELETE', $params, null, null, array("cost" => 1));
    }
    public function privateSpotDeleteOpenOrder($params = array()) {
        return $this->request('open-order', array('private', 'spot'), 'DELETE', $params, null, null, array("cost" => 1));
    }
    public function privateSpotDeleteOrderOrderId($params = array()) {
        return $this->request('order/{orderId}', array('private', 'spot'), 'DELETE', $params, null, null, array("cost" => 1));
    }
    public function privateSpotPutOrderOrderId($params = array()) {
        return $this->request('order/{orderId}', array('private', 'spot'), 'PUT', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1EntrustPlanDetail($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-detail', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1EntrustPlanList($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1EntrustPlanListHistory($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-list-history', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1EntrustProfitDetail($params = array()) {
        return $this->request('future/trade/v1/entrust/profit-detail', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1EntrustProfitList($params = array()) {
        return $this->request('future/trade/v1/entrust/profit-list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1OrderDetail($params = array()) {
        return $this->request('future/trade/v1/order/detail', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1OrderList($params = array()) {
        return $this->request('future/trade/v1/order/list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1OrderListHistory($params = array()) {
        return $this->request('future/trade/v1/order/list-history', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureTradeV1OrderTradeList($params = array()) {
        return $this->request('future/trade/v1/order/trade-list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1AccountInfo($params = array()) {
        return $this->request('future/user/v1/account/info', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1BalanceBills($params = array()) {
        return $this->request('future/user/v1/balance/bills', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1BalanceDetail($params = array()) {
        return $this->request('future/user/v1/balance/detail', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1BalanceFundingRateList($params = array()) {
        return $this->request('future/user/v1/balance/funding-rate-list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1BalanceList($params = array()) {
        return $this->request('future/user/v1/balance/list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1PositionAdl($params = array()) {
        return $this->request('future/user/v1/position/adl', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1PositionList($params = array()) {
        return $this->request('future/user/v1/position/list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1UserCollectionList($params = array()) {
        return $this->request('future/user/v1/user/collection/list', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearGetFutureUserV1UserListenKey($params = array()) {
        return $this->request('future/user/v1/user/listen-key', array('private', 'linear'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1EntrustCancelAllPlan($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-all-plan', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1EntrustCancelAllProfitStop($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-all-profit-stop', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1EntrustCancelPlan($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-plan', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1EntrustCancelProfitStop($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-profit-stop', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1EntrustCreatePlan($params = array()) {
        return $this->request('future/trade/v1/entrust/create-plan', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1EntrustCreateProfit($params = array()) {
        return $this->request('future/trade/v1/entrust/create-profit', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1EntrustUpdateProfitStop($params = array()) {
        return $this->request('future/trade/v1/entrust/update-profit-stop', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1OrderCancel($params = array()) {
        return $this->request('future/trade/v1/order/cancel', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1OrderCancelAll($params = array()) {
        return $this->request('future/trade/v1/order/cancel-all', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1OrderCreate($params = array()) {
        return $this->request('future/trade/v1/order/create', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1OrderCreateBatch($params = array()) {
        return $this->request('future/trade/v1/order/create-batch', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureTradeV1OrderUpdate($params = array()) {
        return $this->request('future/trade/v1/order/update', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureUserV1AccountOpen($params = array()) {
        return $this->request('future/user/v1/account/open', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureUserV1PositionAdjustLeverage($params = array()) {
        return $this->request('future/user/v1/position/adjust-leverage', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureUserV1PositionAutoMargin($params = array()) {
        return $this->request('future/user/v1/position/auto-margin', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureUserV1PositionCloseAll($params = array()) {
        return $this->request('future/user/v1/position/close-all', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureUserV1PositionMargin($params = array()) {
        return $this->request('future/user/v1/position/margin', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureUserV1UserCollectionAdd($params = array()) {
        return $this->request('future/user/v1/user/collection/add', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureUserV1UserCollectionCancel($params = array()) {
        return $this->request('future/user/v1/user/collection/cancel', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateLinearPostFutureUserV1PositionChangeType($params = array()) {
        return $this->request('future/user/v1/position/change-type', array('private', 'linear'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1EntrustPlanDetail($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-detail', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1EntrustPlanList($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1EntrustPlanListHistory($params = array()) {
        return $this->request('future/trade/v1/entrust/plan-list-history', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1EntrustProfitDetail($params = array()) {
        return $this->request('future/trade/v1/entrust/profit-detail', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1EntrustProfitList($params = array()) {
        return $this->request('future/trade/v1/entrust/profit-list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1OrderDetail($params = array()) {
        return $this->request('future/trade/v1/order/detail', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1OrderList($params = array()) {
        return $this->request('future/trade/v1/order/list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1OrderListHistory($params = array()) {
        return $this->request('future/trade/v1/order/list-history', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureTradeV1OrderTradeList($params = array()) {
        return $this->request('future/trade/v1/order/trade-list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1AccountInfo($params = array()) {
        return $this->request('future/user/v1/account/info', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1BalanceBills($params = array()) {
        return $this->request('future/user/v1/balance/bills', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1BalanceDetail($params = array()) {
        return $this->request('future/user/v1/balance/detail', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1BalanceFundingRateList($params = array()) {
        return $this->request('future/user/v1/balance/funding-rate-list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1BalanceList($params = array()) {
        return $this->request('future/user/v1/balance/list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1PositionAdl($params = array()) {
        return $this->request('future/user/v1/position/adl', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1PositionList($params = array()) {
        return $this->request('future/user/v1/position/list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1UserCollectionList($params = array()) {
        return $this->request('future/user/v1/user/collection/list', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInverseGetFutureUserV1UserListenKey($params = array()) {
        return $this->request('future/user/v1/user/listen-key', array('private', 'inverse'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1EntrustCancelAllPlan($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-all-plan', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1EntrustCancelAllProfitStop($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-all-profit-stop', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1EntrustCancelPlan($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-plan', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1EntrustCancelProfitStop($params = array()) {
        return $this->request('future/trade/v1/entrust/cancel-profit-stop', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1EntrustCreatePlan($params = array()) {
        return $this->request('future/trade/v1/entrust/create-plan', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1EntrustCreateProfit($params = array()) {
        return $this->request('future/trade/v1/entrust/create-profit', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1EntrustUpdateProfitStop($params = array()) {
        return $this->request('future/trade/v1/entrust/update-profit-stop', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1OrderCancel($params = array()) {
        return $this->request('future/trade/v1/order/cancel', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1OrderCancelAll($params = array()) {
        return $this->request('future/trade/v1/order/cancel-all', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1OrderCreate($params = array()) {
        return $this->request('future/trade/v1/order/create', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1OrderCreateBatch($params = array()) {
        return $this->request('future/trade/v1/order/create-batch', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureTradeV1OrderUpdate($params = array()) {
        return $this->request('future/trade/v1/order/update', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureUserV1AccountOpen($params = array()) {
        return $this->request('future/user/v1/account/open', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureUserV1PositionAdjustLeverage($params = array()) {
        return $this->request('future/user/v1/position/adjust-leverage', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureUserV1PositionAutoMargin($params = array()) {
        return $this->request('future/user/v1/position/auto-margin', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureUserV1PositionCloseAll($params = array()) {
        return $this->request('future/user/v1/position/close-all', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureUserV1PositionMargin($params = array()) {
        return $this->request('future/user/v1/position/margin', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureUserV1UserCollectionAdd($params = array()) {
        return $this->request('future/user/v1/user/collection/add', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateInversePostFutureUserV1UserCollectionCancel($params = array()) {
        return $this->request('future/user/v1/user/collection/cancel', array('private', 'inverse'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateUserGetUserAccount($params = array()) {
        return $this->request('user/account', array('private', 'user'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateUserGetUserAccountApiKey($params = array()) {
        return $this->request('user/account/api-key', array('private', 'user'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function privateUserPostUserAccount($params = array()) {
        return $this->request('user/account', array('private', 'user'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateUserPostUserAccountApiKey($params = array()) {
        return $this->request('user/account/api-key', array('private', 'user'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function privateUserPutUserAccountApiKey($params = array()) {
        return $this->request('user/account/api-key', array('private', 'user'), 'PUT', $params, null, null, array("cost" => 1));
    }
    public function privateUserDeleteUserAccountApikeyId($params = array()) {
        return $this->request('user/account/{apikeyId}', array('private', 'user'), 'DELETE', $params, null, null, array("cost" => 1));
    }
}
