<?php

namespace ccxt\abstract;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code


abstract class zaif extends \ccxt\Exchange {
    public function public_get_depth_pair($params = array()) {
        return $this->request('depth/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_currencies_pair($params = array()) {
        return $this->request('currencies/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_currencies_all($params = array()) {
        return $this->request('currencies/all', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_currency_pairs_pair($params = array()) {
        return $this->request('currency_pairs/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_currency_pairs_all($params = array()) {
        return $this->request('currency_pairs/all', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_last_price_pair($params = array()) {
        return $this->request('last_price/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_ticker_pair($params = array()) {
        return $this->request('ticker/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function public_get_trades_pair($params = array()) {
        return $this->request('trades/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function private_post_active_orders($params = array()) {
        return $this->request('active_orders', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function private_post_cancel_order($params = array()) {
        return $this->request('cancel_order', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function private_post_deposit_history($params = array()) {
        return $this->request('deposit_history', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function private_post_get_id_info($params = array()) {
        return $this->request('get_id_info', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function private_post_get_info($params = array()) {
        return $this->request('get_info', 'private', 'POST', $params, null, null, array("cost" => 10));
    }
    public function private_post_get_info2($params = array()) {
        return $this->request('get_info2', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function private_post_get_personal_info($params = array()) {
        return $this->request('get_personal_info', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function private_post_trade($params = array()) {
        return $this->request('trade', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function private_post_trade_history($params = array()) {
        return $this->request('trade_history', 'private', 'POST', $params, null, null, array("cost" => 50));
    }
    public function private_post_withdraw($params = array()) {
        return $this->request('withdraw', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function private_post_withdraw_history($params = array()) {
        return $this->request('withdraw_history', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function ecapi_post_createinvoice($params = array()) {
        return $this->request('createInvoice', 'ecapi', 'POST', $params, null, null, array("cost" => 1));
    }
    public function ecapi_post_getinvoice($params = array()) {
        return $this->request('getInvoice', 'ecapi', 'POST', $params, null, null, array("cost" => 1));
    }
    public function ecapi_post_getinvoiceidsbyordernumber($params = array()) {
        return $this->request('getInvoiceIdsByOrderNumber', 'ecapi', 'POST', $params, null, null, array("cost" => 1));
    }
    public function ecapi_post_cancelinvoice($params = array()) {
        return $this->request('cancelInvoice', 'ecapi', 'POST', $params, null, null, array("cost" => 1));
    }
    public function tlapi_post_get_positions($params = array()) {
        return $this->request('get_positions', 'tlapi', 'POST', $params, null, null, array("cost" => 66));
    }
    public function tlapi_post_position_history($params = array()) {
        return $this->request('position_history', 'tlapi', 'POST', $params, null, null, array("cost" => 66));
    }
    public function tlapi_post_active_positions($params = array()) {
        return $this->request('active_positions', 'tlapi', 'POST', $params, null, null, array("cost" => 5));
    }
    public function tlapi_post_create_position($params = array()) {
        return $this->request('create_position', 'tlapi', 'POST', $params, null, null, array("cost" => 33));
    }
    public function tlapi_post_change_position($params = array()) {
        return $this->request('change_position', 'tlapi', 'POST', $params, null, null, array("cost" => 33));
    }
    public function tlapi_post_cancel_position($params = array()) {
        return $this->request('cancel_position', 'tlapi', 'POST', $params, null, null, array("cost" => 33));
    }
    public function fapi_get_groups_group_id($params = array()) {
        return $this->request('groups/{group_id}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function fapi_get_last_price_group_id_pair($params = array()) {
        return $this->request('last_price/{group_id}/{pair}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function fapi_get_ticker_group_id_pair($params = array()) {
        return $this->request('ticker/{group_id}/{pair}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function fapi_get_trades_group_id_pair($params = array()) {
        return $this->request('trades/{group_id}/{pair}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function fapi_get_depth_group_id_pair($params = array()) {
        return $this->request('depth/{group_id}/{pair}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetDepthPair($params = array()) {
        return $this->request('depth/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetCurrenciesPair($params = array()) {
        return $this->request('currencies/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetCurrenciesAll($params = array()) {
        return $this->request('currencies/all', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetCurrencyPairsPair($params = array()) {
        return $this->request('currency_pairs/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetCurrencyPairsAll($params = array()) {
        return $this->request('currency_pairs/all', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetLastPricePair($params = array()) {
        return $this->request('last_price/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetTickerPair($params = array()) {
        return $this->request('ticker/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function publicGetTradesPair($params = array()) {
        return $this->request('trades/{pair}', 'public', 'GET', $params, null, null, array("cost" => 1));
    }
    public function privatePostActiveOrders($params = array()) {
        return $this->request('active_orders', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function privatePostCancelOrder($params = array()) {
        return $this->request('cancel_order', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function privatePostDepositHistory($params = array()) {
        return $this->request('deposit_history', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function privatePostGetIdInfo($params = array()) {
        return $this->request('get_id_info', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function privatePostGetInfo($params = array()) {
        return $this->request('get_info', 'private', 'POST', $params, null, null, array("cost" => 10));
    }
    public function privatePostGetInfo2($params = array()) {
        return $this->request('get_info2', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function privatePostGetPersonalInfo($params = array()) {
        return $this->request('get_personal_info', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function privatePostTrade($params = array()) {
        return $this->request('trade', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function privatePostTradeHistory($params = array()) {
        return $this->request('trade_history', 'private', 'POST', $params, null, null, array("cost" => 50));
    }
    public function privatePostWithdraw($params = array()) {
        return $this->request('withdraw', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function privatePostWithdrawHistory($params = array()) {
        return $this->request('withdraw_history', 'private', 'POST', $params, null, null, array("cost" => 5));
    }
    public function ecapiPostCreateInvoice($params = array()) {
        return $this->request('createInvoice', 'ecapi', 'POST', $params, null, null, array("cost" => 1));
    }
    public function ecapiPostGetInvoice($params = array()) {
        return $this->request('getInvoice', 'ecapi', 'POST', $params, null, null, array("cost" => 1));
    }
    public function ecapiPostGetInvoiceIdsByOrderNumber($params = array()) {
        return $this->request('getInvoiceIdsByOrderNumber', 'ecapi', 'POST', $params, null, null, array("cost" => 1));
    }
    public function ecapiPostCancelInvoice($params = array()) {
        return $this->request('cancelInvoice', 'ecapi', 'POST', $params, null, null, array("cost" => 1));
    }
    public function tlapiPostGetPositions($params = array()) {
        return $this->request('get_positions', 'tlapi', 'POST', $params, null, null, array("cost" => 66));
    }
    public function tlapiPostPositionHistory($params = array()) {
        return $this->request('position_history', 'tlapi', 'POST', $params, null, null, array("cost" => 66));
    }
    public function tlapiPostActivePositions($params = array()) {
        return $this->request('active_positions', 'tlapi', 'POST', $params, null, null, array("cost" => 5));
    }
    public function tlapiPostCreatePosition($params = array()) {
        return $this->request('create_position', 'tlapi', 'POST', $params, null, null, array("cost" => 33));
    }
    public function tlapiPostChangePosition($params = array()) {
        return $this->request('change_position', 'tlapi', 'POST', $params, null, null, array("cost" => 33));
    }
    public function tlapiPostCancelPosition($params = array()) {
        return $this->request('cancel_position', 'tlapi', 'POST', $params, null, null, array("cost" => 33));
    }
    public function fapiGetGroupsGroupId($params = array()) {
        return $this->request('groups/{group_id}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function fapiGetLastPriceGroupIdPair($params = array()) {
        return $this->request('last_price/{group_id}/{pair}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function fapiGetTickerGroupIdPair($params = array()) {
        return $this->request('ticker/{group_id}/{pair}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function fapiGetTradesGroupIdPair($params = array()) {
        return $this->request('trades/{group_id}/{pair}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
    public function fapiGetDepthGroupIdPair($params = array()) {
        return $this->request('depth/{group_id}/{pair}', 'fapi', 'GET', $params, null, null, array("cost" => 1));
    }
}
