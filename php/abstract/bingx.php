<?php

namespace ccxt\abstract;

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code


abstract class bingx extends \ccxt\Exchange {
    public function fund_v1_private_get_account_balance($params = array()) {
        return $this->request('account/balance', array('fund', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_public_get_server_time($params = array()) {
        return $this->request('server/time', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_public_get_common_symbols($params = array()) {
        return $this->request('common/symbols', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_public_get_market_trades($params = array()) {
        return $this->request('market/trades', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_public_get_market_depth($params = array()) {
        return $this->request('market/depth', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_public_get_market_kline($params = array()) {
        return $this->request('market/kline', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_public_get_ticker_24hr($params = array()) {
        return $this->request('ticker/24hr', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_public_get_ticker_price($params = array()) {
        return $this->request('ticker/price', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_public_get_ticker_bookticker($params = array()) {
        return $this->request('ticker/bookTicker', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_private_get_trade_query($params = array()) {
        return $this->request('trade/query', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_private_get_trade_openorders($params = array()) {
        return $this->request('trade/openOrders', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_private_get_trade_historyorders($params = array()) {
        return $this->request('trade/historyOrders', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v1_private_get_trade_mytrades($params = array()) {
        return $this->request('trade/myTrades', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function spot_v1_private_get_user_commissionrate($params = array()) {
        return $this->request('user/commissionRate', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function spot_v1_private_get_account_balance($params = array()) {
        return $this->request('account/balance', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function spot_v1_private_post_trade_order($params = array()) {
        return $this->request('trade/order', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function spot_v1_private_post_trade_cancel($params = array()) {
        return $this->request('trade/cancel', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function spot_v1_private_post_trade_batchorders($params = array()) {
        return $this->request('trade/batchOrders', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spot_v1_private_post_trade_order_cancelreplace($params = array()) {
        return $this->request('trade/order/cancelReplace', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spot_v1_private_post_trade_cancelorders($params = array()) {
        return $this->request('trade/cancelOrders', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spot_v1_private_post_trade_cancelopenorders($params = array()) {
        return $this->request('trade/cancelOpenOrders', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spot_v1_private_post_trade_cancelallafter($params = array()) {
        return $this->request('trade/cancelAllAfter', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spot_v2_public_get_market_depth($params = array()) {
        return $this->request('market/depth', array('spot', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v2_public_get_market_kline($params = array()) {
        return $this->request('market/kline', array('spot', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v3_private_get_get_asset_transfer($params = array()) {
        return $this->request('get/asset/transfer', array('spot', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v3_private_get_asset_transfer($params = array()) {
        return $this->request('asset/transfer', array('spot', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v3_private_get_capital_deposit_hisrec($params = array()) {
        return $this->request('capital/deposit/hisrec', array('spot', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v3_private_get_capital_withdraw_history($params = array()) {
        return $this->request('capital/withdraw/history', array('spot', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spot_v3_private_post_post_asset_transfer($params = array()) {
        return $this->request('post/asset/transfer', array('spot', 'v3', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_public_get_ticker_price($params = array()) {
        return $this->request('ticker/price', array('swap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v1_public_get_market_historicaltrades($params = array()) {
        return $this->request('market/historicalTrades', array('swap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v1_public_get_market_markpriceklines($params = array()) {
        return $this->request('market/markPriceKlines', array('swap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v1_public_get_trade_multiassetsrules($params = array()) {
        return $this->request('trade/multiAssetsRules', array('swap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v1_private_get_positionside_dual($params = array()) {
        return $this->request('positionSide/dual', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_get_trade_batchcancelreplace($params = array()) {
        return $this->request('trade/batchCancelReplace', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_get_trade_fullorder($params = array()) {
        return $this->request('trade/fullOrder', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v1_private_get_maintmarginratio($params = array()) {
        return $this->request('maintMarginRatio', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v1_private_get_trade_positionhistory($params = array()) {
        return $this->request('trade/positionHistory', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v1_private_get_positionmargin_history($params = array()) {
        return $this->request('positionMargin/history', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v1_private_get_twap_openorders($params = array()) {
        return $this->request('twap/openOrders', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_get_twap_historyorders($params = array()) {
        return $this->request('twap/historyOrders', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_get_twap_orderdetail($params = array()) {
        return $this->request('twap/orderDetail', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_get_trade_assetmode($params = array()) {
        return $this->request('trade/assetMode', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_get_user_marginassets($params = array()) {
        return $this->request('user/marginAssets', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_post_trade_cancelreplace($params = array()) {
        return $this->request('trade/cancelReplace', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swap_v1_private_post_positionside_dual($params = array()) {
        return $this->request('positionSide/dual', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_post_trade_batchcancelreplace($params = array()) {
        return $this->request('trade/batchCancelReplace', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_post_trade_closeposition($params = array()) {
        return $this->request('trade/closePosition', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swap_v1_private_post_trade_getvst($params = array()) {
        return $this->request('trade/getVst', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_post_twap_order($params = array()) {
        return $this->request('twap/order', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_post_twap_cancelorder($params = array()) {
        return $this->request('twap/cancelOrder', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v1_private_post_trade_assetmode($params = array()) {
        return $this->request('trade/assetMode', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v2_public_get_server_time($params = array()) {
        return $this->request('server/time', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_contracts($params = array()) {
        return $this->request('quote/contracts', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_price($params = array()) {
        return $this->request('quote/price', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_depth($params = array()) {
        return $this->request('quote/depth', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_trades($params = array()) {
        return $this->request('quote/trades', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_premiumindex($params = array()) {
        return $this->request('quote/premiumIndex', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_fundingrate($params = array()) {
        return $this->request('quote/fundingRate', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_klines($params = array()) {
        return $this->request('quote/klines', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_openinterest($params = array()) {
        return $this->request('quote/openInterest', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_ticker($params = array()) {
        return $this->request('quote/ticker', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_public_get_quote_bookticker($params = array()) {
        return $this->request('quote/bookTicker', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_private_get_user_balance($params = array()) {
        return $this->request('user/balance', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_user_positions($params = array()) {
        return $this->request('user/positions', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_user_income($params = array()) {
        return $this->request('user/income', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_trade_openorders($params = array()) {
        return $this->request('trade/openOrders', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_trade_openorder($params = array()) {
        return $this->request('trade/openOrder', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_trade_order($params = array()) {
        return $this->request('trade/order', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_trade_margintype($params = array()) {
        return $this->request('trade/marginType', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swap_v2_private_get_trade_leverage($params = array()) {
        return $this->request('trade/leverage', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_trade_forceorders($params = array()) {
        return $this->request('trade/forceOrders', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_private_get_trade_allorders($params = array()) {
        return $this->request('trade/allOrders', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_trade_allfillorders($params = array()) {
        return $this->request('trade/allFillOrders', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_trade_fillhistory($params = array()) {
        return $this->request('trade/fillHistory', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_user_income_export($params = array()) {
        return $this->request('user/income/export', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_user_commissionrate($params = array()) {
        return $this->request('user/commissionRate', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_get_quote_bookticker($params = array()) {
        return $this->request('quote/bookTicker', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swap_v2_private_post_trade_order($params = array()) {
        return $this->request('trade/order', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_post_trade_batchorders($params = array()) {
        return $this->request('trade/batchOrders', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_post_trade_closeallpositions($params = array()) {
        return $this->request('trade/closeAllPositions', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_post_trade_cancelallafter($params = array()) {
        return $this->request('trade/cancelAllAfter', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v2_private_post_trade_margintype($params = array()) {
        return $this->request('trade/marginType', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v2_private_post_trade_leverage($params = array()) {
        return $this->request('trade/leverage', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v2_private_post_trade_positionmargin($params = array()) {
        return $this->request('trade/positionMargin', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swap_v2_private_post_trade_order_test($params = array()) {
        return $this->request('trade/order/test', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_delete_trade_order($params = array()) {
        return $this->request('trade/order', array('swap', 'v2', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_delete_trade_batchorders($params = array()) {
        return $this->request('trade/batchOrders', array('swap', 'v2', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function swap_v2_private_delete_trade_allopenorders($params = array()) {
        return $this->request('trade/allOpenOrders', array('swap', 'v2', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function swap_v3_public_get_quote_klines($params = array()) {
        return $this->request('quote/klines', array('swap', 'v3', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswap_v1_public_get_market_contracts($params = array()) {
        return $this->request('market/contracts', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswap_v1_public_get_market_premiumindex($params = array()) {
        return $this->request('market/premiumIndex', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswap_v1_public_get_market_openinterest($params = array()) {
        return $this->request('market/openInterest', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswap_v1_public_get_market_klines($params = array()) {
        return $this->request('market/klines', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswap_v1_public_get_market_depth($params = array()) {
        return $this->request('market/depth', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswap_v1_public_get_market_ticker($params = array()) {
        return $this->request('market/ticker', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswap_v1_private_get_trade_leverage($params = array()) {
        return $this->request('trade/leverage', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_trade_forceorders($params = array()) {
        return $this->request('trade/forceOrders', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_trade_allfillorders($params = array()) {
        return $this->request('trade/allFillOrders', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_trade_openorders($params = array()) {
        return $this->request('trade/openOrders', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_trade_orderdetail($params = array()) {
        return $this->request('trade/orderDetail', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_trade_orderhistory($params = array()) {
        return $this->request('trade/orderHistory', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_trade_margintype($params = array()) {
        return $this->request('trade/marginType', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_user_commissionrate($params = array()) {
        return $this->request('user/commissionRate', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_user_positions($params = array()) {
        return $this->request('user/positions', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_get_user_balance($params = array()) {
        return $this->request('user/balance', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_post_trade_order($params = array()) {
        return $this->request('trade/order', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_post_trade_leverage($params = array()) {
        return $this->request('trade/leverage', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_post_trade_allopenorders($params = array()) {
        return $this->request('trade/allOpenOrders', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_post_trade_closeallpositions($params = array()) {
        return $this->request('trade/closeAllPositions', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_post_trade_margintype($params = array()) {
        return $this->request('trade/marginType', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_post_trade_positionmargin($params = array()) {
        return $this->request('trade/positionMargin', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_delete_trade_allopenorders($params = array()) {
        return $this->request('trade/allOpenOrders', array('cswap', 'v1', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function cswap_v1_private_delete_trade_cancelorder($params = array()) {
        return $this->request('trade/cancelOrder', array('cswap', 'v1', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function contract_v1_private_get_allposition($params = array()) {
        return $this->request('allPosition', array('contract', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function contract_v1_private_get_allorders($params = array()) {
        return $this->request('allOrders', array('contract', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function contract_v1_private_get_balance($params = array()) {
        return $this->request('balance', array('contract', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function wallets_v1_private_get_capital_config_getall($params = array()) {
        return $this->request('capital/config/getall', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function wallets_v1_private_get_capital_deposit_address($params = array()) {
        return $this->request('capital/deposit/address', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function wallets_v1_private_get_capital_innertransfer_records($params = array()) {
        return $this->request('capital/innerTransfer/records', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function wallets_v1_private_get_capital_subaccount_deposit_address($params = array()) {
        return $this->request('capital/subAccount/deposit/address', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function wallets_v1_private_get_capital_deposit_subhisrec($params = array()) {
        return $this->request('capital/deposit/subHisrec', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function wallets_v1_private_get_capital_subaccount_innertransfer_records($params = array()) {
        return $this->request('capital/subAccount/innerTransfer/records', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function wallets_v1_private_get_capital_deposit_riskrecords($params = array()) {
        return $this->request('capital/deposit/riskRecords', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function wallets_v1_private_post_capital_withdraw_apply($params = array()) {
        return $this->request('capital/withdraw/apply', array('wallets', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function wallets_v1_private_post_capital_innertransfer_apply($params = array()) {
        return $this->request('capital/innerTransfer/apply', array('wallets', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function wallets_v1_private_post_capital_subaccountinnertransfer_apply($params = array()) {
        return $this->request('capital/subAccountInnerTransfer/apply', array('wallets', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function wallets_v1_private_post_capital_deposit_createsubaddress($params = array()) {
        return $this->request('capital/deposit/createSubAddress', array('wallets', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function subaccount_v1_private_get_list($params = array()) {
        return $this->request('list', array('subAccount', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 10));
    }
    public function subaccount_v1_private_get_assets($params = array()) {
        return $this->request('assets', array('subAccount', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function subaccount_v1_private_get_allaccountbalance($params = array()) {
        return $this->request('allAccountBalance', array('subAccount', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function subaccount_v1_private_post_create($params = array()) {
        return $this->request('create', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 10));
    }
    public function subaccount_v1_private_post_apikey_create($params = array()) {
        return $this->request('apiKey/create', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function subaccount_v1_private_post_apikey_edit($params = array()) {
        return $this->request('apiKey/edit', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function subaccount_v1_private_post_apikey_del($params = array()) {
        return $this->request('apiKey/del', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function subaccount_v1_private_post_updatestatus($params = array()) {
        return $this->request('updateStatus', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 10));
    }
    public function account_v1_private_get_uid($params = array()) {
        return $this->request('uid', array('account', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function account_v1_private_get_apikey_query($params = array()) {
        return $this->request('apiKey/query', array('account', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function account_v1_private_get_account_apipermissions($params = array()) {
        return $this->request('account/apiPermissions', array('account', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function account_v1_private_get_allaccountbalance($params = array()) {
        return $this->request('allAccountBalance', array('account', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function account_v1_private_post_innertransfer_authorizesubaccount($params = array()) {
        return $this->request('innerTransfer/authorizeSubAccount', array('account', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function account_transfer_v1_private_get_subaccount_asset_transferhistory($params = array()) {
        return $this->request('subAccount/asset/transferHistory', array('account', 'transfer', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function account_transfer_v1_private_post_subaccount_transferasset_supportcoins($params = array()) {
        return $this->request('subAccount/transferAsset/supportCoins', array('account', 'transfer', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function account_transfer_v1_private_post_subaccount_transferasset($params = array()) {
        return $this->request('subAccount/transferAsset', array('account', 'transfer', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function user_auth_private_post_userdatastream($params = array()) {
        return $this->request('userDataStream', array('user', 'auth', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function user_auth_private_put_userdatastream($params = array()) {
        return $this->request('userDataStream', array('user', 'auth', 'private'), 'PUT', $params, null, null, array("cost" => 2));
    }
    public function user_auth_private_delete_userdatastream($params = array()) {
        return $this->request('userDataStream', array('user', 'auth', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function copytrading_v1_private_get_swap_trace_currenttrack($params = array()) {
        return $this->request('swap/trace/currentTrack', array('copyTrading', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function copytrading_v1_private_post_swap_trace_closetrackorder($params = array()) {
        return $this->request('swap/trace/closeTrackOrder', array('copyTrading', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function copytrading_v1_private_post_swap_trace_settpsl($params = array()) {
        return $this->request('swap/trace/setTPSL', array('copyTrading', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function copytrading_v1_private_post_spot_trader_sellorder($params = array()) {
        return $this->request('spot/trader/sellOrder', array('copyTrading', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 10));
    }
    public function api_v3_private_get_asset_transfer($params = array()) {
        return $this->request('asset/transfer', array('api', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function api_v3_private_get_asset_transferrecord($params = array()) {
        return $this->request('asset/transferRecord', array('api', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function api_v3_private_get_capital_deposit_hisrec($params = array()) {
        return $this->request('capital/deposit/hisrec', array('api', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function api_v3_private_get_capital_withdraw_history($params = array()) {
        return $this->request('capital/withdraw/history', array('api', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function api_v3_private_post_post_asset_transfer($params = array()) {
        return $this->request('post/asset/transfer', array('api', 'v3', 'private'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function api_asset_v1_private_post_transfer($params = array()) {
        return $this->request('transfer', array('api', 'asset', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function api_asset_v1_public_get_transfer_supportcoins($params = array()) {
        return $this->request('transfer/supportCoins', array('api', 'asset', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function fundV1PrivateGetAccountBalance($params = array()) {
        return $this->request('account/balance', array('fund', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PublicGetServerTime($params = array()) {
        return $this->request('server/time', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PublicGetCommonSymbols($params = array()) {
        return $this->request('common/symbols', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PublicGetMarketTrades($params = array()) {
        return $this->request('market/trades', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PublicGetMarketDepth($params = array()) {
        return $this->request('market/depth', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PublicGetMarketKline($params = array()) {
        return $this->request('market/kline', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PublicGetTicker24hr($params = array()) {
        return $this->request('ticker/24hr', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PublicGetTickerPrice($params = array()) {
        return $this->request('ticker/price', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PublicGetTickerBookTicker($params = array()) {
        return $this->request('ticker/bookTicker', array('spot', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PrivateGetTradeQuery($params = array()) {
        return $this->request('trade/query', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PrivateGetTradeOpenOrders($params = array()) {
        return $this->request('trade/openOrders', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PrivateGetTradeHistoryOrders($params = array()) {
        return $this->request('trade/historyOrders', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV1PrivateGetTradeMyTrades($params = array()) {
        return $this->request('trade/myTrades', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function spotV1PrivateGetUserCommissionRate($params = array()) {
        return $this->request('user/commissionRate', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function spotV1PrivateGetAccountBalance($params = array()) {
        return $this->request('account/balance', array('spot', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function spotV1PrivatePostTradeOrder($params = array()) {
        return $this->request('trade/order', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function spotV1PrivatePostTradeCancel($params = array()) {
        return $this->request('trade/cancel', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function spotV1PrivatePostTradeBatchOrders($params = array()) {
        return $this->request('trade/batchOrders', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spotV1PrivatePostTradeOrderCancelReplace($params = array()) {
        return $this->request('trade/order/cancelReplace', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spotV1PrivatePostTradeCancelOrders($params = array()) {
        return $this->request('trade/cancelOrders', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spotV1PrivatePostTradeCancelOpenOrders($params = array()) {
        return $this->request('trade/cancelOpenOrders', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spotV1PrivatePostTradeCancelAllAfter($params = array()) {
        return $this->request('trade/cancelAllAfter', array('spot', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function spotV2PublicGetMarketDepth($params = array()) {
        return $this->request('market/depth', array('spot', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV2PublicGetMarketKline($params = array()) {
        return $this->request('market/kline', array('spot', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV3PrivateGetGetAssetTransfer($params = array()) {
        return $this->request('get/asset/transfer', array('spot', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV3PrivateGetAssetTransfer($params = array()) {
        return $this->request('asset/transfer', array('spot', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV3PrivateGetCapitalDepositHisrec($params = array()) {
        return $this->request('capital/deposit/hisrec', array('spot', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV3PrivateGetCapitalWithdrawHistory($params = array()) {
        return $this->request('capital/withdraw/history', array('spot', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function spotV3PrivatePostPostAssetTransfer($params = array()) {
        return $this->request('post/asset/transfer', array('spot', 'v3', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV1PublicGetTickerPrice($params = array()) {
        return $this->request('ticker/price', array('swap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV1PublicGetMarketHistoricalTrades($params = array()) {
        return $this->request('market/historicalTrades', array('swap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV1PublicGetMarketMarkPriceKlines($params = array()) {
        return $this->request('market/markPriceKlines', array('swap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV1PublicGetTradeMultiAssetsRules($params = array()) {
        return $this->request('trade/multiAssetsRules', array('swap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV1PrivateGetPositionSideDual($params = array()) {
        return $this->request('positionSide/dual', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivateGetTradeBatchCancelReplace($params = array()) {
        return $this->request('trade/batchCancelReplace', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivateGetTradeFullOrder($params = array()) {
        return $this->request('trade/fullOrder', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV1PrivateGetMaintMarginRatio($params = array()) {
        return $this->request('maintMarginRatio', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV1PrivateGetTradePositionHistory($params = array()) {
        return $this->request('trade/positionHistory', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV1PrivateGetPositionMarginHistory($params = array()) {
        return $this->request('positionMargin/history', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV1PrivateGetTwapOpenOrders($params = array()) {
        return $this->request('twap/openOrders', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivateGetTwapHistoryOrders($params = array()) {
        return $this->request('twap/historyOrders', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivateGetTwapOrderDetail($params = array()) {
        return $this->request('twap/orderDetail', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivateGetTradeAssetMode($params = array()) {
        return $this->request('trade/assetMode', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivateGetUserMarginAssets($params = array()) {
        return $this->request('user/marginAssets', array('swap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivatePostTradeCancelReplace($params = array()) {
        return $this->request('trade/cancelReplace', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swapV1PrivatePostPositionSideDual($params = array()) {
        return $this->request('positionSide/dual', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivatePostTradeBatchCancelReplace($params = array()) {
        return $this->request('trade/batchCancelReplace', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivatePostTradeClosePosition($params = array()) {
        return $this->request('trade/closePosition', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swapV1PrivatePostTradeGetVst($params = array()) {
        return $this->request('trade/getVst', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivatePostTwapOrder($params = array()) {
        return $this->request('twap/order', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivatePostTwapCancelOrder($params = array()) {
        return $this->request('twap/cancelOrder', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV1PrivatePostTradeAssetMode($params = array()) {
        return $this->request('trade/assetMode', array('swap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV2PublicGetServerTime($params = array()) {
        return $this->request('server/time', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuoteContracts($params = array()) {
        return $this->request('quote/contracts', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuotePrice($params = array()) {
        return $this->request('quote/price', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuoteDepth($params = array()) {
        return $this->request('quote/depth', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuoteTrades($params = array()) {
        return $this->request('quote/trades', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuotePremiumIndex($params = array()) {
        return $this->request('quote/premiumIndex', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuoteFundingRate($params = array()) {
        return $this->request('quote/fundingRate', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuoteKlines($params = array()) {
        return $this->request('quote/klines', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuoteOpenInterest($params = array()) {
        return $this->request('quote/openInterest', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuoteTicker($params = array()) {
        return $this->request('quote/ticker', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PublicGetQuoteBookTicker($params = array()) {
        return $this->request('quote/bookTicker', array('swap', 'v2', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PrivateGetUserBalance($params = array()) {
        return $this->request('user/balance', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetUserPositions($params = array()) {
        return $this->request('user/positions', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetUserIncome($params = array()) {
        return $this->request('user/income', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetTradeOpenOrders($params = array()) {
        return $this->request('trade/openOrders', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetTradeOpenOrder($params = array()) {
        return $this->request('trade/openOrder', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetTradeOrder($params = array()) {
        return $this->request('trade/order', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetTradeMarginType($params = array()) {
        return $this->request('trade/marginType', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function swapV2PrivateGetTradeLeverage($params = array()) {
        return $this->request('trade/leverage', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetTradeForceOrders($params = array()) {
        return $this->request('trade/forceOrders', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PrivateGetTradeAllOrders($params = array()) {
        return $this->request('trade/allOrders', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetTradeAllFillOrders($params = array()) {
        return $this->request('trade/allFillOrders', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetTradeFillHistory($params = array()) {
        return $this->request('trade/fillHistory', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetUserIncomeExport($params = array()) {
        return $this->request('user/income/export', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetUserCommissionRate($params = array()) {
        return $this->request('user/commissionRate', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateGetQuoteBookTicker($params = array()) {
        return $this->request('quote/bookTicker', array('swap', 'v2', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function swapV2PrivatePostTradeOrder($params = array()) {
        return $this->request('trade/order', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivatePostTradeBatchOrders($params = array()) {
        return $this->request('trade/batchOrders', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivatePostTradeCloseAllPositions($params = array()) {
        return $this->request('trade/closeAllPositions', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivatePostTradeCancelAllAfter($params = array()) {
        return $this->request('trade/cancelAllAfter', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV2PrivatePostTradeMarginType($params = array()) {
        return $this->request('trade/marginType', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV2PrivatePostTradeLeverage($params = array()) {
        return $this->request('trade/leverage', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV2PrivatePostTradePositionMargin($params = array()) {
        return $this->request('trade/positionMargin', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function swapV2PrivatePostTradeOrderTest($params = array()) {
        return $this->request('trade/order/test', array('swap', 'v2', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateDeleteTradeOrder($params = array()) {
        return $this->request('trade/order', array('swap', 'v2', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateDeleteTradeBatchOrders($params = array()) {
        return $this->request('trade/batchOrders', array('swap', 'v2', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function swapV2PrivateDeleteTradeAllOpenOrders($params = array()) {
        return $this->request('trade/allOpenOrders', array('swap', 'v2', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function swapV3PublicGetQuoteKlines($params = array()) {
        return $this->request('quote/klines', array('swap', 'v3', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswapV1PublicGetMarketContracts($params = array()) {
        return $this->request('market/contracts', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswapV1PublicGetMarketPremiumIndex($params = array()) {
        return $this->request('market/premiumIndex', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswapV1PublicGetMarketOpenInterest($params = array()) {
        return $this->request('market/openInterest', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswapV1PublicGetMarketKlines($params = array()) {
        return $this->request('market/klines', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswapV1PublicGetMarketDepth($params = array()) {
        return $this->request('market/depth', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswapV1PublicGetMarketTicker($params = array()) {
        return $this->request('market/ticker', array('cswap', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function cswapV1PrivateGetTradeLeverage($params = array()) {
        return $this->request('trade/leverage', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetTradeForceOrders($params = array()) {
        return $this->request('trade/forceOrders', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetTradeAllFillOrders($params = array()) {
        return $this->request('trade/allFillOrders', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetTradeOpenOrders($params = array()) {
        return $this->request('trade/openOrders', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetTradeOrderDetail($params = array()) {
        return $this->request('trade/orderDetail', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetTradeOrderHistory($params = array()) {
        return $this->request('trade/orderHistory', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetTradeMarginType($params = array()) {
        return $this->request('trade/marginType', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetUserCommissionRate($params = array()) {
        return $this->request('user/commissionRate', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetUserPositions($params = array()) {
        return $this->request('user/positions', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateGetUserBalance($params = array()) {
        return $this->request('user/balance', array('cswap', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivatePostTradeOrder($params = array()) {
        return $this->request('trade/order', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivatePostTradeLeverage($params = array()) {
        return $this->request('trade/leverage', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivatePostTradeAllOpenOrders($params = array()) {
        return $this->request('trade/allOpenOrders', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivatePostTradeCloseAllPositions($params = array()) {
        return $this->request('trade/closeAllPositions', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivatePostTradeMarginType($params = array()) {
        return $this->request('trade/marginType', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivatePostTradePositionMargin($params = array()) {
        return $this->request('trade/positionMargin', array('cswap', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateDeleteTradeAllOpenOrders($params = array()) {
        return $this->request('trade/allOpenOrders', array('cswap', 'v1', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function cswapV1PrivateDeleteTradeCancelOrder($params = array()) {
        return $this->request('trade/cancelOrder', array('cswap', 'v1', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function contractV1PrivateGetAllPosition($params = array()) {
        return $this->request('allPosition', array('contract', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function contractV1PrivateGetAllOrders($params = array()) {
        return $this->request('allOrders', array('contract', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function contractV1PrivateGetBalance($params = array()) {
        return $this->request('balance', array('contract', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function walletsV1PrivateGetCapitalConfigGetall($params = array()) {
        return $this->request('capital/config/getall', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function walletsV1PrivateGetCapitalDepositAddress($params = array()) {
        return $this->request('capital/deposit/address', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function walletsV1PrivateGetCapitalInnerTransferRecords($params = array()) {
        return $this->request('capital/innerTransfer/records', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function walletsV1PrivateGetCapitalSubAccountDepositAddress($params = array()) {
        return $this->request('capital/subAccount/deposit/address', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function walletsV1PrivateGetCapitalDepositSubHisrec($params = array()) {
        return $this->request('capital/deposit/subHisrec', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function walletsV1PrivateGetCapitalSubAccountInnerTransferRecords($params = array()) {
        return $this->request('capital/subAccount/innerTransfer/records', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function walletsV1PrivateGetCapitalDepositRiskRecords($params = array()) {
        return $this->request('capital/deposit/riskRecords', array('wallets', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function walletsV1PrivatePostCapitalWithdrawApply($params = array()) {
        return $this->request('capital/withdraw/apply', array('wallets', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function walletsV1PrivatePostCapitalInnerTransferApply($params = array()) {
        return $this->request('capital/innerTransfer/apply', array('wallets', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function walletsV1PrivatePostCapitalSubAccountInnerTransferApply($params = array()) {
        return $this->request('capital/subAccountInnerTransfer/apply', array('wallets', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function walletsV1PrivatePostCapitalDepositCreateSubAddress($params = array()) {
        return $this->request('capital/deposit/createSubAddress', array('wallets', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function subAccountV1PrivateGetList($params = array()) {
        return $this->request('list', array('subAccount', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 10));
    }
    public function subAccountV1PrivateGetAssets($params = array()) {
        return $this->request('assets', array('subAccount', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function subAccountV1PrivateGetAllAccountBalance($params = array()) {
        return $this->request('allAccountBalance', array('subAccount', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function subAccountV1PrivatePostCreate($params = array()) {
        return $this->request('create', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 10));
    }
    public function subAccountV1PrivatePostApiKeyCreate($params = array()) {
        return $this->request('apiKey/create', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function subAccountV1PrivatePostApiKeyEdit($params = array()) {
        return $this->request('apiKey/edit', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function subAccountV1PrivatePostApiKeyDel($params = array()) {
        return $this->request('apiKey/del', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function subAccountV1PrivatePostUpdateStatus($params = array()) {
        return $this->request('updateStatus', array('subAccount', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 10));
    }
    public function accountV1PrivateGetUid($params = array()) {
        return $this->request('uid', array('account', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function accountV1PrivateGetApiKeyQuery($params = array()) {
        return $this->request('apiKey/query', array('account', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function accountV1PrivateGetAccountApiPermissions($params = array()) {
        return $this->request('account/apiPermissions', array('account', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function accountV1PrivateGetAllAccountBalance($params = array()) {
        return $this->request('allAccountBalance', array('account', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function accountV1PrivatePostInnerTransferAuthorizeSubAccount($params = array()) {
        return $this->request('innerTransfer/authorizeSubAccount', array('account', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function accountTransferV1PrivateGetSubAccountAssetTransferHistory($params = array()) {
        return $this->request('subAccount/asset/transferHistory', array('account', 'transfer', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function accountTransferV1PrivatePostSubAccountTransferAssetSupportCoins($params = array()) {
        return $this->request('subAccount/transferAsset/supportCoins', array('account', 'transfer', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function accountTransferV1PrivatePostSubAccountTransferAsset($params = array()) {
        return $this->request('subAccount/transferAsset', array('account', 'transfer', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function userAuthPrivatePostUserDataStream($params = array()) {
        return $this->request('userDataStream', array('user', 'auth', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function userAuthPrivatePutUserDataStream($params = array()) {
        return $this->request('userDataStream', array('user', 'auth', 'private'), 'PUT', $params, null, null, array("cost" => 2));
    }
    public function userAuthPrivateDeleteUserDataStream($params = array()) {
        return $this->request('userDataStream', array('user', 'auth', 'private'), 'DELETE', $params, null, null, array("cost" => 2));
    }
    public function copyTradingV1PrivateGetSwapTraceCurrentTrack($params = array()) {
        return $this->request('swap/trace/currentTrack', array('copyTrading', 'v1', 'private'), 'GET', $params, null, null, array("cost" => 2));
    }
    public function copyTradingV1PrivatePostSwapTraceCloseTrackOrder($params = array()) {
        return $this->request('swap/trace/closeTrackOrder', array('copyTrading', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function copyTradingV1PrivatePostSwapTraceSetTPSL($params = array()) {
        return $this->request('swap/trace/setTPSL', array('copyTrading', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 2));
    }
    public function copyTradingV1PrivatePostSpotTraderSellOrder($params = array()) {
        return $this->request('spot/trader/sellOrder', array('copyTrading', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 10));
    }
    public function apiV3PrivateGetAssetTransfer($params = array()) {
        return $this->request('asset/transfer', array('api', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function apiV3PrivateGetAssetTransferRecord($params = array()) {
        return $this->request('asset/transferRecord', array('api', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 5));
    }
    public function apiV3PrivateGetCapitalDepositHisrec($params = array()) {
        return $this->request('capital/deposit/hisrec', array('api', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function apiV3PrivateGetCapitalWithdrawHistory($params = array()) {
        return $this->request('capital/withdraw/history', array('api', 'v3', 'private'), 'GET', $params, null, null, array("cost" => 1));
    }
    public function apiV3PrivatePostPostAssetTransfer($params = array()) {
        return $this->request('post/asset/transfer', array('api', 'v3', 'private'), 'POST', $params, null, null, array("cost" => 1));
    }
    public function apiAssetV1PrivatePostTransfer($params = array()) {
        return $this->request('transfer', array('api', 'asset', 'v1', 'private'), 'POST', $params, null, null, array("cost" => 5));
    }
    public function apiAssetV1PublicGetTransferSupportCoins($params = array()) {
        return $this->request('transfer/supportCoins', array('api', 'asset', 'v1', 'public'), 'GET', $params, null, null, array("cost" => 5));
    }
}
