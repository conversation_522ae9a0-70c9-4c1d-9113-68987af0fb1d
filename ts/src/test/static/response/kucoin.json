{"exchange": "kucoin", "skipKeys": [], "options": {}, "methods": {"fetchOpenOrders": [{"description": "fetch spot open order", "method": "fetchOpenOrders", "input": [], "httpResponse": {"code": "200000", "data": {"currentPage": 1, "pageSize": 50, "totalNum": 1, "totalPage": 1, "items": [{"id": "6556088034d111000738f7b8", "symbol": "LTC-USDT", "opType": "DEAL", "type": "limit", "side": "buy", "price": "55", "size": "0.1", "funds": "0", "dealFunds": "0", "dealSize": "0", "fee": "0", "feeCurrency": "USDT", "stp": "", "stop": "", "stopTriggered": false, "stopPrice": "0", "timeInForce": "GTC", "postOnly": false, "hidden": false, "iceberg": false, "visibleSize": "0", "cancelAfter": 0, "channel": "API", "clientOid": "168ea0a0-0cf2-493d-be18-b606be913ce6", "remark": null, "tags": "partner:ccxt", "isActive": true, "cancelExist": false, "createdAt": 1700137089012, "tradeType": "TRADE"}]}}, "parsedResponse": [{"info": {"id": "6556088034d111000738f7b8", "symbol": "LTC-USDT", "opType": "DEAL", "type": "limit", "side": "buy", "price": "55", "size": "0.1", "funds": "0", "dealFunds": "0", "dealSize": "0", "fee": "0", "feeCurrency": "USDT", "stp": "", "stop": "", "stopTriggered": false, "stopPrice": "0", "timeInForce": "GTC", "postOnly": false, "hidden": false, "iceberg": false, "visibleSize": "0", "cancelAfter": 0, "channel": "API", "clientOid": "168ea0a0-0cf2-493d-be18-b606be913ce6", "remark": null, "tags": "partner:ccxt", "isActive": true, "cancelExist": false, "createdAt": 1700137089012, "tradeType": "TRADE"}, "id": "6556088034d111000738f7b8", "clientOrderId": "168ea0a0-0cf2-493d-be18-b606be913ce6", "symbol": "LTC/USDT", "type": "limit", "timeInForce": "GTC", "postOnly": false, "side": "buy", "amount": 0.1, "price": 55, "stopPrice": 0, "triggerPrice": 0, "cost": 0, "filled": 0, "remaining": 0.1, "timestamp": 1700137089012, "datetime": "2023-11-16T12:18:09.012Z", "fee": {"currency": "USDT", "cost": 0}, "status": "open", "lastTradeTimestamp": null, "average": null, "trades": [], "fees": [{"currency": "USDT", "cost": 0}], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchMyTrades": [{"description": "Fetch spot trade", "method": "fetchMyTrades", "input": ["LTC/USDT", null, 10], "httpResponse": {"code": "200000", "data": {"currentPage": 1, "pageSize": 10, "totalNum": 1, "totalPage": 1, "items": [{"symbol": "LTC-USDT", "tradeId": "3607194733924353", "orderId": "65560a018b40f60007d5fe93", "counterOrderId": "65560a017bbc4b0001fed90a", "side": "buy", "liquidity": "taker", "forceTaker": false, "price": "72.8", "size": "0.010055", "funds": "0.732004", "fee": "0.000732004", "feeRate": "0.001", "feeCurrency": "USDT", "stop": "", "tradeType": "TRADE", "type": "market", "createdAt": 1700137473000}]}}, "parsedResponse": [{"info": {"symbol": "LTC-USDT", "tradeId": "3607194733924353", "orderId": "65560a018b40f60007d5fe93", "counterOrderId": "65560a017bbc4b0001fed90a", "side": "buy", "liquidity": "taker", "forceTaker": false, "price": "72.8", "size": "0.010055", "funds": "0.732004", "fee": "0.000732004", "feeRate": "0.001", "feeCurrency": "USDT", "stop": "", "tradeType": "TRADE", "type": "market", "createdAt": 1700137473000}, "id": "3607194733924353", "order": "65560a018b40f60007d5fe93", "timestamp": 1700137473000, "datetime": "2023-11-16T12:24:33.000Z", "symbol": "LTC/USDT", "type": "market", "takerOrMaker": "taker", "side": "buy", "price": 72.8, "amount": 0.010055, "cost": 0.732004, "fee": {"cost": 0.000732004, "currency": "USDT", "rate": 0.001}, "fees": [{"cost": 0.000732004, "currency": "USDT", "rate": 0.001}]}]}], "fetchClosedOrders": [{"description": "spot closed orders", "method": "fetchClosedOrders", "input": ["LTC/USDT", null, 10], "httpResponse": {"code": "200000", "data": {"currentPage": 1, "pageSize": 10, "totalNum": 1, "totalPage": 1, "items": [{"id": "65ba3fc497c9e300079f2003", "symbol": "LTC-USDT", "opType": "DEAL", "type": "market", "side": "buy", "price": "0", "size": "0.1", "funds": "0", "dealFunds": "6.81", "dealSize": "0.1", "fee": "0.00681", "feeCurrency": "USDT", "stp": "", "stop": "", "stopTriggered": false, "stopPrice": "0", "timeInForce": "GTC", "postOnly": false, "hidden": false, "iceberg": false, "visibleSize": "0", "cancelAfter": 0, "channel": "API", "clientOid": "ce802c82-3b80-4adb-8da5-c39103f102af", "remark": null, "tags": "partner:ccxt", "isActive": false, "cancelExist": false, "createdAt": 1706704836933, "tradeType": "TRADE"}]}}, "parsedResponse": [{"info": {"id": "65ba3fc497c9e300079f2003", "symbol": "LTC-USDT", "opType": "DEAL", "type": "market", "side": "buy", "price": "0", "size": "0.1", "funds": "0", "dealFunds": "6.81", "dealSize": "0.1", "fee": "0.00681", "feeCurrency": "USDT", "stp": "", "stop": "", "stopTriggered": false, "stopPrice": "0", "timeInForce": "GTC", "postOnly": false, "hidden": false, "iceberg": false, "visibleSize": "0", "cancelAfter": 0, "channel": "API", "clientOid": "ce802c82-3b80-4adb-8da5-c39103f102af", "remark": null, "tags": "partner:ccxt", "isActive": false, "cancelExist": false, "createdAt": 1706704836933, "tradeType": "TRADE"}, "id": "65ba3fc497c9e300079f2003", "clientOrderId": "ce802c82-3b80-4adb-8da5-c39103f102af", "symbol": "LTC/USDT", "type": "market", "timeInForce": "GTC", "postOnly": false, "side": "buy", "amount": 0.1, "price": 68.1, "stopPrice": 0, "triggerPrice": 0, "cost": 6.81, "filled": 0.1, "remaining": 0, "timestamp": 1706704836933, "datetime": "2024-01-31T12:40:36.933Z", "fee": {"currency": "USDT", "cost": 0.00681}, "status": "closed", "lastTradeTimestamp": null, "average": 68.1, "trades": [], "fees": [{"currency": "USDT", "cost": 0.00681}], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}]}], "fetchOHLCV": [{"description": "spot ohlcv with since and limit", "method": "fetchOHLCV", "input": ["BTC/USDT", "1h", 1706628191000, 3], "httpResponse": {"code": "200000", "data": [["1706637600", "43396.4", "43369.4", "43491.9", "43285.2", "135.********", "5859577.939581874"], ["1706634000", "43501.1", "43396.4", "43598.9", "43305.2", "101.********", "4390218.48746545"], ["1706630400", "43383.7", "43501", "43521.9", "43222.8", "101.********", "4391287.808186813"]]}, "parsedResponse": [[*************, 43383.7, 43521.9, 43222.8, 43501, 101.********], [*************, 43501.1, 43598.9, 43305.2, 43396.4, 101.********], [*************, 43396.4, 43491.9, 43285.2, 43369.4, 135.********]]}], "fetchLedger": [{"description": "fetch usdt ledger", "method": "fetchLedger", "input": [], "httpResponse": {"code": "200000", "data": {"currentPage": 1, "pageSize": 50, "totalNum": 1, "totalPage": 1, "items": [{"id": "65ca404f77c7250007b8ddd7", "currency": "USDT", "amount": "5", "fee": "0", "balance": "0", "accountType": "TRADE", "bizType": "Transfer", "direction": "out", "createdAt": *************, "context": ""}]}}, "parsedResponse": [{"id": "65ca404f77c7250007b8ddd7", "direction": "out", "account": "TRADE", "referenceId": null, "referenceAccount": "TRADE", "type": "transfer", "currency": "USDT", "amount": 5, "timestamp": *************, "datetime": "2024-02-12T15:59:11.875Z", "before": null, "after": null, "status": null, "fee": null, "info": {"id": "65ca404f77c7250007b8ddd7", "currency": "USDT", "amount": "5", "fee": "0", "balance": "0", "accountType": "TRADE", "bizType": "Transfer", "direction": "out", "createdAt": *************, "context": ""}}]}], "fetchOrder": [{"description": "fetch order", "method": "fetchOrder", "input": ["65e371dd0ddaa40007cb437b", "LTC/USDT"], "httpResponse": {"code": "200000", "data": {"id": "65e371dd0ddaa40007cb437b", "symbol": "LTC-USDT", "opType": "DEAL", "type": "market", "side": "buy", "price": "0", "size": "0.01", "funds": "0", "dealFunds": "0.91937", "dealSize": "0.01", "fee": "0.********", "feeCurrency": "USDT", "stp": "", "stop": "", "stopTriggered": false, "stopPrice": "0", "timeInForce": "GTC", "postOnly": false, "hidden": false, "iceberg": false, "visibleSize": "0", "cancelAfter": 0, "channel": "API", "clientOid": "6d92a4dc-7c44-40e9-afd8-a25cfb86384a", "remark": null, "tags": "partner:ccxt", "isActive": false, "cancelExist": false, "createdAt": *************, "tradeType": "TRADE"}}, "parsedResponse": {"info": {"id": "65e371dd0ddaa40007cb437b", "symbol": "LTC-USDT", "opType": "DEAL", "type": "market", "side": "buy", "price": "0", "size": "0.01", "funds": "0", "dealFunds": "0.91937", "dealSize": "0.01", "fee": "0.********", "feeCurrency": "USDT", "stp": "", "stop": "", "stopTriggered": false, "stopPrice": "0", "timeInForce": "GTC", "postOnly": false, "hidden": false, "iceberg": false, "visibleSize": "0", "cancelAfter": 0, "channel": "API", "clientOid": "6d92a4dc-7c44-40e9-afd8-a25cfb86384a", "remark": null, "tags": "partner:ccxt", "isActive": false, "cancelExist": false, "createdAt": *************, "tradeType": "TRADE"}, "id": "65e371dd0ddaa40007cb437b", "clientOrderId": "6d92a4dc-7c44-40e9-afd8-a25cfb86384a", "symbol": "LTC/USDT", "type": "market", "timeInForce": "GTC", "postOnly": false, "side": "buy", "amount": 0.01, "price": 91.937, "stopPrice": 0, "triggerPrice": 0, "cost": 0.91937, "filled": 0.01, "remaining": 0, "timestamp": *************, "datetime": "2024-03-02T18:37:17.667Z", "fee": {"currency": "USDT", "cost": 0.********}, "status": "closed", "lastTradeTimestamp": null, "average": 91.937, "trades": [], "fees": [{"currency": "USDT", "cost": 0.********}], "lastUpdateTimestamp": null, "reduceOnly": null, "takeProfitPrice": null, "stopLossPrice": null}}], "fetchTrades": [{"description": "fetchTrades", "method": "fetchTrades", "disabledGO": true, "input": ["BTC/USDT", null, 1], "httpResponse": {"code": "200000", "data": [{"sequence": "7613805866401793", "price": "67347.4", "size": "0.00453030", "side": "sell", "time": 1709897616745000000}]}, "parsedResponse": [{"info": {"sequence": "7613805866401793", "price": "67347.4", "size": "0.00453030", "side": "sell", "time": 1709897616745000000}, "id": null, "order": null, "timestamp": 1709897616745, "datetime": "2024-03-08T11:33:36.745Z", "symbol": "BTC/USDT", "type": null, "takerOrMaker": null, "side": "sell", "price": 67347.4, "amount": 0.0045303, "cost": 305.10392622, "fee": {"cost": null, "currency": null}, "fees": []}]}], "fetchTicker": [{"description": "fetch ticker", "method": "fetchTicker", "input": ["BTC/USDT"], "httpResponse": {"code": "200000", "data": {"time": 1709897710012, "symbol": "BTC-USDT", "buy": "67399.9", "sell": "67400", "changeRate": "0.0089", "changePrice": "597.9", "high": "67978.8", "low": "66541.4", "vol": "6442.17728633", "volValue": "433203364.463489651", "last": "67400", "averagePrice": "66654.25518537", "takerFeeRate": "0.001", "makerFeeRate": "0.001", "takerCoefficient": "1", "makerCoefficient": "1"}}, "parsedResponse": {"symbol": "BTC/USDT", "timestamp": 1709897710012, "datetime": "2024-03-08T11:35:10.012Z", "high": 67978.8, "low": 66541.4, "bid": 67399.9, "bidVolume": null, "ask": 67400, "askVolume": null, "vwap": 67244.86849853185, "open": 66802.1, "close": 67400, "last": 67400, "previousClose": null, "change": 597.9, "percentage": 0.89, "average": 66654.25518537, "baseVolume": 6442.17728633, "quoteVolume": 433203364.46348965, "markPrice": null, "indexPrice": null, "info": {"time": 1709897710012, "symbol": "BTC-USDT", "buy": "67399.9", "sell": "67400", "changeRate": "0.0089", "changePrice": "597.9", "high": "67978.8", "low": "66541.4", "vol": "6442.17728633", "volValue": "433203364.463489651", "last": "67400", "averagePrice": "66654.25518537", "takerFeeRate": "0.001", "makerFeeRate": "0.001", "takerCoefficient": "1", "makerCoefficient": "1"}}}], "withdraw": [{"description": "with networkCode", "method": "withdraw", "input": ["USDT", 3, "******************************************", null, {"network": "OP"}], "httpResponse": {"code": "200000", "data": {"withdrawalId": "6717ed803a3595000738e671"}}, "parsedResponse": {"info": {"withdrawalId": "6717ed803a3595000738e671"}, "id": "6717ed803a3595000738e671", "timestamp": null, "datetime": null, "network": null, "address": null, "addressTo": null, "addressFrom": null, "tag": null, "tagTo": null, "tagFrom": null, "currency": "USDT", "amount": null, "txid": null, "type": "deposit", "status": null, "comment": null, "internal": null, "fee": null, "updated": null}}], "fetchBalance": [{"description": "fetch balance", "method": "fetchBalance", "input": [], "httpResponse": {"code": "200000", "data": [{"id": "64feeeb6b4f1fb0007948e80", "currency": "USDT", "type": "trade", "balance": "40.********", "available": "40.********", "holds": "0"}, {"id": "64feeec4cd03f30007aa5a8f", "currency": "LTC", "type": "trade", "balance": "0.171021", "available": "0.171021", "holds": "0"}, {"id": "64ff093e478dba00072d3bc7", "currency": "BTC", "type": "trade", "balance": "0", "available": "0", "holds": "0"}, {"id": "655e450a1245b90007517903", "currency": "TRX", "type": "trade", "balance": "0", "available": "0", "holds": "0"}]}, "parsedResponse": {"info": {"code": "200000", "data": [{"id": "64feeeb6b4f1fb0007948e80", "currency": "USDT", "type": "trade", "balance": "40.********", "available": "40.********", "holds": "0"}, {"id": "64feeec4cd03f30007aa5a8f", "currency": "LTC", "type": "trade", "balance": "0.171021", "available": "0.171021", "holds": "0"}, {"id": "64ff093e478dba00072d3bc7", "currency": "BTC", "type": "trade", "balance": "0", "available": "0", "holds": "0"}, {"id": "655e450a1245b90007517903", "currency": "TRX", "type": "trade", "balance": "0", "available": "0", "holds": "0"}]}, "timestamp": null, "datetime": null, "USDT": {"free": 40.********, "used": 0, "total": 40.********}, "LTC": {"free": 0.171021, "used": 0, "total": 0.171021}, "BTC": {"free": 0, "used": 0, "total": 0}, "TRX": {"free": 0, "used": 0, "total": 0}, "free": {"USDT": 40.********, "LTC": 0.171021, "BTC": 0, "TRX": 0}, "used": {"USDT": 0, "LTC": 0, "BTC": 0, "TRX": 0}, "total": {"USDT": 40.********, "LTC": 0.171021, "BTC": 0, "TRX": 0}}}, {"description": "cross balance", "method": "fetchBalance", "input": [{"marginMode": "cross"}], "httpResponse": {"code": "200000", "data": {"debtRatio": "0", "accounts": [{"currency": "USDT", "totalBalance": "5", "availableBalance": "5", "holdBalance": "0", "liability": "0", "maxBorrowSize": "20"}]}}, "parsedResponse": {"info": {"code": "200000", "data": {"debtRatio": "0", "accounts": [{"currency": "USDT", "totalBalance": "5", "availableBalance": "5", "holdBalance": "0", "liability": "0", "maxBorrowSize": "20"}]}}, "timestamp": null, "datetime": null, "USDT": {"free": 5, "used": 0, "total": 5, "debt": 0}, "free": {"USDT": 5}, "used": {"USDT": 0}, "total": {"USDT": 5}, "debt": {"USDT": 0}}}, {"description": "isolated balance", "method": "fetchBalance", "input": [{"marginMode": "isolated"}], "httpResponse": {"code": "200000", "data": {"totalAssetOfQuoteCurrency": "5", "totalLiabilityOfQuoteCurrency": "0", "timestamp": *************, "assets": [{"symbol": "LTC-USDT", "status": "EFFECTIVE", "debtRatio": "0", "baseAsset": {"currency": "LTC", "borrowEnabled": true, "transferInEnabled": true, "total": "0", "hold": "0", "available": "0", "liability": "0", "interest": "0", "maxBorrowSize": "0.4"}, "quoteAsset": {"currency": "USDT", "borrowEnabled": true, "transferInEnabled": true, "total": "5", "hold": "0", "available": "5", "liability": "0", "interest": "0", "maxBorrowSize": "45"}}]}}, "parsedResponse": {"info": {"code": "200000", "data": {"totalAssetOfQuoteCurrency": "5", "totalLiabilityOfQuoteCurrency": "0", "timestamp": *************, "assets": [{"symbol": "LTC-USDT", "status": "EFFECTIVE", "debtRatio": "0", "baseAsset": {"currency": "LTC", "borrowEnabled": true, "transferInEnabled": true, "total": "0", "hold": "0", "available": "0", "liability": "0", "interest": "0", "maxBorrowSize": "0.4"}, "quoteAsset": {"currency": "USDT", "borrowEnabled": true, "transferInEnabled": true, "total": "5", "hold": "0", "available": "5", "liability": "0", "interest": "0", "maxBorrowSize": "45"}}]}}, "timestamp": null, "datetime": null, "LTC/USDT": {"LTC": {"free": 0, "used": 0, "total": 0, "debt": 0}, "USDT": {"free": 5, "used": 0, "total": 5, "debt": 0}, "free": {"LTC": 0, "USDT": 5}, "used": {"LTC": 0, "USDT": 0}, "total": {"LTC": 0, "USDT": 5}, "debt": {"LTC": 0, "USDT": 0}}}}], "fetchOrderBook": [{"description": "fetch orderbook", "method": "fetchOrderBook", "input": ["BTC/USDT", 20], "httpResponse": {"code": "200000", "data": {"time": 1709898508055, "sequence": "11149534850", "bids": [["67539.7", "1.1829567"], ["67538.5", "0.25229199"]], "asks": [["67539.8", "1.08883964"], ["67539.9", "0.0001"]]}}, "parsedResponse": {"symbol": "BTC/USDT", "bids": [[67539.7, 1.1829567], [67538.5, 0.25229199]], "asks": [[67539.8, 1.08883964], [67539.9, 0.0001]], "timestamp": 1709898508055, "datetime": "2024-03-08T11:48:28.055Z", "nonce": 11149534850}}], "fetchTransactionFee": [{"description": "Fetch transaction fee", "method": "fetchTransactionFee", "input": ["BTC"], "httpResponse": {"code": "200000", "data": {"currency": "BTC", "limitBTCAmount": "15.71610959", "usedBTCAmount": "0.00000000", "quotaCurrency": "USDT", "limitQuotaCurrencyAmount": "999999.00000000", "usedQuotaCurrencyAmount": "0", "remainAmount": "15.71610959", "availableAmount": "0", "withdrawMinFee": "0.0004", "innerWithdrawMinFee": "0", "withdrawMinSize": "0.001", "isWithdrawEnabled": true, "precision": 8, "chain": "BTC", "reason": null, "lockedAmount": "0"}}, "parsedResponse": {"info": {"code": "200000", "data": {"currency": "BTC", "limitBTCAmount": "15.71610959", "usedBTCAmount": "0.00000000", "quotaCurrency": "USDT", "limitQuotaCurrencyAmount": "999999.00000000", "usedQuotaCurrencyAmount": "0", "remainAmount": "15.71610959", "availableAmount": "0", "withdrawMinFee": "0.0004", "innerWithdrawMinFee": "0", "withdrawMinSize": "0.001", "isWithdrawEnabled": true, "precision": 8, "chain": "BTC", "reason": null, "lockedAmount": "0"}}, "withdraw": {"BTC": 0.0004}, "deposit": {}}}], "createDepositAddress": [{"description": "with network", "method": "createDepositAddress", "input": ["BNB", {"networkCode": "BEP20"}], "httpResponse": {"code": "200000", "data": {"address": "******************************************", "memo": null, "chainId": "bsc", "to": "MAIN", "expirationDate": 0, "currency": "BNB", "chainName": "BEP20"}}, "parsedResponse": {"info": {"address": "******************************************", "memo": null, "chainId": "bsc", "to": "MAIN", "expirationDate": 0, "currency": "BNB", "chainName": "BEP20"}, "currency": "BNB", "network": "BEP20", "address": "******************************************", "tag": null}}], "fetchCurrencies": [{"description": "fetchCurrencies", "method": "fetchCurrencies", "input": [], "httpResponse": {"code": "200000", "data": [{"currency": "USDT", "name": "USDT", "fullName": "<PERSON><PERSON>", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "APT", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "aptos"}, {"chainName": "<PERSON><PERSON>(Polkadot)", "withdrawalMinSize": "10", "depositMinSize": "0.7", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "1984", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "statemint"}, {"chainName": "OPTIMISM", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 500, "preConfirms": 100, "contractAddress": "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, {"chainName": "KCC", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x0039f574ee5cc39bdd162e9a88e3eb1f111baf48", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, {"chainName": "SOL", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 200, "preConfirms": 200, "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sol"}, {"chainName": "TON", "withdrawalMinSize": "1", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "ton"}, {"chainName": "BEP20", "withdrawalMinSize": "10", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, {"chainName": "KAVA EVM Co-Chain", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x919c1c267bc06a7039e03fcc2ef738525769109c", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "<PERSON><PERSON><PERSON><PERSON>"}, {"chainName": "Polygon POS", "withdrawalMinSize": "35", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "35", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 800, "preConfirms": 300, "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "matic"}, {"chainName": "ERC20", "withdrawalMinSize": "30", "depositMinSize": "7.5", "withdrawFeeRate": "0.002", "withdrawMaxFee": "36", "withdrawalMinFee": "5.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, {"chainName": "AVAX C-Chain", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 32, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "avaxc"}, {"chainName": "NEAR", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 5, "preConfirms": 5, "contractAddress": "usdt.tether-token.near", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "near"}, {"chainName": "TRC20", "withdrawalMinSize": "3", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 3, "preConfirms": 3, "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "trx"}, {"chainName": "XTZ", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 10, "preConfirms": 10, "contractAddress": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "xtz"}, {"chainName": "ARBITRUM", "withdrawalMinSize": "5", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}]}]}, "parsedResponse": {"USDT": {"info": {"currency": "USDT", "name": "USDT", "fullName": "<PERSON><PERSON>", "precision": 8, "confirms": null, "contractAddress": null, "isMarginEnabled": true, "isDebitEnabled": true, "chains": [{"chainName": "APT", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "aptos"}, {"chainName": "<PERSON><PERSON>(Polkadot)", "withdrawalMinSize": "10", "depositMinSize": "0.7", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "1984", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "statemint"}, {"chainName": "OPTIMISM", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 500, "preConfirms": 100, "contractAddress": "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, {"chainName": "KCC", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x0039f574ee5cc39bdd162e9a88e3eb1f111baf48", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, {"chainName": "SOL", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 200, "preConfirms": 200, "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sol"}, {"chainName": "TON", "withdrawalMinSize": "1", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "ton"}, {"chainName": "BEP20", "withdrawalMinSize": "10", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, {"chainName": "KAVA EVM Co-Chain", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x919c1c267bc06a7039e03fcc2ef738525769109c", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "<PERSON><PERSON><PERSON><PERSON>"}, {"chainName": "Polygon POS", "withdrawalMinSize": "35", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "35", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 800, "preConfirms": 300, "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "matic"}, {"chainName": "ERC20", "withdrawalMinSize": "30", "depositMinSize": "7.5", "withdrawFeeRate": "0.002", "withdrawMaxFee": "36", "withdrawalMinFee": "5.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, {"chainName": "AVAX C-Chain", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 32, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "avaxc"}, {"chainName": "NEAR", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 5, "preConfirms": 5, "contractAddress": "usdt.tether-token.near", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "near"}, {"chainName": "TRC20", "withdrawalMinSize": "3", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 3, "preConfirms": 3, "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "trx"}, {"chainName": "XTZ", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 10, "preConfirms": 10, "contractAddress": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "xtz"}, {"chainName": "ARBITRUM", "withdrawalMinSize": "5", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}]}, "id": "USDT", "numericId": null, "code": "USDT", "precision": 1e-06, "type": "crypto", "name": "<PERSON><PERSON>", "active": true, "deposit": true, "withdraw": true, "fee": 0.5, "fees": {}, "networks": {"APT": {"info": {"chainName": "APT", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "aptos"}, "id": "aptos", "name": "APT", "code": "APT", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": null, "max": null}}}, "statemint": {"info": {"chainName": "<PERSON><PERSON>(Polkadot)", "withdrawalMinSize": "10", "depositMinSize": "0.7", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 30, "preConfirms": 30, "contractAddress": "1984", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "statemint"}, "id": "statemint", "name": "<PERSON><PERSON>(Polkadot)", "code": "statemint", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 0.7, "max": null}}}, "OPTIMISM": {"info": {"chainName": "OPTIMISM", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 500, "preConfirms": 100, "contractAddress": "0x94b008aa00579c1307b0ef2c499ad98a8ce58e58", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "optimism"}, "id": "optimism", "name": "OPTIMISM", "code": "OPTIMISM", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": null, "max": null}}}, "KCC": {"info": {"chainName": "KCC", "withdrawalMinSize": "1", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x0039f574ee5cc39bdd162e9a88e3eb1f111baf48", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "kcc"}, "id": "kcc", "name": "KCC", "code": "KCC", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": null, "max": null}}}, "SOL": {"info": {"chainName": "SOL", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 200, "preConfirms": 200, "contractAddress": "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "sol"}, "id": "sol", "name": "SOL", "code": "SOL", "active": true, "fee": 1.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": null, "max": null}}}, "TON": {"info": {"chainName": "TON", "withdrawalMinSize": "1", "depositMinSize": "0.1", "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 1, "preConfirms": 1, "contractAddress": "EQCxE6mUtQJKFnGfaROTKOt1lZbDiiX1kCixRv7Nw2Id_sDs", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": true, "chainId": "ton"}, "id": "ton", "name": "TON", "code": "TON", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 1, "max": null}, "deposit": {"min": 0.1, "max": null}}}, "BEP20": {"info": {"chainName": "BEP20", "withdrawalMinSize": "10", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 60, "preConfirms": 15, "contractAddress": "0x55d398326f99059ff775485246999027b3197955", "withdrawPrecision": 8, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "bsc"}, "id": "bsc", "name": "BEP20", "code": "BEP20", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-08, "limits": {"withdraw": {"min": 10, "max": null}, "deposit": {"min": 1, "max": null}}}, "kavaevm": {"info": {"chainName": "KAVA EVM Co-Chain", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 20, "preConfirms": 20, "contractAddress": "0x919c1c267bc06a7039e03fcc2ef738525769109c", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "<PERSON><PERSON><PERSON><PERSON>"}, "id": "<PERSON><PERSON><PERSON><PERSON>", "name": "KAVA EVM Co-Chain", "code": "<PERSON><PERSON><PERSON><PERSON>", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": null, "max": null}}}, "MATIC": {"info": {"chainName": "Polygon POS", "withdrawalMinSize": "35", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "35", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 800, "preConfirms": 300, "contractAddress": "0xc2132d05d31c914a87c6611c10748aeb04b58e8f", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "matic"}, "id": "matic", "name": "Polygon POS", "code": "MATIC", "active": false, "fee": 35, "deposit": true, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 35, "max": null}, "deposit": {"min": null, "max": null}}}, "ERC20": {"info": {"chainName": "ERC20", "withdrawalMinSize": "30", "depositMinSize": "7.5", "withdrawFeeRate": "0.002", "withdrawMaxFee": "36", "withdrawalMinFee": "5.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 96, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "eth"}, "id": "eth", "name": "ERC20", "code": "ERC20", "active": true, "fee": 5.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 30, "max": null}, "deposit": {"min": 7.5, "max": null}}}, "AVAXC": {"info": {"chainName": "AVAX C-Chain", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 32, "preConfirms": 32, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "avaxc"}, "id": "avaxc", "name": "AVAX C-Chain", "code": "AVAXC", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": null, "max": null}}}, "NEAR": {"info": {"chainName": "NEAR", "withdrawalMinSize": "3", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "0.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 5, "preConfirms": 5, "contractAddress": "usdt.tether-token.near", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "near"}, "id": "near", "name": "NEAR", "code": "NEAR", "active": true, "fee": 0.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": null, "max": null}}}, "TRC20": {"info": {"chainName": "TRC20", "withdrawalMinSize": "3", "depositMinSize": "1", "withdrawFeeRate": "0", "withdrawalMinFee": "1.5", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 3, "preConfirms": 3, "contractAddress": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "trx"}, "id": "trx", "name": "TRC20", "code": "TRC20", "active": true, "fee": 1.5, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 3, "max": null}, "deposit": {"min": 1, "max": null}}}, "XTZ": {"info": {"chainName": "XTZ", "withdrawalMinSize": "2", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": true, "isDepositEnabled": true, "confirms": 10, "preConfirms": 10, "contractAddress": "KT1XnTn74bUtxHfDtBmm2bGZAQfhPbvKWR8o", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "xtz"}, "id": "xtz", "name": "XTZ", "code": "XTZ", "active": true, "fee": 1, "deposit": true, "withdraw": true, "precision": 1e-06, "limits": {"withdraw": {"min": 2, "max": null}, "deposit": {"min": null, "max": null}}}, "ARBONE": {"info": {"chainName": "ARBITRUM", "withdrawalMinSize": "5", "depositMinSize": null, "withdrawFeeRate": "0", "withdrawalMinFee": "1", "isWithdrawEnabled": false, "isDepositEnabled": true, "confirms": 1900, "preConfirms": 120, "contractAddress": "******************************************", "withdrawPrecision": 6, "maxWithdraw": null, "maxDeposit": null, "needTag": false, "chainId": "arbitrum"}, "id": "arbitrum", "name": "ARBITRUM", "code": "ARBONE", "active": false, "fee": 1, "deposit": true, "withdraw": false, "precision": 1e-06, "limits": {"withdraw": {"min": 5, "max": null}, "deposit": {"min": null, "max": null}}}}, "limits": {"deposit": {"min": 0.1, "max": null}, "withdraw": {"min": 1, "max": null}}}}}], "fetchBorrowInterest": [{"description": "cross fetch borrow interest", "method": "fetchBorrowInterest", "input": ["USDT"], "httpResponse": {"code": "200000", "data": {"totalAssetOfQuoteCurrency": "5", "totalLiabilityOfQuoteCurrency": "0", "debtRatio": "0", "status": "EFFECTIVE", "accounts": [{"currency": "USDT", "total": "5", "available": "5", "hold": "0", "liability": "0", "maxBorrowSize": "20", "borrowEnabled": true, "transferInEnabled": true}, {"currency": "1INCH", "total": "0", "available": "0", "hold": "0", "liability": "0", "maxBorrowSize": "70", "borrowEnabled": true, "transferInEnabled": true}, {"currency": "ZRO", "total": "0", "available": "0", "hold": "0", "liability": "0", "maxBorrowSize": "5", "borrowEnabled": true, "transferInEnabled": true}]}}, "parsedResponse": [{"info": {"currency": "USDT", "total": "5", "available": "5", "hold": "0", "liability": "0", "maxBorrowSize": "20", "borrowEnabled": true, "transferInEnabled": true}, "symbol": null, "currency": "USDT", "interest": null, "interestRate": null, "amountBorrowed": 0, "marginMode": "cross", "timestamp": null, "datetime": null}]}, {"description": "isolated fetch borrow interest", "method": "fetchBorrowInterest", "input": ["BTC", "BTC/USDT", null, null, {"marginMode": "isolated"}], "httpResponse": {"code": "200000", "data": {"totalAssetOfQuoteCurrency": "5", "totalLiabilityOfQuoteCurrency": "0", "timestamp": 1731485747638, "assets": [{"symbol": "LTC-USDT", "status": "EFFECTIVE", "debtRatio": "0", "baseAsset": {"currency": "LTC", "borrowEnabled": true, "transferInEnabled": true, "liability": "0", "total": "0", "available": "0", "hold": "0", "maxBorrowSize": "0.6"}, "quoteAsset": {"currency": "USDT", "borrowEnabled": true, "transferInEnabled": true, "liability": "0", "total": "5", "available": "5", "hold": "0", "maxBorrowSize": "45"}}, {"symbol": "BTC-USDT", "status": "EFFECTIVE", "debtRatio": "0", "baseAsset": {"currency": "BTC", "borrowEnabled": true, "transferInEnabled": true, "liability": "0", "total": "0", "available": "0", "hold": "0", "maxBorrowSize": "0"}, "quoteAsset": {"currency": "USDT", "borrowEnabled": true, "transferInEnabled": true, "liability": "0", "total": "0", "available": "0", "hold": "0", "maxBorrowSize": "0"}}, {"symbol": "ZRO-USDT", "status": "EFFECTIVE", "debtRatio": "0", "baseAsset": {"currency": "ZRO", "borrowEnabled": true, "transferInEnabled": true, "liability": "0", "total": "0", "available": "0", "hold": "0", "maxBorrowSize": "0"}, "quoteAsset": {"currency": "USDT", "borrowEnabled": true, "transferInEnabled": true, "liability": "0", "total": "0", "available": "0", "hold": "0", "maxBorrowSize": "0"}}]}}, "parsedResponse": [{"info": {"symbol": "BTC-USDT", "status": "EFFECTIVE", "debtRatio": "0", "baseAsset": {"currency": "BTC", "borrowEnabled": true, "transferInEnabled": true, "liability": "0", "total": "0", "available": "0", "hold": "0", "maxBorrowSize": "0"}, "quoteAsset": {"currency": "USDT", "borrowEnabled": true, "transferInEnabled": true, "liability": "0", "total": "0", "available": "0", "hold": "0", "maxBorrowSize": "0"}}, "symbol": "BTC/USDT", "currency": "BTC", "interest": null, "interestRate": null, "amountBorrowed": 0, "marginMode": "isolated", "timestamp": null, "datetime": null}]}]}}