// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

import testSharedMethods from './test.sharedMethods.js';
function testTradingFee(exchange, skippedProperties, method, symbol, entry) {
    const format = {
        'info': {},
        'symbol': 'ETH/BTC',
        'maker': exchange.parseNumber('0.002'),
        'taker': exchange.parseNumber('0.003'),
        // todo: most exchanges do not have the below props implemented, so comment out it temporarily
        // 'percentage': false,
        // 'tierBased': false,
    };
    const emptyAllowedFor = ['tierBased', 'percentage', 'symbol'];
    testSharedMethods.assertStructure(exchange, skippedProperties, method, entry, format, emptyAllowedFor);
    testSharedMethods.assertSymbol(exchange, skippedProperties, method, entry, 'symbol', symbol);
}
export default testTradingFee;
