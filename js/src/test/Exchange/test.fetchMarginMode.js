// ----------------------------------------------------------------------------

// PLEASE DO NOT EDIT THIS FILE, IT IS GENERATED AND WILL BE OVERWRITTEN:
// https://github.com/ccxt/ccxt/blob/master/CONTRIBUTING.md#how-to-contribute-code
// EDIT THE CORRESPONDENT .ts FILE INSTEAD

import testMarginMode from './base/test.marginMode.js';
async function testFetchMarginMode(exchange, skippedProperties, symbol) {
    const method = 'fetchMarginMode';
    const marginMode = await exchange.fetchMarginMode(symbol);
    testMarginMode(exchange, skippedProperties, method, marginMode);
    return true;
}
export default testFetchMarginMode;
